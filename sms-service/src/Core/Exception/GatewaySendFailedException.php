<?php

declare(strict_types = 1);

namespace SmsService\Core\Exception;

use SmsService\Core\Http\ResponseStatusCode;
use Throwable;

class GatewaySendFailedException extends RuntimeException
{

    public function __construct(
        string $message = '',
        ?Throwable $previous = null,
        int $code = ResponseStatusCode::BAD_REQUEST_400,
    )
    {
        parent::__construct($message, $previous, $code);
    }

}
