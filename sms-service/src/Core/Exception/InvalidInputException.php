<?php

declare(strict_types = 1);

namespace SmsService\Core\Exception;

use SmsService\Core\Http\ResponseStatusCode;
use Throwable;

class InvalidInputException extends RuntimeException
{

    /**
     * @param array<string,int|string> $parameters
     */
    public function __construct(string $message, ?Throwable $previous = null, int $code = ResponseStatusCode::BAD_REQUEST_400, array $parameters = [])
    {
        parent::__construct($message, $previous, $code, $parameters);
    }

    public static function createMissingException(string $path): self
    {
        return new self(message: 'Value of %path% is missing.', parameters: ['%path%' => $path]);
    }

    public static function createInvalidTypeException(string $path, string $type): self
    {
        return new self(message: 'Value of %path% must be %type%.', parameters: ['%path%' => $path, '%type%' => $type]);
    }

    public static function fromConstraintViolationList(string $errorMessage): self
    {
        return new self(message: $errorMessage, code: ResponseStatusCode::UNPROCESSABLE_ENTITY_422);
    }

    public static function createUnsupportedFilterTypeException(string $type): self
    {
        return new self(message: 'Invalid filter type %type%.', parameters: ['%type%' => $type]);
    }

}
