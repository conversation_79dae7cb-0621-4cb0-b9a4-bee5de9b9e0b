<?php

declare(strict_types = 1);

namespace SmsService\Core\Exception;

use RuntimeException as NativeRuntimeException;
use SmsService\Core\Http\ResponseStatusCode;
use Throwable;

abstract class RuntimeException extends NativeRuntimeException
{

    /**
     * @param array<string, int|string> $parameters
     */
    protected function __construct(
        string $message = '',
        ?Throwable $previous = null,
        int $code = ResponseStatusCode::BAD_REQUEST_400,
        protected array $parameters = [],
    )
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * @return array<string, int|string>
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }

}
