<?php

namespace SmsService\Core\Notification;

use SmsService\SmsMessage\Entity\SmsMessage;
use Symfony\Component\Notifier\Notification\Notification;

class SmsNotification extends Notification
{

    public function __construct(protected SmsMessage $smsMessage)
    {
        parent::__construct($smsMessage->getContent(), [$smsMessage->getGateway()->value]);
    }

    public function getSmsMessage(): SmsMessage
    {
        return $this->smsMessage;
    }

}
