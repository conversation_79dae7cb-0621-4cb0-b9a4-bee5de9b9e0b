<?php

declare(strict_types = 1);

namespace SmsService\Core\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use SmsService\Core\Exception\InvalidInputException;
use SmsService\Core\Input\InputArgs;
use Symfony\Component\HttpFoundation\ParameterBag;

abstract class BaseController extends AbstractFOSRestController
{

    /**
     * @throws InvalidInputException
     */
    protected function getInputArgs(ParameterBag $bag): InputArgs
    {
        return InputArgs::create($bag->all());
    }

}
