<?php
// phpcs:disable PSR1.Files.SideEffects


declare(strict_types = 1);

namespace SmsService\Core\Controller;

use Symfony\Component\HttpFoundation\Response;

final readonly class SuccessOutput
{

    public function __construct(private mixed $data = null, private int $code = Response::HTTP_OK)
    {
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getData(): mixed
    {
        return $this->data;
    }

}
