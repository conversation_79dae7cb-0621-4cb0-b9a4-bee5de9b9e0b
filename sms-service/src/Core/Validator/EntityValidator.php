<?php

namespace SmsService\Core\Validator;

use SmsService\Core\Exception\InvalidInputException;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use function count;

class EntityValidator
{

    public function __construct(
        private readonly ValidatorInterface $validator
    )
    {
    }

    /**
     * Validates an entity and returns constraint violations.
     *
     * @throws InvalidInputException
     */
    public function validateEntity(object $entity): void
    {
        /** @var array<int,ConstraintViolation> $errors */
        $errors = $this->validator->validate($entity);
        if (count($errors) > 0) {
            $errorMessage = "Invalid value for property '" . $errors[0]->getPropertyPath() . "': " . $errors[0]->getMessage();
            throw InvalidInputException::fromConstraintViolationList($errorMessage);
        }
    }

}
