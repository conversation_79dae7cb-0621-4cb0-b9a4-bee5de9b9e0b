<?php

namespace SmsService\Core\Producer;

use OldSound\RabbitMqBundle\RabbitMq\Producer;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AbstractConnection;

class BaseProducer extends Producer
{

    /**
     * @param AMQPChannel|null ...$otherParams
     */
    public function __construct(AbstractConnection $conn, mixed ...$otherParams)
    {
        parent::__construct($conn, ...$otherParams);
    }

}
