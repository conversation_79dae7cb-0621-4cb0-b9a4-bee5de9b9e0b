<?php

namespace SmsService\Core\Service;

use RuntimeException;
use SmsService\Core\Enum\GatewayName;
use SmsService\Core\Notification\SmsNotification;
use SmsService\SmsMessage\Entity\SmsMessage;
use Symfony\Component\Notifier\NotifierInterface;
use Symfony\Component\Notifier\Recipient\Recipient;

class SmsGatewayService
{

    public function __construct(
        private readonly string $defaultGateway,
        private readonly NotifierInterface $notifier
    )
    {
    }

    /**
     * @throws RuntimeException
     */
    public function selectGateway(SmsMessage $smsMessage): void
    {
        $gatewayName = GatewayName::tryFrom($this->defaultGateway);
        if ($gatewayName === null) {
            throw new RuntimeException('Invalid or missing default gateway parameter.');
        }

        // TODO:  implement logic for gateway selection, can be based on source, phone number region, priority etc. (currently not specified in details by docs)
        $smsMessage->setGateway($gatewayName);
    }

    public function sendSmsMessage(SmsMessage $smsMessage): void
    {
        $notification = (new SmsNotification($smsMessage));

        $recipient = new Recipient('', $smsMessage->getPhoneNumberFormatted());

        // responses are handled by event handlers SentMessageEventListener, FailedMessageEventListener
        $this->notifier->send($notification, $recipient);
    }

}
