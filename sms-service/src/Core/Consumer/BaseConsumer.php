<?php

namespace SmsService\Core\Consumer;

use Nette\Utils\Json;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use SmsService\Core\Exception\InvalidInputException;
use SmsService\Core\Input\InputArgs;

abstract class BaseConsumer implements ConsumerInterface
{

    /**
     * @throws InvalidInputException
     */
    protected function getInputArgs(AMQPMessage $message): InputArgs
    {
        return InputArgs::create(
            Json::decode($message->body, Json::FORCE_ARRAY),
        );
    }

}
