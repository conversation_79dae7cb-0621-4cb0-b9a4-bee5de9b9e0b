<?php

declare(strict_types = 1);

namespace SmsService\Core\Utils;

use BackedEnum;
use SmsService\Core\Exception\InvalidIntEnumValueException;
use SmsService\Core\Exception\LogicException;
use function enum_exists;

class IntEnum
{

    /**
     * @param class-string<BackedEnum<int>> $enumClass
     * @return BackedEnum<int>
     *
     * @throws InvalidIntEnumValueException
     */
    public static function create(string $enumClass, int $value): BackedEnum
    {
        if (!enum_exists($enumClass)) {
            throw new LogicException("No enum {$enumClass} found.");
        }

        $enum = $enumClass::tryFrom($value);
        if ($enum === null) {
            throw new InvalidIntEnumValueException($value, $enumClass);
        }

        return $enum;
    }

}
