<?php

declare(strict_types=1);

namespace SmsService\Core\EventSubscriber;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Override;
use SmsService\Core\Exception\DuplicateEntryException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class UniqueConstraintViolationExceptionListener implements EventSubscriberInterface
{

    /**
     * @return array<string, array<int, array<int, int|string>>>
     */
    #[Override]
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::EXCEPTION => [
                ['onUniqueException', 11],
            ],
        ];
    }

    public function onUniqueException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        if ($exception instanceof UniqueConstraintViolationException) {
            $event->setThrowable(DuplicateEntryException::fromUniqueConstraintViolationException($exception));
        }
    }

}
