<?php

declare(strict_types=1);

namespace SmsService\Core\Input;

use DateTimeImmutable;
use Nette\Utils\Strings;
use SmsService\Core\Exception\InvalidInputException;
use function array_key_exists;
use function is_array;
use function is_float;
use function is_int;
use function is_string;

class InputArgs
{

    private bool $weakTypes = false;

    /**
     * @param mixed[] $args
     */
    public function __construct(private readonly array $args)
    {
    }

    /**
     * @param mixed[] $args
     */
    public static function create(array $args): self
    {
        return new self($args);
    }

    public function allowWeakTypes(): void
    {
        $this->weakTypes = true;
    }

    private function hasValue(string $key): bool
    {
        return array_key_exists($key, $this->args);
    }

    /**
     * @throws InvalidInputException
     */
    private function getValue(string $key): mixed
    {
        if (!$this->hasValue($key)) {
            throw InvalidInputException::createMissingException($key);
        }

        return $this->args[$key];
    }

    /**
     * @throws InvalidInputException
     */
    private function getValueOrNull(string $key): mixed
    {
        if (!$this->hasValue($key)) {
            return null;
        }

        return $this->getValue($key);
    }

    /**
     * @throws InvalidInputException
     */
    public function getString(string $key): string
    {
        $value = $this->getValue($key);

        if (!is_string($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'string');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getStringOrNull(string $key): ?string
    {
        $value = $this->getValueOrNull($key);

        if ($value !== null && !is_string($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'string');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getList(string $key): self
    {
        $value = $this->getValue($key);

        if (!is_array($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'list');
        }

        return self::create($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getListOrNull(string $key): ?self
    {
        $value = $this->getValueOrNull($key);

        if ($value === null) {
            return null;
        }

        if (!is_array($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'list');
        }

        return self::create($value);
    }

    /**
     * @return array<string, mixed>
     *
     * @throws InvalidInputException
     */
    public function getIndexedArray(string $key): array
    {
        $value = $this->getValue($key);

        if (!is_array($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'array');
        }

        return $value;
    }

    /**
     * @return array<string, mixed>
     *
     * @throws InvalidInputException
     */
    public function getIndexedArrayOrNull(string $key): ?array
    {
        $value = $this->getValueOrNull($key);

        if ($value === null) {
            return null;
        }
        return $this->getIndexedArray($key);
    }

    /**
     * @throws InvalidInputException
     */
    public function getInt(string $key): int
    {
        $value = $this->getValue($key);

        if ($this->weakTypes && is_string($value)) {
            $value = (int) $value;
        }

        if (!is_int($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'int');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getIntOrNull(string $key): ?int
    {
        $value = $this->getValueOrNull($key);

        if ($this->weakTypes && is_string($value)) {
            $value = (int) $value;
        }

        if ($value !== null && !is_int($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'int');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getFloat(string $key): float
    {
        $value = $this->getValue($key);

        if (!is_float($value) && !is_int($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'float');
        }

        if (is_int($value)) {
            return (float) $value;
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getDate(string $key): DateTimeImmutable
    {
        $value = $this->getValue($key);

        if ($value instanceof DateTimeImmutable) {
            return $value;
        }

        if (!is_string($value) || Strings::match($value, '/^[1-9]{1}\d{3}\-\d{2}\-\d{2}$/') === null) {
            throw InvalidInputException::createInvalidTypeException($key, 'date');
        }

        return new DateTimeImmutable($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getTime(string $key): DateTimeImmutable
    {
        $value = $this->getValue($key);

        if (!is_string($value) || Strings::match($value, '/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/') === null) {
            throw InvalidInputException::createInvalidTypeException($key, 'time');
        }

        return new DateTimeImmutable($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getDateTime(string $key): DateTimeImmutable
    {
        $value = $this->getValue($key);

        if (!is_string($value) ||
            (Strings::match($value, '/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01]) ([01][0-9]|2[0-3]):([0-5][0-9])$/') === null &&
                Strings::match($value, '/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01]) ([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/') === null)) {
            throw InvalidInputException::createInvalidTypeException($key, 'datetime');
        }

        return new DateTimeImmutable($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getBool(string $key): bool
    {
        $value = $this->getValue($key);

        if ($this->weakTypes && is_string($value)) {
            $value = (bool) $value;
        }

        if ($value !== true && $value !== false) {
            throw InvalidInputException::createInvalidTypeException($key, 'bool');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getBoolOrNull(string $key): ?bool
    {
        $value = $this->getValueOrNull($key);

        if ($value === null) {
            return null;
        }

        return $this->getBool($key);
    }

}
