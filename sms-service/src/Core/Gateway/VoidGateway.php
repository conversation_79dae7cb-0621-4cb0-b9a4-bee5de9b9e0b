<?php

namespace SmsService\Core\Gateway;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Override;
use SmsService\Core\Exception\GatewaySendFailedException;
use SmsService\SmsMessage\Entity\SmsMessage;

class VoidGateway extends BaseGateway
{

    public function __construct(
        private readonly EntityManager $entityManager,
    )
    {
    }

    /**
     * @throws GatewaySendFailedException
     */
    #[Override]
    public function sendMessage(SmsMessage $smsMessage): void
    {
        parent::sendMessage($smsMessage);
        $this->entityManager->wrapInTransaction(
            function () use ($smsMessage): void {
            // TODO: implement random failing in void gateway
            // existing gateway might retrieve sent at datetime from response
                $smsMessage->getLog()->setSentAt(new DateTimeImmutable());

            // if rejected by gateway set getLog()->setGatewayResponse();, requeue in MQ for number of attempts or reject - ?do not delete smsmessage for further analysis?
                $this->entityManager->remove($smsMessage);
            },
        );
    }

}
