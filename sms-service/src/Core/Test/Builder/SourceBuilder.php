<?php

declare(strict_types=1);

namespace SmsService\Core\Test\Builder;

use Override;
use SmsService\Source\Entity\Source;

class SourceBuilder implements EntityBuilder
{

    private string $name;

    public function __construct(
        private readonly ObjectFactory $objectFactory
    )
    {
    }

    #[Override]
    public function build(): Source
    {
        $source = new Source(
            name: $this->name,
        );

        $this->objectFactory->getEntityManager()->persist($source);

        return $source;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

}
