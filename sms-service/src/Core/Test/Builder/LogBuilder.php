<?php

declare(strict_types=1);

namespace SmsService\Core\Test\Builder;

use DateTimeImmutable;
use Override;
use SmsService\Core\Enum\GatewayName;
use SmsService\Log\Entity\Log;
use SmsService\SmsMessage\Enum\SmsMessagePriority;
use SmsService\Source\Entity\Source;

class LogBuilder implements EntityBuilder
{

    private Source $source;

    private GatewayName $gateway;

    private int $phonePrefix;

    private SmsMessagePriority $priority;

    public function __construct(
        private readonly ObjectFactory $objectFactory
    )
    {
    }

    #[Override]
    public function build(): Log
    {
        $now = new DateTimeImmutable();

        $log = new Log(
            phonePrefix: $this->phonePrefix,
            source: $this->source ?? $this->objectFactory->getSourceBuilder()->build(),
            priority: $this->priority ?? SmsMessagePriority::NORMAL,
            gateway: $this->gateway ?? GatewayName::VOID,
            queuedAt: $now,
        );

        $this->objectFactory->getEntityManager()->persist($log);

        return $log;
    }

    public function setSource(Source $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function setGateway(GatewayName $gatewayName): self
    {
        $this->gateway = $gatewayName;

        return $this;
    }

    public function setPhonePrefix(int $phonePrefix): self
    {
        $this->phonePrefix = $phonePrefix;

        return $this;
    }

    public function setPriority(SmsMessagePriority $priority): self
    {
        $this->priority = $priority;

        return $this;
    }

}
