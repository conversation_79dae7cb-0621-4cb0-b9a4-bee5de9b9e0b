<?php

declare(strict_types = 1);

namespace SmsService\Core\Test\Builder;

use Doctrine\ORM\EntityManager;
use function array_key_exists;

class ObjectFactory
{

    /**
     * @var int[]
     */
    private array $autoIncrementNumbers = [];

    public function __construct(private readonly EntityManager $entityManager)
    {
    }

    public function resetAutoIncrementNumber(): void
    {
        $this->autoIncrementNumbers = [];
    }

    public function createAutoIncrementNumber(string $namespace): int
    {
        if (!array_key_exists($namespace, $this->autoIncrementNumbers)) {
            $this->autoIncrementNumbers[$namespace] = 0;
        }

        return ++$this->autoIncrementNumbers[$namespace];
    }

    public function getEntityManager(): EntityManager
    {
        return $this->entityManager;
    }

    public function getSmsMessageBuilder(): SmsMessageBuilder
    {
        return new SmsMessageBuilder($this);
    }

    public function getSourceBuilder(): SourceBuilder
    {
        return new SourceBuilder($this);
    }

    public function getLogBuilder(): LogBuilder
    {
        return new LogBuilder($this);
    }

}
