<?php

declare(strict_types=1);

namespace SmsService\Core\Test\Builder;

use libphonenumber\PhoneNumber;
use Override;
use SmsService\Core\Enum\GatewayName;
use SmsService\SmsMessage\Entity\SmsMessage;
use SmsService\SmsMessage\Enum\SmsMessagePriority;
use SmsService\Source\Entity\Source;

class SmsMessageBuilder implements EntityBuilder
{

    private Source $source;

    private GatewayName $gateway;

    private PhoneNumber $phoneNumber;

    private string $content;

    private SmsMessagePriority $priority;

    public function __construct(
        private readonly ObjectFactory $objectFactory
    )
    {
    }

    #[Override]
    public function build(): SmsMessage
    {
        $smsMessage = new SmsMessage(
            phoneNumber: $this->phoneNumber,
            content: $this->content,
            source: $this->source ?? $this->objectFactory->getSourceBuilder()->build(),
            priority: $this->priority ?? SmsMessagePriority::NORMAL,
            gateway: $this->gateway ?? GatewayName::VOID,
        );

        $this->objectFactory->getEntityManager()->persist($smsMessage);

        return $smsMessage;
    }

    public function setSource(Source $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function setGateway(GatewayName $gatewayName): self
    {
        $this->gateway = $gatewayName;

        return $this;
    }

    public function setPhoneNumber(PhoneNumber $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function setPriority(SmsMessagePriority $priority): self
    {
        $this->priority = $priority;

        return $this;
    }

}
