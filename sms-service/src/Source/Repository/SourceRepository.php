<?php

namespace SmsService\Source\Repository;

use Doctrine\ORM\EntityManager;
use SmsService\Source\Entity\Source;

class SourceRepository
{

    public function __construct(private readonly EntityManager $em)
    {
    }

    public function findByName(string $name): ?Source
    {
        return $this->em->createQueryBuilder()
            ->select('s')
            ->from(Source::class, 's')
            ->andWhere('s.name = :name')
            ->setParameter('name', $name)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
