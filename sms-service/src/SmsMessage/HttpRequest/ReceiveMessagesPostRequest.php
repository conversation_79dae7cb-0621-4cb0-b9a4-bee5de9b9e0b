<?php

declare(strict_types = 1);

namespace SmsService\SmsMessage\HttpRequest;

use Symfony\Component\Validator\Constraints\NotBlank;

class ReceiveMessagesPostRequest
{

    /**
     * @param array<SmsMessagePostRequest> $messages
     */
    public function __construct(
        #[NotBlank()]
        private readonly ?string $source,
        #[NotBlank()]
        private readonly ?array $messages,
    )
    {
    }

    public function getSource(): string
    {
        return $this->source ?? '';
    }

    /**
     * @return array<SmsMessagePostRequest>
     */
    public function getMessages(): array
    {
        return $this->messages ?? [];
    }

}
