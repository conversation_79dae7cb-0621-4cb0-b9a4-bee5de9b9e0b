<?php

namespace SmsService\SmsMessage\Repository;

use Doctrine\ORM\EntityManager;
use SmsService\SmsMessage\Entity\SmsMessage;
use SmsService\SmsMessage\Enum\SmsMessagePriority;
use SmsService\SmsMessage\Exception\NoSmsMessageFoundException;

class SmsMessageRepository
{

    public function __construct(
        private readonly EntityManager $em,
    )
    {
    }

    /**
     * @return array<int, SmsMessage>
     */
    public function findByPriority(SmsMessagePriority $priority): array
    {
        return $this->em->createQueryBuilder()
            ->select('s')
            ->from(SmsMessage::class, 's')
            ->andWhere('s.priority = :priority')->setParameter('priority', $priority->value)
            ->orderBy('s.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @throws NoSmsMessageFoundException
     */
    public function getById(int $id): SmsMessage
    {
        $smsMessage = $this->em->createQueryBuilder()
            ->select('s')
            ->from(SmsMessage::class, 's')
            ->andWhere('s.id = :id')
            ->setParameter('id', $id)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        if ($smsMessage === null) {
            throw NoSmsMessageFoundException::byId($id);
        }

        return $smsMessage;
    }

}
