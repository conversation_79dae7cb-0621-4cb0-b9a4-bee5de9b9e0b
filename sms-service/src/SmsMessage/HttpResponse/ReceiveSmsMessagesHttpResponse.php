<?php

namespace SmsService\SmsMessage\HttpResponse;

use SmsService\SmsMessage\Error\SmsMessageErrorResponse;

class ReceiveSmsMessagesHttpResponse
{

    protected int $queued = 0;

    protected int $rejected = 0;

    /**
     * @var array<SmsMessageErrorResponse>
     */
    public array $errors = [];

    public function smsMessageQueued(): void
    {
        $this->queued++;
    }

    public function smsMessageRejected(SmsMessageErrorResponse $error): void
    {
        $this->errors[] = $error;
        $this->rejected++;
    }

    /**
     * @return array<SmsMessageErrorResponse>
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

}
