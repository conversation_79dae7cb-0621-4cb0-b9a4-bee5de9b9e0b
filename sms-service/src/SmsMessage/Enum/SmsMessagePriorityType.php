<?php

declare(strict_types = 1);

namespace SmsService\SmsMessage\Enum;

use Override;
use SmsService\Core\Doctrine\IntEnumType;

class SmsMessagePriorityType extends IntEnumType
{

    final public const string NAME = 'sms_message_priority';

    #[Override]
    public function getEnumClass(): string
    {
        return SmsMessagePriority::class;
    }

    #[Override]
    public function getName(): string
    {
        return self::NAME;
    }

}
