<?php

namespace SmsService\SmsMessage\Consumer;

use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use Override;
use PhpAmqpLib\Message\AMQPMessage;
use SmsService\Core\Consumer\BaseConsumer;
use SmsService\Core\Exception\GatewaySendFailedException;
use SmsService\Core\Exception\GatewaySendFailedTooManyTimesException;
use SmsService\Core\Exception\InvalidInputException;
use SmsService\Log\Logger;
use SmsService\SmsMessage\Exception\NoSmsMessageFoundException;
use SmsService\SmsMessage\Facade\SmsMessageFacade;

class SendSmsMessageConsumer extends BaseConsumer
{

    public function __construct(
        private readonly Logger $logger,
        private readonly SmsMessageFacade $smsMessageFacade
    )
    {
    }

    #[Override]
    public function execute(AMQPMessage $msg): int
    {
        try {
            $args = $this->getInputArgs($msg);

            $this->smsMessageFacade->sendSmsMessage($args);

            return ConsumerInterface::MSG_ACK;

        } catch (NoSmsMessageFoundException $e) {
            $this->logger->logInfoMessage($e);
            return ConsumerInterface::MSG_REJECT;

        } catch (InvalidInputException $e) {
            $this->logger->logErrorException($e);
            return ConsumerInterface::MSG_REJECT;

            // @phpstan-ignore-next-line
        } catch (GatewaySendFailedTooManyTimesException $e) {
            // reject message if failed to send too many times
            $this->logger->logErrorException($e);

            return ConsumerInterface::MSG_REJECT;

            // @phpstan-ignore-next-line
        } catch (GatewaySendFailedException $e) {
            $this->logger->logErrorException($e);

            return ConsumerInterface::MSG_REJECT_REQUEUE;

        }
    }

}
