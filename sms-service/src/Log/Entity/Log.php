<?php

declare(strict_types=1);

namespace SmsService\Log\Entity;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\ManyToOne;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use SmsService\Core\Entity\BaseEntity;
use SmsService\Core\Enum\GatewayName;
use SmsService\Core\Enum\GatewayNameType;
use SmsService\SmsMessage\Enum\SmsMessagePriority;
use SmsService\SmsMessage\Enum\SmsMessagePriorityType;
use SmsService\Source\Entity\Source;

#[Entity]
class Log extends BaseEntity
{

    use TimestampableEntity;

    #[ManyToOne(targetEntity: Source::class)]
    private Source $source;

    #[Column(type: Types::SMALLINT, nullable: true)]
    private ?int $phonePrefix = null;

    #[Column(type: SmsMessagePriorityType::NAME, nullable: false)]
    private SmsMessagePriority $priority;

    #[Column(type: GatewayNameType::NAME, nullable: true)]
    private ?GatewayName $gateway = null;

    #[Column(type: Types::DATETIMETZ_IMMUTABLE, nullable: true)]
    private ?DateTimeImmutable $queuedAt = null;

    #[Column(type: Types::TEXT, nullable: true)]
    private ?string $rejectedError = null;

    #[Column(type: Types::INTEGER, nullable: false)]
    private int $sendAttempts = 0;

    #[Column(type: Types::DATETIMETZ_IMMUTABLE, nullable: true)]
    private ?DateTimeImmutable $sentAt = null;

    #[Column(type: Types::TEXT, nullable: true)]
    private ?string $gatewayResponse = null;

    public function __construct(?int $phonePrefix, SmsMessagePriority $priority, Source $source, ?GatewayName $gateway = null, ?DateTimeImmutable $queuedAt = null)
    {
        $this->phonePrefix = $phonePrefix;
        $this->priority = $priority;
        $this->source = $source;
        $this->gateway = $gateway;
        $this->queuedAt = $queuedAt;
    }

    public function sendFailed(): void
    {
        $this->sendAttempts++;
    }

    public function getSendAttempts(): int
    {
        return $this->sendAttempts;
    }

    public function getSentAt(): ?DateTimeImmutable
    {
        return $this->sentAt;
    }

    public function setSentAt(DateTimeImmutable $sentAt): void
    {
        $this->sentAt = $sentAt;
    }

    public function setSendFailed(): void
    {
        $this->sendAttempts++;
    }

    public function setRejectedError(string $rejectedError): void
    {
        $this->rejectedError = $rejectedError;
    }

    public function setGateway(GatewayName $gatewayName): void
    {
        $this->gateway = $gatewayName;
    }

    public function getGatewayResponse(): ?string
    {
        return $this->gatewayResponse;
    }

    public function setGatewayResponse(string $gatewayResponse): void
    {
        $this->gatewayResponse = $gatewayResponse;
    }

}
