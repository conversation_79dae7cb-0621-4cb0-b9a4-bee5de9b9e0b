# Read the documentation: https://symfony.com/doc/master/bundles/FOSRestBundle/index.html
fos_rest:
  serializer:
    serialize_null: true
  body_listener:
    enabled: true
    array_normalizer: fos_rest.normalizer.camel_keys
  format_listener:
    enabled: true
    rules:
      - { path: '^/', priorities: ['json'], fallback_format: json, prefer_extension: false }
  param_fetcher_listener: force
  view:
    view_response_listener: force
    formats:
      json: true
  routing_loader: false
  exception:
    enabled: true
    flatten_exception_format: 'legacy'
