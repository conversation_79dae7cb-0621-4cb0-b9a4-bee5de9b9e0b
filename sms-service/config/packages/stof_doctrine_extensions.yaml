# Read the documentation: https://symfony.com/doc/current/bundles/StofDoctrineExtensionsBundle/index.html
# See the official DoctrineExtensions documentation for more details: https://github.com/doctrine-extensions/DoctrineExtensions/tree/main/doc
stof_doctrine_extensions:
    default_locale: '%env(resolve:APP_DEFAULT_LOCALE)%'
    persist_default_translation: true
    orm:
        default:
            translatable: true
            softdeleteable: true
            timestampable: true