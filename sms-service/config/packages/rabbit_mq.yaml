old_sound_rabbit_mq:
  connections:
    default:
      host: '%env(RABBITMQ_HOST)%'
      port: '%env(RABBITMQ_DEFAULT_PORT)%'
      user: '%env(RABBITMQ_DEFAULT_USER)%'
      password: '%env(RABBITMQ_DEFAULT_PASS)%'
      vhost: '%env(RABBITMQ_DEFAULT_VHOST)%'
      lazy: true
      connection_timeout: 3
      read_write_timeout: 3

  consumers:
    send_sms_message:
      connection: default
      exchange_options: { name: 'send_sms_message', type: direct  }
      queue_options: { name: 'sms_messages', arguments: {'x-max-priority': ['I',10]} }
      callback: 'SmsService\SmsMessage\Consumer\SendSmsMessageConsumer'
      options:
        no_ack: false
      graceful_max_execution:
        timeout: 120
