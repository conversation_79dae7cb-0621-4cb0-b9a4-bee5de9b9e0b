parameters:
    doctrine.orm.entity_manager.class: Doctrine\ORM\EntityManager
    router.request_context.host: '%env(APP_HOST_URL)%'
    router.request_context.scheme: 'https'

services:
    _defaults:
        public: true
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: false # Automatically registers your services as commands, event subscribers, etc.

    _instanceof:
        SmsService\Core\Controller\BaseController:
            calls:
                - method: setContainer
                  arguments: [ '@service_container' ]
            tags: [ 'controller.service_arguments' ]
        SmsService\Core\Producer\BaseProducer:
            tags: ['app.producer']

    PhpAmqpLib\Connection\AbstractConnection: '@old_sound_rabbit_mq.connection.default'

    Doctrine\ORM\EntityManager: '@doctrine.orm.entity_manager'

    GuzzleHttp\Client:

    Sentry\Monolog\Handler:
        arguments:
            $hub: '@Sentry\State\HubInterface'
            $level: !php/const Monolog\Logger::ERROR
    Monolog\Processor\PsrLogMessageProcessor:
        tags: { name: monolog.processor, handler: sentry }

    # Notifier
    SmsService\Core\Notifier\Bridge\Tmobilecz\TmobileczTransportFactory:
        tags: [texter.transport_factory]

    # Log
    SmsService\Log\Service\LogService:
    SmsService\Log\Logger:

    # Core
    SmsService\Core\Controller\DefaultController:
    
    SmsService\Core\Service\SmsGatewayService:
        arguments: ['%env(APP_GATEWAY_DEFAULT)%']
    
    SmsService\Core\EventSubscriber\ExceptionSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    SmsService\Core\EventSubscriber\UniqueConstraintViolationExceptionListener:
        tags: [ 'kernel.event_subscriber' ]
    SmsService\Core\EventSubscriber\SentMessageEventListener:
        tags: [ 'kernel.event_subscriber' ]
    SmsService\Core\EventSubscriber\FailedMessageEventListener:
        tags: [ 'kernel.event_subscriber' ]
        arguments: ['%env(APP_GATEWAY_MAX_SEND_ATTEMPTS)%']
    
    SmsService\Source\Repository\SourceRepository:
    SmsService\Log\Repository\LogRepository:
    SmsService\Core\Command\ProcessQueueCommand:
    SmsService\Core\Validator\EntityValidator:

    SmsService\SmsMessage\Controller\SmsMessageController:        
    SmsService\SmsMessage\Repository\SmsMessageRepository:
    SmsService\SmsMessage\Facade\SmsMessageFacade:
    SmsService\SmsMessage\Producer\SendSmsMessageProducer:
    SmsService\SmsMessage\Consumer\SendSmsMessageConsumer:

    SmsService\Core\Service\PhoneNumberHelperService:
