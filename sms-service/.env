###> symfony/framework-bundle ###
APP_ENV=prod
APP_SECRET=2fb234cb34a4f49f899cfb98fcfb8a9e
APP_DEFAULT_LOCALE=en
APP_HOST_URL=sms-service.goodsailors.com
APP_GATEWAY_DEFAULT=sms/void
APP_GATEWAY_MAX_SEND_ATTEMPTS=5
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
DATABASE_URL=********************************/smsservice?charset=utf8&sslmode=prefer
###< doctrine/doctrine-bundle ###

###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=semaphore
###< symfony/lock ###

###> sentry/sentry-symfony ###
SENTRY_DSN=
###< sentry/sentry-symfony ###

RABBITMQ_HOST=rabbit
RABBITMQ_DEFAULT_USER=rabbitmq
RABBITMQ_DEFAULT_PASS=rabbitmqy
RABBITMQ_DEFAULT_VHOST=smsservice
RABBITMQ_DEFAULT_PORT=5672

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

###> symfony/mailer ###
MAILER_DSN=null://null
###< symfony/mailer ###

###> symfony/fake-sms-notifier ###
FAKE_SMS_DSN=fakesms+logger://default
###< symfony/fake-sms-notifier ###

###> symfony/twilio-notifier ###
TWILIO_DSN=twilio://SID:TOKEN@default?from=FROM
#TWILIO_DSN=null://null
###< symfony/twilio-notifier ###

###> symfony/tmobilecz-notifier ###
TMOBILECZ_DSN=tmobilecz://username:<EMAIL>?from=
###< symfony/tmobilecz-notifier ###
