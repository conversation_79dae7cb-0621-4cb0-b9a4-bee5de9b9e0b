<?php

namespace SmsService\Tests\SmsMessage\Consumer;

use libphonenumber\PhoneNumberUtil;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use SmsService\Core\Enum\GatewayName;
use SmsService\Log\Entity\Log;
use SmsService\Log\Logger;
use SmsService\SmsMessage\Consumer\SendSmsMessageConsumer;
use SmsService\SmsMessage\Facade\SmsMessageFacade;
use SmsService\Tests\TestCase;

class SendSmsMessageConsumerTest extends TestCase
{

    public function testConsumptionSendSmsMessage(): void
    {
        $source = $this->getObjectFactory()->getSourceBuilder()->setName('pm\dev')->build();

        $smsMessage = $this->getObjectFactory()
            ->getSmsMessageBuilder()
            ->setSource($source)
            ->setGateway(GatewayName::VOID)
            ->setPhoneNumber(PhoneNumberUtil::getInstance()->parse('+420777934865'))
            ->setContent('Your guest Mr. <PERSON> arrived!')
            ->build();

        $this->getEntityManager()->flush();

        $amqpMessage = new AMQPMessage('{"id":' . $smsMessage->getId() . '}');

        $consumer = new SendSmsMessageConsumer(
            logger: self::getContainer()->get(Logger::class),
            smsMessageFacade: self::getContainer()->get(SmsMessageFacade::class),
        );

        self::assertEquals(ConsumerInterface::MSG_ACK, $consumer->execute($amqpMessage));
        $this->getEntityManager()->clear();

        $smsMessageLog = $this->getEntityManager()->getRepository(Log::class)->findOneBy(['id' => $smsMessage->getLog()->getId()]);
        self::assertNotNull($smsMessageLog);
        self::assertNotNull($smsMessageLog->getSentAt());
    }

    public function testConsumptionSendSmsMessageFail(): void
    {
        $source = $this->getObjectFactory()->getSourceBuilder()->setName('pm\dev')->build();

        $smsMessage = $this->getObjectFactory()
            ->getSmsMessageBuilder()
            ->setSource($source)
            ->setGateway(GatewayName::TWILIO)
            ->setPhoneNumber(PhoneNumberUtil::getInstance()->parse('+420777934865'))
            ->setContent('Your guest Mr. Smith arrived!')
            ->build();

        $this->getEntityManager()->flush();

        $amqpMessage = new AMQPMessage('{"id":' . $smsMessage->getId() . '}');

        $consumer = new SendSmsMessageConsumer(
            logger: self::getContainer()->get(Logger::class),
            smsMessageFacade: self::getContainer()->get(SmsMessageFacade::class),
        );

        self::assertEquals(ConsumerInterface::MSG_REJECT_REQUEUE, $consumer->execute($amqpMessage));
        $this->getEntityManager()->clear();

        $smsMessageLog = $this->getEntityManager()->getRepository(Log::class)->findOneBy(['id' => $smsMessage->getLog()->getId()]);
        self::assertNotNull($smsMessageLog);
        self::assertNotNull($smsMessageLog->getGatewayResponse());
    }

}
