<?php

namespace SmsService\Tests\SmsMessage\Controller;

use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use Nette\Utils\FileSystem;
use SmsService\Core\Http\RequestMethod;
use SmsService\SmsMessage\Entity\SmsMessage;
use SmsService\SmsMessage\Enum\SmsMessagePriority;
use SmsService\SmsMessage\Producer\SendSmsMessageProducer;
use SmsService\SmsMessage\Repository\SmsMessageRepository;
use SmsService\Tests\TestCase;

class SmsMessageControllerTest extends TestCase
{

    private function readData(string $path): string
    {
        return FileSystem::read(__DIR__ . '/RequestData/' . $path);
    }

    public function testReceiveSmsMessages(): void
    {
        $sendSmsMessageProducerMock = $this->getMockBuilder(SendSmsMessageProducer::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['publish'])
            ->getMock();
        $sendSmsMessageProducerMock->expects(self::atLeastOnce())
            ->method('publish')
            ->with(
                self::anything(),
            );
        self::getContainer()->set(SendSmsMessageProducer::class, $sendSmsMessageProducerMock);

        $client = $this->createClientWithoutUser();
        $client->request(
            method: RequestMethod::POST,
            uri: '/api/v1/messages',
            content: $this->readData('receive_sms_messages.json'),
        );
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(2, $responseData['data']['queued']);
        self::assertSame(0, $responseData['data']['rejected']);

        /** @var SmsMessageRepository $smsMessagesRepository */
        $smsMessagesRepository = $this->getEntityManager()->getRepository(SmsMessage::class);

        $smsMessages = $smsMessagesRepository->findByPriority(SmsMessagePriority::NORMAL);

        $smsMessagesHighPriority = $smsMessagesRepository->findByPriority(SmsMessagePriority::HIGH);

        self::assertNotEmpty($smsMessages);

        self::assertNotEmpty($smsMessagesHighPriority);

        self::assertSame('+420777813764', PhoneNumberUtil::getInstance()->format($smsMessages[0]->getPhoneNumber(), PhoneNumberFormat::E164));
        self::assertSame('Your guest Mr. Smith arrived!', $smsMessages[0]->getContent());
        self::assertSame(SmsMessagePriority::NORMAL, $smsMessages[0]->getPriority());

        self::assertSame('+420777813123', PhoneNumberUtil::getInstance()->format($smsMessagesHighPriority[0]->getPhoneNumber(), PhoneNumberFormat::E164));
        self::assertSame('Your guest Mr. Smith is waiting!', $smsMessagesHighPriority[0]->getContent());
        self::assertSame(SmsMessagePriority::HIGH, $smsMessagesHighPriority[0]->getPriority());
    }

    public function testReceiveInvalidSmsMessages(): void
    {
        $sendSmsMessageProducerMock = $this->getMockBuilder(SendSmsMessageProducer::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['publish'])
            ->getMock();
        $sendSmsMessageProducerMock->expects(self::never())
            ->method('publish')
            ->with(
                self::anything(),
            );
        self::getContainer()->set(SendSmsMessageProducer::class, $sendSmsMessageProducerMock);

        $client = $this->createClientWithoutUser();
        $client->request(
            method: RequestMethod::POST,
            uri: '/api/v1/messages',
            content: $this->readData('receive_sms_messages_invalid.json'),
        );

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(0, $responseData['data']['queued']);
        self::assertSame(3, $responseData['data']['rejected']);

        self::assertSame('number', $responseData['data']['errors'][0]['errors'][0]['property']);
        self::assertSame('The string supplied is too short to be a phone number.', $responseData['data']['errors'][0]['errors'][0]['message']);

        self::assertSame('content', $responseData['data']['errors'][1]['errors'][0]['property']);
        self::assertSame('This value should not be blank.', $responseData['data']['errors'][1]['errors'][0]['message']);

        self::assertSame('content', $responseData['data']['errors'][2]['errors'][0]['property']);
        self::assertSame('This value should not be blank.', $responseData['data']['errors'][2]['errors'][0]['message']);
    }

}
