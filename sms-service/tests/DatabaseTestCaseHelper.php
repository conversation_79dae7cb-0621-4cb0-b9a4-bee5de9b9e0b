<?php

declare(strict_types=1);

namespace SmsService\Tests;

use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\HttpKernel\KernelInterface;
use function dd;

class DatabaseTestCaseHelper
{

    public static bool $isDatabasePrepared = false;

    public static function prepareDatabaseIfNotPrepared(KernelInterface $kernel): void
    {
        if (!self::$isDatabasePrepared) {
            self::prepareDatabase($kernel);
            self::$isDatabasePrepared = true;
        }
    }

    public static function prepareDatabase(KernelInterface $kernel): void
    {
        $commandInputs = [
            [
                'command' => 'doctrine:database:create',
                '--if-not-exists' => true,
            ],
            [
                'command' => 'doctrine:schema:update',
                '--force' => true,
                '--complete' => true,
            ],
        ];
        try {
            $output = new ConsoleOutput();
            foreach ($commandInputs as $input) {
                $command = self::getApplication($kernel)->find($input['command']);

                $arrayInput = new ArrayInput($input);
                $arrayInput->setInteractive(false);

                $command->run($arrayInput, $output);
                $kernelContainer = $kernel->getContainer();

                /** @var EntityManagerInterface $doctrine */
                $doctrine = $kernelContainer->get('doctrine');

                $connection = $doctrine->getConnection();

                if ($connection->isConnected()) {
                    $connection->close();
                }
            }
        } catch (Exception $e) {
            dd($e);
        }
    }

    public static function getApplication(KernelInterface $kernel): Application
    {
        return new Application($kernel);
    }

}
