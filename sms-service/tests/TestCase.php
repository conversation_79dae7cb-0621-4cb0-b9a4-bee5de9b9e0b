<?php

declare(strict_types = 1);

namespace SmsService\Tests;

use Doctrine\ORM\EntityManager;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Override;
use SmsService\Core\Http\ResponseStatusCode;
use SmsService\Core\Test\Builder\ObjectFactory;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;
use function is_string;

abstract class TestCase extends WebTestCase
{

    private ?ObjectFactory $objectFactory = null;

    private KernelBrowser $client;

    protected ?string $token = null;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
        DatabaseTestCaseHelper::prepareDatabaseIfNotPrepared($this->client->getKernel());
        $this->getObjectFactory()->resetAutoIncrementNumber();
        $this->truncateTables();
    }

    public function getEntityManager(): EntityManager
    {
        /** @var EntityManager $entityManager */
        $entityManager = self::getContainer()->get(EntityManager::class);

        return $entityManager;
    }

    public function getObjectFactory(): ObjectFactory
    {
        if ($this->objectFactory === null) {
            $this->objectFactory = new ObjectFactory($this->getEntityManager());
        }

        return $this->objectFactory;
    }

    protected function truncateTables(): void
    {
        $connection = $this->getEntityManager()->getConnection();
        $tables = $connection->createSchemaManager()->listTables();

        foreach ($tables as $table) {
            $connection->executeQuery("ALTER TABLE {$table->getName()} DISABLE TRIGGER ALL");
        }

        foreach ($tables as $table) {
            $connection->executeQuery("TRUNCATE TABLE {$table->getName()} CASCADE");

            if ($table->hasColumn('id')) {
                $connection->executeQuery("ALTER SEQUENCE {$table->getName()}_id_seq RESTART WITH 1");
            }
        }

        foreach ($tables as $table) {
            $connection->executeQuery("ALTER TABLE {$table->getName()} ENABLE TRIGGER ALL");
        }
    }

    /**
     * @return mixed[]
     */
    protected function assertResponseSuccess(KernelBrowser $client, int $code = ResponseStatusCode::OK_200): array
    {
        $response = $client->getResponse();

        self::assertIsString($response->getContent());

        self::assertSame($code, $response->getStatusCode(), $response->getContent());

        $responseData = [];

        /** @var bool|string $responseJson */
        $responseJson = $response->getContent();
        if (is_string($responseJson) && $responseJson !== '') {
            try {
                $responseData = Json::decode($responseJson, Json::FORCE_ARRAY);
            } catch (JsonException $e) {
                self::fail("{$e->getMessage()} - {$responseJson}");
            }
        }

        self::assertIsArray($responseData);

        return $responseData;
    }

    protected function assertResponseError(KernelBrowser $client, int $code): void
    {
        $responseData = $this->assertResponseSuccess($client, $code);
        self::assertSame($code, $responseData['code']);
    }

    protected function assertResponseBadRequest(KernelBrowser $client, string $message, int $code = Response::HTTP_BAD_REQUEST): void
    {
        $responseData = $this->assertResponseSuccess($client, $code);
        self::assertSame($code, $responseData['code']);
        self::assertSame($message, $responseData['message']);
    }

    public function createClientWithoutUser(): KernelBrowser
    {
        $this->client->setServerParameters([
            'CONTENT_TYPE' => 'application/json',
            'ACCEPT' => 'application/json',
        ]);
        return $this->client;
    }

    public function createClientWithApiKey(string $apiKey): KernelBrowser
    {
        $this->client->setServerParameters([
            'CONTENT_TYPE' => 'application/json',
            'ACCEPT' => 'application/json',
            'HTTP_Authorization' => "Bearer {$apiKey}",
        ]);

        return $this->client;
    }

}
