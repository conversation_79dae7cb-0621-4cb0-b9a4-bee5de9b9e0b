server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    root        /application/public;
    client_header_buffer_size   256k;
    client_max_body_size        10m;
    
    location / {
        try_files $uri /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        fastcgi_pass php-upstream;
        fastcgi_read_timeout 300;
        proxy_read_timeout 300;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        internal;
    }
    
    location ~ \.php$ {
        return 404;
    }

    error_log  /dev/stderr;
    access_log /dev/stdout logger-json;

    fastcgi_buffer_size 32k;
    fastcgi_buffers 4 32k;
}
