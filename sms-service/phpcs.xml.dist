<?xml version="1.0"?>
<ruleset
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
    name="Developartment Coding Standard"
>
    <arg name="basepath" value="."/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="80"/>
    <arg name="report-width" value="auto"/>
    <arg name="tab-width" value="4"/>
    <arg name="encoding" value="utf-8"/>
    <arg name="cache" value="var/phpcs.cache"/>
    <arg name="colors"/>
    <arg value="s"/>
    <arg value="p"/>

    <file>migrations/</file>
    <file>src/</file>
    <file>tests/</file>

    <config name="installed_paths" value="vendor/slevomat/coding-standard"/>

    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>
    <rule ref="Generic.Classes.DuplicateClassName"/>
    <rule ref="Generic.CodeAnalysis.EmptyStatement">
        <exclude name="Generic.CodeAnalysis.EmptyStatement.DetectedCatch"/><!-- empty catch statements are allowed when commented -->
    </rule>
    <rule ref="Generic.CodeAnalysis.ForLoopShouldBeWhileLoop"/>
    <rule ref="Generic.CodeAnalysis.UnconditionalIfStatement"/>
    <rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
    <rule ref="Generic.Files.ByteOrderMark"/>
    <rule ref="Generic.Files.EndFileNewline"/>
    <rule ref="Generic.Files.InlineHTML"/>
    <rule ref="Generic.Files.LineEndings"/>
    <rule ref="Generic.Files.OneClassPerFile"/>
    <rule ref="Generic.Files.OneInterfacePerFile"/>
    <rule ref="Generic.Files.OneTraitPerFile"/>
    <rule ref="Generic.Formatting.SpaceAfterNot">
        <properties>
            <property name="spacing" value="0"/>
        </properties>
    </rule>
    <rule ref="Generic.Formatting.SpaceAfterCast"/>
    <rule ref="Generic.Formatting.MultipleStatementAlignment">
        <properties>
            <property name="maxPadding" value="1"/>
        </properties>
    </rule>
    <rule ref="Generic.Functions.CallTimePassByReference"/>
    <rule ref="Generic.Functions.OpeningFunctionBraceBsdAllman"/>
    <rule ref="Generic.NamingConventions.ConstructorName"/>
    <rule ref="Generic.PHP.BacktickOperator"/>
    <rule ref="Generic.PHP.DiscourageGoto"/>
    <rule ref="Generic.PHP.LowerCaseKeyword"/>
    <rule ref="Generic.PHP.LowerCaseType"/>
    <rule ref="Generic.PHP.CharacterBeforePHPOpeningTag"/>
    <rule ref="Generic.PHP.DeprecatedFunctions"/>
    <rule ref="Generic.PHP.DisallowAlternativePHPTags"/>
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property
                name="forbiddenFunctions"
                type="array"
                value="
                    mt_rand=>rand,
                    sizeof=>count,
                    print=>echo,
                    join=>implode,
                    split=>explode,
                    is_null=>null,
                    create_function=>null,
                    key_exists=>array_key_exists,
                    file_get_contents=>Nette\Utils\FileSystem::read,
                    file_put_contents=>Nette\Utils\FileSystem::write,
                    unlink=>Nette\Utils\FileSystem::delete,
                    mkdir=>Nette\Utils\FileSystem::createDir,
                    floatval=>null,
                    boolval=>null,
                    intval=>null,
                    strval=>null,
                    settype=>null,
                    exit=>null,
                    json_decode=>Nette\Utils\Json::decode,
                    json_encode=>Nette\Utils\Json::encode,
                    GuzzleHttp\json_encode=>Nette\Utils\Json::encode,
                    GuzzleHttp\json_decode=>Nette\Utils\Json::decode
                "
            />
        </properties>
    </rule>
    <rule ref="Generic.Strings.UnnecessaryStringConcat">
        <properties>
            <property name="allowMultiline" value="true"/>
        </properties>
    </rule>
    <rule ref="Generic.VersionControl.GitMergeConflict"/>
    <rule ref="Generic.WhiteSpace.IncrementDecrementSpacing"/>
    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="tabIndent" value="true"/>
        </properties>
    </rule>
    <rule ref="PEAR.Classes.ClassDeclaration"/>
    <rule ref="PEAR.Commenting.InlineComment"/>
    <rule ref="PEAR.Formatting.MultiLineAssignment"/>
    <rule ref="PEAR.WhiteSpace.ObjectOperatorIndent"/>
    <rule ref="PSR1.Classes.ClassDeclaration.MissingNamespace"/>
    <rule ref="PSR1.Methods.CamelCapsMethodName.NotCamelCaps"/>
    <rule ref="PSR2">
        <exclude name="Generic.Files.LineLength"/>
        <exclude name="PEAR.Functions.ValidDefaultValue"/>
        <exclude name="PSR2.Classes.ClassDeclaration"/>
        <exclude name="PSR2.ControlStructures.ControlStructureSpacing.SpacingAfterOpenBrace"/>
        <exclude name="PSR2.ControlStructures.SwitchDeclaration.caseIndent"/>
        <exclude name="PSR2.ControlStructures.SwitchDeclaration.defaultIndent"/>
        <exclude name="Squiz.Functions.LowercaseFunctionKeywords"/>
        <exclude name="Squiz.ControlStructures.LowercaseDeclaration"/>
        <exclude name="Squiz.Functions.MultiLineFunctionDeclaration.NewlineBeforeOpenBrace"/>
        <exclude name="Squiz.WhiteSpace.ControlStructureSpacing.SpacingAfterOpenBrace"/>
        <exclude name="Squiz.WhiteSpace.ControlStructureSpacing.SpacingBeforeClose"/>
    </rule>
    <rule ref="PSR12.Classes.ClassInstantiation"/>
    <rule ref="PSR12.Functions.NullableTypeDeclaration"/>
    <rule ref="PSR12.Keywords.ShortFormTypeKeywords"/>
    <rule ref="PSR12.Operators.OperatorSpacing"/>
    <rule ref="Squiz.Arrays.ArrayBracketSpacing"/>
    <rule ref="Squiz.Arrays.ArrayDeclaration">
        <exclude name="Squiz.Arrays.ArrayDeclaration.CloseBraceNewLine"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.CloseBraceNotAligned"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.DoubleArrowNotAligned"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.FirstIndexNoNewline"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.FirstValueNoNewline"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.IndexNoNewline"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.KeyNotAligned"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.MultiLineNotAllowed"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.NoCommaAfterLast"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.SingleLineNotAllowed"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.ValueNoNewline"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.ValueNotAligned"/>
    </rule>
    <rule ref="Squiz.ControlStructures.InlineIfDeclaration">
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.ElvisSpacing"/>
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.NoBrackets"/>
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.NotSingleLine"/>
    </rule>
    <rule ref="Squiz.Classes.ClassFileName"/>
    <rule ref="Squiz.Classes.SelfMemberReference"/>
    <rule ref="Squiz.Commenting.DocCommentAlignment">
        <exclude name="Squiz.Commenting.DocCommentAlignment.SpaceAfterStar"/>
    </rule>
    <rule ref="Squiz.Commenting.EmptyCatchComment"/>
    <rule ref="Squiz.Commenting.FunctionComment">
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamTag"/>
        <exclude name="Squiz.Commenting.FunctionComment.ParamNameNoMatch"/>
        <exclude name="Squiz.Commenting.FunctionComment.InvalidTypeHint"/>
        <exclude name="Squiz.Commenting.FunctionComment.EmptyThrows"/>
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectParamVarName"/>
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectTypeHint"/>
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturn"/>
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturnNotVoid"/>
        <exclude name="Squiz.Commenting.FunctionComment.Missing"/>
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamComment"/>
        <exclude name="Squiz.Commenting.FunctionComment.MissingReturn"/>
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentFullStop"/>
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentNotCapital"/>
        <exclude name="Squiz.Commenting.FunctionComment.ScalarTypeHintMissing"/>
        <exclude name="Squiz.Commenting.FunctionComment.SpacingAfterParamName"/>
        <exclude name="Squiz.Commenting.FunctionComment.SpacingAfterParamType"/>
        <exclude name="Squiz.Commenting.FunctionComment.ThrowsNoFullStop"/>
        <exclude name="Squiz.Commenting.FunctionComment.ThrowsNotCapital"/>
        <exclude name="Squiz.Commenting.FunctionComment.TypeHintMissing"/>
        <exclude name="Squiz.Commenting.FunctionComment.InvalidNoReturn"/>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.InvalidThrows">
        <message>Exception type missing for @throws annotation</message>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.DuplicateReturn">
        <message>Only 1 @return annotation is allowed in a function comment</message>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.ExtraParamComment">
        <message>Extra @param annotation</message>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.MissingParamTag">
        <message>@param annotation for parameter "%s" missing</message>
    </rule>
    <rule ref="Squiz.Commenting.InlineComment">
        <exclude name="Squiz.Commenting.InlineComment.NotCapital"/>
        <exclude name="Squiz.Commenting.InlineComment.InvalidEndChar"/>
        <exclude name="Squiz.Commenting.InlineComment.DocBlock"/>
    </rule>
    <rule ref="Squiz.Functions.GlobalFunction"/>
    <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing">
        <properties>
            <property name="equalsSpacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.Operators.IncrementDecrementUsage">
        <exclude name="Squiz.Operators.IncrementDecrementUsage.NoBrackets"/>
    </rule>
    <rule ref="Squiz.Operators.ValidLogicalOperators"/>
    <rule ref="Squiz.PHP.CommentedOutCode"/>
    <rule ref="Squiz.PHP.GlobalKeyword"/>
    <rule ref="Squiz.PHP.Heredoc">
        <exclude name="Squiz.PHP.Heredoc.NotAllowed"/>
    </rule>
    <rule ref="Squiz.PHP.InnerFunctions"/>
    <rule ref="Squiz.PHP.LowercasePHPFunctions"/>
    <rule ref="Squiz.PHP.NonExecutableCode"/>
    <rule ref="Squiz.Scope.StaticThisUsage"/>
    <rule ref="Squiz.Scope.MethodScope"/>
    <rule ref="Squiz.Strings.ConcatenationSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
            <property name="spacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.Strings.DoubleQuoteUsage">
        <exclude name="Squiz.Strings.DoubleQuoteUsage.ContainsVar"/>
    </rule>
    <rule ref="Squiz.Strings.EchoedStrings"/>
    <rule ref="Squiz.WhiteSpace.CastSpacing"/>
    <rule ref="Squiz.WhiteSpace.FunctionOpeningBraceSpace"/>
    <rule ref="Squiz.WhiteSpace.FunctionSpacing">
        <properties>
            <property name="spacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.LanguageConstructSpacing"/>
    <rule ref="Squiz.WhiteSpace.LogicalOperatorSpacing"/>
    <rule ref="Squiz.WhiteSpace.MemberVarSpacing"/>
    <rule ref="Squiz.WhiteSpace.ObjectOperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.OperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SemicolonSpacing"/>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace">
        <properties>
            <property name="ignoreBlankLines" value="false"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EmptyLines">
        <severity>5</severity>
    </rule>
    <rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
    <rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference" />
    <rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility" />
    <rule ref="SlevomatCodingStandard.Classes.ClassStructure">
        <properties>
            <property name="groups" type="array">
                <element value="uses"/>
                <element value="constants"/>
                <element value="properties"/>
                <element value="constructor"/>
                <element value="destructor"/>
                <element value="methods"/>
                <element value="enum cases"/>
            </property>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Classes.UselessLateStaticBinding" />
    <rule ref="SlevomatCodingStandard.Classes.DisallowLateStaticBindingForConstants" />
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiConstantDefinition" />
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiPropertyDefinition" />
    <rule ref="SlevomatCodingStandard.Classes.MethodSpacing" />
    <rule ref="SlevomatCodingStandard.Classes.EmptyLinesAroundClassBraces"/>
    <rule ref="SlevomatCodingStandard.Classes.TraitUseDeclaration" />
    <rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing" />
    <rule ref="SlevomatCodingStandard.Commenting.DocCommentSpacing">
        <properties>
            <property name="linesCountBeforeFirstContent" value="0"/>
            <property name="linesCountAfterLastContent" value="0"/>
            <property name="linesCountBetweenDescriptionAndAnnotations" value="1"/>
            <property name="linesCountBetweenAnnotationsGroups" value="1"/>
            <property name="annotationsGroups" type="array">
                <element value="
                    @Get,
                    @Post,
                    @Put,
                    @Patch,
                    @Delete,
                "/>
                <element value="
                    @QueryParam,
                "/>
                <element value="
                    @var,
                    @param,
                    @return,
                "/>
                <element value="
                    @template,
                    @extends,
                    @implements,
                    @phpstan-var,
                    @phpstan-param,
                    @phpstan-return,
                "/>
                <element value="
                    @ORM\,"
                />
                <element value="
                    @throws,
                "/>
            </property>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Commenting.EmptyComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.DisallowOneLinePropertyDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.InlineDocCommentDeclaration"/>
    <rule ref="SlevomatCodingStandard.Commenting.UselessInheritDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenComments">
        <properties>
            <property name="forbiddenCommentPatterns" type="array" value="~^[a-zA-Z0-9]+ constructor.?$~,~PhpStorm~,~^[GS]et [a-zA-Z0-9]+$~"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenAnnotations">
        <properties>
            <property name="forbiddenAnnotations" type="array" value="@author,@created,@version,@package,@copyright,@license,@since,@link,@group,@expectedException" />
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.ControlStructures.AssignmentInCondition"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.LanguageConstructWithParentheses"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.NewWithParentheses"/>
    <rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>
    <rule ref="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly">
        <exclude name="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly.ReferencedGeneralException" />
    </rule>
    <rule ref="SlevomatCodingStandard.Functions.RequireTrailingCommaInCall"/>
    <rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>
    <rule ref="SlevomatCodingStandard.Functions.UselessParameterDefaultValue"/>
    <rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
    <rule ref="SlevomatCodingStandard.Files.TypeNameMatchesFileName">
        <properties>
            <property
                name="rootNamespaces"
                type="array"
                value="
                    migrations=>DoctrineMigrations,
                    src=>SmsService,
                    tests=>SmsService\Tests,
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>
    <rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses">
        <properties>
            <property name="caseSensitive" value="false"/>
            <property name="psr12Compatible" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.DisallowGroupUse"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceDeclaration"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceSpacing"/>
    <rule ref="SlevomatCodingStandard.Namespaces.RequireOneNamespaceInFile"/>
    <rule ref="SlevomatCodingStandard.Namespaces.MultipleUsesPerLine"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseDoesNotStartWithBackslash"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseSpacing"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UselessAlias"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UnusedUses">
        <properties>
            <property name="searchAnnotations" type="bool" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly">
        <properties>
            <property name="allowFullyQualifiedGlobalFunctions" type="bool" value="false"/>
            <property name="allowFullyQualifiedNameForCollidingFunctions" type="bool" value="false"/>
            <property name="allowFallbackGlobalFunctions" type="bool" value="false"/>

            <property name="allowFullyQualifiedGlobalConstants" type="bool" value="false"/>
            <property name="allowFullyQualifiedNameForCollidingConstants" type="bool" value="false"/>
            <property name="allowFallbackGlobalConstants" type="bool" value="false"/>

            <property name="allowPartialUses" value="false"/>
            <property name="searchAnnotations" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly.PartialUse">
        <exclude-pattern>config/bundles.php</exclude-pattern>
    </rule>
    <rule ref="SlevomatCodingStandard.Numbers.DisallowNumericLiteralSeparator" />
    <rule ref="SlevomatCodingStandard.Operators.DisallowEqualOperators" />
    <rule ref="SlevomatCodingStandard.Operators.SpreadOperatorSpacing" />
    <rule ref="SlevomatCodingStandard.PHP.DisallowDirectMagicInvokeCall" />
    <rule ref="SlevomatCodingStandard.PHP.ShortList"/>
    <rule ref="SlevomatCodingStandard.PHP.TypeCast"/>
    <rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
    <rule ref="SlevomatCodingStandard.PHP.OptimizedFunctionsWithoutUnpacking"/>
    <rule ref="SlevomatCodingStandard.PHP.ReferenceSpacing"/>
    <rule ref="SlevomatCodingStandard.PHP.RequireNowdoc"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullableTypeForNullDefaultValue" />
    <rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition" />
    <rule ref="SlevomatCodingStandard.TypeHints.UselessConstantTypeHint" />
    <rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHint">
        <properties>
            <property name="enableNativeTypeHint" value="true"/>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.LongTypeHints"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHintSpacing"/>
    <rule ref="SlevomatCodingStandard.Variables.DuplicateAssignmentToVariable"/>
    <rule ref="SlevomatCodingStandard.Variables.UnusedVariable">
        <properties>
            <property name="ignoreUnusedValuesWhenOnlyKeysAreUsedInForeach" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.AssignmentInCondition"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowContinueWithoutIntegerOperandInSwitch"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.EarlyExit">
        <exclude name="SlevomatCodingStandard.ControlStructures.EarlyExit.EarlyExitNotUsed"/>
    </rule>
</ruleset>
