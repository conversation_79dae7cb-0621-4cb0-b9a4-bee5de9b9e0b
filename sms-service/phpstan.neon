includes:
    - ./vendor/phpstan/phpstan-doctrine/extension.neon
    - ./vendor/phpstan/phpstan-phpunit/extension.neon
    - ./vendor/phpstan/phpstan-phpunit/rules.neon
    - ./vendor/phpstan/phpstan-symfony/extension.neon
    - ./vendor/phpstan/phpstan-webmozart-assert/extension.neon
    - ./vendor/pepakriz/phpstan-exception-rules/extension.neon
    - ./vendor/phpstan/phpstan-strict-rules/rules.neon

parametersSchema:
    root: string()

parameters:
    level: max
    paths:
        - src
        - tests
    parallel:
        processTimeout: 1000.0
    root: %rootDir%/../../..
    tmpDir: %rootDir%/../../../var/phpstan/
    symfony:
        container_xml_path: %root%/var/cache/test/SmsService_KernelTestDebugContainer.xml
    doctrine:
        repositoryClass: 'Doctrine\ORM\EntityRepository'
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    exceptionRules:
        reportUnusedCheckedThrowsInSubtypes: false
        checkedExceptions:
            - SmsService\Core\Exception\RuntimeException
            - Pepakriz\PHPStanExceptionRules\UnsupportedClassException
            - Pepakriz\PHPStanExceptionRules\UnsupportedFunctionException
            - Psr\Http\Client\ClientExceptionInterface
        methodWhitelist:
            PHPUnit\Framework\TestCase: '#^(setup|setupbeforeclass|teardown|teardownafterclass|test.*)$#i'
    excludePaths:
        - %root%/src/bootstrap.php
        - %root%/tests/bootstrap.php
    bootstrapFiles:
        - %root%/vendor/squizlabs/php_codesniffer/autoload.php
        - %root%/vendor/squizlabs/php_codesniffer/src/Util/Tokens.php
    stubFiles:
        - ./PHPStan/stubs/BackedEnum.php.stub
    checkExplicitMixed: false
    ignoreErrors:
        # BackedEnum generics solution
        - '#^Enum .*? has @implements tag, but does not implement any interface.$#'
        - '#(.*) of method (.*)::convertToPHPValue\(\) should be contravariant with parameter \$value \(mixed\) of method (.*)::convertToPHPValue\(\)#'
        - '#(.*) of method (.*)::convertToDatabaseValue\(\) should be contravariant with parameter \$value \(mixed\) of method (.*)::convertToDatabaseValue\(\)#'

