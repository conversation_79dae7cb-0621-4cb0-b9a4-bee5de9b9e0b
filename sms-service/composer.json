{"type": "project", "name": "goodsailors/sms-service", "description": "SMS Service", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": "8.3.*", "ext-amqp": "^2.0", "ext-ctype": "*", "ext-iconv": "*", "ext-simplexml": "*", "composer/package-versions-deprecated": "*********", "doctrine/annotations": "^2.0", "doctrine/doctrine-bundle": "^2.10", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.8", "friendsofsymfony/rest-bundle": "3.*", "gedmo/doctrine-extensions": "^3.13", "guzzlehttp/guzzle": "^7.2", "jms/serializer-bundle": "^5.0", "johngrogg/ics-parser": "^3.3", "nelmio/api-doc-bundle": "^4.12", "nelmio/cors-bundle": "^2.3", "nette/caching": "^3.1", "nette/utils": "^3.2", "odolbeau/phone-number-bundle": "^4.0", "php-amqplib/php-amqplib": "3.*", "php-amqplib/rabbitmq-bundle": "^2.13", "prewk/xml-string-streamer": "^1.1", "sentry/sentry-symfony": "^5.0", "stof/doctrine-extensions-bundle": "^1.7", "symfony/asset": "7.1.*", "symfony/browser-kit": "7.1.*", "symfony/cache": "7.1.*", "symfony/clock": "^7.1", "symfony/config": "7.1.*", "symfony/console": "7.1.*", "symfony/dependency-injection": "7.1.*", "symfony/dotenv": "7.1.*", "symfony/fake-sms-notifier": "^7.1", "symfony/flex": "^2.1", "symfony/framework-bundle": "^7.1", "symfony/http-client": "7.1.*", "symfony/http-kernel": "7.1.*", "symfony/intl": "7.1.*", "symfony/lock": "7.1.*", "symfony/mailer": "7.1.*", "symfony/monolog-bundle": "^3.8", "symfony/notifier": "7.1.*", "symfony/proxy-manager-bridge": "^6.4", "symfony/runtime": "7.1.*", "symfony/security-bundle": "7.1.*", "symfony/security-http": "7.1.*", "symfony/serializer": "7.1.*", "symfony/translation": "7.1.*", "symfony/twilio-notifier": "7.1.*", "symfony/uid": "7.1.*", "symfony/validator": "7.1.*", "symfony/yaml": "7.1.*"}, "require-dev": {"pepakriz/phpstan-exception-rules": "^0.12.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-doctrine": "^1.3", "phpstan/phpstan-phpunit": "1.4.0", "phpstan/phpstan-strict-rules": "^1.3", "phpstan/phpstan-symfony": "^1.2", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^10.0", "rector/rector": "^1.0.0", "slevomat/coding-standard": "^8.2", "squizlabs/php_codesniffer": "^3.7", "symfony/phpunit-bridge": "^7.1"}, "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "symfony/flex": true, "php-http/discovery": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"SmsService\\": "src/"}}, "autoload-dev": {"psr-4": {"SmsService\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "check:cs": "php vendor/bin/phpcs", "check:cs:fix": "php vendor/bin/phpcbf", "check:rector": "./vendor/bin/rector process --dry-run", "check:rector:fix": "./vendor/bin/rector process", "check:types": "php -d memory_limit=2G ./vendor/bin/phpstan analyse -c phpstan.neon", "check:tests": "php vendor/bin/phpunit tests --colors=always --stderr --stop-on-failure", "check": ["@check:cs", "@check:rector", "@check:types"], "fix": ["@check:cs:fix", "@check:rector:fix"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.1.*"}}}