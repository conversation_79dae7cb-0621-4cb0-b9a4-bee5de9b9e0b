/* eslint-disable @typescript-eslint/no-explicit-any */

import { create, StoreApi, UseBoundStore } from 'zustand'
import { devtools } from 'zustand/middleware'

import { globalSlice } from './slices/globalSlice'
import { popsSlice } from './slices/popsSlice'
import { AllSlices, WithSelectors } from './types'

const createSelectors = <S extends UseBoundStore<StoreApi<object>>>(
    _store: S,
) => {
    const store = _store as WithSelectors<typeof _store>
    store.use = {}
    for (const k of Object.keys(store.getState())) {
        ;(store.use as any)[k] = () => store((s) => s[k as keyof typeof s])
    }

    return store
}

export const useAppStoreBase = create<AllSlices>()(
    devtools((...a) => ({
        ...popsSlice(...a),
        ...globalSlice(...a),
    })),
)

export const useAppStore = createSelectors(useAppStoreBase)
