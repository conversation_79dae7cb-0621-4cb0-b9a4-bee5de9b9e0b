import React from 'react'
import { Navigate, useLocation, useSearchParams } from 'react-router'

import createRequestParams from '@/utils/createRequestParams'

interface TabGuardProps<E> {
    validValues: E[]
    defaultTab: E
    children:
        | ((props: { tab: E; onTab: (tab: E) => void }) => React.ReactNode)
        | React.ReactNode
}

const TabGuard = <E extends string>({
    validValues,
    defaultTab,
    children,
}: TabGuardProps<E>) => {
    const location = useLocation()

    const [searchParams, setSearchParams] = useSearchParams()
    const tab = (searchParams.get('tab') || '') as E

    const onTab = (tab: E) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('tab', tab)
        setSearchParams(newSearchParams, { replace: true })
    }

    if (!validValues.includes(tab)) {
        const params = createRequestParams({
            tab: defaultTab,
        })

        return <Navigate replace to={location.pathname + params} />
    }

    return typeof children === 'function' ? (
        <>
            {children({
                tab: tab as E,
                onTab,
            })}
        </>
    ) : (
        <>{children}</>
    )
}

export default TabGuard
