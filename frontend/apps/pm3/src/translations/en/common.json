{"actions": {"create-customer": "Create customer", "update-customer": "Update customer", "select-groups": "Select groups", "create-tenant-group": "Create tenant group", "update-tenant-group": "Update tenant group", "deselect-all": "Deselect all", "select-all": "Select all", "leave-website": "Leave website", "create-tenant": "Create tenant", "update-tenant": "Update tenant", "create-legislation": "Create legislation", "update-legislation": "Update legislation", "create-unit": "Create unit", "update-unit": "Update unit", "undo": "Undo", "create-project": "Create project", "create-facility": "Create facility", "save": "Save", "create-company": "Create company", "create-currency": "Create currency", "create-contract": "Create contract", "open": "Open", "remove": "Remove", "continue-without-changes": "Continue without saving", "add-template": "Add template", "add-filter": "Add filter", "remove-all": "Remove all", "collapse-all": "Collapse all", "close": "Close", "back": "Back", "delete": "Delete", "cancel": "Cancel", "update": "Update", "continue": "Continue", "save-changes": "Save changes", "discard-changes": "Discard changes", "create-group": "Create group", "update-group": "Update group", "show-detail": "Show detail", "create-position": "Create position", "add-period": "Add period", "add-break": "Add break", "add-position": "Add position", "add-another-position": "Add another position", "add-another-shift": "Add another shift", "generate": "Generate", "detail": "Detail", "change": "Change", "add-automatically": "Add automatically", "add": "Add", "duplicate": "Duplicate", "remove-plan": "Remove plan", "send-sms": "Send SMS", "select-contact-groups": "Select contact groups", "carry-over-from-prev": "Carry over from previous", "send-emails-with-attachments": "Send emails with attachments", "update-event": "Update event", "update-pops": "Update PoPS", "create-super-pops": "Create SuperPoPS", "update-super-pops": "Update SuperPoPS", "update-object-group": "Update object group", "create-object-group": "Create object group", "create-event": "Create event", "report-event": "Report an event", "export": "Export", "export-to-target": "Export to {{target}}", "create-pops": "Create PoPS", "add-event": "Add event", "lock-pops": "Lock PoPS", "lock-superpops": "Lock SuperPoPS", "logout": "Logout"}, "texts": {"leave-website-confirm": "Are you sure you want to leave this page?", "no-changes": "No changes", "write-for-search-address": "Type to search for address", "manual-address-entry": "Manual address entry", "success-action": "Action completed successfully", "leave-unsaved-confirm": "Not all changes have been saved. Are you sure you want to leave?", "fill-and-submit": "Fill in the information below and submit.", "change-password-description": "To set a new password, please enter the combination of your current and new password below. Thank you for securing your account!", "user-profile-updated": "User profile updated", "password-updated": "Password updated successfully", "delete-group-confirmation": "Are you sure you want to delete the group?", "save-changes-question": "Do you want to save these changes?", "sending-emergency-message": "Sending emergency SMS message", "sending-group-emergency-message": "Sending SMS messages to selected groups", "lock-super-pops-confirmation": "Are you sure you want to lock this SuperPoPS?", "lock-pops-confirmation": "Are you sure you want to lock this PoPS?", "pops-attachment-uploaded": "Attachment has been uploaded", "pops-attachment-deleted": "Attachment has been deleted", "pops-needs-created": "A new PoPS needs to be created.", "event-info-post-creation": "Event information will be displayed after a new PoPS is created", "warning-critical-event-selected": "You have selected a critical event. If you are sure, proceed with creating the event", "warning-messages-emergency": "These messages will reach management and the client, please do not use them lightly.", "file-info-upload": "Click or drag to upload a photo/attachment", "delete-permission-template-confirmation": "Are you sure you want to delete the permission template?", "delete-permission-group-confirmation": "Are you sure you want to delete the permission group?", "delete-facility-group-confirmation": "Are you sure you want to delete the facility group?", "delete-currency-confirmation": "Are you sure you want to delete this currency?", "delete-contract-confirmation": "Are you sure you want to delete this contract?", "delete-company-confirmation": "Are you sure you want to delete the company?", "delete-project-confirmation": "Are you sure you want to delete this project?", "delete-unit-confirmation": "Are you sure you want to delete this unit?", "delete-tenant-confirmation": "Are you sure you want to delete this tenant?", "delete-customer-confirmation": "Are you sure you want to delete this customer?", "delete-legislation-confirmation": "Are you sure you want to delete this legislation?", "delete-tenant-group-confirmation": "Are you sure you want to delete this tenant group?"}, "errors": {"files-not-uploaded": "Files were not uploaded. Maximum number of files: {{maxFiles}}", "name-already-exists": "Name already exists", "invalid-format": "Invalid format", "time-range-must-include-current-time": "The time range must include the current time", "end-date-must-be-after-start-date": "End date must be after start date.", "invalid-credentials": "Invalid credentials", "required-field": "This field is required", "invalid-data": "Invalid data", "too-short-password": "Your password is too short", "passwords-dont-match": "Your passwords do not match.", "file-too-large": "Uploaded file is too large", "file-invalid-type": "Incorrect file format"}, "labels": {"customer": "Customer", "customers": "Customers", "create-customer": "Create customer", "update-customer": "Update customer", "select-item": "Select item", "quick-object-selection-by-groups": "Quick object selection by groups", "assigned": "Assigned", "unassigned": "Unassigned", "tenant-group": "Tenant group", "tenant-groups": "Tenant groups", "create-tenant-group": "Create tenant group", "update-tenant-group": "Update tenant group", "selected": "Selected", "is-readonly": "Read only", "tenant": "Tenant", "tenants": "Tenants", "create-tenant": "Create tenant", "update-tenant": "Update tenant", "legislations": "Legislations", "create-legislation": "Create legislation", "update-legislation": "Update legislation", "email": "E-mail", "phone": "Phone", "unit": "Unit", "units": "Units", "create-unit": "Create unit", "update-unit": "Update unit", "parent-category": "Parent category", "created": "Created", "removed": "Removed", "moved": "Moved", "updated": "Updated", "no": "No", "yes": "Yes", "severity": "Severity", "is-hidden": "Is hidden", "change-history": "Change history", "category-tree": "Category tree", "projects": "Projects", "project": "Project", "create-project": "Create project", "update-project": "Update project", "address": "Address", "city": "City", "id-number": "ID number", "street": "Street", "country": "Country", "postcode": "Postcode", "note": "Note", "contact-email": "Contact email", "companies": "Companies", "company": "Company", "create-company": "Create company", "update-company": "Update company", "abbreviation": "Abbreviation", "currencies": "Currencies", "currency": "<PERSON><PERSON><PERSON><PERSON>", "create-currency": "Create currency", "update-currency": "Update currency", "contracts": "Contracts", "create-contract": "Create contract", "update-contract": "Update contract", "contract": "Contract", "pops-alert-interval": "PoPS interval", "pops-alert-interval-time_one": "Every minute", "pops-alert-interval-time_two": "Every 2 minutes", "pops-alert-interval-time_few": "Every {{count}} minutes", "pops-alert-interval-time_other": "Every {{count}} minutes", "selected-object": "Selected object", "category-selection": "Category selection", "favorite-category": "Favorite category", "create-event": "Create event", "highest-priority": "Highest priority", "inactive": "Inactive", "code": "Code", "objects-groups": "Object groups", "update-permission-template": "Update permission template", "update-permission-group": "Update permission group", "updated-by": "Updated by", "no-options": "No options", "filter": "Filter", "columns": "Columns", "update-event": "Update event", "loading": "Loading...", "add-new-event": "Add new event", "create-new-pops": "Create new PoPS", "login": "<PERSON><PERSON>", "event-time": "Event time", "event": "Event", "last-activity": "Last activity", "attaches": "Attaches", "add-event": "Add event", "author": "Author", "emergency-button": "Emergency button", "group-emergency-button": "Emergency button, message to groups", "emergency-situation": "Emergency situation", "status": "Status", "active": "Active", "closed": "Closed", "shift-start": "Start of shift", "shift-start-superpops": "Start SuperPops", "shift-end": "End of shift", "shift-end-superpops": "End SuperPops", "event-description": "Event description", "shift-range": "Shift range", "shift-range-superpops": "Shift range", "active-pops": "Active PoPS", "inactive-pops": "Inactive PoPs", "superpops": "SuperPoPS", "choose-all-groups": "Choose all groups", "locked": "Locked", "activity-check": "Activity Check", "your-inactivity": "Your Inactivity", "activity-confirmation": "I am Active", "attachments": "Attachments", "event-category": {"NONE": "Internal", "MEDIUM": "Medium", "CRITICAL": "Critical", "LOW": "Standard"}, "total-objects_one": "Total {{count}} object", "total-objects_few": "Total {{count}} objects", "total-objects_other": "Total {{count}} objects", "selected-objects": "Selected objects", "services": "Services", "wap": "Wages and personnel", "position": "Position", "period": "Period", "code-object": "Code object", "editor": "Editor", "involment": "Involment", "effectivity": "Effectivity", "attendance": "Attendance", "attendance-status": "Attendance status", "count-of-positions": "Count of positions", "count-of-errors": "Count of errors", "generate-from": "Generate from", "generate-to": "Generate to", "shift": "Shift", "start-of-shift": "Start of the {{index}}. shift", "end-of-shift": "End of the {{index}}. shift", "not-selected": "Not selected", "product": "Product", "center": "Center", "order": "Order", "name-of-position": "Name of position", "inactive-from": "Inactive from", "billing-type": "Billing type", "billing-status": "Billing status", "position-rate": "Position rate", "invoice-rate": "Invoice rate", "billing-rate": "Billing rate", "break": "Break", "break-from": "Break from", "break-to": "Break to", "day": "Day", "time": "Time", "leadership-allowance": "Leadership allowance", "business-phone": "Business phone", "business-mobile-phone": "Business mobile phone", "business-email": "Business e-mail", "fax": "Fax", "repeat-password": "Repeat password", "current-password": "Current password", "new-password": "New password", "settings": "Settings", "personal-contact-information": "Personal Contact Information", "first-name": "First Name", "last-name": "Last Name", "company-identification-number": "ID", "business-contact-information": "Business Contact Information", "personal-settings": "Personal settings", "purpose": "Purpose", "group": "Group", "count-of-objects": "Count of objects", "group-detail": "Group detail", "pops": "PoPS", "super-pops": "SuperPoPS", "profile": "Profile", "change-password": "Change password", "events": "Events", "current-pops": "Today's PoPS", "locked-pops": "Locked <PERSON>", "pops-detail": "PoPS Detail", "super-pops-detail": "Detail SuperPoPS", "event-detail": "Event Detail", "code-list": "Code lists", "usage": "Usage", "date": "Date", "sign-in-to": "Sign in to", "password": "Password", "your-email-or-id": "E-mail / Personal Number", "update-group": "Update group", "create-group": "Create group", "added-permissions": "Added permissions", "discarded-permissions": "Discarded permissions", "personal-no": "Personal no", "value": "Value", "information": "Information", "created-at": "Created at", "updated-at": "Created at", "inserted-by": "Inserted by", "greater-than": "Greater than", "is-after": "Is after", "is-before": "Is before", "less-than": "Less than", "starts-with": "Starts with", "equals": "Equals", "includes": "Includes", "dashboard": "Dashboard", "groups": "Groups", "objects": "Objects", "persons": "Persons", "permissions": "Permissions", "search": "Search", "actions": "Actions", "templates": "Templates", "object": "Object", "name": "Name", "group-name": "Group name", "group-usage": "Group usage", "template": "Template", "category": "Category", "description": "Description", "allowed": "Allowed", "template-name": "Template name", "template-usage": "Template usage", "object-code": "Object code", "permission-groups": "Permission groups", "permission-templates": "Permission templates", "create-permission-template": "Create permission template", "create-permission-group": "Create permission group", "basic-information": "Basic information", "workload": "Workload", "type": "Type", "list-of-users": "List of users", "service-editor": "Service editor", "attendance-approval": "Attendance approval", "invoice-approval": "Invoice approval", "applications": "Applications", "pops-alert": "Pops alert", "attachment_resolution": "Attachment resolution", "object-information": "Object information", "object-address": "Object address", "customer-information": "Customer information", "customer-address": "Customer address", "legislation": "Legislation"}}