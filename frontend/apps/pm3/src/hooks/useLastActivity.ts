import dayjs from 'dayjs'
import React from 'react'

export const useLastActivity = (time: string | undefined) => {
    const [lastActivity, setLastActivity] = React.useState<string | null>(null)

    React.useEffect(() => {
        const updateActivity = () => {
            setLastActivity(time ? dayjs(time).fromNow() : null)
        }

        const interval = setInterval(() => {
            updateActivity()
        }, 1000)

        updateActivity()

        return () => {
            clearInterval(interval)
        }
    }, [time])

    return lastActivity
}
