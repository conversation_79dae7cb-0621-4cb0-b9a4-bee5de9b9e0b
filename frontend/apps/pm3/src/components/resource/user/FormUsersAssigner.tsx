import { useMediaQuery } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useField } from 'formik'
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import { ResourcesAssigner } from '@/components/input/ResourcesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import { UserResponse } from '@/requests/responses'
import getUsersRequest from '@/requests/user/getUsersRequest'
import { formatUserName } from '@/utils/format'

interface FormUsersAssignerProps {
    name: string
    disabled?: boolean
}

const FormUsersAssigner = ({ name, disabled }: FormUsersAssignerProps) => {
    const downMd = useMediaQuery((theme) => theme.breakpoints.down('md'))

    const { t } = useTranslation()

    const [field, , helpers] = useField<number[]>(name)

    const assignerColumns: GridColDef<UserResponse>[] = [
        {
            field: 'personal_no',
            headerName: t('labels.personal-no'),
            filterable: false,
            sortable: false,
            width: 150,
        },
        {
            field: 'fullName',
            headerName: t('labels.name'),
            filterable: false,
            sortable: false,
            width: 200,
            valueGetter: (__, row) => formatUserName(row),
        },
    ]

    return (
        <>
            <ResourcesAssigner
                disabled={disabled}
                columns={assignerColumns}
                layout={downMd ? 'column' : 'row'}
                assignedIds={field.value}
                onChange={(value) => helpers.setValue(value)}
                queryKey={queryKeys.users.index}
                request={async (params) => {
                    const response = await getUsersRequest(params)

                    return {
                        ...response,
                        list: _.map(response.list, (item) => ({
                            ...item,
                            id: item.id,
                            label: formatUserName(item),
                        })),
                    }
                }}
            />
        </>
    )
}

export default FormUsersAssigner
