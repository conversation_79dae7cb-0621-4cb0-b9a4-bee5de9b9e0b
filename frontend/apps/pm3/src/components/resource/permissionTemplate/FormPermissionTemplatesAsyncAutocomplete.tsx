import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getPermissionTemplatesRequest from '@/requests/permissionTemplate/getPermissionTemplatesRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormPermissionTemplatesAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormPermissionTemplatesAsyncAutocomplete = ({
    name,
    multiple,
}: FormPermissionTemplatesAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.permissionTemplates.index}
            request={async (params) => {
                const response = await getPermissionTemplatesRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.id,
                        label: item.name,
                    })),
                }
            }}
            data-testid='TEMPLATE'
            label={t('labels.template')}
        />
    )
}

export default FormPermissionTemplatesAsyncAutocomplete
