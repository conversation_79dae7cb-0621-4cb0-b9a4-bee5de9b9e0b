import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getProjectsRequest from '@/requests/project/getProjectsRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormProjectsAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormProjectsAsyncAutocomplete = ({
    name,
    multiple,
}: FormProjectsAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.projects.index}
            request={async (params) => {
                const response = await getProjectsRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.id,
                        label: item.name,
                    })),
                }
            }}
            data-testid='PROJECT'
            label={t('labels.project')}
        />
    )
}

export default FormProjectsAsyncAutocomplete
