import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getCompaniesRequest from '@/requests/company/getCompaniesRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormCompaniesAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormCompaniesAsyncAutocomplete = ({
    name,
    multiple,
}: FormCompaniesAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.companies.index}
            request={async (params) => {
                const response = await getCompaniesRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.id,
                        label: item.name,
                    })),
                }
            }}
            data-testid='COMPANY'
            label={t('labels.company')}
        />
    )
}

export default FormCompaniesAsyncAutocomplete
