import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import { useMediaQuery } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useField } from 'formik'
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import { ResourcesAssigner } from '@/components/input/ResourcesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import { TenantResponse } from '@/requests/responses'
import getTenantsRequest from '@/requests/tenant/getTenantsRequest'

import CellLink from '../../other/CellLink'
import StatusChip from '../../other/StatusChip'

interface FormTenantsAssignerProps {
    name: string
    disabled?: boolean
}

const FormTenantsAssigner = ({ name, disabled }: FormTenantsAssignerProps) => {
    const downMd = useMediaQuery((theme) => theme.breakpoints.down('md'))

    const { t } = useTranslation()

    const [field, , helpers] = useField<number[]>(name)

    const { gridStringOperators } = useGridOperators()

    const assignerColumns: GridColDef<TenantResponse>[] = [
        {
            field: 'name',
            headerName: t('labels.tenant'),
            width: 200,
            filterOperators: gridStringOperators(),
            renderCell: ({ row }) => {
                const href = createHrefWithParams(
                    routerPaths.codeLists.tenant.index,
                    {
                        tenantId: row.id,
                    },
                )

                return <CellLink label={row.name} to={href} />
            },
        },
        {
            minWidth: 140,
            field: 'read_only',
            type: 'boolean',
            headerName: t('labels.is-readonly'),
        },
        {
            minWidth: 140,
            field: 'active',
            type: 'boolean',
            sortable: false,
            headerName: t('labels.status'),
            renderCell: ({ row }) => {
                return <StatusChip isActive={row.active} />
            },
        },
    ]

    return (
        <>
            <ResourcesAssigner
                disabled={disabled}
                columns={assignerColumns}
                layout={downMd ? 'column' : 'row'}
                assignedIds={field.value}
                onChange={(value) => helpers.setValue(value)}
                queryKey={queryKeys.tenants.index}
                request={async (params) => {
                    const response = await getTenantsRequest(params)

                    return {
                        ...response,
                        list: _.map(response.list, (item) => ({
                            ...item,
                            id: item.id,
                            label: item.name,
                        })),
                    }
                }}
            />
        </>
    )
}

export default FormTenantsAssigner
