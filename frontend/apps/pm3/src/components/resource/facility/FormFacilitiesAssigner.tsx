import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import { useMediaQuery } from '@mui/material'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useField } from 'formik'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { ResourcesAssigner } from '@/components/input/ResourcesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getFacilitiesWithSuperPopsRequest, {
    GetFacilitiesWithSuperPopsParams,
} from '@/requests/facility/getFacilitiesWithSuperPopsRequest'
import { FacilityWithSuperPopsResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'
import { formatDate } from '@/utils/format'

import CellLink from '../../other/CellLink'
import FacilitiesQuickSelectionDialog from './FacilitiesQuickSelectionDialog'

interface FormFacilitiesAssignerProps {
    name: string
    disabled?: boolean
    excludeFacilitiesInSuperPops?: boolean
}

const FormFacilitiesAssigner = ({
    name,
    disabled,
    excludeFacilitiesInSuperPops,
}: FormFacilitiesAssignerProps) => {
    const downMd = useMediaQuery((theme) => theme.breakpoints.down('md'))

    const { t } = useTranslation()

    const [field, , helpers] = useField<number[]>(name)

    const [quickSelectionOpen, setQuickSelectionOpen] = React.useState(false)

    const { gridStringOperators } = useGridOperators()

    const handleSelection = (facilitiesIds: number[]) => {
        helpers.setValue(_.uniq(facilitiesIds))
        setQuickSelectionOpen(false)
    }

    const assignerColumns: GridColDef<FacilityWithSuperPopsResponse>[] = [
        {
            field: 'name',
            headerName: t('labels.object'),
            width: 200,
            filterOperators: gridStringOperators(),
            renderCell: ({ row }) => {
                const href = createHrefWithParams(
                    routerPaths.codeLists.facilities.index,
                    {
                        facilityId: row.id,
                    },
                )

                return <CellLink label={row.name} to={href} />
            },
        },
        {
            minWidth: 120,
            field: 'status',
            filterable: false,
            sortable: false,
            headerName: t('labels.pops'),
            renderCell: ({ row }) => {
                if (!row.last_pops) return <Chip size='small' label='-' />
                return (
                    <Chip
                        size='small'
                        color={row.last_pops.is_locked ? 'default' : 'success'}
                        label={
                            row.last_pops.is_locked
                                ? t('labels.closed')
                                : t('labels.active')
                        }
                    />
                )
            },
        },
        {
            minWidth: 200,
            field: 'last_pops.startsAt',
            sortable: false,
            filterable: false,
            headerName: t('labels.shift-start'),
            valueGetter: (__, row) => {
                if (row.last_pops?.starts_at) {
                    return formatDate(row.last_pops.starts_at)
                }

                return '-'
            },
        },
        {
            minWidth: 200,
            field: 'last_pops.endsAt',
            sortable: false,
            filterable: false,
            headerName: t('labels.shift-end'),
            valueGetter: (__, row) => {
                if (row.last_pops?.starts_at) {
                    return formatDate(row.last_pops.ends_at)
                }

                return '-'
            },
        },
        {
            width: 150,
            field: 'code',
            headerName: t('labels.object-code'),
            sortable: false,
            filterable: false,
        },
    ]

    return (
        <>
            <ResourcesAssigner
                disabled={disabled}
                columns={assignerColumns}
                layout={downMd ? 'column' : 'row'}
                assignedActions={
                    downMd ? (
                        <Button
                            size='small'
                            onClick={() => setQuickSelectionOpen(true)}
                        >
                            {t('actions.select-groups')}
                        </Button>
                    ) : undefined
                }
                unassignedActions={
                    <Button
                        size='small'
                        onClick={() => setQuickSelectionOpen(true)}
                    >
                        {t('actions.select-groups')}
                    </Button>
                }
                assignedIds={field.value}
                onChange={(value) => helpers.setValue(value)}
                queryKey={queryKeys.facilities.withSuperPops}
                request={async (__, paramsObject, side) => {
                    const injectedParams =
                        paramsObject as GetFacilitiesWithSuperPopsParams

                    if (side === 'unassigned' && excludeFacilitiesInSuperPops) {
                        injectedParams.nullOrIsLocked = true
                    }

                    const response = await getFacilitiesWithSuperPopsRequest(
                        createRequestParams(injectedParams),
                    )

                    return {
                        ...response,
                        list: _.map(response.list, (item) => ({
                            ...item,
                            id: item.id,
                            label: item.name,
                        })),
                    }
                }}
            />

            {quickSelectionOpen && (
                <FacilitiesQuickSelectionDialog
                    onSelection={handleSelection}
                    onClose={() => setQuickSelectionOpen(false)}
                />
            )}
        </>
    )
}

export default FormFacilitiesAssigner
