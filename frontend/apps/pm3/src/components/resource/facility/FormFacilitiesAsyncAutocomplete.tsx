import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getFacilitiesRequest from '@/requests/facility/getFacilitiesRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormFacilitiesAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormFacilitiesAsyncAutocomplete = ({
    name,
    multiple,
}: FormFacilitiesAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.facilities.index}
            request={async (params) => {
                const response = await getFacilitiesRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.facility.id,
                        label: item.facility.name,
                    })),
                }
            }}
            data-testid='OBJECT'
            label={t('labels.object')}
        />
    )
}

export default FormFacilitiesAsyncAutocomplete
