import DialogHeader from '@goodsailors/ui/components/other/DialogHeader'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSorting from '@goodsailors/ui/hooks/useGridSort'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import { useTheme } from '@mui/material'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import {
    DataGridPro,
    GridColDef,
    GridRowSelectionModel,
    useGridApiRef,
} from '@mui/x-data-grid-pro'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { gridFilterMap } from '@/config/gridFilter'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getFacilityGroupsRequest, {
    GetFacilityGroupsParams,
} from '@/requests/facilityGroup/getFacilityGroupsRequest'
import { FacilityGroupResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

import BackButton from '../../other/BackButton'
import CellLink from '../../other/CellLink'
import DataGridHeader from '../../other/DataGridHeader'

interface FacilitiesQuickSelectionDialogProps {
    onClose: () => void
    onSelection: (facilitiesIds: number[]) => void
}

const FacilitiesQuickSelectionDialog = ({
    onClose,
    onSelection,
}: FacilitiesQuickSelectionDialogProps) => {
    const { t } = useTranslation()
    const { breakpoints } = useTheme()
    const gridRef = useGridApiRef()

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSorting()
    const { gridStringOperators } = useGridOperators()

    const [selectionModel, setSelectionModel] =
        React.useState<GridRowSelectionModel>({
            type: 'include',
            ids: new Set(),
        })

    const params = createRequestParams<GetFacilityGroupsParams>({
        sort,
        filter,
        pagination,
        search: debouncedSearch,
    })

    const { data: groups, isFetching } = useQuery({
        queryFn: () => getFacilityGroupsRequest(params),
        queryKey: [...queryKeys.facilityGroups.index, params],
        placeholderData: keepPreviousData,
    })

    const handleSelectedGroups = () => {
        const facititiesIds = _.reduce(
            groups?.list,
            (acc, group) => {
                if (
                    !selectionModel.ids.has(group.id) &&
                    selectionModel.type !== 'exclude'
                ) {
                    return acc
                }

                const ids = _.map(group.facilities, (f) => f.id)
                acc = _.concat(acc, ids)
                return acc
            },
            [] as number[],
        )

        setSelectionModel({
            type: 'include',
            ids: new Set(),
        })

        onSelection(facititiesIds)
    }

    const columns = React.useMemo<GridColDef<FacilityGroupResponse>[]>(
        () => [
            {
                minWidth: 250,
                field: 'name',
                filterOperators: gridStringOperators(),
                headerName: t('labels.name'),
                renderCell: ({ row }) => {
                    const href = createHrefWithParams(
                        routerPaths.codeLists.facilityGroup.index,
                        {
                            facilityGroupId: row.id,
                        },
                    )

                    return <CellLink label={row.name} to={href} />
                },
            },
            {
                minWidth: 140,
                field: 'usage',
                filterOperators: gridStringOperators(),
                headerName: t('labels.usage'),
            },
            {
                minWidth: 140,
                field: 'facilities',
                sortable: false,
                filterable: false,
                headerName: t('labels.count-of-objects'),
                valueGetter: (__, row) => row.facilities.length,
            },
        ],
        [gridStringOperators, t],
    )

    return (
        <Dialog
            open
            onClose={onClose}
            sx={{
                '.MuiPaper-root': {
                    maxWidth: 1100,
                    width: '100%',
                },
            }}
        >
            <DialogHeader
                title={t('labels.quick-object-selection-by-groups')}
                onClose={onClose}
            />

            <DialogContent sx={{ p: 0 }}>
                <DataGridHeader
                    gridRef={gridRef}
                    onSearch={setSearch}
                    search={search}
                />

                <DataGridPro
                    disableColumnPinning
                    pagination
                    sx={{
                        height: '100%',
                        minHeight: 150,
                        [breakpoints.up('lg')]: {
                            minHeight: 400,
                        },
                    }}
                    checkboxSelection
                    apiRef={gridRef}
                    columns={columns}
                    loading={isFetching}
                    paginationMode='server'
                    sortingMode='server'
                    filterMode='server'
                    disableColumnSelector
                    paginationModel={paginationModel}
                    filterModel={filterModel}
                    rowSelectionModel={selectionModel}
                    onRowSelectionModelChange={setSelectionModel}
                    onPaginationModelChange={setPaginationModel}
                    onFilterModelChange={(model) => setFilterModel(model)}
                    sortModel={sortModel}
                    onSortModelChange={(model) => setSortModel(model)}
                    rows={groups?.list || []}
                    rowCount={groups?.total_count || 0}
                    initialState={{
                        density: 'compact',
                        pinnedColumns: {
                            right: ['actions'],
                        },
                    }}
                />
            </DialogContent>

            <DialogActions sx={{ justifyContent: 'space-between', mt: 1 }}>
                <BackButton onClick={onClose} color='inherit' />

                <Button
                    data-testid='CONFIRM'
                    disabled={
                        selectionModel.type === 'include' &&
                        selectionModel.ids.size === 0
                    }
                    onClick={handleSelectedGroups}
                >
                    {t('actions.continue')}
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default FacilitiesQuickSelectionDialog
