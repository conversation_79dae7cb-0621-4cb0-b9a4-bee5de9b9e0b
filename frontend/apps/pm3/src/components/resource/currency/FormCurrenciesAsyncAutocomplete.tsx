import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getCurrenciesRequest from '@/requests/currency/getCurrenciesRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormCurrenciesAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormCurrenciesAsyncAutocomplete = ({
    name,
    multiple,
}: FormCurrenciesAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.currencies.index}
            request={async (params) => {
                const response = await getCurrenciesRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.id,
                        label: item.name,
                    })),
                }
            }}
            data-testid='CURRENCY'
            label={t('labels.currency')}
        />
    )
}

export default FormCurrenciesAsyncAutocomplete
