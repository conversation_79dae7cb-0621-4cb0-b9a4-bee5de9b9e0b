import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getLegislationsRequest from '@/requests/legislation/getLegislationsRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormLegislationsAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormLegislationsAsyncAutocomplete = ({
    name,
    multiple,
}: FormLegislationsAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.legislations.index}
            request={async (params) => {
                const response = await getLegislationsRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.id,
                        label: item.name,
                    })),
                }
            }}
            data-testid='LEGISLATION'
            label={t('labels.legislation')}
        />
    )
}

export default FormLegislationsAsyncAutocomplete
