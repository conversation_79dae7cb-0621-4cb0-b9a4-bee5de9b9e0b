import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import queryKeys from '@/config/queryClient/queryKeys'
import getContractsRequest from '@/requests/contract/getContractsRequest'

import FormAsyncAutocomplete from '../../form/FormAsyncAutocomplete'

interface FormContractsAsyncAutocompleteProps {
    name: string
    multiple?: boolean
}

const FormContractsAsyncAutocomplete = ({
    name,
    multiple,
}: FormContractsAsyncAutocompleteProps) => {
    const { t } = useTranslation()

    return (
        <FormAsyncAutocomplete
            size='small'
            name={name}
            multiple={multiple}
            queryKey={queryKeys.contracts.index}
            request={async (params) => {
                const response = await getContractsRequest(params)
                return {
                    ...response,
                    list: _.map(response.list, (item) => ({
                        id: item.id,
                        label: item.name,
                    })),
                }
            }}
            data-testid='CONTRACT'
            label={t('labels.contract')}
        />
    )
}

export default FormContractsAsyncAutocomplete
