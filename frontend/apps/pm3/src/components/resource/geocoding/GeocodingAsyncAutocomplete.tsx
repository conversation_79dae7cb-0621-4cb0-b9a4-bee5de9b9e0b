import AsyncAutocomplete from '@goodsailors/ui/components/inputs/AsyncAutocomplete'
import { AsyncAutocompleteProps } from '@goodsailors/ui/components/inputs/AsyncAutocomplete'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import { useInfiniteQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'

import queryKeys from '@/config/queryClient/queryKeys'
import getGeocodingRequest, {
    GetGeocodingParams,
} from '@/requests/common/getGeocodingRequest'
import {
    GeocodingItemResponse,
    GeocodingRegionalStructureItemName,
} from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'
import { generateId } from '@/utils/generate'

interface ProcessedGeocodingItem {
    country: string
    city: string
    street: string
    region: string
    postcode: string
}

interface GeocodingAsyncAutocompleteProps
    extends Pick<AsyncAutocompleteProps<unknown>, 'label'> {
    onChange: (
        item: ProcessedGeocodingItem,
        original: GeocodingItemResponse,
        searchedValue: string,
    ) => void
    onClose: (searchedValue: string) => void
    searchedValue: string
}

const GeocodingAsyncAutocomplete = ({
    onChange,
    searchedValue,
    onClose,
    ...props
}: GeocodingAsyncAutocompleteProps) => {
    const [search, setSearch, debouncedSearch] =
        useDebouncedValue<string>(searchedValue)

    const {
        data: geocoding,
        isLoading: isGeocodingLoading,
        isFetchingNextPage: isGeocodingFetchingNextPage,
        fetchNextPage: fetchGeocodingNextPage,
        hasNextPage: hasGeocodingNextPage,
    } = useInfiniteQuery({
        queryKey: [...queryKeys.common.geocoding, debouncedSearch],
        queryFn: () => {
            const params = createRequestParams<GetGeocodingParams>({
                query: debouncedSearch || '',
                limit: 8,
            })

            return getGeocodingRequest(params)
        },
        initialPageParam: 0,
        getNextPageParam: () => {
            return undefined
        },
    })

    const processGeocodingItem = (geocodingItem: GeocodingItemResponse) => {
        const data: ProcessedGeocodingItem = {
            country: '',
            city: '',
            street: '',
            region: '',
            postcode: geocodingItem.zip,
        }

        const cityParts: string[] = []
        const streetParts: string[] = [geocodingItem.name]
        const regionParts: string[] = []

        _.forEach(geocodingItem.regionalStructure, (item) => {
            switch (item.type) {
                case GeocodingRegionalStructureItemName.COUNTRY: {
                    data.country = item.name
                    break
                }
                case GeocodingRegionalStructureItemName.STREET: {
                    if (
                        !streetParts.includes(item.name) &&
                        !cityParts.includes(item.name)
                    ) {
                        streetParts.push(item.name)
                    }

                    break
                }
                case GeocodingRegionalStructureItemName.REGION: {
                    if (!regionParts.includes(item.name)) {
                        regionParts.push(item.name)
                    }

                    break
                }
                case GeocodingRegionalStructureItemName.MUNICIPALITY:
                case GeocodingRegionalStructureItemName.MUNICIPALITY_PART: {
                    if (
                        !cityParts.includes(item.name) &&
                        !streetParts.includes(item.name)
                    ) {
                        cityParts.push(item.name)
                    }
                    break
                }
            }
        })

        data.city = cityParts.join(', ')
        data.region = regionParts.join(', ')
        data.street = streetParts.join(', ')

        const label = [data.street, data.city, data.region]
            .filter(Boolean)
            .join(', ')

        return { data, label, original: geocodingItem }
    }

    const geocodingOptions = React.useMemo(() => {
        if (!geocoding?.pages) return []

        const options = geocoding.pages.flatMap((item) =>
            item.items.map((item) => {
                return {
                    id: generateId(),
                    ...processGeocodingItem(item),
                }
            }),
        )

        return options
    }, [geocoding])

    return (
        <AsyncAutocomplete
            {...props}
            size='small'
            multiple={false}
            onChange={(value) => {
                const geocodingOption = _.find(
                    geocodingOptions,
                    (option) => option.id === value,
                )

                if (geocodingOption) {
                    onChange(
                        geocodingOption.data,
                        geocodingOption.original,
                        debouncedSearch,
                    )
                }

                setSearch(debouncedSearch)
            }}
            search={search}
            onSearch={setSearch}
            options={geocodingOptions}
            value={null}
            onClose={(__, reason) => {
                if (reason === 'blur') {
                    onClose(debouncedSearch)
                }
            }}
            hasNextPage={hasGeocodingNextPage}
            getNextPage={() => fetchGeocodingNextPage()}
            loadingNextPage={isGeocodingFetchingNextPage}
            loading={isGeocodingLoading}
        />
    )
}

export default GeocodingAsyncAutocomplete
