import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import { useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import React from 'react'
import { Outlet } from 'react-router'

import { useCurrentUser } from '@/config/queryClient/queries'

import NavBar from './NavBar'
import SideBar from './SideBar'

interface LayoutProps {
    actions?: React.ReactNode
    logoUrl: string
    breadcrumbsMap: Record<string, string>
    sidebarItems: {
        href: string
        label: string
        icon: React.ReactNode
        testId?: string
    }[]
}

const Layout = ({
    actions,
    logoUrl,
    breadcrumbsMap,
    sidebarItems,
}: LayoutProps) => {
    const { spacing } = useTheme()

    const [sidebarControl, setSidebarControl] = React.useState({
        open: true,
        dirty: false,
    })

    const toggleSidebar = () => {
        setSidebarControl((prev) => ({
            open: !prev.open,
            dirty: true,
        }))
    }

    const { data: currentUser } = useCurrentUser()

    return (
        <Stack height={'100vh'}>
            <NavBar
                currentUser={currentUser?.data}
                breadcrumbsMap={breadcrumbsMap}
                logoUrl={logoUrl}
                sidebarControl={sidebarControl}
                toggleSidebar={toggleSidebar}
                actions={actions}
                sidebarItems={sidebarItems}
            />

            <Stack
                direction='row'
                flexGrow={1}
                height={`calc(100% - ${spacing(8)})`}
            >
                <SideBar
                    sidebarControl={sidebarControl}
                    setSidebarControl={setSidebarControl}
                    sidebarItems={sidebarItems}
                />

                <Box
                    p={1}
                    display='flex'
                    width='100%'
                    sx={{
                        overflowX: 'hidden',
                        overflowY: 'auto',

                        '& > div, & > form': {
                            flex: 1,
                            height: '100%',
                            width: '100%',
                        },
                    }}
                >
                    <React.Suspense fallback={<LoadingSpinner />}>
                        <Outlet />
                    </React.Suspense>
                </Box>
            </Stack>
        </Stack>
    )
}

export default Layout
