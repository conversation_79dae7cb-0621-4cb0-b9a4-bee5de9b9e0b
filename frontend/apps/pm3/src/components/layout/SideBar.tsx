import PrimarySidebar from '@goodsailors/ui/components/layout/PrimarySidebar'
import PrimarySidebarItem from '@goodsailors/ui/components/layout/PrimarySidebarItem'
import { useTheme } from '@mui/material'
import Link from '@mui/material/Link'
import React from 'react'

import useWindowSize from '@/hooks/useWindowSize'

interface SideBarProps {
    sidebarControl: {
        open: boolean
        dirty: boolean
    }
    setSidebarControl: React.Dispatch<
        React.SetStateAction<{
            open: boolean
            dirty: boolean
        }>
    >
    sidebarItems: {
        href: string
        label: string
        icon: React.ReactNode
        testId?: string
    }[]
}

const SideBar = ({
    sidebarControl,
    setSidebarControl,
    sidebarItems,
}: SideBarProps) => {
    const size = useWindowSize()
    const { palette, breakpoints } = useTheme()
    const mdBreakpoint = breakpoints.values.md

    React.useEffect(() => {
        const { dirty } = sidebarControl
        const open = size.width >= mdBreakpoint
        if (dirty || open === sidebarControl.open) return

        setSidebarControl({
            open,
            dirty,
        })
    }, [sidebarControl, size.width, mdBreakpoint, setSidebarControl])

    return (
        <PrimarySidebar
            open={sidebarControl.open}
            sx={{
                ...(size.width < mdBreakpoint && {
                    position: 'fixed',
                    zIndex: 1300,
                    height: '100%',
                }),
                zIndex: 1200,
                '& .MuiPaper-root': {
                    background: palette.blueGrey[50],
                },
            }}
        >
            {sidebarItems.map((sidebarItem, i) => (
                <Link
                    key={i}
                    href={sidebarItem.href}
                    data-testid={sidebarItem.testId}
                >
                    <PrimarySidebarItem
                        isActive={location.pathname === sidebarItem.href}
                        icon={sidebarItem.icon}
                        label={sidebarItem.label}
                    />
                </Link>
            ))}
        </PrimarySidebar>
    )
}

export default SideBar
