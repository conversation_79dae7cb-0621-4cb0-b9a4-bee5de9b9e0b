import PrimarySidebar from '@goodsailors/ui/components/layout/PrimarySidebar'
import AppsIcon from '@mui/icons-material/Apps'
import CloseIcon from '@mui/icons-material/Close'
import DehazeIcon from '@mui/icons-material/Dehaze'
import ExpandLess from '@mui/icons-material/ExpandLess'
import ExpandMore from '@mui/icons-material/ExpandMore'
import Logout from '@mui/icons-material/Logout'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'
import { useTheme } from '@mui/material'
import Collapse from '@mui/material/Collapse'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import List from '@mui/material/List'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useMutation } from '@tanstack/react-query'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { CurrentTime } from '@/components/other/CurrentTime'
import routerPaths from '@/config/routerPaths'
import authLogoutRequest from '@/requests/auth/authLogoutRequest'
import { UserDetailResponse } from '@/requests/responses'
import { formatUserName } from '@/utils/format'

import NavBarApps from './NavbarApps'
import SwitchLanguage from './SwitchLanguage'

interface MobileSidePanelProps {
    currentUser: UserDetailResponse
}

const MobileSidePanel = ({ currentUser }: MobileSidePanelProps) => {
    const { palette } = useTheme()
    const [sidebarOpen, setSidebarOpen] = React.useState(false)
    const [collapseOpen, setCollapseOpen] = React.useState(true)

    const { t } = useTranslation()

    const { mutate } = useMutation({
        mutationFn: () => authLogoutRequest(),
        onSuccess: () => {
            window.location.href = routerPaths.auth.login
        },
    })

    const onLogout = () => {
        mutate()
    }

    return (
        <>
            <IconButton onClick={() => setSidebarOpen(true)}>
                <DehazeIcon />
            </IconButton>

            {sidebarOpen && (
                <PrimarySidebar
                    open={sidebarOpen}
                    sx={{
                        top: 0,
                        right: 0,
                        position: 'fixed',
                        zIndex: 1300,
                        background: palette.common.white,
                        boxShadow: '0 1px 4px rgb(0 0 0 / 0.06)',

                        width: '300px',
                        height: '100vh',
                        maxHeight: '100vh',

                        '& .MuiPaper-root': {
                            width: '100%',
                        },
                        '& .MuiList-root': {
                            maxHeight: '100%',
                        },
                    }}
                >
                    <Stack
                        sx={{
                            height: '100%',
                        }}
                    >
                        <Stack
                            direction='row'
                            gap={1}
                            ml={1}
                            mb={1}
                            justifyContent='space-between'
                            alignItems='center'
                        >
                            <SwitchLanguage />
                            <CurrentTime />

                            <IconButton onClick={() => setSidebarOpen(false)}>
                                <CloseIcon />
                            </IconButton>
                        </Stack>

                        <Divider />

                        <Stack
                            direction='column'
                            alignItems='flex-start'
                            sx={{
                                pl: 3,
                                py: 2,
                            }}
                        >
                            <Typography
                                fontWeight={600}
                                fontSize={14}
                                color='text.primary'
                            >
                                {formatUserName(currentUser)}
                            </Typography>

                            <Typography variant='caption'>
                                {currentUser.identifier}
                            </Typography>
                        </Stack>

                        <Divider />

                        <List disablePadding sx={{ px: 2, py: 1 }}>
                            <ListItemButton sx={{ mb: 1 }}>
                                <ListItemIcon>
                                    <SettingsOutlinedIcon />
                                </ListItemIcon>

                                <ListItemText primary={t('labels.settings')} />
                            </ListItemButton>

                            <ListItemButton
                                onClick={() => setCollapseOpen(!collapseOpen)}
                            >
                                <ListItemIcon>
                                    <AppsIcon />
                                </ListItemIcon>

                                <ListItemText
                                    primary={t('labels.applications')}
                                />

                                {collapseOpen ? <ExpandLess /> : <ExpandMore />}
                            </ListItemButton>
                        </List>

                        <Collapse
                            in={collapseOpen}
                            timeout='auto'
                            unmountOnExit
                            sx={{
                                overflowY: 'auto',
                                px: 2,
                                pb: 1,
                            }}
                        >
                            <NavBarApps
                                currentUser={currentUser}
                                type='mobile'
                            />
                        </Collapse>

                        <Divider />

                        <MenuItem
                            onClick={onLogout}
                            sx={{
                                textAlign: 'left',
                                pt: 2,
                                pl: 3,
                            }}
                        >
                            <ListItemIcon>
                                <Logout />
                            </ListItemIcon>

                            <ListItemText primary={t('actions.logout')} />
                        </MenuItem>
                    </Stack>
                </PrimarySidebar>
            )}
        </>
    )
}

export default MobileSidePanel
