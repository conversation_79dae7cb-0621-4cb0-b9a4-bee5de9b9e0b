import Chip, { ChipProps } from '@mui/material/Chip'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { EventCategorySeverityName } from '@/requests/responses'

interface EventCategoryChipProps extends Omit<ChipProps, 'label' | 'color'> {
    name: EventCategorySeverityName
}

const EventCategoryChip = ({ name, ...props }: EventCategoryChipProps) => {
    const { t } = useTranslation()

    const color = React.useMemo(() => {
        switch (name) {
            case EventCategorySeverityName.LOW: {
                return 'success'
            }
            case EventCategorySeverityName.CRITICAL: {
                return 'error'
            }
            case EventCategorySeverityName.MEDIUM: {
                return 'warning'
            }
            case EventCategorySeverityName.NONE: {
                return 'primary'
            }
            default: {
                return 'default'
            }
        }
    }, [name])

    return (
        <Chip
            color={color}
            label={t(`labels.event-category.${name}`)}
            {...props}
        />
    )
}

export default EventCategoryChip
