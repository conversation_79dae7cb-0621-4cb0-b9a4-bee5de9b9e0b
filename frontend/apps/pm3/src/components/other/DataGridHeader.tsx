import DataGridToolbar from '@goodsailors/ui/components/other/DataGridToolbar'
import SearchRounded from '@mui/icons-material/SearchRounded'
import Stack, { StackProps } from '@mui/material/Stack'
import TextField from '@mui/material/TextField'
import { GridApiPro } from '@mui/x-data-grid-pro'
import React from 'react'
import { useTranslation } from 'react-i18next'

interface DataGridHeaderProps extends StackProps {
    search: string
    onSearch: (value: string) => void
    gridRef: React.RefObject<GridApiPro | null>
    actions?: React.ReactNode
}

const DataGridHeader = ({
    onSearch,
    search,
    gridRef,
    actions,
    ...props
}: DataGridHeaderProps) => {
    const { t } = useTranslation()

    return (
        <Stack
            p={2}
            gap={2}
            flex='none'
            height={70}
            direction='row'
            alignItems='center'
            {...props}
        >
            <TextField
                value={search}
                size='small'
                placeholder={t('labels.search')}
                sx={{
                    maxWidth: 300,
                    mr: 'auto',

                    '.MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                    },
                }}
                slotProps={{
                    input: {
                        startAdornment: <SearchRounded />,
                    },
                }}
                onChange={(e) => onSearch(e.target.value)}
            />

            <DataGridToolbar gridRef={gridRef} />
            {actions}
        </Stack>
    )
}

export default DataGridHeader
