import Chip from '@mui/material/Chip'
import { useTranslation } from 'react-i18next'

interface StatusChipProps {
    isActive: boolean
}

const StatusChip = ({ isActive }: StatusChipProps) => {
    const { t } = useTranslation('common')

    if (isActive) {
        return <Chip size='small' color='success' label={t('labels.active')} />
    }

    return <Chip size='small' label={t('labels.inactive')} />
}

export default StatusChip
