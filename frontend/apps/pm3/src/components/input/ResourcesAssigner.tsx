import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import AddRounded from '@mui/icons-material/AddRounded'
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import RemoveRounded from '@mui/icons-material/RemoveRounded'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import {
    DataGridPro,
    GridColDef,
    GridFilterModel,
    GridPaginationModel,
    GridRowSelectionModel,
    GridSortModel,
    useGridApiRef,
} from '@mui/x-data-grid-pro'
import {
    keepPreviousData,
    useQuery,
    useQueryClient,
} from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { gridFilterMap } from '@/config/gridFilter'
import { AllParams, FilterTypeName } from '@/requests/params'
import { ApiManyResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

import DataGridHeader from '../other/DataGridHeader'
import AsyncAutocomplete from './AsyncAutocomplete'

type BaseItem = { id: number }

interface ResourcesAssignerProps<T extends BaseItem, P extends AllParams> {
    disabled?: boolean
    queryKey: (string | number)[]
    onChange: (resourcesIds: number[]) => void
    request: (
        params: string,
        paramsObject: P,
        side: 'assigned' | 'unassigned' | 'autocomplete',
    ) => Promise<ApiManyResponse<T & { label: string }>>
    assignedIds: number[]
    columns: GridColDef<T>[]
    assignedActions?: React.ReactNode
    unassignedActions?: React.ReactNode
    layout?: 'row' | 'column'
}

export const ResourcesAssigner = <T extends BaseItem, P extends AllParams>({
    queryKey,
    request,
    assignedIds,
    columns,
    onChange,
    disabled,
    assignedActions,
    layout = 'row',
    unassignedActions,
}: ResourcesAssignerProps<T, P>) => {
    const { t } = useTranslation()
    const queryClient = useQueryClient()

    const [
        unassignedPagination,
        unassignedPaginationModel,
        setUnassignedPaginationModel,
    ] = useGridPagination()

    const [
        assignedPagination,
        assignedPaginationModel,
        setAssignedPaginationModel,
    ] = useGridPagination()

    const [assignedFilter, assignedFilterModel, setAssignedFilterModel] =
        useGridFilter(gridFilterMap)

    const [unassignedFilter, unassignedFilterModel, setUnassignedFilterModel] =
        useGridFilter(gridFilterMap)

    const [assignedSort, assignedSortModel, setAssignedSortModel] =
        useGridSort()

    const [unassignedSort, unassignedSortModel, setUnassignedSortModel] =
        useGridSort()

    const [assignedSearch, setAssignedSearch, debouncedAssignedSearch] =
        useDebouncedValue<string>('')

    const [unassignedSearch, setUnassignedSearch, debouncedUnassignedSearch] =
        useDebouncedValue<string>('')

    const [assignedParamsObject, assignedParams] = React.useMemo(() => {
        const value: AllParams = {
            pagination: assignedPagination,
            sort: assignedSort,
            search: debouncedAssignedSearch || undefined,
            filter: [
                {
                    field: 'id',
                    type: FilterTypeName.IN,
                    value: _.join(assignedIds, ','),
                },
                ...assignedFilter,
            ],
        }

        return [value, createRequestParams(value)]
    }, [
        assignedFilter,
        assignedIds,
        assignedPagination,
        assignedSort,
        debouncedAssignedSearch,
    ])

    const [unassignedParamsObject, unassignedParams] = React.useMemo(() => {
        const value: AllParams = {
            pagination: unassignedPagination,
            sort: unassignedSort,
            search: debouncedUnassignedSearch || undefined,
            filter: [
                {
                    field: 'id',
                    type: FilterTypeName.NOT_IN,
                    value: _.join(assignedIds, ','),
                },
                ...unassignedFilter,
            ],
        }

        return [value, createRequestParams(value)]
    }, [
        assignedIds,
        debouncedUnassignedSearch,
        unassignedFilter,
        unassignedPagination,
        unassignedSort,
    ])

    const { data: assignedResources, isFetching: isAssignedResourcesFetching } =
        useQuery({
            queryFn: () =>
                request(assignedParams, assignedParamsObject as P, 'assigned'),
            queryKey: [...queryKey, assignedParams],
            placeholderData: keepPreviousData,
        })

    const {
        data: unassignedResources,
        isFetching: isUnassignedResourcesFetching,
    } = useQuery({
        queryFn: () =>
            request(
                unassignedParams,
                unassignedParamsObject as P,
                'unassigned',
            ),
        queryKey: [...queryKey, unassignedParams],
        placeholderData: keepPreviousData,
        enabled: layout === 'row',
    })

    const onAddResources = async (resources: T[]) => {
        const previousAssignedData = queryClient.getQueryData<
            ApiManyResponse<T>
        >([...queryKey, assignedParams])

        const previousUnassignedData = queryClient.getQueryData<
            ApiManyResponse<T>
        >([...queryKey, unassignedParams])

        const ids = _.map(resources, 'id')
        const newValue = _.concat(assignedIds, ids)

        const newAssignedParams = createRequestParams({
            ...assignedParamsObject,
            filter: _.map(assignedParamsObject.filter, (item) => {
                if (item.field === 'id') {
                    return {
                        field: 'id',
                        type: FilterTypeName.IN,
                        value: _.join(newValue, ','),
                    }
                }

                return item
            }),
        })

        const newUnassignedParams = createRequestParams({
            ...unassignedParamsObject,
            filter: _.map(unassignedParamsObject.filter, (item) => {
                if (item.field === 'id') {
                    return {
                        field: 'id',
                        type: FilterTypeName.NOT_IN,
                        value: _.join(newValue, ','),
                    }
                }

                return item
            }),
        })

        onChange(newValue)

        queryClient.setQueryData<ApiManyResponse<T>>(
            [...queryKey, newAssignedParams],
            () => ({
                status: previousAssignedData?.status || 200,
                list: _.concat(resources, previousAssignedData?.list || []),
                total_count:
                    (previousAssignedData?.total_count || 0) + resources.length,
            }),
        )

        queryClient.setQueryData<ApiManyResponse<T>>(
            [...queryKey, newUnassignedParams],
            () => ({
                status: previousUnassignedData?.status || 200,
                list: _.filter(
                    previousUnassignedData?.list || [],
                    (prevItem) => {
                        return !ids.includes(prevItem.id)
                    },
                ),
                total_count: Math.max(
                    (previousUnassignedData?.total_count || 0) -
                        resources.length,
                    0,
                ),
            }),
        )
    }

    const onRemoveResources = (resources: T[]) => {
        const previousAssignedData = queryClient.getQueryData<
            ApiManyResponse<T>
        >([...queryKey, assignedParams])

        const previousUnassignedData = queryClient.getQueryData<
            ApiManyResponse<T>
        >([...queryKey, unassignedParams])

        const ids = _.map(resources, 'id')
        const newValue = _.filter(assignedIds, (id) => !ids.includes(id))

        const newAssignedParams = createRequestParams({
            ...assignedParamsObject,
            filter: _.map(assignedParamsObject.filter, (item) => {
                if (item.field === 'id') {
                    return {
                        field: 'id',
                        type: FilterTypeName.IN,
                        value: _.join(newValue, ','),
                    }
                }

                return item
            }),
        })

        const newUnassignedParams = createRequestParams({
            ...unassignedParamsObject,
            filter: _.map(assignedParamsObject.filter, (item) => {
                if (item.field === 'id') {
                    return {
                        field: 'id',
                        type: FilterTypeName.NOT_IN,
                        value: _.join(newValue, ','),
                    }
                }

                return item
            }),
        })

        onChange(newValue)

        queryClient.setQueryData<ApiManyResponse<T>>(
            [...queryKey, newUnassignedParams],
            () => ({
                status: previousUnassignedData?.status || 200,
                list: _.concat(resources, previousUnassignedData?.list || []),
                total_count:
                    (previousUnassignedData?.total_count || 0) +
                    resources.length,
            }),
        )

        queryClient.setQueryData<ApiManyResponse<T>>(
            [...queryKey, newAssignedParams],
            () => ({
                status: previousAssignedData?.status || 200,
                list: _.filter(previousAssignedData?.list || [], (prevItem) => {
                    return !ids.includes(prevItem.id)
                }),
                total_count: Math.max(
                    (previousAssignedData?.total_count || 0) - resources.length,
                    0,
                ),
            }),
        )
    }

    return (
        <Stack
            flex={1}
            direction={layout}
            sx={{
                ...(layout === 'row'
                    ? { overflow: 'hidden !important' }
                    : { overflow: 'auto !important', py: 1 }),
            }}
        >
            {layout === 'column' && (
                <>
                    <AsyncAutocomplete
                        multiple
                        headless
                        size='small'
                        value={assignedIds}
                        queryKey={queryKey}
                        label={t('labels.select-item')}
                        onChange={(additionalIds) => {
                            const joinedIds = [...assignedIds, ...additionalIds]
                            onChange(joinedIds as number[])
                        }}
                        request={(params, paramsObject) =>
                            request(params, paramsObject as P, 'autocomplete')
                        }
                    />

                    <Divider sx={{ my: 2 }} />
                </>
            )}

            <ResourcesAssignerSide
                disabled={disabled}
                layout={layout}
                actions={assignedActions}
                isLoading={isAssignedResourcesFetching}
                title={t('labels.assigned')}
                onRemove={onRemoveResources}
                search={assignedSearch}
                onSearch={setAssignedSearch}
                rows={assignedResources?.list || []}
                rowsCount={assignedResources?.total_count || 0}
                filterModel={assignedFilterModel}
                paginationModel={assignedPaginationModel}
                sortModel={assignedSortModel}
                setFilterModel={setAssignedFilterModel}
                setSortModel={setAssignedSortModel}
                setPaginationModel={setAssignedPaginationModel}
                operation='unassign'
                columns={columns}
                onAdd={() => {}}
            />

            {layout === 'row' && (
                <>
                    <Divider orientation='vertical' flexItem />

                    <ResourcesAssignerSide
                        disabled={disabled}
                        layout={layout}
                        actions={unassignedActions}
                        isLoading={isUnassignedResourcesFetching}
                        title={t('labels.unassigned')}
                        onAdd={onAddResources}
                        onSearch={setUnassignedSearch}
                        search={unassignedSearch}
                        rows={unassignedResources?.list || []}
                        rowsCount={unassignedResources?.total_count || 0}
                        filterModel={unassignedFilterModel}
                        paginationModel={unassignedPaginationModel}
                        sortModel={unassignedSortModel}
                        setFilterModel={setUnassignedFilterModel}
                        setSortModel={setUnassignedSortModel}
                        setPaginationModel={setUnassignedPaginationModel}
                        operation='assign'
                        columns={columns}
                        onRemove={() => {}}
                    />
                </>
            )}
        </Stack>
    )
}

interface ResourcesAssignerSideProps<T extends BaseItem> {
    rows: T[]
    title: string
    search: string
    rowsCount: number
    isLoading: boolean
    disabled?: boolean
    columns: GridColDef<T>[]
    actions?: React.ReactNode
    layout: 'row' | 'column'
    operation: 'assign' | 'unassign'
    onSearch: (search: string) => void
    onAdd: (resources: T[]) => void
    onRemove: (resources: T[]) => void
    sortModel: GridSortModel
    setSortModel: React.Dispatch<React.SetStateAction<GridSortModel>>
    filterModel?: GridFilterModel
    setFilterModel: React.Dispatch<
        React.SetStateAction<GridFilterModel | undefined>
    >
    paginationModel: GridPaginationModel
    setPaginationModel: React.Dispatch<
        React.SetStateAction<GridPaginationModel>
    >
}

const ResourcesAssignerSide = <T extends BaseItem>({
    onSearch,
    search,
    title,
    columns,
    rows,
    actions,
    operation,
    onAdd,
    onRemove,
    rowsCount,
    isLoading = false,
    paginationModel,
    setPaginationModel,
    setFilterModel,
    filterModel,
    setSortModel,
    sortModel,
    disabled,
    layout,
}: ResourcesAssignerSideProps<T>) => {
    const gridApiRef = useGridApiRef()
    const { t } = useTranslation()

    const [selectionModel, setSelectionModel] =
        React.useState<GridRowSelectionModel>({
            type: 'include',
            ids: new Set(),
        })

    const injectedColumns: GridColDef<T>[] = React.useMemo(() => {
        const copiedColumns = [...columns]

        if (!disabled) {
            copiedColumns.push({
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: ({ row }) =>
                    operation === 'assign' ? (
                        <IconButton size='small' onClick={() => onAdd([row])}>
                            <AddRounded fontSize='small' />
                        </IconButton>
                    ) : (
                        <IconButton
                            size='small'
                            onClick={() => onRemove([row])}
                        >
                            <RemoveRounded fontSize='small' />
                        </IconButton>
                    ),
            })
        }

        return copiedColumns
    }, [columns, disabled, onAdd, onRemove, operation, t])

    return (
        <Stack
            sx={{
                ...(layout === 'row'
                    ? { flex: 1, maxWidth: '50%' }
                    : {
                          flex: 'none',
                          width: '100%',
                          minHeight: 300,
                          maxHeight: 500,
                      }),
            }}
        >
            <Stack
                pb={1}
                gap={2}
                px={1.5}
                direction='row'
                alignItems='center'
                minHeight={50}
            >
                <Stack direction='row' mr='auto' alignItems='center' gap={1}>
                    {operation === 'assign' ? (
                        <CancelOutlinedIcon fontSize='small' />
                    ) : (
                        <CheckCircleOutlinedIcon
                            color='success'
                            fontSize='small'
                        />
                    )}

                    <Typography>
                        {title} {isLoading ? '' : `(${rowsCount || 0})`}
                    </Typography>
                </Stack>

                {Number(selectionModel.ids.size) === 0 &&
                    selectionModel.type !== 'exclude' &&
                    actions}

                {(Number(selectionModel.ids.size) > 0 ||
                    selectionModel.type === 'exclude') &&
                    operation === 'unassign' && (
                        <Button
                            size='small'
                            color='error'
                            sx={{ flex: 'none' }}
                            startIcon={<RemoveRounded />}
                            onClick={() => {
                                const resources = _.filter(rows, (row) => {
                                    return (
                                        Boolean(
                                            selectionModel.ids.has(row.id),
                                        ) || selectionModel.type === 'exclude'
                                    )
                                })

                                onRemove(resources)
                                setSelectionModel({
                                    type: 'include',
                                    ids: new Set(),
                                })
                            }}
                        >
                            {t('actions.remove')}
                        </Button>
                    )}

                {(Number(selectionModel?.ids.size) > 0 ||
                    selectionModel?.type === 'exclude') &&
                    operation === 'assign' && (
                        <Button
                            size='small'
                            startIcon={<AddRounded />}
                            onClick={() => {
                                const resources = _.filter(rows, (row) => {
                                    return (
                                        Boolean(
                                            selectionModel?.ids.has(row.id),
                                        ) || selectionModel?.type === 'exclude'
                                    )
                                })

                                onAdd(resources)
                                setSelectionModel({
                                    type: 'include',
                                    ids: new Set(),
                                })
                            }}
                        >
                            {t('actions.add')}
                        </Button>
                    )}
            </Stack>

            <DataGridHeader
                p={0}
                height='auto'
                search={search}
                onSearch={onSearch}
                gridRef={gridApiRef}
            />

            <DataGridPro
                pagination
                apiRef={gridApiRef}
                disableColumnPinning
                sx={{ flex: 1 }}
                rows={rows || []}
                loading={isLoading}
                sortModel={sortModel}
                filterMode='server'
                sortingMode='server'
                filterModel={filterModel}
                columns={injectedColumns}
                checkboxSelection={!disabled}
                onSortModelChange={setSortModel}
                paginationModel={paginationModel}
                rowSelectionModel={selectionModel}
                onFilterModelChange={setFilterModel}
                disableRowSelectionOnClick={disabled}
                onPaginationModelChange={setPaginationModel}
                onRowSelectionModelChange={setSelectionModel}
                paginationMode={'server'}
                rowCount={rowsCount}
                initialState={{
                    density: 'compact',
                    pinnedColumns: {
                        right: ['actions'],
                    },
                }}
            />
        </Stack>
    )
}
