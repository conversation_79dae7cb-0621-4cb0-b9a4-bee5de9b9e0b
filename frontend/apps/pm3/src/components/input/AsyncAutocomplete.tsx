import UIAsyncAutocomplete, {
    AsyncAutocompleteOption,
    AsyncAutocompletePropsBase as UIAsyncAutocompletePropsBase,
    MultipleAsyncAutocompleteProps,
    SingleAsyncAutocompleteProps,
} from '@goodsailors/ui/components/inputs/AsyncAutocomplete'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'

import { DEFAULT_OPTIONS_LIMIT } from '@/config/common'
import { AllParams, FilterTypeName } from '@/requests/params'
import { ApiManyResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

export interface AsyncAutocompleteApi {
    getSearch: () => string
}

export const useAsyncAutocompleteApiRef = () =>
    React.useRef(null) as React.RefObject<AsyncAutocompleteApi | null>

interface AsyncAutocompletePropsBase<
    O extends AsyncAutocompleteOption,
    P extends AllParams,
> extends Omit<
        UIAsyncAutocompletePropsBase<O>,
        | 'search'
        | 'loading'
        | 'options'
        | 'onSearch'
        | 'hasNextPage'
        | 'getNextPage'
        | 'loadingNextPage'
    > {
    headless?: boolean
    apiRef?: React.Ref<AsyncAutocompleteApi>
    queryKey: (string | number)[]
    onChange: (resourcesIds: number[]) => void
    request: (params: string, paramsObject: P) => Promise<ApiManyResponse<O>>
}

export type AsyncAutocompleteProps<
    O extends AsyncAutocompleteOption,
    P extends AllParams,
> = AsyncAutocompletePropsBase<O, P> &
    (SingleAsyncAutocompleteProps | MultipleAsyncAutocompleteProps)

const AsyncAutocomplete = <
    O extends AsyncAutocompleteOption,
    P extends AllParams,
>({
    apiRef,
    onChange,
    queryKey,
    request,
    value,
    multiple,
    label,
    error,
    headless,
    helperText,
    ...props
}: AsyncAutocompleteProps<O, P>) => {
    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [deafaultParamsObject, defaultParams] = React.useMemo(() => {
        const paramsObject: AllParams = {
            pagination: {
                limit: DEFAULT_OPTIONS_LIMIT,
                offset: 0,
            },
            filter: [
                {
                    field: 'id',
                    type: FilterTypeName.IN,
                    value: value ? _.flatten([value]).filter(Boolean) : [],
                },
            ],
        }

        return [paramsObject, createRequestParams(paramsObject)]
    }, [value])

    const { data: defaultResources, isPending: isPendingDefaultResources } =
        useQuery({
            queryFn: () => request(defaultParams, deafaultParamsObject as P),
            queryKey: [...queryKey, defaultParams],
        })

    const {
        data: resources,
        isLoading: isResourcesLoading,
        isFetchingNextPage: isResourcesFetchingNextPage,
        fetchNextPage: fetchResourcesNextPage,
        hasNextPage: hasResourcesNextPage,
    } = useInfiniteQuery({
        queryKey: [...queryKey, value, debouncedSearch],
        queryFn: ({ pageParam = 0 }) => {
            const paramsObject: AllParams = {
                pagination: {
                    limit: 10,
                    offset: pageParam,
                },
                search: debouncedSearch || undefined,
            }

            const exceptIds = _.flatten([value]).filter(Boolean)

            if (exceptIds.length > 0) {
                paramsObject.filter = [
                    {
                        field: 'id',
                        type: FilterTypeName.NOT_IN,
                        value: exceptIds as number[],
                    },
                ]
            }

            return request(createRequestParams(paramsObject), paramsObject as P)
        },
        initialPageParam: 0,
        getNextPageParam: (lastPage, allPages) => {
            const totalLoaded = allPages.reduce(
                (count, page) => count + page.list.length,
                0,
            )
            return totalLoaded < lastPage.total_count ? totalLoaded : undefined
        },
    })

    const options = React.useMemo(() => {
        const defaultOptions = _.map(defaultResources?.list, (resource) => ({
            id: resource.id,
            label: resource.label,
        }))

        if (!resources?.pages && headless) return []
        if (!resources?.pages) return defaultOptions

        const options = resources.pages.flatMap((page) =>
            page.list.map((resource) => ({
                id: resource.id,
                label: resource.label,
            })),
        )

        if (headless) return options

        return _.uniqBy([...defaultOptions, ...options], 'id')
    }, [defaultResources?.list, resources?.pages, headless])

    React.useImperativeHandle(
        apiRef,
        () => ({
            getSearch: () => debouncedSearch,
        }),
        [debouncedSearch],
    )

    const baseProps = React.useMemo(() => {
        return {
            search,
            label,
            options,
            hasNextPage: hasResourcesNextPage,
            getNextPage: fetchResourcesNextPage,
            loadingNextPage: isResourcesFetchingNextPage,
            loading: isResourcesLoading || isPendingDefaultResources,
            helperText: helperText,
            error: error,
            onSearch: setSearch,
        }
    }, [
        error,
        fetchResourcesNextPage,
        hasResourcesNextPage,
        helperText,
        isPendingDefaultResources,
        isResourcesFetchingNextPage,
        isResourcesLoading,
        label,
        options,
        search,
        setSearch,
    ])

    if (multiple) {
        return (
            <UIAsyncAutocomplete
                {...props}
                {...baseProps}
                multiple
                onChange={onChange}
                options={options as O[]}
                value={headless ? [] : value}
            />
        )
    }

    return (
        <UIAsyncAutocomplete
            {...props}
            {...baseProps}
            onChange={onChange}
            options={options as O[]}
            value={headless ? null : value}
        />
    )
}

export default AsyncAutocomplete
