import DialogHeader from '@goodsailors/ui/components/other/DialogHeader'
import SmsOutlined from '@mui/icons-material/SmsOutlined'
import { alpha, useTheme } from '@mui/material'
import Alert from '@mui/material/Alert'
import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Form, Formik, useField } from 'formik'
import * as _ from 'lodash'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import * as yup from 'yup'

import sendEmergencyMessageRequest, {
    SendEmergencyMessageBody,
    SendEmergencyMessageBodyKeys,
} from '@/requests/emergency/sendEmergencyMessage'
import getTenantGroupsByFacilityRequest from '@/requests/tenant/getTenantsByFacilityRequest'

import FormTextField from '../form/FormTextField'
import BackButton from '../other/BackButton'
import SubmitButton from '../other/SubmitButton'
import FormFacilitiesAsyncAutocomplete from '../resource/facility/FormFacilitiesAsyncAutocomplete'

interface PopsEmergencyGroupDialogProps {
    onClose: () => void
}

const PopsEmergencyGroupDialog = ({
    onClose,
}: PopsEmergencyGroupDialogProps) => {
    const { t } = useTranslation('common')
    const { palette, spacing } = useTheme()

    const { mutate, isPending } = useMutation({
        mutationFn: (sendEmergencyMessageBody: SendEmergencyMessageBody) =>
            sendEmergencyMessageRequest(sendEmergencyMessageBody),
        onSuccess: () => {
            toast.success(t('texts.success-action'))
            onClose()
        },
    })

    const initialValues: SendEmergencyMessageBody = {
        [SendEmergencyMessageBodyKeys.TENANT_GROUP_IDS]: [],
        [SendEmergencyMessageBodyKeys.FACILITY_ID]: '',
        [SendEmergencyMessageBodyKeys.MESSAGE]: '',
        [SendEmergencyMessageBodyKeys.TENANT_IDS]: [],
    }

    const validationSchema = yup.object().shape({
        [SendEmergencyMessageBodyKeys.FACILITY_ID]: yup
            .string()
            .required('errors.required-field'),
        [SendEmergencyMessageBodyKeys.TENANT_GROUP_IDS]: yup
            .array()
            .min(1, 'errors.required-field'),
        [SendEmergencyMessageBodyKeys.MESSAGE]: yup
            .string()
            .required('errors.required-field'),
    })

    const onSubmit = (values: SendEmergencyMessageBody) => {
        mutate(values)
    }

    return (
        <Dialog
            open={true}
            onClose={onClose}
            sx={{
                '.MuiPaper-root': {
                    maxWidth: 480,
                    width: '100%',
                },
            }}
        >
            <DialogHeader
                title={t('labels.group-emergency-button')}
                onClose={onClose}
            />

            <Formik
                initialValues={initialValues}
                onSubmit={onSubmit}
                validationSchema={validationSchema}
            >
                <Form>
                    <DialogContent>
                        <Box
                            mb={2}
                            mx='auto'
                            width={80}
                            height={80}
                            display='flex'
                            fontSize={40}
                            alignItems='center'
                            justifyContent='center'
                            borderRadius={spacing(2)}
                            bgcolor={alpha(palette.success.main, 0.15)}
                        >
                            <SmsOutlined fontSize='inherit' color='success' />
                        </Box>

                        <Typography
                            mb={1}
                            variant='h5'
                            align='center'
                            fontWeight={700}
                        >
                            {t('labels.emergency-situation')}?
                        </Typography>

                        <Typography
                            mb={2}
                            variant='h6'
                            align='center'
                            fontWeight={600}
                        >
                            {t('texts.sending-group-emergency-message')}
                        </Typography>

                        <Typography
                            align='center'
                            fontWeight={500}
                            color='text.secondary'
                        >
                            {t('texts.fill-and-submit')}
                        </Typography>

                        <Stack gap={2} mt={4}>
                            <FormFacilitiesAsyncAutocomplete
                                name={SendEmergencyMessageBodyKeys.FACILITY_ID}
                            />

                            <TenantsSelect />

                            <FormTextField
                                multiline
                                minRows={4}
                                maxRows={4}
                                name={SendEmergencyMessageBodyKeys.MESSAGE}
                                label={t('labels.event-description')}
                            />
                        </Stack>

                        <Stack mt={4}>
                            <Alert severity='warning'>
                                {t('texts.warning-messages-emergency')}
                            </Alert>
                        </Stack>
                    </DialogContent>

                    <DialogActions sx={{ justifyContent: 'space-between' }}>
                        <BackButton
                            size='medium'
                            color='inherit'
                            onClick={onClose}
                        />

                        <SubmitButton
                            size='medium'
                            variant='contained'
                            loading={isPending}
                        >
                            {t('actions.send-sms')}
                        </SubmitButton>
                    </DialogActions>
                </Form>
            </Formik>
        </Dialog>
    )
}

// TODO: Implement me
const TenantsSelect = () => {
    const { t } = useTranslation()

    const [{ value }] = useField(SendEmergencyMessageBodyKeys.FACILITY_ID)

    const { data: tenants } = useQuery({
        queryKey: ['...'], // TOOD
        enabled: Boolean(value),
        queryFn: () => getTenantGroupsByFacilityRequest(value, ''),
    })

    return (
        <FormTextField
            select
            size='small'
            slotProps={{
                select: {
                    multiple: true,
                },
            }}
            label={t('actions.select-contact-groups')}
            name={SendEmergencyMessageBodyKeys.TENANT_GROUP_IDS}
        >
            {_.map(tenants?.list, (tenant) => (
                <MenuItem key={tenant.id} value={tenant.id}>
                    {tenant.name}
                </MenuItem>
            ))}
        </FormTextField>
    )
}

export default PopsEmergencyGroupDialog
