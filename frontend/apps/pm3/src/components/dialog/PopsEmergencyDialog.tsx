import DialogHeader from '@goodsailors/ui/components/other/DialogHeader'
import SmsFailedOutlined from '@mui/icons-material/SmsFailedOutlined'
import { alpha, useTheme } from '@mui/material'
import Alert from '@mui/material/Alert'
import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Form, Formik } from 'formik'
import * as _ from 'lodash'
import { useMemo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import * as yup from 'yup'

import queryKeys from '@/config/queryClient/queryKeys'
import sendEmergencyMessageRequest, {
    SendEmergencyMessageBody,
    SendEmergencyMessageBodyKeys,
} from '@/requests/emergency/sendEmergencyMessage'
import getFacilitiesByPasswordRequest from '@/requests/facility/getFacilitiesByPasswordRequest'

import FormTextField from '../form/FormTextField'
import BackButton from '../other/BackButton'
import SubmitButton from '../other/SubmitButton'

interface PopsEmergencyDialogProps {
    onClose: () => void
}

// TODO: IMPLEMENT ME
const PopsEmergencyDialog = ({ onClose }: PopsEmergencyDialogProps) => {
    const { t } = useTranslation('common')
    const { palette, spacing } = useTheme()

    const { mutate, isPending } = useMutation({
        mutationFn: (sendEmergencyMessageBody: SendEmergencyMessageBody) =>
            sendEmergencyMessageRequest(sendEmergencyMessageBody),
        onSuccess: () => {
            toast.success(t('texts.success-action'))
            onClose()
        },
    })

    const { data: facilities } = useQuery({
        queryKey: queryKeys.facilities.facilitiesByPermission,
        queryFn: () =>
            getFacilitiesByPasswordRequest('PERM_PANIC_BUTTON_TENANT'),
    })

    const facilitiesOptions = useMemo(
        () =>
            _.map(facilities?.data, (item) => ({
                value: item.id,
                label: item.name,
            })),
        [facilities?.data],
    )

    const initialValues: SendEmergencyMessageBody = {
        [SendEmergencyMessageBodyKeys.FACILITY_ID]: '',
        [SendEmergencyMessageBodyKeys.MESSAGE]: '',
        [SendEmergencyMessageBodyKeys.TENANT_GROUP_IDS]: [],
        [SendEmergencyMessageBodyKeys.TENANT_IDS]: [],
    }

    const validationSchema = yup.object().shape({
        [SendEmergencyMessageBodyKeys.FACILITY_ID]: yup
            .string()
            .required('errors.required-field'),
        [SendEmergencyMessageBodyKeys.MESSAGE]: yup
            .string()
            .required('errors.required-field'),
    })

    const onSubmit = (values: SendEmergencyMessageBody) => {
        mutate(values)
    }

    return (
        <Dialog
            open={true}
            onClose={onClose}
            sx={{
                '.MuiPaper-root': {
                    maxWidth: 480,
                    width: '100%',
                },
            }}
        >
            <DialogHeader
                title={t('labels.emergency-button')}
                onClose={onClose}
            />

            <Formik
                initialValues={initialValues}
                onSubmit={onSubmit}
                validationSchema={validationSchema}
            >
                <Form>
                    <DialogContent>
                        <Box
                            mb={2}
                            mx='auto'
                            width={80}
                            height={80}
                            display='flex'
                            fontSize={40}
                            alignItems='center'
                            justifyContent='center'
                            borderRadius={spacing(2)}
                            bgcolor={alpha(palette.error.main, 0.15)}
                        >
                            <SmsFailedOutlined
                                fontSize='inherit'
                                color='error'
                            />
                        </Box>

                        <Typography
                            mb={1}
                            variant='h5'
                            align='center'
                            fontWeight={700}
                        >
                            {t('labels.emergency-situation')}?
                        </Typography>

                        <Typography
                            mb={2}
                            variant='h6'
                            align='center'
                            fontWeight={600}
                        >
                            {t('texts.sending-emergency-message')}
                        </Typography>

                        <Typography
                            align='center'
                            fontWeight={500}
                            color='text.secondary'
                        >
                            {t('texts.fill-and-submit')}
                        </Typography>

                        <Stack gap={4} mt={4}>
                            <FormTextField
                                fullWidth
                                name={SendEmergencyMessageBodyKeys.FACILITY_ID}
                                label={t('labels.object')}
                                select
                            >
                                {facilitiesOptions.map(({ label, value }) => (
                                    <MenuItem key={value} value={value}>
                                        {label}
                                    </MenuItem>
                                ))}
                            </FormTextField>

                            <FormTextField
                                multiline
                                minRows={4}
                                maxRows={4}
                                name={SendEmergencyMessageBodyKeys.MESSAGE}
                                label={t('labels.event-description')}
                            />
                        </Stack>

                        <Stack mt={4}>
                            <Alert severity='warning'>
                                {t('texts.warning-messages-emergency')}
                            </Alert>
                        </Stack>
                    </DialogContent>

                    <DialogActions sx={{ justifyContent: 'space-between' }}>
                        <BackButton
                            size='medium'
                            color='inherit'
                            onClick={onClose}
                        />

                        <SubmitButton
                            size='medium'
                            variant='contained'
                            loading={isPending}
                        >
                            {t('actions.send-sms')}
                        </SubmitButton>
                    </DialogActions>
                </Form>
            </Formik>
        </Dialog>
    )
}

export default PopsEmergencyDialog
