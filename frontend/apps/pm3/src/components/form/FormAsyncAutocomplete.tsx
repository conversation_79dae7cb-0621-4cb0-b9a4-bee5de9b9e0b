import { AsyncAutocompleteOption } from '@goodsailors/ui/components/inputs/AsyncAutocomplete'
import { useField } from 'formik'
import { useTranslation } from 'react-i18next'

import { AllParams } from '@/requests/params'

import AsyncAutocomplete, {
    AsyncAutocompleteProps,
} from '../input/AsyncAutocomplete'

type FormAsyncAutocompleteProps<
    O extends AsyncAutocompleteOption,
    P extends AllParams,
> = Omit<AsyncAutocompleteProps<O, P>, 'onChange' | 'value'> & {
    name: string
}

type Value = number[] | number | null

const FormAsyncAutocomplete = <
    O extends AsyncAutocompleteOption,
    P extends AllParams,
>({
    name,
    ...props
}: FormAsyncAutocompleteProps<O, P>) => {
    const { t } = useTranslation()

    const [field, meta, helpers] = useField<Value>(name)

    const isError = meta.touched && Boolean(meta.error)
    const errorMessage = isError ? t(meta.error as never) : ''

    if (props.multiple) {
        return (
            <AsyncAutocomplete
                size='small'
                {...props}
                multiple={true}
                onChange={(value) => helpers.setValue(value as number[])}
                value={field.value as number[]}
                helperText={errorMessage}
                error={isError}
            />
        )
    }

    return (
        <AsyncAutocomplete
            {...props}
            size='small'
            multiple={false}
            onChange={(value) => helpers.setValue(value as number | null)}
            value={field.value as number | null}
            helperText={errorMessage}
            error={isError}
        />
    )
}

export default FormAsyncAutocomplete
