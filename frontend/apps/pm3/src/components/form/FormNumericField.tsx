import NumericField, {
    NumericFieldProps,
} from '@goodsailors/ui/components/inputs/NumericField'
import { useField } from 'formik'
import React from 'react'
import { useTranslation } from 'react-i18next'

export type FormNumericFieldProps = NumericFieldProps

const FormNumericField = ({ label, name, ...props }: FormNumericFieldProps) => {
    const { t } = useTranslation()

    const [field, meta, helpers] = useField<unknown>(name)

    const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (props.onChange) {
            props.onChange(event)
        }
        helpers.setValue(event.target.value)
    }

    const isError = meta.touched && Boolean(meta.error)
    const errorMessage = isError ? t(meta.error as never) : ''

    return (
        <NumericField
            {...props}
            fullWidth
            size='small'
            data-testid='FORM_TEXT_FIELD'
            onChange={onChange}
            onBlur={field.onBlur}
            name={field.name}
            value={Number(field.value) || null}
            helperText={errorMessage}
            error={isError}
            label={label}
        />
    )
}

export default FormNumericField
