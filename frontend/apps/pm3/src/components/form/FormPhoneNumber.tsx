import PhoneNumber, {
    PhoneNumberProps,
} from '@goodsailors/ui/components/inputs/PhoneNumber'
import { useField } from 'formik'
import { useTranslation } from 'react-i18next'

export interface FormPhoneNumberProps
    extends Omit<PhoneNumberProps, 'value' | 'onChange'> {
    /**
     * The name of the field, used to identify the field in the form.
     */
    name: string
}

/**
 * FormPhoneNumber is a component that provides a phone number input field
 * integrated with Formik. It validates the phone number and displays the prefix
 * and flag of the currently selected country. Users can input their phone number conveniently.
 */
const FormPhoneNumber = ({ name, ...props }: FormPhoneNumberProps) => {
    const { t } = useTranslation()
    const [field, meta, helpers] = useField<string>(name)

    const isError = meta.touched && Boolean(meta.error)
    const errorMessage = isError ? t(meta.error as never) : ''

    return (
        <PhoneNumber
            data-testid='FORM_PHONE_NUMBER'
            size='small'
            {...field}
            {...props}
            value={field.value}
            helperText={errorMessage}
            error={isError}
            onChange={(value) => helpers.setValue(value)}
        />
    )
}

export default FormPhoneNumber
