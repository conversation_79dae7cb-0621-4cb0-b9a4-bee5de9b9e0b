import Box from '@mui/material/Box'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Popover from '@mui/material/Popover'
import { darken } from '@mui/material/styles'
import { useField } from 'formik'
import React from 'react'
import { ChromePicker } from 'react-color'

interface FormColorPickerProps {
    /**
     * The name of the field, used to identify the field in the form.
     */
    name: string

    /**
     * The label for the color picker input.
     */
    label: string
}

/**
 * FormColorPicker is a component that provides a color picker input field
 * integrated with Formik. It allows users to select a color from a color picker.
 */
const FormColorPicker: React.FC<FormColorPickerProps> = ({ name, label }) => {
    const [field, , helpers] = useField(name)

    const [element, setElement] = React.useState<HTMLDivElement | null>(null)

    const onChange = (value: string) => {
        helpers.setValue(value)
    }

    const value = field.value || '#fff'

    return (
        <>
            <FormControl fullWidth data-testid='FORM_COLOR_PICKER'>
                <InputLabel shrink>{label}</InputLabel>

                <Box
                    sx={{
                        mt: 1,
                        minHeight: 42,
                        width: '100%',
                        height: '100%',
                        borderRadius: 3,
                        cursor: 'pointer',
                        border: '1px solid',
                        background: value,
                        borderColor: darken(value, 0.3),

                        '&:hover': {
                            borderColor: darken(value, 1),
                        },
                    }}
                    onClick={(e) => setElement(e.currentTarget)}
                />
            </FormControl>

            <Popover
                open={Boolean(element)}
                anchorEl={element}
                onClose={() => setElement(null)}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
            >
                <ChromePicker
                    color={field.value}
                    onChange={(color) => onChange(color.hex)}
                />
            </Popover>
        </>
    )
}

export default FormColorPicker
