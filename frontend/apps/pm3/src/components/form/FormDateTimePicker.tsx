import { DateTimePicker, DateTimePickerProps } from '@mui/x-date-pickers-pro'
import dayjs from 'dayjs'
import { useField } from 'formik'
import { useTranslation } from 'react-i18next'

export type FormDateTimePickerProps = DateTimePickerProps & {
    /**
     * The name of the field, used to identify the field in the form.
     */
    name: string
}

/**
 * FormDateTimePicker is a component that provides a date and time picker input field
 * integrated with Formik. It allows users to select a date and time.
 */
const FormDateTimePicker = ({ name, ...props }: FormDateTimePickerProps) => {
    const { t } = useTranslation()

    const [field, meta, helpers] = useField(name)

    const isError = meta.touched && Boolean(meta.error)
    const errorMessage = isError ? t(meta.error as never) : ''

    return (
        <DateTimePicker
            onChange={(value) => helpers.setValue(value)}
            value={field.value}
            {...props}
            name={field.name}
            referenceDate={dayjs()}
            slotProps={{
                ...props.slotProps,
                textField: {
                    size: 'small',
                    error: isError,
                    helperText: errorMessage,
                    ...props.slotProps?.textField,
                },
            }}
        />
    )
}

export default FormDateTimePicker
