import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, UserDetailResponse } from '../responses'

const updateUserRequest = async (id: number, body: UpdateUserBody) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<UserDetailResponse, 'user-profile-updated'>
    >(`/api/v1/user/${id}`, body)

    return data
}

export interface UpdateUserBody {
    [UpdateUserBodyKeys.EMAIL]: string
    [UpdateUserBodyKeys.FIRST_NAME]: string
    [UpdateUserBodyKeys.LAST_NAME]: string
    [UpdateUserBodyKeys.PHONE]: string
    [UpdateUserBodyKeys.COMPANY_NUMBER]: string
}

export enum UpdateUserBodyKeys {
    EMAIL = 'email',
    FIRST_NAME = 'firstName',
    LAST_NAME = 'lastName',
    PHONE = 'cell_phone',
    COMPANY_NUMBER = 'taxNo',
}

export default updateUserRequest
