import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, UserDetailResponse } from '../responses'

const changeUserPasswordRequest = async (
    id: number,
    body: ChangeUserPasswordBody,
) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<UserDetailResponse, 'password-updated'>
    >(`/api/v1/user/${id}/password`, body)

    return data
}

export interface ChangeUserPasswordBody {
    [ChangeUserPasswordBodyKeys.OLD_PASSWORD]: string
    [ChangeUserPasswordBodyKeys.NEW_PASSWORD]: string
}

export enum ChangeUserPasswordBodyKeys {
    OLD_PASSWORD = 'old_password',
    NEW_PASSWORD = 'new_password',
}

export default changeUserPasswordRequest
