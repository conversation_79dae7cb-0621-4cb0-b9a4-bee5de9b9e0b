import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, CurrencyResponse } from '../responses'

const updateCurrencyRequest = async (
    currencyId: number,
    body: UpdateCurrencyBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<CurrencyResponse, ''>
    >(`/api/v1/currency/${currencyId}`, body)

    return data
}

export interface UpdateCurrencyBody {
    [UpdateCurrencyBodyKeys.NAME]: string
    [UpdateCurrencyBodyKeys.ABBR]: string
}

export enum UpdateCurrencyBodyKeys {
    NAME = 'name',
    ABBR = 'abbr',
}

export default updateCurrencyRequest
