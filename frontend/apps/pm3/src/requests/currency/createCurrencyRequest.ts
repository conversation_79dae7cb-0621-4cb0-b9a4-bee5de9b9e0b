import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, CurrencyResponse } from '../responses'

const createCurrencyRequest = async (body: CreateCurrencyBody) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<CurrencyResponse, ''>
    >(`/api/v1/currency`, body)

    return data
}

export interface CreateCurrencyBody {
    [CreateCurrencyBodyKeys.NAME]: string
    [CreateCurrencyBodyKeys.ABBR]: string
}

export enum CreateCurrencyBodyKeys {
    NAME = 'name',
    ABBR = 'abbr',
}

export default createCurrencyRequest
