import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, CurrencyResponse } from '../responses'

const getCurrenciesRequest = async (params: string) => {
    const { data } = await restClient().get<ApiManyResponse<CurrencyResponse>>(
        '/api/v1/currency' + params,
    )

    return data
}

export interface GetCurrenciesParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {}

export default getCurrenciesRequest
