import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse, PermissionGroupResponse } from '../responses'

const getPermissionGroupRequest = async (permissionGroupId: number) => {
    const { data } = await restClient().get<
        ApiSingleResponse<PermissionGroupResponse>
    >(`/api/v1/permission-group/${permissionGroupId}`)

    return data
}

export default getPermissionGroupRequest
