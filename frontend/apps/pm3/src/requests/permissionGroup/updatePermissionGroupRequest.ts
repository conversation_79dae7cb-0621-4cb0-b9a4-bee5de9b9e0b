import { restClient } from '@/config/restClient/restClient'

import {
    ApiSingleResponseWithMessage,
    PermissionGroupResponse,
} from '../responses'

const updatePermissionGroupRequest = async (
    groupId: number,
    body: UpdatePermissionGroupBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<PermissionGroupResponse, ''>
    >(`/api/v1/permission-group/${groupId}`, body)

    return data
}

export interface UpdatePermissionGroupBody {
    [UpdatePermissionGroupBodyKeys.NAME]: string
    [UpdatePermissionGroupBodyKeys.USAGE]: string
    [UpdatePermissionGroupBodyKeys.USERS]: number[]
    [UpdatePermissionGroupBodyKeys.FACILITIES]: number[]
    [UpdatePermissionGroupBodyKeys.PERMISSION_TEMPLATES]: number[]
}

export enum UpdatePermissionGroupBodyKeys {
    NAME = 'name',
    USAGE = 'usage',
    USERS = 'users',
    FACILITIES = 'facilities',
    PERMISSION_TEMPLATES = 'permissionTemplates',
}

export default updatePermissionGroupRequest
