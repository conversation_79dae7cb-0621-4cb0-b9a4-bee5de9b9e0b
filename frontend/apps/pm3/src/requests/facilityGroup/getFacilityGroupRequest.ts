import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse, FacilityGroupResponse } from '../responses'

const getFacilityGroupRequest = async (facilityGroupId: number) => {
    const { data } = await restClient().get<
        ApiSingleResponse<FacilityGroupResponse>
    >(`/api/v1/facility-group/${facilityGroupId}`)

    return data
}

export default getFacilityGroupRequest
