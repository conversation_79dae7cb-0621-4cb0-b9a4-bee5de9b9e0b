import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, FacilityGroupResponse } from '../responses'

const getFacilityGroupsRequest = async (params: string) => {
    const { data } = await restClient().get<
        ApiManyResponse<FacilityGroupResponse>
    >('/api/v1/facility-group' + params)

    return data
}

export interface GetFacilityGroupsParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {}

export default getFacilityGroupsRequest
