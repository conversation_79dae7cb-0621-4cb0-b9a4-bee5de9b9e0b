import { restClient } from '@/config/restClient/restClient'

import {
    ApiSingleResponseWithMessage,
    PermissionTemplateResponse,
} from '../responses'

const createPermissionTemplateRequest = async (
    body: CreatePermissionTemplateBody,
) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<PermissionTemplateResponse, ''>
    >(`/api/v1/permission-template`, body)

    return data
}

export interface CreatePermissionTemplateBody {
    [CreatePermissionTemplateBodyKeys.NAME]: string
    [CreatePermissionTemplateBodyKeys.USAGE]: string
    [CreatePermissionTemplateBodyKeys.PERMISSIONS]: string[]
}

export enum CreatePermissionTemplateBodyKeys {
    NAME = 'name',
    USAGE = 'usage',
    PERMISSIONS = 'permissions',
}

export default createPermissionTemplateRequest
