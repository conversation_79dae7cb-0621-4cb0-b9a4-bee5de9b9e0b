import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse, PermissionTemplateResponse } from '../responses'

const getPermissionTemplateRequest = async (permissionTemplateId: number) => {
    const { data } = await restClient().get<
        ApiSingleResponse<PermissionTemplateResponse>
    >(`/api/v1/permission-template/${permissionTemplateId}`)

    return data
}

export default getPermissionTemplateRequest
