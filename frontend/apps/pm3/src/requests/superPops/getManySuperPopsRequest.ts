import { restClient } from '@/config/restClient/restClient'

import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '../params'
import { ApiManyResponse, SuperPopsResponse } from '../responses'

const getManySuperPopsRequest = async (params: string) => {
    const { data } = await restClient().get<ApiManyResponse<SuperPopsResponse>>(
        `/api/v1/super-pops` + params,
    )

    return data
}

export interface GetManySuperPopsParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {
    account: number
    isLocked?: boolean
}

export default getManySuperPopsRequest
