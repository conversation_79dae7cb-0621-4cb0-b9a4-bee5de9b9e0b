import { Dayjs } from 'dayjs'

import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse, SuperPopsResponse } from '../responses'

const updateSuperPopsRequest = async (
    id: number,
    updateSuperPopsBody: UpdateSuperPopsBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponse<SuperPopsResponse>
    >(`/api/v1/super-pops/${id}`, updateSuperPopsBody)

    return data
}

export interface UpdateSuperPopsBody {
    [UpdateSuperPopsBodyKeys.ACCOUNT_ID]: number
    [UpdateSuperPopsBodyKeys.FACILITIES_IDS]: number[]
    [UpdateSuperPopsBodyKeys.STARTS_AT]: Dayjs
    [UpdateSuperPopsBodyKeys.ENDS_AT]: Dayjs
    [UpdateSuperPopsBodyKeys.IS_LOCKED]: boolean
}

export enum UpdateSuperPopsBodyKeys {
    ACCOUNT_ID = 'accountId',
    FACILITIES_IDS = 'facilitiesIds',
    STARTS_AT = 'startsAt',
    ENDS_AT = 'endsAt',
    IS_LOCKED = 'isLocked',
}

export default updateSuperPopsRequest
