import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, TenantGroupResponse } from '../responses'

const getTenantGroupsRequest = async (params: string) => {
    const { data } = await restClient().get<
        ApiManyResponse<TenantGroupResponse>
    >('/api/v1/tenant-group' + params)

    return data
}

export interface GetTenantGroupsParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {}

export default getTenantGroupsRequest
