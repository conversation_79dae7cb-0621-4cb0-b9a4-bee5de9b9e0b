import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, TenantGroupResponse } from '../responses'

const createTenantGroupRequest = async (body: CreateTenantGroupBody) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<TenantGroupResponse, ''>
    >(`/api/v1/tenant-group`, body)

    return data
}

export interface CreateTenantGroupBody {
    [CreateTenantGroupBodyKeys.NAME]: string
    [CreateTenantGroupBodyKeys.ACTIVE]: boolean
    [CreateTenantGroupBodyKeys.TENANTS]: number[]
}

export enum CreateTenantGroupBodyKeys {
    NAME = 'name',
    ACTIVE = 'active',
    TENANTS = 'tenants',
}

export default createTenantGroupRequest
