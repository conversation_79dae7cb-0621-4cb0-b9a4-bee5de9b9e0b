import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse } from '../responses'

const sendEmergencyMessageRequest = async (
    sendEmergencyMessageBody: SendEmergencyMessageBody,
) => {
    const { data } = await restClient().post<ApiSingleResponse<unknown>>(
        `/api/v1/tenant-messenger/send-emergency-message`,
        sendEmergencyMessageBody,
    )

    return data
}

export interface SendEmergencyMessageBody {
    [SendEmergencyMessageBodyKeys.TENANT_GROUP_IDS]: number[]
    [SendEmergencyMessageBodyKeys.FACILITY_ID]: string | null
    [SendEmergencyMessageBodyKeys.MESSAGE]: string
    [SendEmergencyMessageBodyKeys.TENANT_IDS]: number[]
}

export enum SendEmergencyMessageBodyKeys {
    TENANT_GROUP_IDS = 'tenantGroupIds',
    FACILITY_ID = 'facilityId',
    MESSAGE = 'message',
    TENANT_IDS = 'tenantIds',
}

export default sendEmergencyMessageRequest
