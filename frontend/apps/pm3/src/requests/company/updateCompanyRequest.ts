import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, CompanyResponse } from '../responses'

const updateCompanyRequest = async (
    companyId: number,
    body: UpdateCompanyBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<CompanyResponse, ''>
    >(`/api/v1/company/${companyId}`, body)

    return data
}

export interface UpdateCompanyBody {
    [UpdateCompanyBodyKeys.NAME]: string
    [UpdateCompanyBodyKeys.ACTIVE]: boolean
    [UpdateCompanyBodyKeys.ABBR]: string
    [UpdateCompanyBodyKeys.CODE]: string
    [UpdateCompanyBodyKeys.ICO]: string
    [UpdateCompanyBodyKeys.NOTE]: string
    [UpdateCompanyBodyKeys.COUNTRY]: string
    [UpdateCompanyBodyKeys.CITY]: string
    [UpdateCompanyBodyKeys.STREET]: string
    [UpdateCompanyBodyKeys.POSTAL_CODE]: string
    [UpdateCompanyBodyKeys.CONTACT_EMAIL]: string
    [UpdateCompanyBodyKeys.ORIGINAL_RESPONSE]: object
}

export enum UpdateCompanyBodyKeys {
    NAME = 'name',
    ACTIVE = 'active',
    ABBR = 'abbr',
    CODE = 'code',
    ICO = 'cid',
    NOTE = 'note',
    COUNTRY = 'country',
    CITY = 'city',
    STREET = 'street',
    POSTAL_CODE = 'postalCode',
    CONTACT_EMAIL = 'contactEmail',
    ORIGINAL_RESPONSE = 'originalResponse',
}

export default updateCompanyRequest
