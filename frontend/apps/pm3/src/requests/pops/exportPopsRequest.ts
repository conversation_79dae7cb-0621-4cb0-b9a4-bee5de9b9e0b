import { restClient } from '@/config/restClient/restClient'

import { GetPopsEventsParams } from './getPopsEventsRequest'

const exportPopsRequest = async (
    target: ExportPopsTargetName,
    params: string,
) => {
    const { data } = await restClient().get<Blob>(
        `/api/v1/pops-event-${target}` + params,
        {
            responseType: 'blob',
        },
    )

    return data
}

export interface ExportPopsParams extends GetPopsEventsParams {
    popsId: number
}

export enum ExportPopsTargetName {
    PDF = 'pdf',
    XLS = 'xls',
}

export default exportPopsRequest
