import { Dayjs } from 'dayjs'

import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, PopsResponse } from '../responses'

const updatePopsRequest = async (popsId: number, body: UpdatePopsBody) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<PopsResponse, 'pops-updated'>
    >(`/api/v1/pops/${popsId}`, body)

    return data
}

export type UpdatePopsBody = {
    [UpdatePopsBodyKeys.ACCOUNT_ID]: number
    [UpdatePopsBodyKeys.FACILITY_ID]: number
    [UpdatePopsBodyKeys.STARTS_AT]: Dayjs
    [UpdatePopsBodyKeys.ENDS_AT]: Dayjs
    [UpdatePopsBodyKeys.IS_LOCKED]: boolean
    [UpdatePopsBodyKeys.IS_SEND_ATTACHMENT]: boolean
}

export enum UpdatePopsBodyKeys {
    ACCOUNT_ID = 'accountId',
    FACILITY_ID = 'facilityId',
    STARTS_AT = 'startsAt',
    ENDS_AT = 'endsAt',
    IS_LOCKED = 'isLocked',
    IS_SEND_ATTACHMENT = 'is_send_attachment',
}

export default updatePopsRequest
