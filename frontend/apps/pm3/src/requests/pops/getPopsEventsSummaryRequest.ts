import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse, PopsEventsSummaryResponse } from '../responses'

const getPopsEventsSummaryRequest = async (params: string) => {
    const { data } = await restClient().get<
        ApiSingleResponse<PopsEventsSummaryResponse[]>
    >('/api/v1/pops-event-summary' + params)

    return data
}

export interface GetPopsEventsSummaryParams {
    popsId?: number
    accountId?: number
    eventDate?: string
}

export default getPopsEventsSummaryRequest
