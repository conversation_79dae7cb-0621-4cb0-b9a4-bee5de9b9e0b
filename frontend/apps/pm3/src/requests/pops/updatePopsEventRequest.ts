import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, PopsEventResponse } from '../responses'

const updatePopsEventRequest = async (
    eventId: number,
    body: UpdatePopsEventBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<PopsEventResponse, 'pops-event-updated'>
    >(`/api/v1/pops-event/${eventId}`, body)

    return data
}

export type UpdatePopsEventBody = {
    [UpdatePopsEventBodyKeys.DESCRIPTION]: string
    [UpdatePopsEventBodyKeys.ATTACHMENTS_COUNT]: number
}

export enum UpdatePopsEventBodyKeys {
    DESCRIPTION = 'description',
    ATTACHMENTS_COUNT = 'attachmentsCount',
}

export default updatePopsEventRequest
