import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, PopsEventResponse } from '../responses'

const getPopsEventsRequest = async (params: string) => {
    const { data } = await restClient().get<ApiManyResponse<PopsEventResponse>>(
        '/api/v1/pops-event' + params,
    )

    return data
}

export interface GetPopsEventsParams
    extends PaginationParams,
        SearchParams,
        SortParams,
        FilterParams {
    popsId?: number
    accountId: number
    isLocked?: boolean
    eventDate?: string
    facilityId?: number
    hasAttachment?: boolean
}

export default getPopsEventsRequest
