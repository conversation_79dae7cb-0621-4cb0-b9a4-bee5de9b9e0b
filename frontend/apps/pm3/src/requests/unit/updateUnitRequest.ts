import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, UnitResponse } from '../responses'

const updateUnitRequest = async (unitId: number, body: UpdateUnitBody) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<UnitResponse, ''>
    >(`/api/v1/unit/${unitId}`, body)

    return data
}

export interface UpdateUnitBody {
    [UpdateUnitBodyKeys.NAME]: string
    [UpdateUnitBodyKeys.CODE]: string
    [UpdateUnitBodyKeys.ACTIVE]: boolean
}

export enum UpdateUnitBodyKeys {
    NAME = 'name',
    CODE = 'code',
    ACTIVE = 'active',
}

export default updateUnitRequest
