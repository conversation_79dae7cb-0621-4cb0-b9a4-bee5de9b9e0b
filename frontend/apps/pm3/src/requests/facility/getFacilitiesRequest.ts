import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, FacilityResponse } from '../responses'

const getFacilitiesRequest = async (params: string) => {
    const { data } = await restClient().get<ApiManyResponse<FacilityResponse>>(
        '/api/v1/facility' + params,
    )
    return data
}

export interface GetFacilitiesParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {
    superPopsId?: number
    nullOrIsLocked?: boolean
}

export default getFacilitiesRequest
