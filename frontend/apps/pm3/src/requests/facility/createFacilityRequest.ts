import * as _ from 'lodash'

import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, FacilityResponse } from '../responses'

const createFacilityRequest = async (body: CreateFacilityBody) => {
    const injectedBody = _.omit(body, 'address')

    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<FacilityResponse, ''>
    >(`/api/v1/facility`, injectedBody)

    return data
}

export interface CreateFacilityBody {
    [CreateFacilityBodyKeys.NAME]: string
    [CreateFacilityBodyKeys.CODE]: string
    [CreateFacilityBodyKeys.COMPANY_ID]: number | null
    [CreateFacilityBodyKeys.CONTRACT_ID]: number | null
    [CreateFacilityBodyKeys.PROJECT_ID]: number | null
    [CreateFacilityBodyKeys.LEGISLATION_ID]: number | null
    [CreateFacilityBodyKeys.STREET]: string
    [CreateFacilityBodyKeys.CITY]: string
    [CreateFacilityBodyKeys.POSTAL_CODE]: string
    [CreateFacilityBodyKeys.COUNTRY]: string
    [CreateFacilityBodyKeys.CUSTOMER_NAME]: string
    [CreateFacilityBodyKeys.CUSTOMER_STREET]: string
    [CreateFacilityBodyKeys.CUSTOMER_POSTAL_CODE]: string
    [CreateFacilityBodyKeys.CUSTOMER_COUNTRY]: string
    [CreateFacilityBodyKeys.IS_ACTIVE]: boolean
    [CreateFacilityBodyKeys.IS_POPS_ALERT]: boolean
    [CreateFacilityBodyKeys.POPS_ALERT_INTERVAL]: number | null
    [CreateFacilityBodyKeys.ATTACHMENT_RESOLUTION]: string
}

export enum CreateFacilityBodyKeys {
    NAME = 'name',
    CODE = 'code',
    COMPANY_ID = 'companyId',
    CONTRACT_ID = 'contractId',
    PROJECT_ID = 'projectId',
    LEGISLATION_ID = 'legislationId',
    STREET = 'street',
    CITY = 'city',
    POSTAL_CODE = 'postalCode',
    COUNTRY = 'country',
    CUSTOMER_NAME = 'customerName',
    CUSTOMER_STREET = 'customerStreet',
    CUSTOMER_CITY = 'customerCity',
    CUSTOMER_POSTAL_CODE = 'customerPostalCode',
    CUSTOMER_COUNTRY = 'customerCountry',
    IS_ACTIVE = 'isActive',
    IS_POPS_ALERT = 'isPopsAlert',
    POPS_ALERT_INTERVAL = 'popsAlertInterval',
    ATTACHMENT_RESOLUTION = 'attachmentResolution',
}

export default createFacilityRequest
