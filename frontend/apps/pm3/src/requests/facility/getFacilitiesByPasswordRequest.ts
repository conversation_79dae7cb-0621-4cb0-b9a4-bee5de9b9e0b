import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponse, FacilityItemResponse } from '../responses'

// TODO: change permission to enum
const getFacilitiesByPasswordRequest = async (permission: string) => {
    const { data } = await restClient().get<
        ApiSingleResponse<FacilityItemResponse[]>
    >(`/api/v1/facilities-by-permission/${permission}`)

    return data
}

export default getFacilitiesByPasswordRequest
