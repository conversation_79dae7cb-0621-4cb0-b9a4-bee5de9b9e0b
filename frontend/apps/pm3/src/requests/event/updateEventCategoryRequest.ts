import { restClient } from '@/config/restClient/restClient'

import {
    ApiSingleResponseWithMessage,
    EventCategoryResponse,
    EventCategorySeverityName,
} from '../responses'

const updateEventCategoryRequest = async (
    eventCategoryId: number,
    body: UpdateEventCategoryBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<EventCategoryResponse, ''>
    >(`/api/v1/event-category/${eventCategoryId}`, body)

    return data
}

export interface UpdateEventCategoryBody {
    [UpdateEventCategoryKeysEnum.LABEL]: string
    [UpdateEventCategoryKeysEnum.CODE]: string
    [UpdateEventCategoryKeysEnum.IS_HIDDEN]: boolean
    [UpdateEventCategoryKeysEnum.SEVERITY]: EventCategorySeverityName
    [UpdateEventCategoryKeysEnum.PARENT_CATEGORY]: number
    [UpdateEventCategoryKeysEnum.TRANSLATIONS]: {
        locale: string
        localizations: {
            field: string
            value: string
        }[]
    }[]
}

export enum UpdateEventCategoryKeysEnum {
    LABEL = 'label',
    CODE = 'code',
    IS_HIDDEN = 'isHidden',
    SEVERITY = 'severity',
    PARENT_CATEGORY = 'parentCategory',
    TRANSLATIONS = 'translations',
}

export default updateEventCategoryRequest
