import { restClient } from '@/config/restClient/restClient'

import { FilterParams, PaginationParams, SearchParams } from '../params'
import { ApiManyResponse, EventCategoryResponse } from '../responses'

const getEventsCategoriesRequest = async (params: string) => {
    const { data } = await restClient().get<
        ApiManyResponse<EventCategoryResponse>
    >('/api/v1/event-category' + params)

    return data
}

export interface GetEventsCategoriesParams
    extends SearchParams,
        PaginationParams,
        FilterParams {
    accountId?: number
}

export default getEventsCategoriesRequest
