import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, ContractResponse } from '../responses'

const updateContractRequest = async (
    contractId: number,
    body: UpdateContractBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<ContractResponse, ''>
    >(`/api/v1/contract/${contractId}`, body)

    return data
}

export interface UpdateContractBody {
    [UpdateContractBodyKeys.NAME]: string
    [UpdateContractBodyKeys.CODE]: string
    [UpdateContractBodyKeys.ACTIVE]: boolean
}

export enum UpdateContractBodyKeys {
    NAME = 'name',
    CODE = 'code',
    ACTIVE = 'active',
}

export default updateContractRequest
