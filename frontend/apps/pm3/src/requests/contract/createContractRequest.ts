import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, ContractResponse } from '../responses'

const createContractRequest = async (body: CreateContractBody) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<ContractResponse, ''>
    >(`/api/v1/contract`, body)

    return data
}

export interface CreateContractBody {
    [CreateContractBodyKeys.NAME]: string
    [CreateContractBodyKeys.CODE]: string
    [CreateContractBodyKeys.ACTIVE]: boolean
}

export enum CreateContractBodyKeys {
    NAME = 'name',
    CODE = 'code',
    ACTIVE = 'active',
}

export default createContractRequest
