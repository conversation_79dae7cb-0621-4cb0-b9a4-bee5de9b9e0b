import { restClient } from '@/config/restClient/restClient'
import { FilterParams, PaginationParams, SearchParams } from '@/requests/params'

import { ApiManyResponse, PermissionResponse } from '../responses'

const getPermissionsRequest = async (params: string) => {
    const { data } = await restClient().get<
        ApiManyResponse<PermissionResponse>
    >('/api/v1/permission' + params)

    return data
}

export interface GetPermissionsParams
    extends PaginationParams,
        SearchParams,
        FilterParams {}

export default getPermissionsRequest
