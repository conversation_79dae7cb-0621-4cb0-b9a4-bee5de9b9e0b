import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, ProjectResponse } from '../responses'

const getProjectsRequest = async (params: string) => {
    const { data } = await restClient().get<ApiManyResponse<ProjectResponse>>(
        '/api/v1/project' + params,
    )

    return data
}

export interface GetProjectsParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {}

export default getProjectsRequest
