import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, ProjectResponse } from '../responses'

const createProjectRequest = async (body: CreateProjectBody) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<ProjectResponse, ''>
    >(`/api/v1/project`, body)

    return data
}

export interface CreateProjectBody {
    [CreateProjectBodyKeys.NAME]: string
    [CreateProjectBodyKeys.CODE]: string
    [CreateProjectBodyKeys.ACTIVE]: boolean
}

export enum CreateProjectBodyKeys {
    NAME = 'name',
    CODE = 'code',
    ACTIVE = 'active',
}

export default createProjectRequest
