import { GridFilterItem } from '@goodsailors/ui/hooks/useGridFilter'

export interface PaginationParams {
    pagination?: {
        offset: number
        limit: number
    }
}

export interface SearchParams {
    search?: string
}

export interface SortParams {
    sort?: {
        sortBy: string
        sortMethod: 'ASC' | 'DESC'
    }
}

export interface FilterParams {
    filter?: GridFilterItem<FilterTypeName>[]
}

export enum FilterTypeName {
    MATCH = 'match',
    STARTS_WITH = 'startsWith',
    ENDS_WITH = 'endsWith',
    CONTAINS = 'contains',
    GREATER_THAN = 'greaterThan',
    LESS_THAN = 'lessThan',
    IN = 'in',
    NOT_IN = 'notIn',
}

export type AllParams = PaginationParams &
    SortParams &
    FilterParams &
    SearchParams
