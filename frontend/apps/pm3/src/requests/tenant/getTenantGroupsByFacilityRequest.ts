import { restClient } from '@/config/restClient/restClient'

import { FilterParams, PaginationParams, SortParams } from '../params'
import { ApiManyResponse, TenantGroupResponse } from '../responses'

const getTenantGroupsByFacilityRequest = async (
    facilityId: number,
    params: string,
) => {
    const { data } = await restClient().get<
        ApiManyResponse<TenantGroupResponse>
    >(`/api/v1/tenant-group/facility/${facilityId}` + params)

    return data
}

export interface GetTenantGroupsByFacilityParams
    extends PaginationParams,
        SortParams,
        FilterParams {}

export default getTenantGroupsByFacilityRequest
