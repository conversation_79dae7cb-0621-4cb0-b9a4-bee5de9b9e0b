import { restClient } from '@/config/restClient/restClient'

import { FilterParams, PaginationParams, SortParams } from '../params'
import { ApiManyResponse, TenantResponse } from '../responses'

const getTenantsByFacilityRequest = async (
    facilityId: number,
    params: string,
) => {
    const { data } = await restClient().get<ApiManyResponse<TenantResponse>>(
        `/api/v1/tenant/facility/${facilityId}` + params,
    )

    return data
}

export interface GetTenantsByFacilityParams
    extends PaginationParams,
        SortParams,
        FilterParams {}

export default getTenantsByFacilityRequest
