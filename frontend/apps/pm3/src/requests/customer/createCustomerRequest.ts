import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, CustomerResponse } from '../responses'

const createCustomerRequest = async (body: CreateCustomerBody) => {
    const { data } = await restClient().post<
        ApiSingleResponseWithMessage<CustomerResponse, ''>
    >(`/api/v1/customer`, body)

    return data
}

export interface CreateCustomerBody {
    [CreateCustomerBodyKeys.NAME]: string
    [CreateCustomerBodyKeys.CID]: string
    [CreateCustomerBodyKeys.COUNTRY]: string
    [CreateCustomerBodyKeys.CITY]: string
    [CreateCustomerBodyKeys.STREET]: string
    [CreateCustomerBodyKeys.POSTAL_CODE]: string
    [CreateCustomerBodyKeys.COORDINATES]: string
    [CreateCustomerBodyKeys.FACILITIES]: number[]
}

export enum CreateCustomerBodyKeys {
    NAME = 'name',
    CID = 'cid',
    COUNTRY = 'country',
    CITY = 'city',
    STREET = 'street',
    POSTAL_CODE = 'postalCode',
    COORDINATES = 'coordinates',
    FACILITIES = 'facilities',
}

export default createCustomerRequest
