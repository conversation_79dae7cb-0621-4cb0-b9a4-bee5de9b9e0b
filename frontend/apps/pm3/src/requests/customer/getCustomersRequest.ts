import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, CustomerResponse } from '../responses'

const getCustomersRequest = async (params: string) => {
    const { data } = await restClient().get<ApiManyResponse<CustomerResponse>>(
        '/api/v1/customer' + params,
    )

    return data
}

export interface GetCustomersParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {}

export default getCustomersRequest
