import { restClient } from '@/config/restClient/restClient'

import { ApiSingleResponseWithMessage, CustomerResponse } from '../responses'

const updateCustomerRequest = async (
    customerId: number,
    body: UpdateCustomerBody,
) => {
    const { data } = await restClient().put<
        ApiSingleResponseWithMessage<CustomerResponse, ''>
    >(`/api/v1/customer/${customerId}`, body)

    return data
}

export interface UpdateCustomerBody {
    [UpdateCustomerBodyKeys.NAME]: string
    [UpdateCustomerBodyKeys.CID]: string
    [UpdateCustomerBodyKeys.COUNTRY]: string
    [UpdateCustomerBodyKeys.CITY]: string
    [UpdateCustomerBodyKeys.STREET]: string
    [UpdateCustomerBodyKeys.POSTAL_CODE]: string
    [UpdateCustomerBodyKeys.COORDINATES]: string
    [UpdateCustomerBodyKeys.FACILITIES]: number[]
}

export enum UpdateCustomerBodyKeys {
    NAME = 'name',
    CID = 'cid',
    COUNTRY = 'country',
    CITY = 'city',
    STREET = 'street',
    POSTAL_CODE = 'postalCode',
    COORDINATES = 'coordinates',
    FACILITIES = 'facilities',
}

export default updateCustomerRequest
