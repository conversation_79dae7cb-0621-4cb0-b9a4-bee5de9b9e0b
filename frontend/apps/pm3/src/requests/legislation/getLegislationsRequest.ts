import { restClient } from '@/config/restClient/restClient'
import {
    FilterParams,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

import { ApiManyResponse, LegislationResponse } from '../responses'

const getLegislationsRequest = async (params: string) => {
    const { data } = await restClient().get<
        ApiManyResponse<LegislationResponse>
    >('/api/v1/legislation' + params)

    return data
}

export interface GetLegislationsParams
    extends PaginationParams,
        SearchParams,
        FilterParams,
        SortParams {}

export default getLegislationsRequest
