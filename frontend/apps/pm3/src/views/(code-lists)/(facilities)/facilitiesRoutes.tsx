import React from 'react'

import routerPaths from '@/config/routerPaths'

const FacilitiesView = React.lazy(
    () => import('./FacilitiesView/FacilitiesView'),
)

const FacilityView = React.lazy(() => import('./FacilityView/FacilityView'))

const CreateFacilityView = React.lazy(
    () => import('./CreateFacilityView/CreateFacilityView'),
)

const facilitiesRoutes = [
    {
        path: routerPaths.codeLists.facilities.index,
        element: <FacilitiesView />,
    },
    {
        path: routerPaths.codeLists.facility.index,
        element: <FacilityView />,
    },
    {
        path: routerPaths.codeLists.facilities.create,
        element: <CreateFacilityView />,
    },
]

export default facilitiesRoutes
