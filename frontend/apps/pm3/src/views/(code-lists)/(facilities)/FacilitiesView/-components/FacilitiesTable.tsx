import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import MenuItem from '@mui/material/MenuItem'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import CellLink from '@/components/other/CellLink'
import DataGridHeader from '@/components/other/DataGridHeader'
import StatusChip from '@/components/other/StatusChip'
import { gridFilterMap } from '@/config/gridFilter'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import usePersistTable from '@/hooks/usePersistTable'
import getFacilitiesRequest, {
    GetFacilitiesParams,
} from '@/requests/facility/getFacilitiesRequest'
import { FacilityResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

const FacilitiesTable = () => {
    const navigate = useNavigate()
    const gridRef = useGridApiRef()
    const { breakpoints } = useTheme()
    const { t } = useTranslation()

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSort()
    const { gridStringOperators } = useGridOperators()

    const params = createRequestParams<GetFacilitiesParams>({
        sort,
        filter,
        pagination,
        search: debouncedSearch,
    })

    const { data: facilities, isFetching } = useQuery({
        queryFn: () => getFacilitiesRequest(params),
        queryKey: [...queryKeys.facilities.index, params],
        placeholderData: keepPreviousData,
    })

    const onDetail = React.useCallback(
        (facilityId: number) => {
            const href = createHrefWithParams(
                routerPaths.codeLists.facility.index,
                {
                    facilityId,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const onCreate = () => {
        navigate(routerPaths.codeLists.facilities.create)
    }

    const columns = React.useMemo<GridColDef<FacilityResponse>[]>(
        () => [
            {
                minWidth: 140,
                field: 'code',
                filterable: false,
                sortable: false,
                filterOperators: gridStringOperators(),
                headerName: t('labels.code'),
                renderCell: ({ row }) => {
                    const href = createHrefWithParams(
                        routerPaths.codeLists.facility.index,
                        {
                            facilityId: row.facility.id,
                        },
                    )

                    return <CellLink label={row.facility.code} to={href} />
                },
            },
            {
                minWidth: 200,
                field: 'name',
                filterOperators: gridStringOperators(),
                headerName: t('labels.name'),
                valueGetter: (__, row) => row.facility.name,
            },
            {
                minWidth: 140,
                field: 'is_active',
                filterable: false,
                sortable: false,
                type: 'boolean',
                headerName: t('labels.status'),
                renderCell: ({ row }) => {
                    return <StatusChip isActive={row.facility.is_active} />
                },
            },
            {
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: ({ row }) => (
                    <IconButtonDropdown
                        icon={<MoreVert fontSize='small' />}
                        menuContent={
                            <MenuItem onClick={() => onDetail(row.facility.id)}>
                                {t('actions.show-detail')}
                            </MenuItem>
                        }
                    />
                ),
            },
        ],
        [gridStringOperators, t, onDetail],
    )

    usePersistTable(gridRef, routerPaths.codeLists.facilities.index)

    return (
        <>
            <Card sx={{ height: '100%', width: '100%' }}>
                <CardContent
                    sx={{
                        p: '0 !important',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <DataGridHeader
                        gridRef={gridRef}
                        onSearch={setSearch}
                        search={search}
                        actions={
                            <Button
                                startIcon={<AddCircleOutlineOutlined />}
                                onClick={onCreate}
                            >
                                {t('actions.create-facility')}
                            </Button>
                        }
                    />

                    <DataGridPro
                        disableColumnPinning
                        pagination
                        getRowId={(row) => row.facility.id}
                        sx={{
                            height: '100%',
                            minHeight: 150,
                            [breakpoints.up('lg')]: {
                                minHeight: 400,
                            },
                        }}
                        apiRef={gridRef}
                        columns={columns}
                        loading={isFetching}
                        paginationMode='server'
                        sortingMode='server'
                        filterMode='server'
                        disableColumnSelector
                        paginationModel={paginationModel}
                        filterModel={filterModel}
                        onPaginationModelChange={setPaginationModel}
                        onFilterModelChange={(model) => setFilterModel(model)}
                        sortModel={sortModel}
                        onSortModelChange={(model) => setSortModel(model)}
                        rows={facilities?.list || []}
                        rowCount={facilities?.total_count || 0}
                        initialState={{
                            density: 'compact',
                            pinnedColumns: {
                                right: ['actions'],
                            },
                        }}
                    />
                </CardContent>
            </Card>
        </>
    )
}

export default FacilitiesTable
