import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Groups2OutlinedIcon from '@mui/icons-material/Groups2Outlined'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import { useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Navigate, useParams } from 'react-router'

import { InformationRow } from '@/components/other/InformationRow'
import StatusChip from '@/components/other/StatusChip'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import getFacilityRequest from '@/requests/facility/getFacilityRequest'

import { FacilityViewTabName } from './types'

const BasicInformationTab = React.lazy(
    () => import('./-tabs/BasicInformationTab'),
)

const TenantGroupsTable = React.lazy(
    () => import('./-components/TenantGroupsTable'),
)

interface FacilityViewContentProps {
    tab: FacilityViewTabName
    onTab: (tab: FacilityViewTabName) => void
}

const FacilityViewContent = ({ onTab, tab }: FacilityViewContentProps) => {
    const { t } = useTranslation('common')

    const { spacing, palette } = useTheme()

    const params = useParams<'facilityId'>()
    const facilityId = parseParamInt(params.facilityId)

    const {
        isPending,
        data: facility,
        isError,
    } = useQuery({
        queryFn: () => getFacilityRequest(facilityId),
        queryKey: queryKeys.facility(facilityId).index,
    })

    if (isPending) return <LoadingSpinner />

    if (isError) {
        return <Navigate to={routerPaths.codeLists.facilities.index} />
    }

    return (
        <Stack gap={1} height='100%'>
            <Card>
                <CardContent
                    sx={{
                        px: 1,
                        gap: 1.5,
                        display: 'flex',
                        flexWrap: 'wrap',
                        alignItems: 'flex-start',
                        py: `${spacing(1.5)} !important`,
                    }}
                >
                    <InformationRow title={t('labels.name')}>
                        <Typography fontWeight={700}>
                            {facility.data.facility.name}
                        </Typography>
                    </InformationRow>

                    <InformationRow title={t('labels.code')}>
                        <Typography fontWeight={700}>
                            {facility.data.facility.code}
                        </Typography>
                    </InformationRow>

                    <InformationRow title={t('labels.status')}>
                        <StatusChip
                            isActive={facility.data.facility.is_active}
                        />
                    </InformationRow>
                </CardContent>
            </Card>

            <Stack flex={1} overflow='hidden'>
                <Tabs
                    value={tab}
                    onChange={(__, value) => {
                        onTab(value)
                    }}
                    sx={{
                        width: 'max-content',
                        background: palette.grey[50],
                        borderTopLeftRadius: spacing(2),
                        borderTopRightRadius: spacing(2),
                    }}
                >
                    <Tab
                        value={FacilityViewTabName.BASIC_INFORMATION}
                        label={t('labels.information')}
                        icon={<InfoOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />

                    <Tab
                        value={FacilityViewTabName.TENANT_GROUPS}
                        label={t('labels.tenant-groups')}
                        icon={<Groups2OutlinedIcon sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />
                </Tabs>

                <React.Suspense fallback={<LoadingSpinner />}>
                    {tab === FacilityViewTabName.BASIC_INFORMATION && (
                        <BasicInformationTab facility={facility.data} />
                    )}

                    {tab === FacilityViewTabName.TENANT_GROUPS && (
                        <TenantGroupsTable facility={facility.data} />
                    )}
                </React.Suspense>
            </Stack>
        </Stack>
    )
}

const FacilityView = () => {
    return (
        <TabGuard
            defaultTab={FacilityViewTabName.BASIC_INFORMATION}
            validValues={_.values(FacilityViewTabName)}
        >
            {(props) => <FacilityViewContent {...props} />}
        </TabGuard>
    )
}

export default FacilityView
