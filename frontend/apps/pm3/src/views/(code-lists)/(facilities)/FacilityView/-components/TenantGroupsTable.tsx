import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { gridFilterMap } from '@/config/gridFilter'
import queryKeys from '@/config/queryClient/queryKeys'
import { FacilityResponse, TenantGroupResponse } from '@/requests/responses'
import getTenantGroupsByFacilityRequest, {
    GetTenantGroupsByFacilityParams,
} from '@/requests/tenant/getTenantGroupsByFacilityRequest'
import createRequestParams from '@/utils/createRequestParams'

interface TenantGroupsTableProps {
    facility: FacilityResponse
}

const TenantGroupsTable = ({ facility }: TenantGroupsTableProps) => {
    const gridRef = useGridApiRef()
    const { breakpoints } = useTheme()
    const { t } = useTranslation('common')

    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSort()
    const { gridStringOperators } = useGridOperators()

    const params = createRequestParams<GetTenantGroupsByFacilityParams>({
        sort,
        filter,
        pagination,
    })

    const { data: tenantGroups, isFetching } = useQuery({
        queryFn: () =>
            getTenantGroupsByFacilityRequest(facility.facility.id, params),
        queryKey: [
            ...queryKeys.facility(facility.facility.id).tenantGroups,
            params,
        ],
        placeholderData: keepPreviousData,
    })

    const columns = React.useMemo<GridColDef<TenantGroupResponse>[]>(
        () => [
            {
                minWidth: 240,
                field: 'name',
                filterOperators: gridStringOperators(),
                headerName: t('labels.name'),
            },
        ],
        [gridStringOperators, t],
    )

    return (
        <>
            <Card sx={{ borderTopLeftRadius: 0, height: '100%' }}>
                <CardContent
                    sx={{
                        p: '0 !important',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <DataGridPro
                        disableColumnPinning
                        pagination
                        sx={{
                            height: '100%',
                            minHeight: 150,
                            [breakpoints.up('lg')]: {
                                minHeight: 400,
                            },
                        }}
                        apiRef={gridRef}
                        columns={columns}
                        loading={isFetching}
                        paginationMode='server'
                        sortingMode='server'
                        filterMode='server'
                        disableColumnSelector
                        paginationModel={paginationModel}
                        filterModel={filterModel}
                        onPaginationModelChange={setPaginationModel}
                        onFilterModelChange={(model) => setFilterModel(model)}
                        sortModel={sortModel}
                        onSortModelChange={(model) => setSortModel(model)}
                        rows={tenantGroups?.list || []}
                        rowCount={tenantGroups?.total_count || 0}
                        initialState={{
                            density: 'compact',
                            pinnedColumns: {
                                right: ['actions'],
                            },
                        }}
                    />
                </CardContent>
            </Card>
        </>
    )
}

export default TenantGroupsTable
