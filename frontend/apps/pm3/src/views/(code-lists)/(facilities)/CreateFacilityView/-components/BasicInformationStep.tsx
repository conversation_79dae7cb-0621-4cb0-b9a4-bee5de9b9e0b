import Divider from '@mui/material/Divider'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useField, useFormikContext } from 'formik'
import { useTranslation } from 'react-i18next'

import FormNumericField from '@/components/form/FormNumericField'
import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import FormCompaniesAsyncAutocomplete from '@/components/resource/company/FormCompaniesAsyncAutocomplete'
import FormContractsAsyncAutocomplete from '@/components/resource/contract/FormContractsAsyncAutocomplete'
import GeocodingAsyncAutocomplete from '@/components/resource/geocoding/GeocodingAsyncAutocomplete'
import FormLegislationsAsyncAutocomplete from '@/components/resource/legislation/FormLegislationsAsyncAutocomplete'
import FormProjectsAsyncAutocomplete from '@/components/resource/project/FormProjectsAsyncAutocomplete'
import {
    CreateFacilityBody,
    CreateFacilityBodyKeys,
} from '@/requests/facility/createFacilityRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation()

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.object-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='FACILITY_NAME'
                name={CreateFacilityBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.code')}
                data-testid='FACILITY_CODE'
                name={CreateFacilityBodyKeys.CODE}
            />

            <FormCompaniesAsyncAutocomplete
                data-testid='COMPANY'
                name={CreateFacilityBodyKeys.COMPANY_ID}
            />

            <FormContractsAsyncAutocomplete
                data-testid='CONTRACT'
                name={CreateFacilityBodyKeys.CONTRACT_ID}
            />

            <FormProjectsAsyncAutocomplete
                data-testid='PROJECT'
                name={CreateFacilityBodyKeys.PROJECT_ID}
            />

            <FormLegislationsAsyncAutocomplete
                data-testid='LEGISLATION'
                name={CreateFacilityBodyKeys.LEGISLATION_ID}
            />

            <Divider sx={{ my: 1 }} />

            <Typography fontWeight={600}>
                {t('labels.object-address')}
            </Typography>

            <FacilityGeocodingAsyncAutocomplete />

            <FormTextField
                label={t('labels.city')}
                data-testid='FACILITY_CITY'
                name={CreateFacilityBodyKeys.CITY}
            />

            <FormTextField
                label={t('labels.country')}
                data-testid='FACILITY_COUNTRY'
                name={CreateFacilityBodyKeys.COUNTRY}
            />

            <FormTextField
                label={t('labels.postcode')}
                data-testid='FACILITY_POSTAL_CODE'
                name={CreateFacilityBodyKeys.POSTAL_CODE}
            />

            <Divider sx={{ my: 1 }} />

            <Typography fontWeight={600}>
                {t('labels.customer-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='CUSTOMER_NAME'
                name={CreateFacilityBodyKeys.CUSTOMER_NAME}
            />

            <Divider sx={{ my: 1 }} />

            <Typography fontWeight={600}>
                {t('labels.customer-address')}
            </Typography>

            <CustomerGeocodingAsyncAutocomplete />

            <FormTextField
                label={t('labels.city')}
                data-testid='CUSTOMER_CITY'
                name={CreateFacilityBodyKeys.CUSTOMER_CITY}
            />

            <FormTextField
                label={t('labels.country')}
                data-testid='CUSTOMER_COUNTRY'
                name={CreateFacilityBodyKeys.CUSTOMER_COUNTRY}
            />

            <FormTextField
                label={t('labels.postcode')}
                data-testid='CUSTOMER_POSTAL_CODE'
                name={CreateFacilityBodyKeys.CUSTOMER_POSTAL_CODE}
            />

            <Divider sx={{ my: 1 }} />
            <Typography fontWeight={600}>{t('labels.settings')}</Typography>

            <FormSwitch
                label={t('labels.active')}
                name={CreateFacilityBodyKeys.IS_ACTIVE}
            />

            <FormSwitch
                label={t('labels.pops-alert')}
                name={CreateFacilityBodyKeys.IS_POPS_ALERT}
            />

            <FormNumericField
                label={t('labels.pops-alert-interval') + ' ' + '(min)'}
                data-testid='FACILITY_POPS_ALERT_INTERVAL'
                name={CreateFacilityBodyKeys.POPS_ALERT_INTERVAL}
            />

            <FormTextField
                label={t('labels.attachment_resolution') + ' ' + '(1920x1080)'}
                data-testid='FACILITY_ATTACHMENT_RESOLUTION'
                name={CreateFacilityBodyKeys.ATTACHMENT_RESOLUTION}
            />
        </Stack>
    )
}

const FacilityGeocodingAsyncAutocomplete = () => {
    const { t } = useTranslation()

    const [field, , helpers] = useField(CreateFacilityBodyKeys.STREET)
    const { setValues } = useFormikContext<CreateFacilityBody>()

    return (
        <GeocodingAsyncAutocomplete
            searchedValue={field.value}
            label={t('labels.street')}
            onClose={(searchedValue) => {
                helpers.setValue(searchedValue)
            }}
            onChange={(item, __, searchedValue) => {
                setValues((prevValues) => ({
                    ...prevValues,
                    city: item.city || '',
                    country: item.country || '',
                    postalCode: item.postcode || '',
                    street: item.street || searchedValue,
                }))
            }}
        />
    )
}

const CustomerGeocodingAsyncAutocomplete = () => {
    const { t } = useTranslation()

    const [field, , helpers] = useField(CreateFacilityBodyKeys.CUSTOMER_STREET)
    const { setValues } = useFormikContext<CreateFacilityBody>()

    return (
        <GeocodingAsyncAutocomplete
            searchedValue={field.value}
            label={t('labels.street')}
            onClose={(searchedValue) => {
                helpers.setValue(searchedValue)
            }}
            onChange={(item, __, searchedValue) => {
                setValues((prevValues) => ({
                    ...prevValues,
                    customerCity: item.city || '',
                    customerCountry: item.country || '',
                    customerPostalCode: item.postcode || '',
                    customerStreet: item.street || searchedValue,
                }))
            }}
        />
    )
}

export default BasicInformationStep
