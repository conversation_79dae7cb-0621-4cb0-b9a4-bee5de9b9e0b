import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import ApartmentOutlinedIcon from '@mui/icons-material/ApartmentOutlined'
import AttributionOutlinedIcon from '@mui/icons-material/AttributionOutlined'
import CorporateFareOutlinedIcon from '@mui/icons-material/CorporateFareOutlined'
import EventOutlinedIcon from '@mui/icons-material/EventOutlined'
import FolderSharedOutlinedIcon from '@mui/icons-material/FolderSharedOutlined'
import ListAltOutlinedIcon from '@mui/icons-material/ListAltOutlined'
import LocalPoliceOutlinedIcon from '@mui/icons-material/LocalPoliceOutlined'
import MapsHomeWorkOutlined from '@mui/icons-material/MapsHomeWorkOutlined'
import PaidOutlinedIcon from '@mui/icons-material/PaidOutlined'
import SentimentSatisfiedAltOutlinedIcon from '@mui/icons-material/SentimentSatisfiedAltOutlined'
import SnippetFolderOutlinedIcon from '@mui/icons-material/SnippetFolderOutlined'
import WorkspacesOutlinedIcon from '@mui/icons-material/WorkspacesOutlined'
import { useQuery } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router'

import Layout from '@/components/layout/Layout'
import PageHead from '@/components/layout/PageHead'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import AppGuard from '@/guard/AppGuard'
import AuthenticatedGuard from '@/guard/AuthenticatedGuard'
import getCompanyRequest from '@/requests/company/getCompanyRequest'
import getContractRequest from '@/requests/contract/getContractRequest'
import getCurrencyRequest from '@/requests/currency/getCurrencyRequest'
import getCustomerRequest from '@/requests/customer/getCustomerRequest'
import getFacilityRequest from '@/requests/facility/getFacilityRequest'
import getFacilityGroupRequest from '@/requests/facilityGroup/getFacilityGroupRequest'
import getLegislationRequest from '@/requests/legislation/getLegislationRequest'
import getProjectRequest from '@/requests/project/getProjectRequest'
import { ApplicationName } from '@/requests/responses'
import getTenantRequest from '@/requests/tenant/getTenantRequest'
import getTenantGroupRequest from '@/requests/tenantGroup/getTenantGroupRequest'
import getUnitRequest from '@/requests/unit/getUnitRequest'

const CodeListsLayoutContent = () => {
    const { t } = useTranslation('common')

    const params = useParams()

    const facilityGroupId = parseParamInt(params.facilityGroupId)
    const projectId = parseParamInt(params.projectId)
    const facilityId = parseParamInt(params.facilityId)
    const contractId = parseParamInt(params.contractId)
    const currencyId = parseParamInt(params.currencyId)
    const companyId = parseParamInt(params.companyId)
    const unitId = parseParamInt(params.unitId)
    const tenantGroupId = parseParamInt(params.tenantGroupId)
    const tenantId = parseParamInt(params.tenantId)
    const customerId = parseParamInt(params.customerId)
    const legislationId = parseParamInt(params.legislationId)

    const { data: facilityGroup } = useQuery({
        queryFn: () => getFacilityGroupRequest(facilityGroupId),
        queryKey: queryKeys.facilityGroup(facilityGroupId).index,
        enabled: Boolean(facilityGroupId),
    })

    const { data: contract } = useQuery({
        queryFn: () => getContractRequest(contractId),
        queryKey: queryKeys.facilityGroup(contractId).index,
        enabled: Boolean(contractId),
    })

    const { data: facility } = useQuery({
        queryFn: () => getFacilityRequest(facilityId),
        queryKey: queryKeys.facility(facilityId).index,
        enabled: Boolean(facilityId),
    })

    const { data: company } = useQuery({
        queryFn: () => getCompanyRequest(companyId),
        queryKey: queryKeys.company(companyId).index,
        enabled: Boolean(companyId),
    })

    const { data: currency } = useQuery({
        queryFn: () => getCurrencyRequest(currencyId),
        queryKey: queryKeys.currency(currencyId).index,
        enabled: Boolean(currencyId),
    })

    const { data: project } = useQuery({
        queryFn: () => getProjectRequest(projectId),
        queryKey: queryKeys.project(projectId).index,
        enabled: Boolean(projectId),
    })

    const { data: unit } = useQuery({
        queryFn: () => getUnitRequest(unitId),
        queryKey: queryKeys.unit(unitId).index,
        enabled: Boolean(unitId),
    })

    const { data: customer } = useQuery({
        queryFn: () => getCustomerRequest(customerId),
        queryKey: queryKeys.customer(customerId).index,
        enabled: Boolean(customerId),
    })

    const { data: tenant } = useQuery({
        queryFn: () => getTenantRequest(tenantId),
        queryKey: queryKeys.tenant(tenantId).index,
        enabled: Boolean(tenantId),
    })

    const { data: legislation } = useQuery({
        queryFn: () => getLegislationRequest(legislationId),
        queryKey: queryKeys.legislation(legislationId).index,
        enabled: Boolean(legislationId),
    })

    const { data: tenantGroup } = useQuery({
        queryFn: () => getTenantGroupRequest(tenantGroupId),
        queryKey: queryKeys.tenantGroup(tenantGroupId).index,
        enabled: Boolean(tenantGroupId),
    })

    const sidebarItems = [
        {
            href: routerPaths.codeLists.facilityGroups.index,
            label: t('labels.objects-groups'),
            icon: <FolderSharedOutlinedIcon />,
            testId: 'OBJECT_GROUPS',
        },
        {
            href: routerPaths.codeLists.facilities.index,
            label: t('labels.objects'),
            icon: <MapsHomeWorkOutlined />,
            testId: 'OBJECTS',
        },
        {
            href: routerPaths.codeLists.currencies.index,
            label: t('labels.currencies'),
            icon: <PaidOutlinedIcon />,
            testId: 'CURRENCIES',
        },
        {
            href: routerPaths.codeLists.contracts.index,
            label: t('labels.contracts'),
            icon: <ListAltOutlinedIcon />,
            testId: 'CONTRACTS',
        },
        {
            href: routerPaths.codeLists.companies.index,
            label: t('labels.companies'),
            icon: <ApartmentOutlinedIcon />,
            testId: 'COMPANIES',
        },
        {
            href: routerPaths.codeLists.projects.index,
            label: t('labels.projects'),
            icon: <SnippetFolderOutlinedIcon />,
            testId: 'PROJECTS',
        },
        {
            href: routerPaths.codeLists.events.index,
            label: t('labels.events'),
            icon: <EventOutlinedIcon />,
            testId: 'EVENT_CATEGORIES',
        },
        {
            href: routerPaths.codeLists.units.index,
            label: t('labels.units'),
            icon: <CorporateFareOutlinedIcon />,
            testId: 'UNITS',
        },
        {
            href: routerPaths.codeLists.customers.index,
            label: t('labels.customers'),
            icon: <AttributionOutlinedIcon />,
            testId: 'CUSTOMERS',
        },
        {
            href: routerPaths.codeLists.tenants.index,
            label: t('labels.tenants'),
            icon: <SentimentSatisfiedAltOutlinedIcon />,
            testId: 'TENANTS',
        },
        {
            href: routerPaths.codeLists.tenantGroups.index,
            label: t('labels.tenant-groups'),
            icon: <WorkspacesOutlinedIcon />,
            testId: 'TENANT_GROUPS',
        },
        {
            href: routerPaths.codeLists.legislations.index,
            label: t('labels.legislations'),
            icon: <LocalPoliceOutlinedIcon />,
        },
    ]

    const map = {
        [routerPaths.codeLists.index]: t('labels.code-list'),
        [routerPaths.codeLists.events.index]: t('labels.events'),
        [routerPaths.codeLists.facilities.index]: t('labels.objects'),
        [routerPaths.codeLists.facility.index]:
            facility?.data.facility.name || '',

        [routerPaths.codeLists.facilityGroups.create]: t('labels.create-group'),
        [routerPaths.codeLists.facilityGroup.update]: t('labels.update-group'),
        [routerPaths.codeLists.facilityGroups.index]: t(
            'labels.objects-groups',
        ),
        [routerPaths.codeLists.facilityGroup.index]:
            facilityGroup?.data.name || '',

        [routerPaths.codeLists.currencies.index]: t('labels.currencies'),
        [routerPaths.codeLists.currency.index]: currency?.data.name || '',
        [routerPaths.codeLists.currency.update]: t('labels.update-currency'),
        [routerPaths.codeLists.currencies.create]: t('labels.create-currency'),

        [routerPaths.codeLists.projects.index]: t('labels.projects'),
        [routerPaths.codeLists.project.index]: project?.data.name || '',
        [routerPaths.codeLists.project.update]: t('labels.update-project'),
        [routerPaths.codeLists.projects.create]: t('labels.create-project'),

        [routerPaths.codeLists.companies.index]: t('labels.companies'),
        [routerPaths.codeLists.company.index]: company?.data.name || '',
        [routerPaths.codeLists.company.update]: t('labels.update-company'),
        [routerPaths.codeLists.companies.create]: t('labels.create-company'),

        [routerPaths.codeLists.contract.update]: t('labels.update-contract'),
        [routerPaths.codeLists.contract.index]: contract?.data.name || '',
        [routerPaths.codeLists.contracts.index]: t('labels.contracts'),
        [routerPaths.codeLists.contracts.create]: t('labels.create-contract'),

        [routerPaths.codeLists.unit.update]: t('labels.update-unit'),
        [routerPaths.codeLists.unit.index]: unit?.data.name || '',
        [routerPaths.codeLists.units.index]: t('labels.units'),
        [routerPaths.codeLists.units.create]: t('labels.create-unit'),

        [routerPaths.codeLists.customer.update]: t('labels.update-customer'),
        [routerPaths.codeLists.customer.index]: customer?.data.name || '',
        [routerPaths.codeLists.customers.index]: t('labels.customers'),
        [routerPaths.codeLists.customers.create]: t('labels.create-customer'),

        [routerPaths.codeLists.tenant.update]: t('labels.update-tenant'),
        [routerPaths.codeLists.tenant.index]: tenant?.data.name || '',
        [routerPaths.codeLists.tenants.index]: t('labels.tenants'),
        [routerPaths.codeLists.tenants.create]: t('labels.create-tenant'),

        [routerPaths.codeLists.legislation.update]: t(
            'labels.update-legislation',
        ),
        [routerPaths.codeLists.legislation.index]: legislation?.data.name || '',
        [routerPaths.codeLists.legislations.index]: t('labels.legislations'),
        [routerPaths.codeLists.legislations.create]: t(
            'labels.create-legislation',
        ),
        [routerPaths.codeLists.tenantGroup.update]: t(
            'labels.update-tenant-group',
        ),
        [routerPaths.codeLists.tenantGroup.index]: tenantGroup?.data.name || '',
        [routerPaths.codeLists.tenantGroups.index]: t('labels.tenant-groups'),
        [routerPaths.codeLists.tenantGroups.create]: t(
            'labels.create-tenant-group',
        ),
    }

    return (
        <>
            <PageHead map={map} />

            <Layout
                logoUrl='/images/logos/default.png'
                breadcrumbsMap={map}
                sidebarItems={sidebarItems}
            />
        </>
    )
}

const CodeListsLayout = () => {
    return (
        <AuthenticatedGuard>
            <AppGuard name={ApplicationName.CODE_LIST}>
                <CodeListsLayoutContent />
            </AppGuard>
        </AuthenticatedGuard>
    )
}

export default CodeListsLayout
