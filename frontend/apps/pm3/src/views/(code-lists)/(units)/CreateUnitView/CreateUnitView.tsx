import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import createUnitRequest, {
    CreateUnitBody,
    CreateUnitBodyKeys,
} from '@/requests/unit/createUnitRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
}

const CreateUnitView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { mutate, isPending } = useMutation({
        mutationFn: createUnitRequest,
        onSuccess: async ({ data }) => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.units.index,
            })

            const href = createHrefWithParams(
                routerPaths.codeLists.unit.index,
                {
                    unitId: data.id,
                },
            )

            toast.success(t('texts.success-action'))
            navigate(href)
        },
    })

    const initialValues: CreateUnitBody = {
        [CreateUnitBodyKeys.CODE]: '',
        [CreateUnitBodyKeys.NAME]: '',
        [CreateUnitBodyKeys.ACTIVE]: true,
    }

    const validationSchema = yup.object().shape({
        [CreateUnitBodyKeys.NAME]: yup
            .string()
            .required('errors.required-field'),
        [CreateUnitBodyKeys.CODE]: yup
            .string()
            .required('errors.required-field'),
    })

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onSubmit = (body: CreateUnitBody) => {
        mutate(body)
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isPending}>
                {step === StepName.BASIC_INFORMATION
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default CreateUnitView
