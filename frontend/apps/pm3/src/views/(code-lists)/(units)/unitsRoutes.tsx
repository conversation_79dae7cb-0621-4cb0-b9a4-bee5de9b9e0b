import React from 'react'

import routerPaths from '@/config/routerPaths'

const UnitsView = React.lazy(() => import('./UnitsView/UnitsView'))

const UnitView = React.lazy(() => import('./UnitView/UnitView'))

const CreateUnitView = React.lazy(
    () => import('./CreateUnitView/CreateUnitView'),
)

const UpdateUnitView = React.lazy(
    () => import('./UpdateUnitView/UpdateUnitView'),
)

const unitsRoutes = [
    {
        path: routerPaths.codeLists.units.index,
        element: <UnitsView />,
    },
    {
        path: routerPaths.codeLists.unit.index,
        element: <UnitView />,
    },
    {
        path: routerPaths.codeLists.units.create,
        element: <CreateUnitView />,
    },
    {
        path: routerPaths.codeLists.unit.update,
        element: <UpdateUnitView />,
    },
]

export default unitsRoutes
