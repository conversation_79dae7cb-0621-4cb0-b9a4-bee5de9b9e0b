import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import { UpdateUnitBodyKeys } from '@/requests/unit/updateUnitRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='CONTRACT_NAME'
                name={UpdateUnitBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.code')}
                data-testid='CONTRACT_CODE'
                name={UpdateUnitBodyKeys.CODE}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={UpdateUnitBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
