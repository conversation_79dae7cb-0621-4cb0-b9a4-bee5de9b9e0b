import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getUnitRequest from '@/requests/unit/getUnitRequest'
import updateUnitRequest, {
    UpdateUnitBody,
    UpdateUnitBodyKeys,
} from '@/requests/unit/updateUnitRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
}

const UpdateUnitView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const unitId = parseParamInt(params.unitId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const {
        isPending: isUnitPending,
        data: unit,
        isError: isUnitError,
    } = useQuery({
        queryFn: () => getUnitRequest(unitId),
        queryKey: queryKeys.unit(unitId).index,
    })

    const onDetail = () => {
        const href = createHrefWithParams(routerPaths.codeLists.unit.index, {
            unitId,
        })

        navigate(href)
    }

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdateUnitBody) => updateUnitRequest(unitId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.unit(unitId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.units.index,
            })

            toast.success(t('texts.success-action'))
            onDetail()
        },
    })

    const initialValues: UpdateUnitBody = {
        [UpdateUnitBodyKeys.NAME]: unit?.data.name || '',
        [UpdateUnitBodyKeys.CODE]: unit?.data.code || '',
        [UpdateUnitBodyKeys.ACTIVE]: Boolean(unit?.data.active),
    }

    const validationSchema = yup.object().shape({
        [UpdateUnitBodyKeys.NAME]: yup
            .string()
            .required('errors.required-field'),
        [UpdateUnitBodyKeys.CODE]: yup
            .string()
            .required('errors.required-field'),
    })

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onSubmit = (body: UpdateUnitBody) => {
        mutate(body)
    }

    if (isUnitPending) return <LoadingSpinner />

    if (isUnitError) {
        return <Navigate to={routerPaths.codeLists.units.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.BASIC_INFORMATION
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateUnitView
