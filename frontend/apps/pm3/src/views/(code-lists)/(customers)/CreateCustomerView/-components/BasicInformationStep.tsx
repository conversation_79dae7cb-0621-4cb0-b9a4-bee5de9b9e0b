import Divider from '@mui/material/Divider'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useField, useFormikContext } from 'formik'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import GeocodingAsyncAutocomplete from '@/components/resource/geocoding/GeocodingAsyncAutocomplete'
import {
    CreateCustomerBody,
    CreateCustomerBodyKeys,
} from '@/requests/customer/createCustomerRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='NAME'
                name={CreateCustomerBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.id-number')}
                data-testid='ICO'
                name={CreateCustomerBodyKeys.CID}
            />

            <Divider sx={{ my: 1 }} />
            <Typography fontWeight={600}>{t('labels.address')}</Typography>
            <CustomerGeocodingAsyncAutocomplete />

            <FormTextField
                label={t('labels.city')}
                data-testid='CITY'
                name={CreateCustomerBodyKeys.CITY}
            />

            <FormTextField
                label={t('labels.country')}
                data-testid='COUNTRY'
                name={CreateCustomerBodyKeys.COUNTRY}
            />

            <FormTextField
                label={t('labels.postcode')}
                data-testid='POSTAL_CODE'
                name={CreateCustomerBodyKeys.POSTAL_CODE}
            />
        </Stack>
    )
}

const CustomerGeocodingAsyncAutocomplete = () => {
    const { t } = useTranslation()

    const [field, , helpers] = useField(CreateCustomerBodyKeys.STREET)
    const { setValues } = useFormikContext<CreateCustomerBody>()

    return (
        <GeocodingAsyncAutocomplete
            searchedValue={field.value}
            label={t('labels.street')}
            onClose={(searchedValue) => {
                helpers.setValue(searchedValue)
            }}
            onChange={(item, original, searchedValue) => {
                setValues((prevValues) => ({
                    ...prevValues,
                    city: item.city || '',
                    country: item.country || '',
                    postalCode: item.postcode || '',
                    street: item.street || searchedValue,
                    coordinates: `${original.position.lat},${original.position.lon}`,
                }))
            }}
        />
    )
}

export default BasicInformationStep
