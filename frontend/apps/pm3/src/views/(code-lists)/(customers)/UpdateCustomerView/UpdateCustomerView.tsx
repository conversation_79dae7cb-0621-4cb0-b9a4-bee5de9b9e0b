import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getCustomerRequest from '@/requests/customer/getCustomerRequest'
import updateCustomerRequest, {
    UpdateCustomerBody,
    UpdateCustomerBodyKeys,
} from '@/requests/customer/updateCustomerRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
    FACILITIES,
}

const UpdateCustomerView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const customerId = parseParamInt(params.customerId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const {
        isPending: isCustomerPending,
        data: customer,
        isError,
    } = useQuery({
        queryFn: () => getCustomerRequest(customerId),
        queryKey: queryKeys.customer(customerId).index,
    })

    const onDetail = () => {
        const href = createHrefWithParams(
            routerPaths.codeLists.customer.index,
            {
                customerId,
            },
        )

        navigate(href)
    }

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdateCustomerBody) =>
            updateCustomerRequest(customerId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.customer(customerId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.customers.index,
            })

            toast.success(t('texts.success-action'))
            onDetail()
        },
    })

    const initialValues: UpdateCustomerBody = {
        [UpdateCustomerBodyKeys.NAME]: customer?.data.name || '',
        [UpdateCustomerBodyKeys.CID]: customer?.data.cid || '',
        [UpdateCustomerBodyKeys.COUNTRY]: customer?.data.country || '',
        [UpdateCustomerBodyKeys.CITY]: customer?.data.city || '',
        [UpdateCustomerBodyKeys.STREET]: customer?.data.street || '',
        [UpdateCustomerBodyKeys.COORDINATES]: customer?.data.coordinates || '',
        [UpdateCustomerBodyKeys.POSTAL_CODE]: customer?.data.postal_code || '',
        [UpdateCustomerBodyKeys.FACILITIES]: _.map(
            customer?.data.facilities,
            (f) => f.id,
        ),
    }

    const validationSchema = React.useMemo(
        () =>
            yup.object().shape({
                [UpdateCustomerBodyKeys.NAME]:
                    step === StepName.BASIC_INFORMATION
                        ? yup.string().required('errors.required-field')
                        : yup.string(),
                [UpdateCustomerBodyKeys.CID]:
                    step === StepName.BASIC_INFORMATION
                        ? yup.string().required('errors.required-field')
                        : yup.string(),
            }),
        [step],
    )

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: UpdateCustomerBody) => {
        if (step !== StepName.FACILITIES) {
            onNext()
            return
        }

        mutate(body)
    }

    if (isCustomerPending) return <LoadingSpinner />

    if (isError) {
        return <Navigate to={routerPaths.codeLists.customers.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.objects')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.FACILITIES
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            maxWidth={1100}
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.FACILITIES && (
                    <FormFacilitiesAssigner
                        name={UpdateCustomerBodyKeys.FACILITIES}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateCustomerView
