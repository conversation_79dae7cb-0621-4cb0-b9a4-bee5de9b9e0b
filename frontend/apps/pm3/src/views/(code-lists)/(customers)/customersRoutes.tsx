import React from 'react'

import routerPaths from '@/config/routerPaths'

const CustomersView = React.lazy(() => import('./CustomersView/CustomersView'))

const CustomerView = React.lazy(() => import('./CustomerView/CustomerView'))

const CreateCustomerView = React.lazy(
    () => import('./CreateCustomerView/CreateCustomerView'),
)

const UpdateCustomerView = React.lazy(
    () => import('./UpdateCustomerView/UpdateCustomerView'),
)

const customersRoutes = [
    {
        path: routerPaths.codeLists.customers.index,
        element: <CustomersView />,
    },
    {
        path: routerPaths.codeLists.customer.index,
        element: <CustomerView />,
    },
    {
        path: routerPaths.codeLists.customers.create,
        element: <CreateCustomerView />,
    },
    {
        path: routerPaths.codeLists.customer.update,
        element: <UpdateCustomerView />,
    },
]

export default customersRoutes
