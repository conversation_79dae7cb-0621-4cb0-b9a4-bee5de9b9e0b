import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import MapsHomeWorkOutlinedIcon from '@mui/icons-material/MapsHomeWorkOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import CircularProgress from '@mui/material/CircularProgress'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import { InformationRow } from '@/components/other/InformationRow'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import deleteCustomerRequest from '@/requests/customer/deleteCustomerRequest'
import getCustomerRequest from '@/requests/customer/getCustomerRequest'

import FacilitiesTab from './-components/FacilitiesTab'
import { CustomerViewTabName } from './types'

const BasicInformationTab = React.lazy(
    () => import('./-components/BasicInformationTab'),
)

interface CustomerViewContentProps {
    tab: CustomerViewTabName
    onTab: (tab: CustomerViewTabName) => void
}

const CustomerViewContent = ({ onTab, tab }: CustomerViewContentProps) => {
    const queryClient = useQueryClient()
    const navigate = useNavigate()

    const { t } = useTranslation('common')

    const { spacing, palette } = useTheme()
    const params = useParams<'customerId'>()
    const customerId = parseParamInt(params.customerId)

    const [deleteOpen, setDeleteOpen] = React.useState(false)

    const {
        isPending,
        data: customer,
        isError,
    } = useQuery({
        queryFn: () => getCustomerRequest(customerId),
        queryKey: queryKeys.customer(customerId).index,
    })

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: () => deleteCustomerRequest(customerId),
        onSuccess: async () => {
            toast.success(t('texts.success-action'))
            navigate(routerPaths.codeLists.companies.index)

            await queryClient.invalidateQueries({
                queryKey: queryKeys.companies.index,
            })
        },
    })

    const onUpdate = React.useCallback(() => {
        const href = createHrefWithParams(
            routerPaths.codeLists.customer.update,
            {
                customerId,
            },
        )

        navigate(href)
    }, [navigate, customerId])

    if (isPending) return <LoadingSpinner />

    if (isError) {
        return <Navigate to={routerPaths.codeLists.companies.index} />
    }

    return (
        <Stack gap={1} height='100%'>
            <Card sx={{ flex: 'none' }}>
                <CardContent
                    sx={{
                        px: 1,
                        gap: 1.5,
                        display: 'flex',
                        flexWrap: 'wrap',
                        alignItems: 'flex-start',
                        py: `${spacing(1.5)} !important`,
                    }}
                >
                    <InformationRow
                        title={t('labels.name')}
                        sx={{ mr: 'auto' }}
                    >
                        <Typography fontWeight={700}>
                            {customer.data.name}
                        </Typography>
                    </InformationRow>

                    <IconButtonDropdown
                        icon={
                            isDeletePending ? (
                                <CircularProgress size={20} />
                            ) : (
                                <MoreVert fontSize='small' />
                            )
                        }
                        menuContent={({ onClose }) => (
                            <Stack>
                                <MenuItem
                                    onClick={onUpdate}
                                    data-testid='RECORD_UPDATE'
                                >
                                    {t('actions.update')}
                                </MenuItem>

                                <MenuItem
                                    onClick={() => {
                                        onClose()
                                        setDeleteOpen(true)
                                    }}
                                    data-testid='RECORD_DELETE'
                                    sx={{
                                        color: `${palette.error.main} !important`,
                                    }}
                                >
                                    {t('actions.delete')}
                                </MenuItem>
                            </Stack>
                        )}
                    />
                </CardContent>
            </Card>

            <Stack flex={1} overflow='hidden'>
                <Tabs
                    value={tab}
                    onChange={(__, value) => {
                        onTab(value)
                    }}
                    sx={{
                        width: 'max-content',
                        background: palette.grey[50],
                        borderTopLeftRadius: spacing(2),
                        borderTopRightRadius: spacing(2),
                    }}
                >
                    <Tab
                        value={CustomerViewTabName.BASIC_INFORMATION}
                        label={t('labels.information')}
                        icon={<InfoOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />

                    <Tab
                        value={CustomerViewTabName.FACILITIES}
                        label={t('labels.objects')}
                        icon={
                            <MapsHomeWorkOutlinedIcon sx={{ fontSize: 20 }} />
                        }
                        iconPosition='start'
                    />
                </Tabs>

                <React.Suspense fallback={<LoadingSpinner />}>
                    {tab === CustomerViewTabName.BASIC_INFORMATION && (
                        <BasicInformationTab customer={customer.data} />
                    )}

                    {tab === CustomerViewTabName.FACILITIES && (
                        <FacilitiesTab customer={customer.data} />
                    )}
                </React.Suspense>
            </Stack>

            {deleteOpen && (
                <ConfirmationDialog
                    isLoading={isDeletePending}
                    onClose={() => setDeleteOpen(false)}
                    onConfirm={() => mutate()}
                    confirmLabel={t('actions.delete')}
                >
                    <Typography>
                        {t('texts.delete-customer-confirmation')}
                    </Typography>
                </ConfirmationDialog>
            )}
        </Stack>
    )
}

const CustomerView = () => {
    return (
        <TabGuard
            validValues={_.values(CustomerViewTabName)}
            defaultTab={CustomerViewTabName.BASIC_INFORMATION}
        >
            {(props) => <CustomerViewContent {...props} />}
        </TabGuard>
    )
}

export default CustomerView
