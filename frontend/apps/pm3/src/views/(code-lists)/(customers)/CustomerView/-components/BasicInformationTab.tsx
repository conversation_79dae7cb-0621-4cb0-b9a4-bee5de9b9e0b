import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined'
import HomeIcon from '@mui/icons-material/Home'
import NumbersIcon from '@mui/icons-material/Numbers'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import { InformationRow } from '@/components/other/InformationRow'
import { CustomerResponse } from '@/requests/responses'
import { formatAddress } from '@/utils/format'

interface BasicInformationTabProps {
    customer: CustomerResponse
}

const BasicInformationTab = ({ customer }: BasicInformationTabProps) => {
    const { t } = useTranslation('common')

    return (
        <Stack direction='row' flex={1}>
            <Card sx={{ borderTopLeftRadius: 0, width: '100%' }}>
                <CardContent
                    sx={{
                        gap: 3,
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Typography fontWeight={600}>
                        {t('labels.basic-information')}
                    </Typography>

                    <InformationRow
                        startIcon={<BadgeOutlinedIcon />}
                        title={t('labels.name')}
                    >
                        <Typography>{customer.name}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<NumbersIcon />}
                        title={t('labels.id-number')}
                    >
                        <Typography>{customer.cid}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<HomeIcon />}
                        title={t('labels.address')}
                    >
                        <Typography>{formatAddress(customer)}</Typography>
                    </InformationRow>
                </CardContent>
            </Card>
        </Stack>
    )
}

export default BasicInformationTab
