import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form } from 'formik'
import * as _ from 'lodash'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import updateCustomerRequest, {
    UpdateCustomerBody,
    UpdateCustomerBodyKeys,
} from '@/requests/customer/updateCustomerRequest'
import { CustomerResponse } from '@/requests/responses'

interface FacilitiesTabProps {
    customer: CustomerResponse
}

const FacilitiesTab = ({ customer }: FacilitiesTabProps) => {
    const { t } = useTranslation()
    const queryClient = useQueryClient()

    const { mutate, isPending } = useMutation({
        mutationFn: (body: UpdateCustomerBody) =>
            updateCustomerRequest(customer.id, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.customer(customer.id).index,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const initialValues: UpdateCustomerBody = {
        [UpdateCustomerBodyKeys.NAME]: customer.name || '',
        [UpdateCustomerBodyKeys.CID]: customer.cid || '',
        [UpdateCustomerBodyKeys.COUNTRY]: customer.country || '',
        [UpdateCustomerBodyKeys.CITY]: customer.city || '',
        [UpdateCustomerBodyKeys.STREET]: customer.street || '',
        [UpdateCustomerBodyKeys.COORDINATES]: customer.coordinates || '',
        [UpdateCustomerBodyKeys.POSTAL_CODE]: customer.postal_code || '',
        [UpdateCustomerBodyKeys.FACILITIES]: _.map(
            customer.facilities,
            (f) => f.id,
        ),
    }

    const onSubmit = (body: UpdateCustomerBody) => {
        mutate(body)
    }

    return (
        <BlockingFormik initialValues={initialValues} onSubmit={onSubmit}>
            {({ resetForm }) => (
                <Card
                    component={Form}
                    sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        borderTopLeftRadius: 0,
                    }}
                >
                    <CardContent
                        sx={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            overflow: 'hidden',
                        }}
                    >
                        <FormFacilitiesAssigner
                            name={UpdateCustomerBodyKeys.FACILITIES}
                        />
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'space-between' }}>
                        <Button
                            type='button'
                            color='inherit'
                            onClick={() => resetForm()}
                        >
                            {t('actions.discard-changes')}
                        </Button>

                        <SubmitButton loading={isPending}>
                            {t('actions.save-changes')}
                        </SubmitButton>
                    </CardActions>
                </Card>
            )}
        </BlockingFormik>
    )
}

export default FacilitiesTab
