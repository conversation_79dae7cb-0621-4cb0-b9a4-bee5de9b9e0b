import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import {
    keepPreviousData,
    useMutation,
    useQuery,
    useQueryClient,
} from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import CellLink from '@/components/other/CellLink'
import DataGridHeader from '@/components/other/DataGridHeader'
import { gridFilterMap } from '@/config/gridFilter'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import usePersistTable from '@/hooks/usePersistTable'
import deleteCustomerRequest from '@/requests/customer/deleteCustomerRequest'
import getCustomersRequest, {
    GetCustomersParams,
} from '@/requests/customer/getCustomersRequest'
import { CustomerResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'
import { formatAddress } from '@/utils/format'

const CustomersTable = () => {
    const { palette, breakpoints } = useTheme()
    const navigate = useNavigate()
    const gridRef = useGridApiRef()
    const queryClient = useQueryClient()

    const { t } = useTranslation('common')

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [customerForDelete, setCustomerForDelete] =
        React.useState<null | CustomerResponse>(null)

    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSort()
    const { gridStringOperators } = useGridOperators()

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: (customerId: number) => deleteCustomerRequest(customerId),
        onSuccess: async () => {
            setCustomerForDelete(null)

            await queryClient.invalidateQueries({
                queryKey: queryKeys.customers.index,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const params = createRequestParams<GetCustomersParams>({
        sort,
        filter,
        pagination,
        search: debouncedSearch,
    })

    const { data: customers, isFetching } = useQuery({
        queryFn: () => getCustomersRequest(params),
        queryKey: [...queryKeys.customers.index, params],
        placeholderData: keepPreviousData,
    })

    const onDetail = React.useCallback(
        (customerId: number) => {
            const href = createHrefWithParams(
                routerPaths.codeLists.customer.index,
                {
                    customerId,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const onUpdate = React.useCallback(
        (customerId: number) => {
            const href = createHrefWithParams(
                routerPaths.codeLists.customer.update,
                {
                    customerId,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const onCreate = () => {
        navigate(routerPaths.codeLists.customers.create)
    }

    const columns = React.useMemo<GridColDef<CustomerResponse>[]>(
        () => [
            {
                minWidth: 240,
                field: 'name',
                filterOperators: gridStringOperators(),
                headerName: t('labels.name'),
                renderCell: ({ row }) => {
                    const href = createHrefWithParams(
                        routerPaths.codeLists.customer.index,
                        {
                            customerId: row.id,
                        },
                    )

                    return <CellLink label={row.name} to={href} />
                },
            },
            {
                minWidth: 140,
                field: 'cid',
                filterOperators: gridStringOperators(),
                headerName: t('labels.id-number'),
            },
            {
                minWidth: 300,
                field: 'address',
                filterable: false,
                sortable: false,
                headerName: t('labels.address'),
                valueGetter: (_, row) => formatAddress(row),
            },
            {
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: ({ row }) => (
                    <IconButtonDropdown
                        icon={<MoreVert fontSize='small' />}
                        menuContent={({ onClose }) => (
                            <Stack>
                                <MenuItem
                                    onClick={() => onDetail(row.id)}
                                    data-testid='RECORD_DETAIL'
                                >
                                    {t('actions.show-detail')}
                                </MenuItem>

                                <MenuItem
                                    onClick={() => onUpdate(row.id)}
                                    data-testid='RECORD_UPDATE'
                                >
                                    {t('actions.update')}
                                </MenuItem>

                                <MenuItem
                                    onClick={() => {
                                        setCustomerForDelete(row)
                                        onClose()
                                    }}
                                    data-testid='RECORD_DELETE'
                                    sx={{
                                        color: `${palette.error.main} !important`,
                                    }}
                                >
                                    {t('actions.delete')}
                                </MenuItem>
                            </Stack>
                        )}
                    />
                ),
            },
        ],
        [gridStringOperators, t, palette.error.main, onDetail, onUpdate],
    )

    usePersistTable(gridRef, routerPaths.codeLists.customers.index)

    return (
        <>
            <Card sx={{ height: '100%', width: '100%' }}>
                <CardContent
                    sx={{
                        p: '0 !important',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <DataGridHeader
                        gridRef={gridRef}
                        onSearch={setSearch}
                        search={search}
                        actions={
                            <Button
                                data-testid='CREATE_NEW'
                                startIcon={<AddCircleOutlineOutlined />}
                                onClick={onCreate}
                            >
                                {t('actions.create-customer')}
                            </Button>
                        }
                    />

                    <DataGridPro
                        disableColumnPinning
                        pagination
                        sx={{
                            height: '100%',
                            minHeight: 150,
                            [breakpoints.up('lg')]: {
                                minHeight: 400,
                            },
                        }}
                        apiRef={gridRef}
                        columns={columns}
                        loading={isFetching}
                        paginationMode='server'
                        sortingMode='server'
                        filterMode='server'
                        disableColumnSelector
                        paginationModel={paginationModel}
                        filterModel={filterModel}
                        onPaginationModelChange={setPaginationModel}
                        onFilterModelChange={(model) => setFilterModel(model)}
                        sortModel={sortModel}
                        onSortModelChange={(model) => setSortModel(model)}
                        rows={customers?.list || []}
                        rowCount={customers?.total_count || 0}
                        initialState={{
                            density: 'compact',
                            pinnedColumns: {
                                right: ['actions'],
                            },
                        }}
                    />
                </CardContent>
            </Card>

            {customerForDelete && (
                <ConfirmationDialog
                    isLoading={isDeletePending}
                    onClose={() => setCustomerForDelete(null)}
                    onConfirm={() => mutate(customerForDelete.id)}
                    confirmLabel={t('actions.delete')}
                >
                    <Typography>
                        {t('texts.delete-customer-confirmation')}
                    </Typography>
                </ConfirmationDialog>
            )}
        </>
    )
}

export default CustomersTable
