import AbcOutlinedIcon from '@mui/icons-material/AbcOutlined'
import AlternateEmailIcon from '@mui/icons-material/AlternateEmail'
import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined'
import HomeIcon from '@mui/icons-material/Home'
import NoteIcon from '@mui/icons-material/Note'
import NumbersIcon from '@mui/icons-material/Numbers'
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined'
import ToggleOnIcon from '@mui/icons-material/ToggleOn'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import { InformationRow } from '@/components/other/InformationRow'
import StatusChip from '@/components/other/StatusChip'
import { CompanyResponse } from '@/requests/responses'
import { formatAddress } from '@/utils/format'

interface BasicInformationTabProps {
    company: CompanyResponse
}

const BasicInformationTab = ({ company }: BasicInformationTabProps) => {
    const { t } = useTranslation('common')

    return (
        <Stack direction='row' flex={1}>
            <Card sx={{ borderTopLeftRadius: 0, width: '100%' }}>
                <CardContent
                    sx={{
                        gap: 3,
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Typography fontWeight={600}>
                        {t('labels.basic-information')}
                    </Typography>

                    <InformationRow
                        startIcon={<BadgeOutlinedIcon />}
                        title={t('labels.name')}
                    >
                        <Typography>{company.name}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<AbcOutlinedIcon />}
                        title={t('labels.abbreviation')}
                    >
                        <Typography>{company.abbr}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<TextSnippetOutlinedIcon />}
                        title={t('labels.code')}
                    >
                        <Typography>{company.code}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<NumbersIcon />}
                        title={t('labels.id-number')}
                    >
                        <Typography>{company.cid}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<ToggleOnIcon />}
                        title={t('labels.status')}
                    >
                        <StatusChip isActive={company.active} />
                    </InformationRow>

                    <InformationRow
                        startIcon={<NoteIcon />}
                        title={t('labels.note')}
                    >
                        <Typography>{company.note || '-'}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<HomeIcon />}
                        title={t('labels.address')}
                    >
                        <Typography>{formatAddress(company)}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<AlternateEmailIcon />}
                        title={t('labels.contact-email')}
                    >
                        <Typography>{company.contact_email || '-'}</Typography>
                    </InformationRow>
                </CardContent>
            </Card>
        </Stack>
    )
}

export default BasicInformationTab
