import Divider from '@mui/material/Divider'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useField, useFormikContext } from 'formik'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import GeocodingAsyncAutocomplete from '@/components/resource/geocoding/GeocodingAsyncAutocomplete'
import {
    UpdateCompanyBody,
    UpdateCompanyBodyKeys,
} from '@/requests/company/updateCompanyRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='NAME'
                name={UpdateCompanyBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.abbreviation')}
                data-testid='ABBREV'
                name={UpdateCompanyBodyKeys.ABBR}
            />

            <FormTextField
                label={t('labels.code')}
                data-testid='CODE'
                name={UpdateCompanyBodyKeys.CODE}
            />

            <FormTextField
                label={t('labels.id-number')}
                data-testid='ICO'
                name={UpdateCompanyBodyKeys.ICO}
            />

            <FormTextField
                type='email'
                label={t('labels.contact-email')}
                data-testid='EMAIL'
                name={UpdateCompanyBodyKeys.CONTACT_EMAIL}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={UpdateCompanyBodyKeys.ACTIVE}
            />

            <FormTextField
                multiline
                minRows={3}
                data-testid='NOTE'
                label={t('labels.note')}
                name={UpdateCompanyBodyKeys.NOTE}
            />

            <Divider sx={{ my: 1 }} />
            <Typography fontWeight={600}>{t('labels.address')}</Typography>
            <CompanyGeocodingAsyncAutocomplete />

            <FormTextField
                label={t('labels.city')}
                data-testid='CITY'
                name={UpdateCompanyBodyKeys.CITY}
            />

            <FormTextField
                label={t('labels.country')}
                data-testid='COUNTRY'
                name={UpdateCompanyBodyKeys.COUNTRY}
            />

            <FormTextField
                label={t('labels.postcode')}
                data-testid='POSTAL_CODE'
                name={UpdateCompanyBodyKeys.POSTAL_CODE}
            />

            <FormTextField
                label={t('labels.street')}
                data-testid='STREET'
                name={UpdateCompanyBodyKeys.STREET}
            />
        </Stack>
    )
}

const CompanyGeocodingAsyncAutocomplete = () => {
    const { t } = useTranslation()

    const [field, , helpers] = useField(UpdateCompanyBodyKeys.STREET)
    const { setValues } = useFormikContext<UpdateCompanyBody>()

    return (
        <GeocodingAsyncAutocomplete
            searchedValue={field.value}
            label={t('labels.street')}
            onClose={(searchedValue) => {
                helpers.setValue(searchedValue)
            }}
            onChange={(item, original, searchedValue) => {
                setValues((prevValues) => ({
                    ...prevValues,
                    city: item.city || '',
                    country: item.country || '',
                    postalCode: item.postcode || '',
                    street: item.street || searchedValue,
                    coordinates: `${original.position.lat},${original.position.lon}`,
                    originalResponse: original,
                }))
            }}
        />
    )
}

export default BasicInformationStep
