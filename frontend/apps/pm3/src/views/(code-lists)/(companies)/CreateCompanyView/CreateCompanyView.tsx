import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import createCompanyRequest, {
    CreateCompanyBody,
    CreateCompanyBodyKeys,
} from '@/requests/company/createCompanyRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
}

const CreateCompanyView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { mutate, isPending } = useMutation({
        mutationFn: createCompanyRequest,
        onSuccess: async ({ data }) => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.companies.index,
            })

            const href = createHrefWithParams(
                routerPaths.codeLists.company.index,
                {
                    companyId: data.id,
                },
            )

            toast.success(t('texts.success-action'))
            navigate(href)
        },
    })

    const initialValues: CreateCompanyBody = {
        [CreateCompanyBodyKeys.NAME]: '',
        [CreateCompanyBodyKeys.ACTIVE]: true,
        [CreateCompanyBodyKeys.ABBR]: '',
        [CreateCompanyBodyKeys.CODE]: '',
        [CreateCompanyBodyKeys.ICO]: '',
        [CreateCompanyBodyKeys.NOTE]: '',
        [CreateCompanyBodyKeys.COUNTRY]: '',
        [CreateCompanyBodyKeys.CITY]: '',
        [CreateCompanyBodyKeys.STREET]: '',
        [CreateCompanyBodyKeys.POSTAL_CODE]: '',
        [CreateCompanyBodyKeys.CONTACT_EMAIL]: '',
        [CreateCompanyBodyKeys.ORIGINAL_RESPONSE]: {},
    }

    const validationSchema = yup.object().shape({
        [CreateCompanyBodyKeys.NAME]: yup
            .string()
            .required('errors.required-field'),
        [CreateCompanyBodyKeys.ABBR]: yup
            .string()
            .required('errors.required-field'),
        [CreateCompanyBodyKeys.CODE]: yup
            .string()
            .required('errors.required-field'),
        [CreateCompanyBodyKeys.ICO]: yup
            .string()
            .required('errors.required-field'),
    })

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onSubmit = (body: CreateCompanyBody) => {
        mutate(body)
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isPending}>
                {step === StepName.BASIC_INFORMATION
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default CreateCompanyView
