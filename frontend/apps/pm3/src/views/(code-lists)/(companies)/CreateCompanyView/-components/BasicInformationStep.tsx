import Divider from '@mui/material/Divider'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useField, useFormikContext } from 'formik'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import GeocodingAsyncAutocomplete from '@/components/resource/geocoding/GeocodingAsyncAutocomplete'
import {
    CreateCompanyBody,
    CreateCompanyBodyKeys,
} from '@/requests/company/createCompanyRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='NAME'
                name={CreateCompanyBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.abbreviation')}
                data-testid='ABBREV'
                name={CreateCompanyBodyKeys.ABBR}
            />

            <FormTextField
                label={t('labels.code')}
                data-testid='CODE'
                name={CreateCompanyBodyKeys.CODE}
            />

            <FormTextField
                label={t('labels.id-number')}
                data-testid='ICO'
                name={CreateCompanyBodyKeys.ICO}
            />

            <FormTextField
                type='email'
                label={t('labels.contact-email')}
                data-testid='EMAIL'
                name={CreateCompanyBodyKeys.CONTACT_EMAIL}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={CreateCompanyBodyKeys.ACTIVE}
            />

            <FormTextField
                multiline
                minRows={3}
                label={t('labels.note')}
                data-testid='NOTE'
                name={CreateCompanyBodyKeys.NOTE}
            />

            <Divider sx={{ my: 1 }} />
            <Typography fontWeight={600}>{t('labels.address')}</Typography>
            <CompanyGeocodingAsyncAutocomplete />

            <FormTextField
                label={t('labels.city')}
                data-testid='CITY'
                name={CreateCompanyBodyKeys.CITY}
            />

            <FormTextField
                label={t('labels.country')}
                data-testid='COUNTRY'
                name={CreateCompanyBodyKeys.COUNTRY}
            />

            <FormTextField
                label={t('labels.postcode')}
                data-testid='POSTAL_CODE'
                name={CreateCompanyBodyKeys.POSTAL_CODE}
            />
        </Stack>
    )
}

const CompanyGeocodingAsyncAutocomplete = () => {
    const { t } = useTranslation()

    const [field, , helpers] = useField(CreateCompanyBodyKeys.STREET)
    const { setValues } = useFormikContext<CreateCompanyBody>()

    return (
        <GeocodingAsyncAutocomplete
            searchedValue={field.value}
            label={t('labels.street')}
            onClose={(searchedValue) => {
                helpers.setValue(searchedValue)
            }}
            onChange={(item, original, searchedValue) => {
                setValues((prevValues) => ({
                    ...prevValues,
                    city: item.city || '',
                    country: item.country || '',
                    postalCode: item.postcode || '',
                    street: item.street || searchedValue,
                    coordinates: `${original.position.lat},${original.position.lon}`,
                    originalResponse: original,
                }))
            }}
        />
    )
}

export default BasicInformationStep
