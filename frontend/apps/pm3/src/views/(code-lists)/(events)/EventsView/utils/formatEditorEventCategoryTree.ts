import * as _ from 'lodash'

import { EventCategoryTreeResponse } from '@/requests/responses'
import { formatEventCategoryName, formatTreeData } from '@/utils/format'
import { generateId } from '@/utils/generate'

import {
    EditorEventCategoryTree,
    EventHistoryItemState,
    EventHistoryItemType,
} from '../types'

/**
 * Recursively generates new IDs for all tree nodes and stores the original ID
 */
const regenerateTreeIds = (
    tree: EditorEventCategoryTree[],
    generatedIds: number[],
): EditorEventCategoryTree[] => {
    return tree.map((item) => {
        const id = generateId()
        generatedIds.push(id)

        return {
            ...item,
            id,
            originalId: item.id,
            children: item.children
                ? regenerateTreeIds(item.children, generatedIds)
                : [],
        }
    })
}

/**
 * Groups history items by type (created, updated, removed, moved)
 */
const groupHistoryByType = (historyItems: EventHistoryItemState[]) => {
    return historyItems.reduce(
        (historyMap, item) => {
            historyMap[item.type] = item
            return historyMap
        },
        {
            updated: undefined,
            removed: undefined,
            created: undefined,
            moved: undefined,
        } as Record<EventHistoryItemType, EventHistoryItemState | undefined>,
    )
}

/**
 * Processes a category with change history and updates its state
 */
const processCategoryWithHistory = (
    category: EditorEventCategoryTree,
    categoryHistoryItems: EventHistoryItemState[],
    removedIds: Set<number>,
) => {
    const historyByType = groupHistoryByType(categoryHistoryItems)

    // If the category was removed, add its ID to the list of removed IDs
    if (historyByType.removed) {
        removedIds.add(historyByType.removed.newState.id)
    }

    // Prioritize data from history (first created, then updated)
    const categoryWithUpdates =
        historyByType.created?.newState ||
        historyByType.updated?.newState ||
        category

    return {
        ...category,
        name: formatEventCategoryName(categoryWithUpdates),
        isCreated: Boolean(historyByType.created),
        isRemoved:
            Boolean(historyByType.removed) || removedIds.has(category.id),
    }
}

/**
 * Processes a category with moved items - from this category or to this category
 */
const processCategoryWithMovedItems = (
    category: EditorEventCategoryTree,
    movedFromThisCategory: EventHistoryItemState[],
    movedToThisCategory: EventHistoryItemState[],
    history: EventHistoryItemState[],
    movedIds: Set<number>,
): EditorEventCategoryTree => {
    const idsOfMovedItems = movedFromThisCategory.map(
        (historyItem) => historyItem.newState.id,
    )
    const generatedIds: number[] = []

    // Update children that were moved
    const updatedChildren: EditorEventCategoryTree[] = _.map(
        category.children,
        (child) => {
            if (idsOfMovedItems.includes(child.id)) {
                return {
                    ...child,
                    id: generateId(),
                    isMoved: true,
                    isRemoved: false,
                    originalId: child.id,
                    children: child.children
                        ? regenerateTreeIds(child.children, generatedIds)
                        : [],
                }
            }

            return child
        },
    )

    // Mark all moved items
    generatedIds.forEach((id) => movedIds.add(id))
    const existingChildrenIds = updatedChildren.map((child) => child.id)

    // Add items moved to this category
    for (const movedItem of movedToThisCategory) {
        if (existingChildrenIds.includes(movedItem.newState.id)) {
            continue
        }

        // Find all created items within the moved item
        const createdHistoryItems = history
            .filter(
                (historyItem) =>
                    Number(historyItem.newState.parent_id) ===
                    movedItem.newState.id,
            )
            .map((item) => item.newState)

        const newItem = {
            ...movedItem.newState,
            children: _.uniqBy(
                [
                    ..._.filter(
                        movedItem.newState.children,
                        (item) => !item.isCreated,
                    ),
                    ...createdHistoryItems,
                ],
                'id',
            ),
            parent_id: Number(movedItem.newState.parent_id),
            name: formatEventCategoryName(movedItem.newState),
        }

        updatedChildren.push(newItem)
    }

    return {
        ...category,
        children: updatedChildren,
    }
}

/**
 * Processes a category with newly created items
 */
const processCategoryWithCreatedItems = (
    category: EditorEventCategoryTree,
    createdInThisCategory: EventHistoryItemState[],
): EditorEventCategoryTree => {
    const newItems = createdInThisCategory.map((item) => item.newState)

    return {
        ...category,
        children: _.uniqBy([...(category.children || []), ...newItems], 'id'),
    }
}

/**
 * Function for formatting the event category tree with applied changes from history
 */
const formatEditorEventCategoryTree = (
    eventCategories: EventCategoryTreeResponse[],
    history: EventHistoryItemState[],
) => {
    const removedIds: Set<number> = new Set()
    const movedIds: Set<number> = new Set()

    return formatTreeData<EventCategoryTreeResponse, EditorEventCategoryTree>(
        eventCategories,
        (eventCategory) => {
            let category = { ...eventCategory } as EditorEventCategoryTree

            // If the parent of the category was removed, mark this category as removed too
            if (removedIds.has(Number(category.parent_id))) {
                removedIds.add(category.id)
            }

            // Find change history for this category
            const categoryHistoryItems = history.filter(
                (item) =>
                    Number(item.newState.id) === Number(category.id) ||
                    Number(item.newState.id) === Number(category.originalId),
            )

            // Process the category's change history
            if (categoryHistoryItems.length > 0) {
                category = processCategoryWithHistory(
                    category,
                    categoryHistoryItems,
                    removedIds,
                )
            }

            // Find items created in this category
            const createdInThisCategory = history.filter(
                (item) =>
                    Number(item.newState.parent_id) === category.id &&
                    item.type === EventHistoryItemType.CREATED,
            )

            // Process newly created items
            if (createdInThisCategory.length > 0) {
                category = processCategoryWithCreatedItems(
                    category,
                    createdInThisCategory,
                )
            }

            // Find items moved from this category
            const movedFromThisCategory = history.filter(
                (item) =>
                    Number(item.prevState?.parent_id) === category.id &&
                    item.type === EventHistoryItemType.MOVED,
            )

            // Find items moved to this category
            const movedToThisCategory = history.filter(
                (item) =>
                    Number(item.newState.parent_id) === category.id &&
                    item.type === EventHistoryItemType.MOVED,
            )

            // Process moved items
            if (
                movedFromThisCategory.length > 0 ||
                movedToThisCategory.length > 0
            ) {
                category = processCategoryWithMovedItems(
                    category,
                    movedFromThisCategory,
                    movedToThisCategory,
                    history,
                    movedIds,
                )
            }

            return {
                ...category,
                isMoved: category.isMoved || movedIds.has(category.id),
                isRemoved: category.isRemoved || removedIds.has(category.id),
                name: category.name || formatEventCategoryName(category),
            }
        },
    )
}

export default formatEditorEventCategoryTree
