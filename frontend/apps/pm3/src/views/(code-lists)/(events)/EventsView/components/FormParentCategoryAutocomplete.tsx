import AsyncAutocomplete from '@goodsailors/ui/components/inputs/AsyncAutocomplete'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { useField } from 'formik'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { DEFAULT_OPTIONS_LIMIT } from '@/config/common'
import queryKeys from '@/config/queryClient/queryKeys'
import getEventsCategoriesRequest, {
    GetEventsCategoriesParams,
} from '@/requests/event/getEventsCategoriesRequest'
import { FilterTypeName } from '@/requests/params'
import { EventCategorySeverityName } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'
import { formatEventCategoryName } from '@/utils/format'

interface FormParentCategoryAutocompleteProps {
    name: string
    severityName: string
}

const FormParentCategoryAutocomplete = ({
    name,
    severityName,
}: FormParentCategoryAutocompleteProps) => {
    const { t } = useTranslation()

    const [field, meta, helpers] = useField<number | null>(name)
    const [, severityMeta, severityHelpers] =
        useField<EventCategorySeverityName>(severityName)

    const [categoriesSearch, setCategoriesSearch, categoriesDebouncedSearch] =
        useDebouncedValue<string>('')

    const defaultCategoriesParams =
        createRequestParams<GetEventsCategoriesParams>({
            pagination: {
                limit: DEFAULT_OPTIONS_LIMIT,
                offset: 0,
            },
            filter: [
                {
                    field: 'id',
                    type: FilterTypeName.IN,
                    value: field.value ? [field.value] : [],
                },
            ],
        })

    const { data: defaultCategories, isPending: isPendingDefaultCategories } =
        useQuery({
            queryFn: () => getEventsCategoriesRequest(defaultCategoriesParams),
            queryKey: [...queryKeys.events.categories, defaultCategoriesParams],
        })

    const {
        data: categories,
        isLoading: isCategoriesLoading,
        isFetchingNextPage: isCategoriesFetchingNextPage,
        fetchNextPage: fetchCategoriesNextPage,
        hasNextPage: hasCategoriesNextPage,
    } = useInfiniteQuery({
        queryKey: [...queryKeys.events.categories, categoriesDebouncedSearch],
        queryFn: ({ pageParam = 0 }) => {
            const params = createRequestParams<GetEventsCategoriesParams>({
                pagination: {
                    limit: 10,
                    offset: pageParam,
                },
                search: categoriesDebouncedSearch || undefined,
            })

            return getEventsCategoriesRequest(params)
        },
        initialPageParam: 0,
        getNextPageParam: (lastPage, allPages) => {
            const totalLoaded = allPages.reduce(
                (count, page) => count + page.list.length,
                0,
            )
            return totalLoaded < lastPage.total_count ? totalLoaded : undefined
        },
    })

    const categoriesOptions = React.useMemo(() => {
        const defaultOptions = _.map(defaultCategories?.list, (category) => ({
            id: category.id,
            label: formatEventCategoryName(category),
        }))

        if (!categories?.pages) return defaultOptions

        const options = categories.pages.flatMap((page) =>
            page.list.map((category) => ({
                id: category.id,
                label: formatEventCategoryName(category),
            })),
        )

        return _.uniqBy([...defaultOptions, ...options], 'id')
    }, [categories, defaultCategories])

    const onChange = (value: number | string | null) => {
        const category = _.chain(categories?.pages || [])
            .flatMap((page) => page.list)
            .find((category) => category.id === value)
            .value()

        helpers.setValue(value as number | null)

        if (category && !severityMeta.touched) {
            severityHelpers.setValue(category.severity)
        }
    }

    const isError = meta.touched && Boolean(meta.error)
    const errorMessage = isError ? t(meta.error as never) : ''

    return (
        <AsyncAutocomplete
            label={t('labels.parent-category')}
            name={name}
            search={categoriesSearch}
            onSearch={setCategoriesSearch}
            options={categoriesOptions}
            hasNextPage={hasCategoriesNextPage}
            getNextPage={() => fetchCategoriesNextPage()}
            loadingNextPage={isCategoriesFetchingNextPage}
            loading={isCategoriesLoading || isPendingDefaultCategories}
            size='small'
            multiple={false}
            onChange={onChange}
            value={field.value as number | null}
            helperText={errorMessage}
            error={isError}
        />
    )
}

export default FormParentCategoryAutocomplete
