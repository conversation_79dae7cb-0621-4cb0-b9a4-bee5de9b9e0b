import { EventCategoryTreeResponse } from '@/requests/responses'

export enum EventHistoryItemType {
    CREATED = 'created',
    MOVED = 'moved',
    REMOVED = 'removed',
    UPDATED = 'updated',
}

export interface EventHistoryItemState {
    type: EventHistoryItemType
    newState: EditorEventCategoryTree
    prevState?: EditorEventCategoryTree
}

export interface EditorEventCategoryTree extends EventCategoryTreeResponse {
    name: string
    isMoved?: boolean
    isRemoved?: boolean
    isCreated?: boolean
    originalId?: number
    children: EditorEventCategoryTree[]
}
