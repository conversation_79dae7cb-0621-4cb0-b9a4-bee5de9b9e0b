import { TreeSelectProps } from '@goodsailors/ui/components/inputs/TreeSelect'
import TreeSelect from '@goodsailors/ui/components/inputs/TreeSelect'
import AddIcon from '@mui/icons-material/Add'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'
import FolderIcon from '@mui/icons-material/Folder'
import FolderOpenIcon from '@mui/icons-material/FolderOpen'
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import { TreeItemLabel } from '@mui/x-tree-view-pro'
import { useTranslation } from 'react-i18next'

import { EditorEventCategoryTree } from '../types'

interface EventsTreeSelectProps
    extends Pick<
        TreeSelectProps<EditorEventCategoryTree>,
        'search' | 'onSearch' | 'apiRef' | 'itemsReordering'
    > {
    data: EditorEventCategoryTree[]
    isLoading: boolean
    setCategoryForUpdate: (item: EditorEventCategoryTree) => void
    handleRemoveCategory: (item: EditorEventCategoryTree) => void
    handleMoveCategory: (
        prevCategory: EditorEventCategoryTree,
        newCategory: EditorEventCategoryTree,
        updatedTree: EditorEventCategoryTree[],
    ) => void
    handleCreateCategoryWithParentId: (id: number) => void
}

const EventsTreeSelect = ({
    data,
    search,
    isLoading,
    onSearch,
    apiRef,
    itemsReordering,
    handleMoveCategory,
    setCategoryForUpdate,
    handleRemoveCategory,
    handleCreateCategoryWithParentId,
}: EventsTreeSelectProps) => {
    const { palette } = useTheme()
    const { t } = useTranslation()

    return (
        <TreeSelect
            itemsReordering={itemsReordering}
            slotProps={{
                container: {
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    flex: 1,
                    pt: 1,
                    sx: {
                        overflowY: 'auto',
                        overflowX: 'hidden',
                    },
                },
                paper: {
                    sx: {
                        maxHeight: '100%',
                        flex: 1,

                        '& > div': {
                            background: '#fff',
                        },

                        '.MuiTreeItem-content:hover .item-actions': {
                            opacity: 1,
                        },
                    },
                },
            }}
            slots={{
                itemCollapseIcon: FolderOpenIcon,
                itemExpandIcon: FolderIcon,
            }}
            onChange={() => {}}
            isItemReorderable={(item) => {
                if (item.isRemoved) return false
                if (item.isMoved) return false

                return true
            }}
            canMoveItemToNewPosition={(__, newCategory) => {
                if (newCategory.isRemoved) return false
                if (newCategory.isMoved) return false

                return true
            }}
            onItemPositionChange={handleMoveCategory}
            value={null}
            search={search}
            apiRef={apiRef}
            onSearch={onSearch}
            label={t('labels.category')}
            data={data}
            isLoading={isLoading}
            renderItemLabel={(item, props) => (
                <>
                    <Box
                        display='flex'
                        sx={{
                            '.MuiSvgIcon-root': {
                                fontSize: 16,
                                color: `${palette.text.secondary} !important`,
                            },
                        }}
                    >
                        {item.is_hidden ? (
                            <VisibilityOffOutlinedIcon />
                        ) : (
                            <VisibilityOutlinedIcon />
                        )}
                    </Box>

                    <TreeItemLabel
                        {...props}
                        sx={{
                            textDecoration:
                                item.isMoved || item.isRemoved
                                    ? 'line-through'
                                    : undefined,
                            color: item.isRemoved
                                ? palette.error.main
                                : undefined,
                        }}
                    />

                    {!item.isRemoved && !item.isMoved && (
                        <Stack
                            gap={0.5}
                            direction='row'
                            className='item-actions'
                            sx={{
                                opacity: 0,

                                '.MuiSvgIcon-root': {
                                    fontSize: 20,
                                },

                                '.MuiIconButton-root': {
                                    borderRadius: 1,
                                    width: 24,
                                    height: 24,
                                },
                            }}
                        >
                            {!item.isCreated && (
                                <IconButton
                                    size='small'
                                    onClick={(event) => {
                                        event.stopPropagation()
                                        handleCreateCategoryWithParentId(
                                            item.id,
                                        )
                                    }}
                                >
                                    <AddIcon />
                                </IconButton>
                            )}

                            <IconButton
                                size='small'
                                onClick={(event) => {
                                    event.stopPropagation()
                                    setCategoryForUpdate(item)
                                }}
                            >
                                <EditIcon />
                            </IconButton>

                            <IconButton
                                size='small'
                                color='error'
                                onClick={(event) => {
                                    event.stopPropagation()
                                    handleRemoveCategory(item)
                                }}
                            >
                                <DeleteIcon />
                            </IconButton>
                        </Stack>
                    )}
                </>
            )}
        />
    )
}

export default EventsTreeSelect
