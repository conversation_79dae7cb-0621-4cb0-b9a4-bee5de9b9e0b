import React from 'react'

import routerPaths from '@/config/routerPaths'

const TenantGroupsView = React.lazy(
    () => import('./TenantGroupsView/TenantGroupsView'),
)

const TenantGroupView = React.lazy(
    () => import('./TenantGroupView/TenantGroupView'),
)

const CreateTenantGroupView = React.lazy(
    () => import('./CreateTenantGroupView/CreateTenantGroupView'),
)

const UpdateTenantGroupView = React.lazy(
    () => import('./UpdateTenantGroupView/UpdateTenantGroupView'),
)

const tenantGroupsRoutes = [
    {
        path: routerPaths.codeLists.tenantGroups.index,
        element: <TenantGroupsView />,
    },
    {
        path: routerPaths.codeLists.tenantGroup.index,
        element: <TenantGroupView />,
    },
    {
        path: routerPaths.codeLists.tenantGroups.create,
        element: <CreateTenantGroupView />,
    },
    {
        path: routerPaths.codeLists.tenantGroup.update,
        element: <UpdateTenantGroupView />,
    },
]

export default tenantGroupsRoutes
