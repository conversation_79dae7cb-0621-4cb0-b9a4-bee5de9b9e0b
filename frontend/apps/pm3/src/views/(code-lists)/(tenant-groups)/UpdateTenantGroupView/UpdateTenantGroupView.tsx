import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormTenantsAssigner from '@/components/resource/tenant/FormTenantsAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getTenantGroupRequest from '@/requests/tenantGroup/getTenantGroupRequest'
import updateTenantGroupRequest, {
    UpdateTenantGroupBody,
    UpdateTenantGroupBodyKeys,
} from '@/requests/tenantGroup/updateTenantGroupRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
    TENANTS,
}

const UpdateTenantGroupView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const tenantGroupId = parseParamInt(params.tenantGroupId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const {
        isPending: isTenantGroupPending,
        data: tenantGroup,
        isError: isTenantGroupError,
    } = useQuery({
        queryFn: () => getTenantGroupRequest(tenantGroupId),
        queryKey: queryKeys.tenantGroup(tenantGroupId).index,
    })

    const onDetail = () => {
        const href = createHrefWithParams(
            routerPaths.codeLists.tenantGroup.index,
            {
                tenantGroupId,
            },
        )

        navigate(href)
    }

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdateTenantGroupBody) =>
            updateTenantGroupRequest(tenantGroupId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenantGroup(tenantGroupId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenantGroups.index,
            })

            toast.success(t('texts.success-action'))
            onDetail()
        },
    })

    const initialValues: UpdateTenantGroupBody = {
        [UpdateTenantGroupBodyKeys.NAME]: tenantGroup?.data.name || '',
        [UpdateTenantGroupBodyKeys.ACTIVE]: Boolean(tenantGroup?.data.active),
        [UpdateTenantGroupBodyKeys.TENANTS]: _.map(
            tenantGroup?.data.tenants,
            (t) => t.id,
        ),
    }

    const validationSchema = React.useMemo(
        () =>
            yup.object().shape({
                [UpdateTenantGroupBodyKeys.NAME]:
                    step === StepName.BASIC_INFORMATION
                        ? yup.string().required('errors.required-field')
                        : yup.string(),
                [UpdateTenantGroupBodyKeys.TENANTS]:
                    step === StepName.TENANTS
                        ? yup.array().min(0, 'errors.required-field')
                        : yup.array(),
            }),
        [step],
    )

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: UpdateTenantGroupBody) => {
        if (step !== StepName.TENANTS) {
            onNext()
            return
        }

        mutate(body)
    }

    if (isTenantGroupPending) return <LoadingSpinner />

    if (isTenantGroupError || tenantGroup.data.read_only) {
        return <Navigate to={routerPaths.codeLists.tenantGroups.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.tenants')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step !== StepName.TENANTS
                    ? t('actions.continue')
                    : t('actions.save')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            maxWidth={1100}
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.TENANTS && (
                    <FormTenantsAssigner
                        name={UpdateTenantGroupBodyKeys.TENANTS}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateTenantGroupView
