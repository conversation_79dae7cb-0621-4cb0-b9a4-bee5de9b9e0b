import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import { UpdateTenantGroupBodyKeys } from '@/requests/tenantGroup/updateTenantGroupRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation()

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                data-testid='NAME'
                label={t('labels.name')}
                name={UpdateTenantGroupBodyKeys.NAME}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={UpdateTenantGroupBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
