import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import {
    keepPreviousData,
    useMutation,
    useQuery,
    useQueryClient,
} from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import CellLink from '@/components/other/CellLink'
import DataGridHeader from '@/components/other/DataGridHeader'
import StatusChip from '@/components/other/StatusChip'
import { gridFilterMap } from '@/config/gridFilter'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import usePersistTable from '@/hooks/usePersistTable'
import { TenantGroupResponse } from '@/requests/responses'
import deleteTenantGroupRequest from '@/requests/tenantGroup/deleteTenantGroupRequest'
import getTenantGroupsRequest, {
    GetTenantGroupsParams,
} from '@/requests/tenantGroup/getTenantGroupsRequest'
import createRequestParams from '@/utils/createRequestParams'

const TenantGroupsTable = () => {
    const { palette, breakpoints } = useTheme()
    const navigate = useNavigate()
    const gridRef = useGridApiRef()
    const queryClient = useQueryClient()

    const { t } = useTranslation('common')

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [tenantGroupForDelete, setTenantGroupForDelete] =
        React.useState<null | TenantGroupResponse>(null)

    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSort()
    const { gridStringOperators } = useGridOperators()

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: (tenantGroupId: number) =>
            deleteTenantGroupRequest(tenantGroupId),
        onSuccess: async () => {
            setTenantGroupForDelete(null)

            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenantGroups.index,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const params = createRequestParams<GetTenantGroupsParams>({
        sort,
        filter,
        pagination,
        search: debouncedSearch,
    })

    const { data: tenantGroups, isFetching } = useQuery({
        queryFn: () => getTenantGroupsRequest(params),
        queryKey: [...queryKeys.tenantGroups.index, params],
        placeholderData: keepPreviousData,
    })

    const onDetail = React.useCallback(
        (tenantGroupId: number) => {
            const href = createHrefWithParams(
                routerPaths.codeLists.tenantGroup.index,
                {
                    tenantGroupId,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const onUpdate = React.useCallback(
        (tenantGroupId: number) => {
            const href = createHrefWithParams(
                routerPaths.codeLists.tenantGroup.update,
                {
                    tenantGroupId,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const onCreate = () => {
        navigate(routerPaths.codeLists.tenantGroups.create)
    }

    const columns = React.useMemo<GridColDef<TenantGroupResponse>[]>(
        () => [
            {
                minWidth: 320,
                field: 'name',
                filterOperators: gridStringOperators(),
                headerName: t('labels.name'),
                renderCell: ({ row }) => {
                    const href = createHrefWithParams(
                        routerPaths.codeLists.tenantGroup.index,
                        {
                            tenantGroupId: row.id,
                        },
                    )

                    return <CellLink label={row.name} to={href} />
                },
            },
            {
                minWidth: 140,
                field: 'read_only',
                type: 'boolean',
                headerName: t('labels.is-readonly'),
            },
            {
                minWidth: 140,
                field: 'active',
                type: 'boolean',
                sortable: false,
                headerName: t('labels.status'),
                renderCell: ({ row }) => {
                    return <StatusChip isActive={row.active} />
                },
            },
            {
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: ({ row }) => (
                    <IconButtonDropdown
                        icon={<MoreVert fontSize='small' />}
                        menuContent={({ onClose }) => (
                            <Stack>
                                <MenuItem
                                    onClick={() => onDetail(row.id)}
                                    data-testid='RECORD_DETAIL'
                                >
                                    {t('actions.show-detail')}
                                </MenuItem>

                                {!row.read_only && (
                                    <MenuItem
                                        onClick={() => onUpdate(row.id)}
                                        data-testid='RECORD_UPDATE'
                                    >
                                        {t('actions.update')}
                                    </MenuItem>
                                )}

                                {!row.read_only && (
                                    <MenuItem
                                        onClick={() => {
                                            setTenantGroupForDelete(row)
                                            onClose()
                                        }}
                                        data-testid='RECORD_DELETE'
                                        sx={{
                                            color: `${palette.error.main} !important`,
                                        }}
                                    >
                                        {t('actions.delete')}
                                    </MenuItem>
                                )}
                            </Stack>
                        )}
                    />
                ),
            },
        ],
        [gridStringOperators, t, palette.error.main, onDetail, onUpdate],
    )

    usePersistTable(gridRef, routerPaths.codeLists.tenantGroups.index)

    return (
        <>
            <Card sx={{ height: '100%', width: '100%' }}>
                <CardContent
                    sx={{
                        p: '0 !important',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <DataGridHeader
                        gridRef={gridRef}
                        onSearch={setSearch}
                        search={search}
                        actions={
                            <Button
                                data-testid='CREATE_NEW'
                                startIcon={<AddCircleOutlineOutlined />}
                                onClick={onCreate}
                            >
                                {t('actions.create-tenant-group')}
                            </Button>
                        }
                    />

                    <DataGridPro
                        disableColumnPinning
                        pagination
                        sx={{
                            height: '100%',
                            minHeight: 150,
                            [breakpoints.up('lg')]: {
                                minHeight: 400,
                            },
                        }}
                        apiRef={gridRef}
                        columns={columns}
                        loading={isFetching}
                        paginationMode='server'
                        sortingMode='server'
                        filterMode='server'
                        disableColumnSelector
                        paginationModel={paginationModel}
                        filterModel={filterModel}
                        onPaginationModelChange={setPaginationModel}
                        onFilterModelChange={(model) => setFilterModel(model)}
                        sortModel={sortModel}
                        onSortModelChange={(model) => setSortModel(model)}
                        rows={tenantGroups?.list || []}
                        rowCount={tenantGroups?.total_count || 0}
                        initialState={{
                            density: 'compact',
                            pinnedColumns: {
                                right: ['actions'],
                            },
                        }}
                    />
                </CardContent>
            </Card>

            {tenantGroupForDelete && (
                <ConfirmationDialog
                    isLoading={isDeletePending}
                    onClose={() => setTenantGroupForDelete(null)}
                    onConfirm={() => mutate(tenantGroupForDelete.id)}
                    confirmLabel={t('actions.delete')}
                >
                    <Typography>
                        {t('texts.delete-tenant-group-confirmation')}
                    </Typography>
                </ConfirmationDialog>
            )}
        </>
    )
}

export default TenantGroupsTable
