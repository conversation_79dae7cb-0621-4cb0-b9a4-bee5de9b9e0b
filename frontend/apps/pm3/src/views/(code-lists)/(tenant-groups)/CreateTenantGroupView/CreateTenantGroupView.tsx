import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormTenantsAssigner from '@/components/resource/tenant/FormTenantsAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import createTenantGroupRequest, {
    CreateTenantGroupBody,
    CreateTenantGroupBodyKeys,
} from '@/requests/tenantGroup/createTenantGroupRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
    TENANTS,
}

const CreateTenantGroupView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { mutate, isPending } = useMutation({
        mutationFn: createTenantGroupRequest,
        onSuccess: async ({ data }) => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenantGroups.index,
            })

            const href = createHrefWithParams(
                routerPaths.codeLists.tenantGroup.index,
                {
                    tenantGroupId: data.id,
                },
            )

            toast.success(t('texts.success-action'))
            navigate(href)
        },
    })

    const initialValues: CreateTenantGroupBody = {
        [CreateTenantGroupBodyKeys.NAME]: '',
        [CreateTenantGroupBodyKeys.ACTIVE]: true,
        [CreateTenantGroupBodyKeys.TENANTS]: [],
    }

    const validationSchema = React.useMemo(
        () =>
            yup.object().shape({
                [CreateTenantGroupBodyKeys.NAME]:
                    step === StepName.BASIC_INFORMATION
                        ? yup.string().required('errors.required-field')
                        : yup.string(),
                [CreateTenantGroupBodyKeys.TENANTS]:
                    step === StepName.TENANTS
                        ? yup.array().min(0, 'errors.required-field')
                        : yup.array(),
            }),
        [step],
    )

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: CreateTenantGroupBody) => {
        if (step !== StepName.TENANTS) {
            onNext()
            return
        }

        mutate(body)
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.tenants')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isPending}>
                {step !== StepName.TENANTS
                    ? t('actions.continue')
                    : t('actions.save')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            maxWidth={1100}
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.TENANTS && (
                    <FormTenantsAssigner
                        name={CreateTenantGroupBodyKeys.TENANTS}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default CreateTenantGroupView
