import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form } from 'formik'
import * as _ from 'lodash'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormTenantsAssigner from '@/components/resource/tenant/FormTenantsAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import { TenantGroupResponse } from '@/requests/responses'
import updateTenantGroupRequest, {
    UpdateTenantGroupBody,
    UpdateTenantGroupBodyKeys,
} from '@/requests/tenantGroup/updateTenantGroupRequest'

interface TenantsTabProps {
    tenantGroup: TenantGroupResponse
}

const TenantsTab = ({ tenantGroup }: TenantsTabProps) => {
    const { t } = useTranslation('common')
    const queryClient = useQueryClient()

    const { mutate, isPending } = useMutation({
        mutationFn: (body: UpdateTenantGroupBody) =>
            updateTenantGroupRequest(tenantGroup.id, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenantGroup(tenantGroup.id).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenantGroups.index,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const initialValues: UpdateTenantGroupBody = {
        [UpdateTenantGroupBodyKeys.NAME]: tenantGroup.name,
        [UpdateTenantGroupBodyKeys.ACTIVE]: tenantGroup.active,
        [UpdateTenantGroupBodyKeys.TENANTS]: _.map(
            tenantGroup.tenants,
            (t) => t.id,
        ),
    }

    const onSubmit = (body: UpdateTenantGroupBody) => {
        mutate(body)
    }

    return (
        <BlockingFormik initialValues={initialValues} onSubmit={onSubmit}>
            {({ resetForm }) => {
                return (
                    <Card
                        component={Form}
                        sx={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            borderTopLeftRadius: 0,
                        }}
                    >
                        <CardContent
                            sx={{
                                flex: 1,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                            }}
                        >
                            <FormTenantsAssigner
                                disabled={tenantGroup.read_only}
                                name={UpdateTenantGroupBodyKeys.TENANTS}
                            />
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between' }}>
                            <Button
                                type='button'
                                color='inherit'
                                onClick={() => resetForm()}
                            >
                                {t('actions.discard-changes')}
                            </Button>

                            <SubmitButton
                                loading={isPending}
                                disabled={tenantGroup.read_only}
                            >
                                {t('actions.save-changes')}
                            </SubmitButton>
                        </CardActions>
                    </Card>
                )
            }}
        </BlockingFormik>
    )
}

export default TenantsTab
