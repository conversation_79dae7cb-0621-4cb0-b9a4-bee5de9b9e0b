import companiesRoutes from './(companies)/companiesRoutes'
import contractsRoutes from './(contracts)/contractsRoutes'
import currenciesRoutes from './(currencies)/currenciesRoutes'
import customersRoutes from './(customers)/customersRoutes'
import eventsRoutes from './(events)/eventsRoutes'
import facilitiesRoutes from './(facilities)/facilitiesRoutes'
import facilityGroupsRoutes from './(facility-groups)/facilityGroupsRoutes'
import homepageRoutes from './(homepage)/homepageRoutes'
import legislationsRoutes from './(legislations)/legislationsRoutes'
import projectsRoutes from './(projects)/projectsRoutes'
import tenantGroupsRoutes from './(tenant-groups)/tenantGroupsRoutes'
import tenantsRoutes from './(tenants)/tenantsRoutes'
import unitsRoutes from './(units)/unitsRoutes'
import CodeListsLayout from './CodeListsLayout'

const codeListsRoutes = [
    {
        element: <CodeListsLayout />,
        children: [
            ...homepageRoutes,
            ...facilityGroupsRoutes,
            ...facilitiesRoutes,
            ...eventsRoutes,
            ...currenciesRoutes,
            ...contractsRoutes,
            ...companiesRoutes,
            ...projectsRoutes,
            ...unitsRoutes,
            ...tenantsRoutes,
            ...customersRoutes,
            ...legislationsRoutes,
            ...tenantGroupsRoutes,
        ],
    },
]

export default codeListsRoutes
