import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined'
import SubtitlesOutlinedIcon from '@mui/icons-material/SubtitlesOutlined'
import ToggleOnIcon from '@mui/icons-material/ToggleOn'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import { InformationRow } from '@/components/other/InformationRow'
import StatusChip from '@/components/other/StatusChip'
import { ContractResponse } from '@/requests/responses'

interface BasicInformationTabProps {
    contract: ContractResponse
}

const BasicInformationTab = ({ contract }: BasicInformationTabProps) => {
    const { t } = useTranslation('common')

    return (
        <Stack direction='row' flex={1}>
            <Card sx={{ borderTopLeftRadius: 0, width: '100%' }}>
                <CardContent
                    sx={{
                        gap: 3,
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Typography fontWeight={600}>
                        {t('labels.basic-information')}
                    </Typography>

                    <InformationRow
                        startIcon={<BadgeOutlinedIcon />}
                        title={t('labels.name')}
                    >
                        <Typography>{contract.name}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<SubtitlesOutlinedIcon />}
                        title={t('labels.code')}
                    >
                        <Typography>{contract.code}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<ToggleOnIcon />}
                        title={t('labels.status')}
                    >
                        <StatusChip isActive={contract.active} />
                    </InformationRow>
                </CardContent>
            </Card>
        </Stack>
    )
}

export default BasicInformationTab
