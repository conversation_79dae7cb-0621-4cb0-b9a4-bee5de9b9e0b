import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import { UpdateContractBodyKeys } from '@/requests/contract/updateContractRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='CONTRACT_NAME'
                name={UpdateContractBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.code')}
                data-testid='CONTRACT_CODE'
                name={UpdateContractBodyKeys.CODE}
            />

            <FormSwitch
                label={t('labels.active')}
                name={UpdateContractBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
