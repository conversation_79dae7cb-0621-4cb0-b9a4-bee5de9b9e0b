import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getContractRequest from '@/requests/contract/getContractRequest'
import updateContractRequest, {
    UpdateContractBody,
    UpdateContractBodyKeys,
} from '@/requests/contract/updateContractRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
}

const UpdateContractView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const contractId = parseParamInt(params.contractId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const {
        isPending: isContractPending,
        data: contract,
        isError: isContractError,
    } = useQuery({
        queryFn: () => getContractRequest(contractId),
        queryKey: queryKeys.contract(contractId).index,
    })

    const onDetail = () => {
        const href = createHrefWithParams(
            routerPaths.codeLists.contract.index,
            {
                contractId,
            },
        )

        navigate(href)
    }

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdateContractBody) =>
            updateContractRequest(contractId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.contract(contractId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.contracts.index,
            })

            toast.success(t('texts.success-action'))
            onDetail()
        },
    })

    const initialValues: UpdateContractBody = {
        [UpdateContractBodyKeys.NAME]: contract?.data.name || '',
        [UpdateContractBodyKeys.CODE]: contract?.data.code || '',
        [UpdateContractBodyKeys.ACTIVE]: Boolean(contract?.data.active),
    }

    const validationSchema = yup.object().shape({
        [UpdateContractBodyKeys.NAME]: yup
            .string()
            .required('errors.required-field'),
        [UpdateContractBodyKeys.CODE]: yup
            .string()
            .required('errors.required-field'),
    })

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onSubmit = (body: UpdateContractBody) => {
        mutate(body)
    }

    if (isContractPending) return <LoadingSpinner />

    if (isContractError) {
        return <Navigate to={routerPaths.codeLists.contracts.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.BASIC_INFORMATION
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateContractView
