import React from 'react'

import routerPaths from '@/config/routerPaths'

const ContractsView = React.lazy(() => import('./ContractsView/ContractsView'))

const ContractView = React.lazy(() => import('./ContractView/ContractView'))

const CreateContractView = React.lazy(
    () => import('./CreateContractView/CreateContractView'),
)

const UpdateContractView = React.lazy(
    () => import('./UpdateContractView/UpdateContractView'),
)

const contractsRoutes = [
    {
        path: routerPaths.codeLists.contracts.index,
        element: <ContractsView />,
    },
    {
        path: routerPaths.codeLists.contract.index,
        element: <ContractView />,
    },
    {
        path: routerPaths.codeLists.contracts.create,
        element: <CreateContractView />,
    },
    {
        path: routerPaths.codeLists.contract.update,
        element: <UpdateContractView />,
    },
]

export default contractsRoutes
