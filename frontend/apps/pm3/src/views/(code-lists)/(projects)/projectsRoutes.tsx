import React from 'react'

import routerPaths from '@/config/routerPaths'

const ProjectsView = React.lazy(() => import('./ProjectsView/ProjectsView'))

const ProjectView = React.lazy(() => import('./ProjectView/ProjectView'))

const CreateProjectView = React.lazy(
    () => import('./CreateProjectView/CreateProjectView'),
)

const UpdateProjectView = React.lazy(
    () => import('./UpdateProjectView/UpdateProjectView'),
)

const projectsRoutes = [
    {
        path: routerPaths.codeLists.projects.index,
        element: <ProjectsView />,
    },
    {
        path: routerPaths.codeLists.project.index,
        element: <ProjectView />,
    },
    {
        path: routerPaths.codeLists.projects.create,
        element: <CreateProjectView />,
    },
    {
        path: routerPaths.codeLists.project.update,
        element: <UpdateProjectView />,
    },
]

export default projectsRoutes
