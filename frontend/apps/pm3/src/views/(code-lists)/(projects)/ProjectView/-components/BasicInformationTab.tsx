import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined'
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined'
import ToggleOnIcon from '@mui/icons-material/ToggleOn'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import { InformationRow } from '@/components/other/InformationRow'
import StatusChip from '@/components/other/StatusChip'
import { ProjectResponse } from '@/requests/responses'

interface BasicInformationTabProps {
    project: ProjectResponse
}

const BasicInformationTab = ({ project }: BasicInformationTabProps) => {
    const { t } = useTranslation('common')

    return (
        <Stack direction='row' flex={1}>
            <Card sx={{ borderTopLeftRadius: 0, width: '100%' }}>
                <CardContent
                    sx={{
                        gap: 3,
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Typography fontWeight={600}>
                        {t('labels.basic-information')}
                    </Typography>

                    <InformationRow
                        startIcon={<BadgeOutlinedIcon />}
                        title={t('labels.name')}
                    >
                        <Typography>{project.name}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<TextSnippetOutlinedIcon />}
                        title={t('labels.code')}
                    >
                        <Typography>{project.code}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<ToggleOnIcon />}
                        title={t('labels.status')}
                    >
                        <StatusChip isActive={project.active} />
                    </InformationRow>
                </CardContent>
            </Card>
        </Stack>
    )
}

export default BasicInformationTab
