import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import CircularProgress from '@mui/material/CircularProgress'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import { InformationRow } from '@/components/other/InformationRow'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import deleteProjectRequest from '@/requests/project/deleteProjectRequest'
import getProjectRequest from '@/requests/project/getProjectRequest'

import { ProjectViewTabName } from './types'

const BasicInformationTab = React.lazy(
    () => import('./-components/BasicInformationTab'),
)

interface ProjectViewContentProps {
    tab: ProjectViewTabName
    onTab: (tab: ProjectViewTabName) => void
}

const ProjectViewContent = ({ onTab, tab }: ProjectViewContentProps) => {
    const queryClient = useQueryClient()
    const navigate = useNavigate()

    const { t } = useTranslation('common')

    const { spacing, palette } = useTheme()
    const params = useParams<'projectId'>()
    const projectId = parseParamInt(params.projectId)

    const [deleteOpen, setDeleteOpen] = React.useState(false)

    const {
        isPending,
        data: project,
        isError,
    } = useQuery({
        queryFn: () => getProjectRequest(projectId),
        queryKey: queryKeys.project(projectId).index,
    })

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: () => deleteProjectRequest(projectId),
        onSuccess: async () => {
            toast.success(t('texts.success-action'))

            navigate(routerPaths.codeLists.projects.index)

            await queryClient.invalidateQueries({
                queryKey: queryKeys.projects.index,
            })
        },
    })

    const onUpdate = React.useCallback(() => {
        const href = createHrefWithParams(
            routerPaths.codeLists.project.update,
            {
                projectId,
            },
        )

        navigate(href)
    }, [navigate, projectId])

    if (isPending) return <LoadingSpinner />

    if (isError) {
        return <Navigate to={routerPaths.codeLists.projects.index} />
    }

    return (
        <Stack gap={1} height='100%'>
            <Card sx={{ flex: 'none' }}>
                <CardContent
                    sx={{
                        px: 1,
                        gap: 1.5,
                        display: 'flex',
                        flexWrap: 'wrap',
                        alignItems: 'flex-start',
                        py: `${spacing(1.5)} !important`,
                    }}
                >
                    <InformationRow title={t('labels.name')}>
                        <Typography fontWeight={700}>
                            {project.data.name}
                        </Typography>
                    </InformationRow>

                    <InformationRow
                        title={t('labels.code')}
                        sx={{ mr: 'auto' }}
                    >
                        <Typography fontWeight={700}>
                            {project.data.code}
                        </Typography>
                    </InformationRow>

                    <IconButtonDropdown
                        icon={
                            isDeletePending ? (
                                <CircularProgress size={20} />
                            ) : (
                                <MoreVert fontSize='small' />
                            )
                        }
                        menuContent={({ onClose }) => (
                            <Stack>
                                <MenuItem
                                    onClick={onUpdate}
                                    data-testid='RECORD_UPDATE'
                                >
                                    {t('actions.update')}
                                </MenuItem>

                                <MenuItem
                                    onClick={() => {
                                        onClose()
                                        setDeleteOpen(true)
                                    }}
                                    data-testid='RECORD_DELETE'
                                    sx={{
                                        color: `${palette.error.main} !important`,
                                    }}
                                >
                                    {t('actions.delete')}
                                </MenuItem>
                            </Stack>
                        )}
                    />
                </CardContent>
            </Card>

            <Stack flex={1} overflow='hidden'>
                <Tabs
                    value={tab}
                    onChange={(__, value) => {
                        onTab(value)
                    }}
                    sx={{
                        width: 'max-content',
                        background: palette.grey[50],
                        borderTopLeftRadius: spacing(2),
                        borderTopRightRadius: spacing(2),
                    }}
                >
                    <Tab
                        value={ProjectViewTabName.BASIC_INFORMATION}
                        label={t('labels.information')}
                        icon={<InfoOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />
                </Tabs>

                <React.Suspense fallback={<LoadingSpinner />}>
                    {tab === ProjectViewTabName.BASIC_INFORMATION && (
                        <BasicInformationTab project={project.data} />
                    )}
                </React.Suspense>
            </Stack>

            {deleteOpen && (
                <ConfirmationDialog
                    isLoading={isDeletePending}
                    onClose={() => setDeleteOpen(false)}
                    onConfirm={() => mutate()}
                    confirmLabel={t('actions.delete')}
                >
                    <Typography>
                        {t('texts.delete-project-confirmation')}
                    </Typography>
                </ConfirmationDialog>
            )}
        </Stack>
    )
}

const ProjectView = () => {
    return (
        <TabGuard
            validValues={_.values(ProjectViewTabName)}
            defaultTab={ProjectViewTabName.BASIC_INFORMATION}
        >
            {(props) => <ProjectViewContent {...props} />}
        </TabGuard>
    )
}

export default ProjectView
