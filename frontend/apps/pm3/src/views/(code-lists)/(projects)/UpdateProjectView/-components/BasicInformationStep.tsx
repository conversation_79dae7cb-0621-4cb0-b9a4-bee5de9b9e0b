import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import { UpdateProjectBodyKeys } from '@/requests/project/updateProjectRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                data-testid='NAME'
                label={t('labels.name')}
                name={UpdateProjectBodyKeys.NAME}
            />

            <FormTextField
                data-testid='CODE'
                label={t('labels.code')}
                name={UpdateProjectBodyKeys.CODE}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={UpdateProjectBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
