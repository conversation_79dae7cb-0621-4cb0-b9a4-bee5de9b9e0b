import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form } from 'formik'
import * as _ from 'lodash'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import updateFacilityGroupRequest, {
    UpdateFacilityGroupBody,
    UpdateFacilityGroupBodyKeys,
} from '@/requests/facilityGroup/updateFacilityGroupRequest'
import { FacilityGroupResponse } from '@/requests/responses'

interface FacilitiesTabProps {
    facilityGroup: FacilityGroupResponse
}

const FacilitiesTab = ({ facilityGroup }: FacilitiesTabProps) => {
    const { t } = useTranslation('common')
    const queryClient = useQueryClient()

    const { mutate, isPending } = useMutation({
        mutationFn: (body: UpdateFacilityGroupBody) =>
            updateFacilityGroupRequest(facilityGroup.id, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.facilityGroup(facilityGroup.id).index,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const initialValues: UpdateFacilityGroupBody = {
        [UpdateFacilityGroupBodyKeys.FACILITIES]: _.map(
            facilityGroup.facilities,
            'id',
        ),
        [UpdateFacilityGroupBodyKeys.NAME]: facilityGroup.name,
        [UpdateFacilityGroupBodyKeys.USAGE]: facilityGroup.usage,
    }

    const onSubmit = (body: UpdateFacilityGroupBody) => {
        mutate(body)
    }

    return (
        <BlockingFormik initialValues={initialValues} onSubmit={onSubmit}>
            {({ resetForm }) => (
                <Card
                    component={Form}
                    sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        borderTopLeftRadius: 0,
                    }}
                >
                    <CardContent
                        sx={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            overflow: 'hidden',
                        }}
                    >
                        <FormFacilitiesAssigner
                            name={UpdateFacilityGroupBodyKeys.FACILITIES}
                        />
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'space-between' }}>
                        <Button
                            type='button'
                            color='inherit'
                            onClick={() => resetForm()}
                        >
                            {t('actions.discard-changes')}
                        </Button>

                        <SubmitButton loading={isPending}>
                            {t('actions.save-changes')}
                        </SubmitButton>
                    </CardActions>
                </Card>
            )}
        </BlockingFormik>
    )
}

export default FacilitiesTab
