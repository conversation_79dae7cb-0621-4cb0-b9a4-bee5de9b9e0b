import React from 'react'

import routerPaths from '@/config/routerPaths'

const FacilityGroupsView = React.lazy(
    () => import('./FacilityGroupsView/FacilityGroupsView'),
)

const FacilityGroupView = React.lazy(
    () => import('./FacilityGroupView/FacilityGroupView'),
)

const CreateFacilityGroupView = React.lazy(
    () => import('./CreateFacilityGroupView/CreateFacilityGroupView'),
)

const UpdateFacilityGroupView = React.lazy(
    () => import('./UpdateFacilityGroupView/UpdateFacilityGroupView'),
)

const facilityGroupsRoutes = [
    {
        path: routerPaths.codeLists.facilityGroups.index,
        element: <FacilityGroupsView />,
    },
    {
        path: routerPaths.codeLists.facilityGroup.index,
        element: <FacilityGroupView />,
    },
    {
        path: routerPaths.codeLists.facilityGroups.create,
        element: <CreateFacilityGroupView />,
    },
    {
        path: routerPaths.codeLists.facilityGroup.update,
        element: <UpdateFacilityGroupView />,
    },
]

export default facilityGroupsRoutes
