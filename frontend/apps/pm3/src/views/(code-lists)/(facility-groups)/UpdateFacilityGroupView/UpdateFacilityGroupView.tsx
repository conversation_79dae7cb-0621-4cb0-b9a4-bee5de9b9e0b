import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getFacilityGroupRequest from '@/requests/facilityGroup/getFacilityGroupRequest'
import updateFacilityGroupRequest, {
    UpdateFacilityGroupBody,
    UpdateFacilityGroupBodyKeys,
} from '@/requests/facilityGroup/updateFacilityGroupRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
    FACILITIES,
}

const UpdateFacilityGroupView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const facilityGroupId = parseParamInt(params.facilityGroupId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const {
        isPending: isGroupPending,
        data: group,
        isError: isGroupError,
    } = useQuery({
        queryFn: () => getFacilityGroupRequest(facilityGroupId),
        queryKey: queryKeys.facilityGroup(facilityGroupId).index,
    })

    const onDetail = () => {
        const href = createHrefWithParams(
            routerPaths.codeLists.facilityGroup.index,
            {
                facilityGroupId: facilityGroupId,
            },
        )

        navigate(href)
    }

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdateFacilityGroupBody) =>
            updateFacilityGroupRequest(facilityGroupId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.facilityGroup(facilityGroupId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.facilityGroups.index,
            })

            toast.success(t('texts.success-action'))
            onDetail()
        },
    })

    const initialValues: UpdateFacilityGroupBody = {
        [UpdateFacilityGroupBodyKeys.FACILITIES]: _.map(
            group?.data.facilities,
            (f) => f.id,
        ),
        [UpdateFacilityGroupBodyKeys.NAME]: group?.data.name || '',
        [UpdateFacilityGroupBodyKeys.USAGE]: group?.data.usage || '',
    }

    const validationSchema = React.useMemo(() => {
        return yup.object().shape({
            [UpdateFacilityGroupBodyKeys.NAME]:
                step === StepName.BASIC_INFORMATION
                    ? yup.string().required('errors.required-field')
                    : yup.string(),
        })
    }, [step])

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: UpdateFacilityGroupBody) => {
        if (step !== StepName.FACILITIES) {
            onNext()
            return
        }

        mutate(body)
    }

    if (isGroupPending) return <LoadingSpinner />

    if (isGroupError) {
        return <Navigate to={routerPaths.codeLists.facilityGroups.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.objects')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.FACILITIES
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            maxWidth={1100}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.FACILITIES && (
                    <FormFacilitiesAssigner
                        name={UpdateFacilityGroupBodyKeys.FACILITIES}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateFacilityGroupView
