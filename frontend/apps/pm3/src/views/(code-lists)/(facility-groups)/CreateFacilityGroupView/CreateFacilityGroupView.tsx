import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import createFacilityGroupRequest, {
    CreateFacilityGroupBody,
    CreateFacilityGroupBodyKeys,
} from '@/requests/facilityGroup/createFacilityGroupRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
    FACILITIES,
}

const CreateFacilityGroupView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { mutate, isPending } = useMutation({
        mutationFn: createFacilityGroupRequest,
        onSuccess: async ({ data }) => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.facilityGroups.index,
            })

            const href = createHrefWithParams(
                routerPaths.codeLists.facilityGroup.index,
                {
                    facilityGroupId: data.id,
                },
            )

            toast.success(t('texts.success-action'))
            navigate(href)
        },
    })

    const initialValues: CreateFacilityGroupBody = {
        [CreateFacilityGroupBodyKeys.FACILITIES]: [],
        [CreateFacilityGroupBodyKeys.NAME]: '',
        [CreateFacilityGroupBodyKeys.USAGE]: '',
    }

    const validationSchema = React.useMemo(() => {
        return yup.object().shape({
            [CreateFacilityGroupBodyKeys.NAME]:
                step === StepName.BASIC_INFORMATION
                    ? yup.string().required('errors.required-field')
                    : yup.string(),
        })
    }, [step])

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: CreateFacilityGroupBody) => {
        if (step !== StepName.FACILITIES) {
            onNext()
            return
        }

        mutate(body)
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.objects')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isPending}>
                {step === StepName.FACILITIES
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            maxWidth={1000}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.FACILITIES && (
                    <FormFacilitiesAssigner
                        name={CreateFacilityGroupBodyKeys.FACILITIES}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default CreateFacilityGroupView
