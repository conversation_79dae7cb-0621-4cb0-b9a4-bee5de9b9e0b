import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import CircularProgress from '@mui/material/CircularProgress'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import { InformationRow } from '@/components/other/InformationRow'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import deleteTenantRequest from '@/requests/tenant/deleteTenantRequest'
import getTenantRequest from '@/requests/tenant/getTenantRequest'

import { TenantViewTabName } from './types'

const BasicInformationTab = React.lazy(
    () => import('./-components/BasicInformationTab'),
)

interface TenantViewContentProps {
    tab: TenantViewTabName
    onTab: (tab: TenantViewTabName) => void
}

const TenantViewContent = ({ onTab, tab }: TenantViewContentProps) => {
    const queryClient = useQueryClient()
    const navigate = useNavigate()

    const { t } = useTranslation('common')

    const { spacing, palette } = useTheme()
    const params = useParams<'tenantId'>()
    const tenantId = parseParamInt(params.tenantId)

    const [deleteOpen, setDeleteOpen] = React.useState(false)

    const {
        isPending,
        data: tenant,
        isError,
    } = useQuery({
        queryFn: () => getTenantRequest(tenantId),
        queryKey: queryKeys.tenant(tenantId).index,
    })

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: () => deleteTenantRequest(tenantId),
        onSuccess: async () => {
            toast.success(t('texts.success-action'))

            navigate(routerPaths.codeLists.tenants.index)

            await queryClient.invalidateQueries({
                queryKey: queryKeys.tenants.index,
            })
        },
    })

    const onUpdate = React.useCallback(() => {
        const href = createHrefWithParams(routerPaths.codeLists.tenant.update, {
            tenantId,
        })

        navigate(href)
    }, [navigate, tenantId])

    if (isPending) return <LoadingSpinner />

    if (isError) {
        return <Navigate to={routerPaths.codeLists.tenants.index} />
    }

    return (
        <Stack gap={1} height='100%'>
            <Card sx={{ flex: 'none' }}>
                <CardContent
                    sx={{
                        px: 1,
                        gap: 1.5,
                        display: 'flex',
                        flexWrap: 'wrap',
                        alignItems: 'flex-start',
                        py: `${spacing(1.5)} !important`,
                    }}
                >
                    <InformationRow
                        title={t('labels.name')}
                        sx={{ mr: 'auto' }}
                    >
                        <Typography fontWeight={700}>
                            {tenant.data.name}
                        </Typography>
                    </InformationRow>

                    {!tenant.data.read_only && (
                        <IconButtonDropdown
                            icon={
                                isDeletePending ? (
                                    <CircularProgress size={20} />
                                ) : (
                                    <MoreVert fontSize='small' />
                                )
                            }
                            menuContent={({ onClose }) => (
                                <Stack>
                                    <MenuItem
                                        onClick={onUpdate}
                                        data-testid='RECORD_UPDATE'
                                    >
                                        {t('actions.update')}
                                    </MenuItem>

                                    <MenuItem
                                        onClick={() => {
                                            onClose()
                                            setDeleteOpen(true)
                                        }}
                                        data-testid='RECORD_DELETE'
                                        sx={{
                                            color: `${palette.error.main} !important`,
                                        }}
                                    >
                                        {t('actions.delete')}
                                    </MenuItem>
                                </Stack>
                            )}
                        />
                    )}
                </CardContent>
            </Card>

            <Stack flex={1} overflow='hidden'>
                <Tabs
                    value={tab}
                    onChange={(__, value) => {
                        onTab(value)
                    }}
                    sx={{
                        width: 'max-content',
                        background: palette.grey[50],
                        borderTopLeftRadius: spacing(2),
                        borderTopRightRadius: spacing(2),
                    }}
                >
                    <Tab
                        value={TenantViewTabName.BASIC_INFORMATION}
                        label={t('labels.information')}
                        icon={<InfoOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />
                </Tabs>

                <React.Suspense fallback={<LoadingSpinner />}>
                    {tab === TenantViewTabName.BASIC_INFORMATION && (
                        <BasicInformationTab tenant={tenant.data} />
                    )}
                </React.Suspense>
            </Stack>

            {deleteOpen && (
                <ConfirmationDialog
                    isLoading={isDeletePending}
                    onClose={() => setDeleteOpen(false)}
                    onConfirm={() => mutate()}
                    confirmLabel={t('actions.delete')}
                >
                    <Typography>
                        {t('texts.delete-tenant-confirmation')}
                    </Typography>
                </ConfirmationDialog>
            )}
        </Stack>
    )
}

const TenantView = () => {
    return (
        <TabGuard
            validValues={_.values(TenantViewTabName)}
            defaultTab={TenantViewTabName.BASIC_INFORMATION}
        >
            {(props) => <TenantViewContent {...props} />}
        </TabGuard>
    )
}

export default TenantView
