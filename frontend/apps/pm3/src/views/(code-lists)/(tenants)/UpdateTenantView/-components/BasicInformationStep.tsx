import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormPhoneNumber from '@/components/form/FormPhoneNumber'
import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import FormFacilitiesAsyncAutocomplete from '@/components/resource/facility/FormFacilitiesAsyncAutocomplete'
import { UpdateTenantBodyKeys } from '@/requests/tenant/updateTenantRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation()

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                data-testid='NAME'
                label={t('labels.name')}
                name={UpdateTenantBodyKeys.NAME}
            />

            <FormTextField
                data-testid='EMAIL'
                label={t('labels.email')}
                name={UpdateTenantBodyKeys.EMAIL}
            />

            <FormPhoneNumber
                data-testid='PHONE'
                label={t('labels.phone')}
                name={UpdateTenantBodyKeys.PHONE}
            />

            <FormFacilitiesAsyncAutocomplete
                name={UpdateTenantBodyKeys.FACILITY_ID}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={UpdateTenantBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
