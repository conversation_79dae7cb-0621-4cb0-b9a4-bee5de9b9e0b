import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormPhoneNumber from '@/components/form/FormPhoneNumber'
import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import FormFacilitiesAsyncAutocomplete from '@/components/resource/facility/FormFacilitiesAsyncAutocomplete'
import { CreateTenantBodyKeys } from '@/requests/tenant/createTenantRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation()

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                data-testid='NAME'
                name={CreateTenantBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.email')}
                data-testid='EMAIL'
                name={CreateTenantBodyKeys.EMAIL}
            />

            <FormPhoneNumber
                label={t('labels.phone')}
                data-testid='PHONE'
                name={CreateTenantBodyKeys.PHONE}
            />

            <FormFacilitiesAsyncAutocomplete
                name={CreateTenantBodyKeys.FACILITY_ID}
            />

            <FormSwitch
                data-testid='ACTIVITY'
                label={t('labels.active')}
                name={CreateTenantBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
