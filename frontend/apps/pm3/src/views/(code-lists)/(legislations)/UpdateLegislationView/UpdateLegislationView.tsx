import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getLegislationRequest from '@/requests/legislation/getLegislationRequest'
import updateLegislationRequest, {
    UpdateLegislationBody,
    UpdateLegislationBodyKeys,
} from '@/requests/legislation/updateLegislationRequest'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
}

const UpdateLegislationView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const legislationId = parseParamInt(params.legislationId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const {
        isPending: isLegislationPending,
        data: legislation,
        isError: isLegislationError,
    } = useQuery({
        queryFn: () => getLegislationRequest(legislationId),
        queryKey: queryKeys.legislation(legislationId).index,
    })

    const onDetail = () => {
        const href = createHrefWithParams(
            routerPaths.codeLists.legislation.index,
            {
                legislationId,
            },
        )

        navigate(href)
    }

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdateLegislationBody) =>
            updateLegislationRequest(legislationId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.legislation(legislationId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.legislations.index,
            })

            toast.success(t('texts.success-action'))
            onDetail()
        },
    })

    const initialValues: UpdateLegislationBody = {
        [UpdateLegislationBodyKeys.NAME]: legislation?.data.name || '',
        [UpdateLegislationBodyKeys.ACTIVE]: Boolean(legislation?.data.active),
        [UpdateLegislationBodyKeys.CURRENCY_ID]:
            legislation?.data.currency.id || -1,
    }

    const validationSchema = yup.object().shape({
        [UpdateLegislationBodyKeys.NAME]: yup
            .string()
            .required('errors.required-field'),
        [UpdateLegislationBodyKeys.CURRENCY_ID]: yup
            .number()
            .min(0, 'errors.required-field'),
    })

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onSubmit = (body: UpdateLegislationBody) => {
        mutate(body)
    }

    if (isLegislationPending) return <LoadingSpinner />

    if (isLegislationError) {
        return <Navigate to={routerPaths.codeLists.legislations.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.BASIC_INFORMATION
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateLegislationView
