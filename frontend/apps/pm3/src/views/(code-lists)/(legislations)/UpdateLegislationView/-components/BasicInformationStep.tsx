import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormSwitch from '@/components/form/FormSwitch'
import FormTextField from '@/components/form/FormTextField'
import FormCurrenciesAsyncAutocomplete from '@/components/resource/currency/FormCurrenciesAsyncAutocomplete'
import { UpdateLegislationBodyKeys } from '@/requests/legislation/updateLegislationRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation()

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.name')}
                name={UpdateLegislationBodyKeys.NAME}
            />

            <FormCurrenciesAsyncAutocomplete
                name={UpdateLegislationBodyKeys.CURRENCY_ID}
            />

            <FormSwitch
                label={t('labels.active')}
                name={UpdateLegislationBodyKeys.ACTIVE}
            />
        </Stack>
    )
}

export default BasicInformationStep
