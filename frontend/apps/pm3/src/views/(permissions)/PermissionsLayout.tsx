import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import DocumentScannerOutlinedIcon from '@mui/icons-material/DocumentScannerOutlined'
import GroupAddOutlinedIcon from '@mui/icons-material/GroupAddOutlined'
import { useQuery } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router'

import Layout from '@/components/layout/Layout'
import PageHead from '@/components/layout/PageHead'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import AuthenticatedGuard from '@/guard/AuthenticatedGuard'
import getPermissionGroupRequest from '@/requests/permissionGroup/getPermissionGroupRequest'
import getPermissionTemplateRequest from '@/requests/permissionTemplate/getPermissionTemplateRequest'

const PermissionsLayoutContent = () => {
    const { t } = useTranslation('common')

    const params = useParams()

    const groupId = parseParamInt(params.groupId)
    const templateId = parseParamInt(params.templateId)

    const sidebarItems = [
        {
            href: routerPaths.permissions.groups.index,
            label: t('labels.groups'),
            icon: <GroupAddOutlinedIcon />,
        },
        {
            href: routerPaths.permissions.templates.index,
            label: t('labels.templates'),
            icon: <DocumentScannerOutlinedIcon />,
        },
    ]

    const { data: template } = useQuery({
        queryFn: () => getPermissionTemplateRequest(templateId),
        queryKey: queryKeys.permissionTemplate(templateId).index,
        enabled: Boolean(templateId),
    })

    const { data: group } = useQuery({
        queryFn: () => getPermissionGroupRequest(groupId),
        queryKey: queryKeys.permissionGroup(groupId).index,
        enabled: Boolean(groupId),
    })

    const map = {
        [routerPaths.permissions.index]: t('labels.permissions'),
        [routerPaths.permissions.groups.index]: t('labels.permission-groups'),
        [routerPaths.permissions.templates.index]: t(
            'labels.permission-templates',
        ),
        [routerPaths.permissions.template.index]: template?.data.name || '',
        [routerPaths.permissions.group.index]: group?.data.name || '',
        [routerPaths.permissions.template.update]: t(
            'labels.update-permission-template',
        ),
        [routerPaths.permissions.templates.create]: t(
            'labels.create-permission-template',
        ),
        [routerPaths.permissions.group.update]: t(
            'labels.update-permission-group',
        ),
        [routerPaths.permissions.groups.create]: t(
            'labels.create-permission-group',
        ),
    }

    return (
        <>
            <PageHead map={map} />

            <Layout
                breadcrumbsMap={map}
                logoUrl='/images/logos/default.png'
                sidebarItems={sidebarItems}
            />
        </>
    )
}

const PermissionsLayout = () => {
    return (
        <AuthenticatedGuard>
            <PermissionsLayoutContent />
        </AuthenticatedGuard>
    )
}

export default PermissionsLayout
