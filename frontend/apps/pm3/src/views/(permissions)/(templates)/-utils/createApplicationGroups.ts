import * as _ from 'lodash'

import { ApplicationName, PermissionResponse } from '@/requests/responses'

export type ApplicationGroups = Record<
    ApplicationName,
    Omit<PermissionResponse, 'application'>[]
>

export const createApplicationGroups = (permissions: PermissionResponse[]) => {
    return _.reduce(
        permissions,
        (acc, permission) => {
            const omittedPermission = _.omit(permission, 'application')
            const applicationCode = permission.application.code

            acc[applicationCode] = _.concat(
                acc[applicationCode] || [],
                omittedPermission,
            )

            return acc
        },
        {} as ApplicationGroups,
    )
}
