import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import { DEFAULT_OPTIONS_LIMIT } from '@/config/common'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import { FilterTypeName } from '@/requests/params'
import getPermissionTemplateRequest from '@/requests/permissionTemplate/getPermissionTemplateRequest'
import getPermissionTemplatesRequest, {
    GetPermissionTemplatesParams,
} from '@/requests/permissionTemplate/getPermissionTemplatesRequest'
import updatePermissionTemplateRequest, {
    UpdatePermissionTemplateBody,
    UpdatePermissionTemplateBodyKeys,
} from '@/requests/permissionTemplate/updatePermissionTemplateRequest'
import { PermissionTemplateResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

const PermissionsSetter = React.lazy(
    () => import('../-components/PermissionsSetter'),
)

enum StepName {
    BASIC_INFORMATION,
    PERMISSIONS,
}

const nameValidator = async (
    value: string,
    template: PermissionTemplateResponse | null,
) => {
    if (!value || !template || template?.name === value) return true

    const templatesParams = createRequestParams<GetPermissionTemplatesParams>({
        pagination: {
            limit: DEFAULT_OPTIONS_LIMIT,
            offset: 0,
        },
        filter: [
            {
                field: 'name',
                type: FilterTypeName.MATCH,
                value: value,
            },
        ],
    })

    try {
        const { list } = await getPermissionTemplatesRequest(templatesParams)
        return list.length === 0
    } catch {
        return true
    }
}

const UpdateTemplateView = () => {
    const queryClient = useQueryClient()
    const navigate = useNavigate()

    const params = useParams()
    const templateId = parseParamInt(params.templateId)

    const [t] = useTranslation('common')

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { isPending, data: template } = useQuery({
        queryFn: () => getPermissionTemplateRequest(templateId),
        queryKey: queryKeys.permissionTemplate(templateId).index,
    })

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdatePermissionTemplateBody) =>
            updatePermissionTemplateRequest(templateId, body),
        onSuccess: async ({ data }) => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionTemplates.index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionTemplate(templateId).index,
            })

            const href = createHrefWithParams(
                routerPaths.permissions.template.index,
                {
                    templateId: data.id,
                },
            )

            toast.success(t('texts.success-action'))
            navigate(href)
        },
    })

    const initialValues: UpdatePermissionTemplateBody = {
        [UpdatePermissionTemplateBodyKeys.NAME]: template?.data.name || '',
        [UpdatePermissionTemplateBodyKeys.USAGE]: template?.data.usage || '',
        [UpdatePermissionTemplateBodyKeys.PERMISSIONS]:
            template?.data.permissions || [],
    }

    const validationSchema = React.useMemo(() => {
        return yup.object().shape({
            [UpdatePermissionTemplateBodyKeys.NAME]:
                step === StepName.BASIC_INFORMATION
                    ? yup
                          .string()
                          .required('errors.required-field')
                          .test(
                              'unique-name',
                              'errors.name-already-exists',
                              (value) =>
                                  nameValidator(value, template?.data || null),
                          )
                    : yup.string(),
            [UpdatePermissionTemplateBodyKeys.USAGE]:
                step === StepName.BASIC_INFORMATION
                    ? yup.string().required('errors.required-field')
                    : yup.string(),
        })
    }, [step, template])

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: UpdatePermissionTemplateBody) => {
        if (step !== StepName.PERMISSIONS) {
            onNext()
            return
        }

        mutate(body)
    }

    if (isPending) return <LoadingSpinner />

    if (!template?.data) {
        return <Navigate to={routerPaths.permissions.templates.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.permissions')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.PERMISSIONS
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validateOnChange={false}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.PERMISSIONS && (
                    <PermissionsSetter
                        name={UpdatePermissionTemplateBodyKeys.PERMISSIONS}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateTemplateView
