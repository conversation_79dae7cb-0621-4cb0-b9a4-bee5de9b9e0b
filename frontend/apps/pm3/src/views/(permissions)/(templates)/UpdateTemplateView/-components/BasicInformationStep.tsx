import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import { UpdatePermissionTemplateBodyKeys } from '@/requests/permissionTemplate/updatePermissionTemplateRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.template-name')}
                name={UpdatePermissionTemplateBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.template-usage')}
                name={UpdatePermissionTemplateBodyKeys.USAGE}
            />
        </Stack>
    )
}

export default BasicInformationStep
