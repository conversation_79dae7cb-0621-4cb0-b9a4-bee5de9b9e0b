import InfoOutlined from '@mui/icons-material/InfoOutlined'
import { alpha, useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import BackButton from '@/components/other/BackButton'

interface PermissionsAlertDialogProps {
    onClose: () => void
    onContinue: () => void
    oldPermissions: string[]
    newPermissions: string[]
}

const PermissionsAlertDialog = ({
    onClose,
    onContinue,
    newPermissions,
    oldPermissions,
}: PermissionsAlertDialogProps) => {
    const { t } = useTranslation('common')

    const { palette, spacing } = useTheme()

    const addedPermissions = React.useMemo(() => {
        return _.reduce(
            newPermissions,
            (acc, p) => {
                if (oldPermissions.includes(p)) return acc
                return acc + 1
            },
            0,
        )
    }, [newPermissions, oldPermissions])

    const removedPermissions = React.useMemo(() => {
        return _.reduce(
            oldPermissions,
            (acc, p) => {
                if (newPermissions.includes(p)) return acc
                return acc + 1
            },
            0,
        )
    }, [oldPermissions, newPermissions])

    return (
        <Dialog
            open={true}
            onClose={onClose}
            sx={{
                '.MuiPaper-root': {
                    maxWidth: 480,
                    width: '100%',
                },
            }}
        >
            <DialogContent
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'column',
                }}
            >
                <Box
                    sx={{
                        width: 80,
                        height: 80,
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: spacing(2),
                        justifyContent: 'center',
                        background: alpha(palette.info.main, 0.2),
                    }}
                >
                    <InfoOutlined sx={{ fontSize: 48 }} color='info' />
                </Box>

                <Typography align='center' variant='h5' mb={2} mt={3}>
                    {t('texts.save-changes-question')}
                </Typography>

                <Stack
                    direction='row'
                    justifyContent='space-around'
                    width='100%'
                >
                    <Stack textAlign='center'>
                        <Typography>{t('labels.added-permissions')}</Typography>
                        <Typography variant='h6'>{addedPermissions}</Typography>
                    </Stack>

                    <Stack textAlign='center'>
                        <Typography>
                            {t('labels.discarded-permissions')}
                        </Typography>

                        <Typography variant='h6'>
                            {removedPermissions}
                        </Typography>
                    </Stack>
                </Stack>
            </DialogContent>

            <DialogActions sx={{ justifyContent: 'space-between' }}>
                <BackButton color='inherit' onClick={onClose} />

                <Button onClick={onContinue}>
                    {t('actions.save-changes')}
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default PermissionsAlertDialog
