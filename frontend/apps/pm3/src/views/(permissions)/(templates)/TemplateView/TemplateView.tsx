import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import SecurityOutlined from '@mui/icons-material/SecurityOutlined'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Chip from '@mui/material/Chip'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import { InformationRow } from '@/components/other/InformationRow'
import { DEFAULT_OPTIONS_LIMIT } from '@/config/common'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import getPermissionsRequest, {
    GetPermissionsParams,
} from '@/requests/permission/getPermissionsRequest'
import deletePermissionTemplateRequest from '@/requests/permissionTemplate/deletePermissionTemplateRequest'
import getPermissionTemplateRequest from '@/requests/permissionTemplate/getPermissionTemplateRequest'
import {
    ApplicationName,
    PermissionTemplateResponse,
} from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

import { createApplicationGroups } from '../-utils/createApplicationGroups'
import { PermissionTemplateViewTabName } from './types'

const BasicInformationTab = React.lazy(
    () => import('./-components/BasicInformationTab'),
)
const PermissionsTab = React.lazy(() => import('./-components/PermissionsTab'))

type TabName = PermissionTemplateViewTabName | ApplicationName

interface TemplateViewContentProps {
    tab: TabName
    onTab: (tab: TabName) => void
}

const TemplateViewContent = ({ onTab, tab }: TemplateViewContentProps) => {
    const queryClient = useQueryClient()
    const navigate = useNavigate()

    const { t } = useTranslation('common')

    const { spacing, palette } = useTheme()
    const params = useParams<'templateId'>()
    const templateId = parseParamInt(params.templateId)

    const [deleteOpen, setDeleteOpen] = React.useState(false)

    const {
        isPending,
        data: template,
        isError,
    } = useQuery({
        queryFn: () => getPermissionTemplateRequest(templateId),
        queryKey: queryKeys.permissionTemplate(templateId).index,
    })

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: () => deletePermissionTemplateRequest(templateId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionTemplates.index,
            })

            toast.success(t('texts.success-action'))
            navigate(routerPaths.permissions.templates.index)
        },
    })

    const onUpdate = (template: PermissionTemplateResponse) => {
        const href = createHrefWithParams(
            routerPaths.permissions.template.update,
            {
                templateId: template.id,
            },
        )

        navigate(href)
    }

    const permissionsParams = createRequestParams<GetPermissionsParams>({
        pagination: {
            limit: DEFAULT_OPTIONS_LIMIT,
            offset: 0,
        },
    })

    const { data: permissions, isPending: isPermissionsPending } = useQuery({
        queryFn: () => getPermissionsRequest(permissionsParams),
        queryKey: [...queryKeys.permissions.index, permissionsParams],
    })

    const applicationGroups = React.useMemo(() => {
        return createApplicationGroups(permissions?.list || [])
    }, [permissions])

    if (isPending || isPermissionsPending) return <LoadingSpinner />

    if (isError) {
        return <Navigate to={routerPaths.permissions.templates.index} />
    }

    return (
        <Stack gap={1} height='100%'>
            <Card>
                <CardContent
                    sx={{
                        px: 1,
                        gap: 1.5,
                        display: 'flex',
                        flexWrap: 'wrap',
                        alignItems: 'center',
                        py: `${spacing(1.5)} !important`,
                    }}
                >
                    <InformationRow title={t('labels.template-name')}>
                        <Typography fontWeight={700}>
                            {template.data.name}
                        </Typography>
                    </InformationRow>

                    <InformationRow title={t('labels.template-usage')}>
                        <Typography fontWeight={700}>
                            {template.data.usage}
                        </Typography>
                    </InformationRow>

                    <InformationRow
                        sx={{ mr: 'auto' }}
                        title={t('labels.permissions')}
                    >
                        <Typography fontWeight={700}>
                            {template.data.permissions.length}
                        </Typography>
                    </InformationRow>

                    <IconButtonDropdown
                        icon={<MoreVert fontSize='small' />}
                        menuContent={({ onClose }) => (
                            <Stack>
                                <MenuItem
                                    onClick={() => {
                                        onUpdate(template.data)
                                    }}
                                >
                                    {t('actions.update')}
                                </MenuItem>

                                <MenuItem
                                    onClick={() => {
                                        onClose()
                                        setDeleteOpen(true)
                                    }}
                                    sx={{
                                        color: `${palette.error.main} !important`,
                                    }}
                                >
                                    {t('actions.delete')}
                                </MenuItem>
                            </Stack>
                        )}
                    />
                </CardContent>
            </Card>

            <Stack flex={1}>
                <Tabs
                    value={tab}
                    variant='scrollable'
                    scrollButtons={false}
                    onChange={(__, value) => {
                        onTab(value)
                    }}
                    sx={{
                        '.MuiTab-root': {
                            background: palette.grey[50],
                            borderRadius: 0,
                        },

                        '.MuiTab-root:first-of-type': {
                            borderTopLeftRadius: spacing(2),
                        },

                        '.MuiTab-root:last-of-type': {
                            borderTopRightRadius: spacing(2),
                        },
                    }}
                >
                    <Tab
                        value={PermissionTemplateViewTabName.BASIC_INFORMATION}
                        label={t('labels.information')}
                        icon={<InfoOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />

                    {_.values(ApplicationName).map((application) => {
                        const applicationPermissionsCodes = _.map(
                            applicationGroups[application],
                            (permission) => permission.code,
                        )

                        const applicationPermissionsByTemplate = _.filter(
                            template.data.permissions,
                            (selected) =>
                                applicationPermissionsCodes.includes(selected),
                        )

                        const formattedSelection = `${applicationPermissionsByTemplate.length}/${applicationPermissionsCodes.length}`

                        const isEmpty =
                            applicationPermissionsByTemplate.length === 0

                        const isSelectedAll =
                            applicationPermissionsByTemplate.length ===
                            applicationPermissionsCodes.length

                        if (applicationPermissionsCodes.length === 0) {
                            return null
                        }

                        return (
                            <Tab
                                key={application}
                                value={application}
                                label={
                                    <Stack
                                        gap={1}
                                        direction='row'
                                        alignItems='center'
                                    >
                                        <Typography>
                                            {t(`labels.${application}`)}
                                        </Typography>

                                        <Chip
                                            size='small'
                                            color={
                                                isEmpty
                                                    ? 'error'
                                                    : isSelectedAll
                                                      ? 'success'
                                                      : 'default'
                                            }
                                            label={formattedSelection}
                                        />
                                    </Stack>
                                }
                                icon={
                                    <SecurityOutlined sx={{ fontSize: 20 }} />
                                }
                                iconPosition='start'
                            />
                        )
                    })}
                </Tabs>

                <React.Suspense fallback={<LoadingSpinner />}>
                    {tab ===
                        PermissionTemplateViewTabName.BASIC_INFORMATION && (
                        <BasicInformationTab template={template.data} />
                    )}

                    {_.values(ApplicationName).includes(
                        tab as ApplicationName,
                    ) && (
                        <PermissionsTab
                            template={template.data}
                            application={tab as ApplicationName}
                        />
                    )}
                </React.Suspense>
            </Stack>

            {deleteOpen && (
                <ConfirmationDialog
                    isLoading={isDeletePending}
                    onClose={() => setDeleteOpen(false)}
                    onConfirm={() => mutate()}
                    confirmLabel={t('actions.delete')}
                >
                    <Typography>
                        {t('texts.delete-permission-template-confirmation')}
                    </Typography>
                </ConfirmationDialog>
            )}
        </Stack>
    )
}

const TemplateView = () => {
    const tabs = _.concat<TabName>(
        _.values(PermissionTemplateViewTabName),
        _.values(ApplicationName),
    )

    return (
        <TabGuard
            defaultTab={PermissionTemplateViewTabName.BASIC_INFORMATION}
            validValues={tabs}
        >
            {(props) => <TemplateViewContent {...props} />}
        </TabGuard>
    )
}

export default TemplateView
