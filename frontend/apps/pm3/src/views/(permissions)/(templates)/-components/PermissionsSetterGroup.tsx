import NativeCheckbox from '@goodsailors/ui/components/inputs/NativeCheckbox'
import Accordion from '@mui/material/Accordion'
import AccordionDetails from '@mui/material/AccordionDetails'
import AccordionSummary from '@mui/material/AccordionSummary'
import Box from '@mui/material/Box'
import Chip from '@mui/material/Chip'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useField } from 'formik'
import * as _ from 'lodash'
import React from 'react'
import AutoSizer from 'react-virtualized-auto-sizer'
import { FixedSizeList } from 'react-window'

import { ApplicationName, PermissionResponse } from '@/requests/responses'

import PermissionsSetterRow from './PermissionsSetterRow'
import PermissionsSetterRowLayout from './PermissionsSetterRowLayout'

interface PermissionsSetterGroupProps {
    name: string
    expanded: boolean
    onExpand: () => void
    application: ApplicationName
    permissions: Omit<PermissionResponse, 'application'>[]
}

const PermissionsSetterGroup = ({
    name,
    application,
    permissions,
    expanded,
    onExpand,
}: PermissionsSetterGroupProps) => {
    const [permissionsField, , permissionsHelper] = useField<string[]>(name)

    const checkedCount = React.useMemo(() => {
        const codes = _.map(permissions, (p) => p.code)
        return _.filter(codes, (c) => _.includes(permissionsField.value, c))
            .length
    }, [permissions, permissionsField.value])

    const isChecked = React.useCallback(
        (code: string) => {
            return _.includes(permissionsField.value, code)
        },
        [permissionsField.value],
    )

    const onChange = (isNativeChecked: boolean, codes: string[]) => {
        let newValue: string[]

        if (isNativeChecked) {
            newValue = _.concat(permissionsField.value || [], codes)
        } else {
            newValue = _.filter(
                permissionsField.value,
                (v) => !codes.includes(v),
            )
        }

        permissionsHelper.setValue(newValue)
    }

    return (
        <>
            <Accordion
                expanded={expanded}
                onChange={onExpand}
                disableGutters
                sx={{
                    boxShadow: 'none',
                    borderRadius: '0 !important',

                    '&::before': { display: 'none' },
                }}
            >
                <AccordionSummary
                    sx={{
                        px: 0,
                        minHeight: 'auto',

                        '.MuiAccordionSummary-content': {
                            m: 0,
                            height: '100%',
                        },
                    }}
                >
                    <PermissionsSetterRowLayout>
                        <Stack direction='row' alignItems='center' gap={1}>
                            <Typography variant='body2' fontWeight={600}>
                                {application}
                            </Typography>

                            <Chip size='small' label={checkedCount || 0} />
                        </Stack>

                        <Box justifyItems='center'>
                            <NativeCheckbox
                                label=''
                                checked={checkedCount === permissions.length}
                                onChange={(e) =>
                                    onChange(
                                        e.target.checked,
                                        _.map(permissions, 'code'),
                                    )
                                }
                            />
                        </Box>
                    </PermissionsSetterRowLayout>
                </AccordionSummary>

                <AccordionDetails sx={{ height: 300, p: 0 }}>
                    <AutoSizer>
                        {({ width, height }) => (
                            <FixedSizeList
                                itemSize={40}
                                width={width}
                                height={height}
                                itemCount={permissions.length}
                            >
                                {({ index, style }) => {
                                    const permission = permissions[index]

                                    return (
                                        <PermissionsSetterRow
                                            style={style}
                                            key={permission.code}
                                            permission={permission}
                                            isChecked={isChecked}
                                            onChange={(isNativeChecked, code) =>
                                                onChange(isNativeChecked, [
                                                    code,
                                                ])
                                            }
                                        />
                                    )
                                }}
                            </FixedSizeList>
                        )}
                    </AutoSizer>
                </AccordionDetails>
            </Accordion>
        </>
    )
}

export default PermissionsSetterGroup
