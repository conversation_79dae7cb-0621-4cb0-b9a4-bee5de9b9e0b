import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import SearchRounded from '@mui/icons-material/SearchRounded'
import { useTheme } from '@mui/material'
import Stack from '@mui/material/Stack'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { DEFAULT_OPTIONS_LIMIT } from '@/config/common'
import queryKeys from '@/config/queryClient/queryKeys'
import getPermissionsRequest, {
    GetPermissionsParams,
} from '@/requests/permission/getPermissionsRequest'
import { ApplicationName } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

import { createApplicationGroups } from '../-utils/createApplicationGroups'
import PermissionsSetterGroup from './PermissionsSetterGroup'
import PermissionsSetterRowLayout from './PermissionsSetterRowLayout'

interface PermissionsSetterProps {
    name: string
}

const PermissionsSetter = ({ name }: PermissionsSetterProps) => {
    const { palette } = useTheme()
    const { t } = useTranslation('common')

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [expandedGroup, setExpandedGroup] = React.useState<string | null>(
        null,
    )

    const permissionsParams = createRequestParams<GetPermissionsParams>({
        pagination: {
            limit: DEFAULT_OPTIONS_LIMIT,
            offset: 0,
        },
        search: debouncedSearch || undefined,
    })

    const { data: permissions, isPending } = useQuery({
        queryFn: () => getPermissionsRequest(permissionsParams),
        queryKey: [...queryKeys.permissions.index, permissionsParams],
    })

    const applicationGroups = React.useMemo(() => {
        return createApplicationGroups(permissions?.list || [])
    }, [permissions])

    return (
        <Stack gap={1}>
            <TextField
                value={search}
                size='small'
                placeholder={t('labels.search')}
                sx={{
                    '.MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                    },
                }}
                slotProps={{
                    input: {
                        startAdornment: <SearchRounded />,
                    },
                }}
                onChange={(e) => setSearch(e.target.value)}
            />

            <Stack borderTop={`1px solid ${palette.divider}`}>
                <PermissionsSetterRowLayout py={2}>
                    <Typography>{t('labels.category')}</Typography>

                    <Typography align='center'>
                        {t('labels.allowed')}
                    </Typography>

                    <Typography>{t('labels.description')}</Typography>
                </PermissionsSetterRowLayout>

                {isPending && <LoadingSpinner />}

                <Stack>
                    {_.keys(applicationGroups).map((application) => {
                        const typedAplication = application as ApplicationName

                        return (
                            <PermissionsSetterGroup
                                name={name}
                                key={typedAplication}
                                application={typedAplication}
                                expanded={application === expandedGroup}
                                permissions={applicationGroups[typedAplication]}
                                onExpand={() =>
                                    setExpandedGroup((p) => {
                                        if (p === typedAplication) return null
                                        return typedAplication
                                    })
                                }
                            />
                        )
                    })}
                </Stack>
            </Stack>
        </Stack>
    )
}

export default PermissionsSetter
