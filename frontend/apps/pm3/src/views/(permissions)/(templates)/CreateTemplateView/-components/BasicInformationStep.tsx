import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import { CreatePermissionTemplateBodyKeys } from '@/requests/permissionTemplate/createPermissionTemplateRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.template-name')}
                name={CreatePermissionTemplateBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.template-usage')}
                name={CreatePermissionTemplateBodyKeys.USAGE}
            />
        </Stack>
    )
}

export default BasicInformationStep
