import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import FormUsersAssigner from '@/components/resource/user/FormUsersAssigner'
import { DEFAULT_OPTIONS_LIMIT } from '@/config/common'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import { FilterTypeName } from '@/requests/params'
import getPermissionGroupRequest from '@/requests/permissionGroup/getPermissionGroupRequest'
import getPermissionGroupsRequest, {
    GetPermissionGroupsParams,
} from '@/requests/permissionGroup/getPermissionGroupsRequest'
import updatePermissionGroupRequest, {
    UpdatePermissionGroupBody,
    UpdatePermissionGroupBodyKeys,
} from '@/requests/permissionGroup/updatePermissionGroupRequest'
import { PermissionGroupResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'

const BasicInformationStep = React.lazy(
    () => import('./-components/BasicInformationStep'),
)

enum StepName {
    BASIC_INFORMATION,
    USERS,
    FACILITIES,
}

const nameValidator = async (
    value: string,
    group: PermissionGroupResponse | null,
) => {
    if (!value || !group || group?.name === value) return true

    const groupsParams = createRequestParams<GetPermissionGroupsParams>({
        pagination: {
            limit: DEFAULT_OPTIONS_LIMIT,
            offset: 0,
        },
        filter: [
            {
                field: 'name',
                type: FilterTypeName.MATCH,
                value: value,
            },
        ],
    })

    try {
        const { list } = await getPermissionGroupsRequest(groupsParams)
        return list.length === 0
    } catch {
        return true
    }
}

const UpdateGroupView = () => {
    const navigate = useNavigate()

    const queryClient = useQueryClient()
    const [t] = useTranslation('common')

    const params = useParams()
    const groupId = parseParamInt(params.groupId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { isPending, data: group } = useQuery({
        queryFn: () => getPermissionGroupRequest(groupId),
        queryKey: queryKeys.permissionGroup(groupId).index,
    })

    const { mutate, isPending: isUpdatePending } = useMutation({
        mutationFn: (body: UpdatePermissionGroupBody) =>
            updatePermissionGroupRequest(groupId, body),
        onSuccess: async ({ data }) => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionGroups.index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionGroup(groupId).index,
            })

            const href = createHrefWithParams(
                routerPaths.permissions.group.index,
                {
                    groupId: data.id,
                },
            )

            toast.success(t('texts.success-action'))
            navigate(href)
        },
    })

    const initialValues: UpdatePermissionGroupBody = {
        [UpdatePermissionGroupBodyKeys.FACILITIES]: _.map(
            group?.data.facilities,
            (f) => f.id,
        ),
        [UpdatePermissionGroupBodyKeys.USERS]: _.map(
            group?.data.users,
            (u) => u.id,
        ),
        [UpdatePermissionGroupBodyKeys.NAME]: group?.data.name || '',
        [UpdatePermissionGroupBodyKeys.USAGE]: group?.data.usage || '',
        [UpdatePermissionGroupBodyKeys.PERMISSION_TEMPLATES]: _.map(
            group?.data.permission_templates,
            (t) => t.id,
        ),
    }

    const validationSchema = React.useMemo(() => {
        return yup.object().shape({
            [UpdatePermissionGroupBodyKeys.NAME]:
                step === StepName.BASIC_INFORMATION
                    ? yup
                          .string()
                          .required('errors.required-field')
                          .test(
                              'unique-name',
                              'errors.name-already-exists',
                              (value) =>
                                  nameValidator(value, group?.data || null),
                          )
                    : yup.string(),
            [UpdatePermissionGroupBodyKeys.PERMISSION_TEMPLATES]:
                step === StepName.BASIC_INFORMATION
                    ? yup.array().min(1, 'errors.required-field')
                    : yup.array(),
        })
    }, [step, group])

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: UpdatePermissionGroupBody) => {
        if (step !== StepName.FACILITIES) {
            onNext()
            return
        }

        mutate(body)
    }

    if (isPending) return <LoadingSpinner />

    if (!group?.data) {
        return <Navigate to={routerPaths.permissions.groups.index} />
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.persons')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.objects')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isUpdatePending}>
                {step === StepName.FACILITIES
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            maxWidth={1100}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validateOnChange={false}
            validationSchema={validationSchema}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.USERS && (
                    <FormUsersAssigner
                        name={UpdatePermissionGroupBodyKeys.USERS}
                    />
                )}

                {step === StepName.FACILITIES && (
                    <FormFacilitiesAssigner
                        name={UpdatePermissionGroupBodyKeys.FACILITIES}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default UpdateGroupView
