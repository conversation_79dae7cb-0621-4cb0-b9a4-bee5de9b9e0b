import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import FormPermissionTemplatesAsyncAutocomplete from '@/components/resource/permissionTemplate/FormPermissionTemplatesAsyncAutocomplete'
import { UpdatePermissionGroupBodyKeys } from '@/requests/permissionGroup/updatePermissionGroupRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={2}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <FormTextField
                label={t('labels.group-name')}
                name={UpdatePermissionGroupBodyKeys.NAME}
            />

            <FormTextField
                label={t('labels.group-usage')}
                name={UpdatePermissionGroupBodyKeys.USAGE}
            />

            <FormPermissionTemplatesAsyncAutocomplete
                multiple
                name={UpdatePermissionGroupBodyKeys.PERMISSION_TEMPLATES}
            />
        </Stack>
    )
}

export default BasicInformationStep
