import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import FormPermissionTemplatesAsyncAutocomplete from '@/components/resource/permissionTemplate/FormPermissionTemplatesAsyncAutocomplete'
import { CreatePermissionGroupBodyKeys } from '@/requests/permissionGroup/createPermissionGroupRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation('common')

    return (
        <Stack gap={4}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <Stack gap={2}>
                <FormTextField
                    label={t('labels.group-name')}
                    name={CreatePermissionGroupBodyKeys.NAME}
                />

                <FormTextField
                    label={t('labels.group-usage')}
                    name={CreatePermissionGroupBodyKeys.USAGE}
                />

                <FormPermissionTemplatesAsyncAutocomplete
                    multiple
                    name={CreatePermissionGroupBodyKeys.PERMISSION_TEMPLATES}
                />
            </Stack>
        </Stack>
    )
}

export default BasicInformationStep
