import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import CellLink from '@/components/other/CellLink'
import DataGridHeader from '@/components/other/DataGridHeader'
import { gridFilterMap } from '@/config/gridFilter'
import queryClient from '@/config/queryClient/queryClient'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import usePersistTable from '@/hooks/usePersistTable'
import deletePermissionGroupRequest from '@/requests/permissionGroup/deletePermissionGroupRequest'
import getPermissionGroupsRequest from '@/requests/permissionGroup/getPermissionGroupsRequest'
import { PermissionGroupResponse } from '@/requests/responses'
import createRequestParams from '@/utils/createRequestParams'
import { formatDate, formatUserName } from '@/utils/format'

const GroupsTable = () => {
    const { palette, breakpoints } = useTheme()
    const navigate = useNavigate()
    const { t } = useTranslation('common')
    const gridRef = useGridApiRef()

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')
    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSort()
    const { gridStringOperators } = useGridOperators()

    const [groupForDelete, setGroupForDelete] =
        React.useState<null | PermissionGroupResponse>(null)

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: (groupId: number) => deletePermissionGroupRequest(groupId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionGroups.index,
            })

            toast.success(t('texts.success-action'))
            setGroupForDelete(null)
        },
    })

    const params = createRequestParams({
        sort,
        filter,
        pagination,
        search: debouncedSearch,
    })

    const { data: groups, isFetching } = useQuery({
        queryFn: () => getPermissionGroupsRequest(params),
        queryKey: [...queryKeys.permissionGroups.index, params],
        placeholderData: keepPreviousData,
    })

    const onDetail = React.useCallback(
        (group: PermissionGroupResponse) => {
            const href = createHrefWithParams(
                routerPaths.permissions.group.index,
                {
                    groupId: group.id,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const onCreate = () => {
        navigate(routerPaths.permissions.groups.create)
    }

    const onUpdate = React.useCallback(
        (group: PermissionGroupResponse) => {
            const href = createHrefWithParams(
                routerPaths.permissions.group.update,
                {
                    groupId: group.id,
                },
            )

            navigate(href)
        },
        [navigate],
    )

    const columns = React.useMemo<GridColDef<PermissionGroupResponse>[]>(
        () => [
            {
                field: 'name',
                headerName: t('labels.group-name').toString(),
                minWidth: 250,
                filterOperators: gridStringOperators(),
                renderCell: ({ row }) => {
                    const href = createHrefWithParams(
                        routerPaths.permissions.group.index,
                        {
                            groupId: row.id,
                        },
                    )

                    return <CellLink label={row.name} to={href} />
                },
            },
            {
                field: 'permissionTemplates',
                headerName: t('labels.templates').toString(),
                filterable: false,
                sortable: false,
                minWidth: 250,
                valueGetter: (__, row) => _.size(row.permission_templates),
            },
            {
                minWidth: 100,
                field: 'users.id',
                headerName: t('labels.persons').toString(),
                filterable: false,
                sortable: false,
                valueGetter: (__, row) => _.size(row.users),
            },
            {
                minWidth: 100,
                field: 'facilities.id',
                headerName: t('labels.objects').toString(),
                filterable: false,
                sortable: false,
                valueGetter: (__, row) => _.size(row.facilities),
            },
            {
                minWidth: 200,
                field: 'created_at',
                headerName: t('labels.created-at').toString(),
                filterable: false,
                sortable: false,
                valueGetter: (value) => formatDate(value),
            },
            {
                minWidth: 200,
                field: 'inserted_by',
                headerName: t('labels.inserted-by').toString(),
                filterable: false,
                sortable: false,
                valueGetter: (__, row) => formatUserName(row.inserted_by),
            },
            {
                minWidth: 200,
                field: 'updated_at',
                headerName: t('labels.updated-at').toString(),
                filterable: false,
                sortable: false,
                valueGetter: (value) => formatDate(value),
            },
            {
                minWidth: 200,
                field: 'updated_by',
                headerName: t('labels.updated-by').toString(),
                filterable: false,
                sortable: false,
                valueGetter: (__, row) => formatUserName(row.updated_by),
            },
            {
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: ({ row }) => {
                    return (
                        <IconButtonDropdown
                            icon={<MoreVert fontSize='small' />}
                            menuContent={({ onClose }) => (
                                <Stack>
                                    <MenuItem onClick={() => onDetail(row)}>
                                        {t('actions.show-detail')}
                                    </MenuItem>

                                    <MenuItem onClick={() => onUpdate(row)}>
                                        {t('actions.update')}
                                    </MenuItem>

                                    <MenuItem
                                        onClick={() => {
                                            setGroupForDelete(row)
                                            onClose()
                                        }}
                                        sx={{
                                            color: `${palette.error.main} !important`,
                                        }}
                                    >
                                        {t('actions.delete')}
                                    </MenuItem>
                                </Stack>
                            )}
                        />
                    )
                },
            },
        ],
        [gridStringOperators, onDetail, onUpdate, palette.error.main, t],
    )

    usePersistTable(gridRef, routerPaths.permissions.groups.index)

    return (
        <Card sx={{ height: '100%', width: '100%' }}>
            <CardContent
                sx={{
                    p: '0 !important',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <DataGridHeader
                    search={search}
                    onSearch={setSearch}
                    gridRef={gridRef}
                    actions={
                        <Button
                            onClick={onCreate}
                            startIcon={<AddCircleOutlineOutlined />}
                        >
                            {t('actions.create-group')}
                        </Button>
                    }
                />

                <DataGridPro
                    disableColumnPinning
                    pagination
                    sx={{
                        height: '100%',
                        minHeight: 150,
                        [breakpoints.up('lg')]: {
                            minHeight: 400,
                        },
                    }}
                    apiRef={gridRef}
                    columns={columns}
                    loading={isFetching}
                    paginationMode='server'
                    sortingMode='server'
                    filterMode='server'
                    disableColumnSelector
                    paginationModel={paginationModel}
                    filterModel={filterModel}
                    onPaginationModelChange={setPaginationModel}
                    onFilterModelChange={(model) => setFilterModel(model)}
                    sortModel={sortModel}
                    onSortModelChange={(model) => setSortModel(model)}
                    rows={groups?.list || []}
                    rowCount={groups?.total_count || 0}
                    initialState={{
                        density: 'compact',
                        pinnedColumns: {
                            right: ['actions'],
                        },
                    }}
                />

                {groupForDelete && (
                    <ConfirmationDialog
                        isLoading={isDeletePending}
                        onClose={() => setGroupForDelete(null)}
                        onConfirm={() => mutate(groupForDelete.id)}
                        confirmLabel={t('actions.delete')}
                    >
                        <Typography>
                            {t('texts.delete-permission-group-confirmation')}
                        </Typography>
                    </ConfirmationDialog>
                )}
            </CardContent>
        </Card>
    )
}

export default GroupsTable
