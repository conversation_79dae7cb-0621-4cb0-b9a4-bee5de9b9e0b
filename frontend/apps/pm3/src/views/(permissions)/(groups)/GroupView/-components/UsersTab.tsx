import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form } from 'formik'
import * as _ from 'lodash'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormUsersAssigner from '@/components/resource/user/FormUsersAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import updatePermissionGroupRequest, {
    UpdatePermissionGroupBody,
    UpdatePermissionGroupBodyKeys,
} from '@/requests/permissionGroup/updatePermissionGroupRequest'
import { PermissionGroupResponse } from '@/requests/responses'

interface UsersTabProps {
    group: PermissionGroupResponse
}

const UsersTab = ({ group }: UsersTabProps) => {
    const { t } = useTranslation('common')

    const queryClient = useQueryClient()

    const { mutate, isPending } = useMutation({
        mutationFn: (body: UpdatePermissionGroupBody) =>
            updatePermissionGroupRequest(group.id, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.permissionGroup(group.id).index,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const initialValues: UpdatePermissionGroupBody = {
        [UpdatePermissionGroupBodyKeys.FACILITIES]: _.map(
            group.facilities,
            'id',
        ),
        [UpdatePermissionGroupBodyKeys.USERS]: _.map(group.users, 'id'),
        [UpdatePermissionGroupBodyKeys.NAME]: group.name,
        [UpdatePermissionGroupBodyKeys.USAGE]: group.usage,
        [UpdatePermissionGroupBodyKeys.PERMISSION_TEMPLATES]: _.map(
            group.permission_templates,
            (t) => t.id,
        ),
    }

    const onSubmit = (body: UpdatePermissionGroupBody) => {
        mutate(body)
    }

    return (
        <BlockingFormik initialValues={initialValues} onSubmit={onSubmit}>
            {({ resetForm }) => (
                <Card
                    component={Form}
                    sx={{
                        flex: '1',
                        display: 'flex',
                        flexDirection: 'column',
                        borderTopLeftRadius: 0,
                    }}
                >
                    <CardContent
                        sx={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            overflow: 'hidden',
                        }}
                    >
                        <FormUsersAssigner
                            name={UpdatePermissionGroupBodyKeys.USERS}
                        />
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'space-between' }}>
                        <Button
                            type='button'
                            color='inherit'
                            onClick={() => resetForm()}
                        >
                            {t('actions.discard-changes')}
                        </Button>

                        <SubmitButton loading={isPending}>
                            {t('actions.save-changes')}
                        </SubmitButton>
                    </CardActions>
                </Card>
            )}
        </BlockingFormik>
    )
}

export default UsersTab
