import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import DocumentScannerOutlined from '@mui/icons-material/DocumentScannerOutlined'
import GroupAddOutlined from '@mui/icons-material/GroupAddOutlined'
import PersonOutlineOutlined from '@mui/icons-material/PersonOutlineOutlined'
import TodayOutlined from '@mui/icons-material/TodayOutlined'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Link from '@mui/material/Link'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import { InformationRow } from '@/components/other/InformationRow'
import routerPaths from '@/config/routerPaths'
import { PermissionGroupResponse } from '@/requests/responses'
import { formatDate, formatUserName } from '@/utils/format'

interface BasicInformationTabProps {
    group: PermissionGroupResponse
}

const BasicInformationTab = ({ group }: BasicInformationTabProps) => {
    const { t } = useTranslation('common')

    return (
        <Stack direction='row' flex={1} overflow='hidden'>
            <Card
                sx={{ borderTopLeftRadius: 0, width: '100%', overflow: 'auto' }}
            >
                <CardContent
                    sx={{
                        gap: 3,
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Typography fontWeight={600}>
                        {t('labels.basic-information')}
                    </Typography>

                    <InformationRow
                        startIcon={<GroupAddOutlined />}
                        title={t('labels.group-name')}
                    >
                        <Typography>{group.name}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<DocumentScannerOutlined />}
                        title={t('labels.templates')}
                    >
                        <Stack gap={1} flexWrap='wrap'>
                            {_.map(group.permission_templates, (template) => {
                                const href = createHrefWithParams(
                                    routerPaths.permissions.template.index,
                                    {
                                        templateId: template.id,
                                    },
                                )

                                return (
                                    <Link key={template.id} href={href}>
                                        {template.name}
                                    </Link>
                                )
                            })}
                        </Stack>
                    </InformationRow>

                    <InformationRow
                        startIcon={<TodayOutlined />}
                        title={t('labels.created-at')}
                    >
                        <Typography>{formatDate(group.created_at)}</Typography>
                    </InformationRow>

                    <InformationRow
                        startIcon={<PersonOutlineOutlined />}
                        title={t('labels.inserted-by')}
                    >
                        <Typography>
                            {formatUserName(group.inserted_by)}
                        </Typography>
                    </InformationRow>
                </CardContent>
            </Card>
        </Stack>
    )
}

export default BasicInformationTab
