import React from 'react'

import routerPaths from '@/config/routerPaths'

const GroupsView = React.lazy(() => import('./GroupsView/GroupsView'))
const GroupView = React.lazy(() => import('./GroupView/GroupView'))
const UpdateGroupView = React.lazy(
    () => import('./UpdateGroupView/UpdateGroupView'),
)
const CreateGroupView = React.lazy(
    () => import('./CreateGroupView/CreateGroupView'),
)

const groupsRoutes = [
    {
        path: routerPaths.permissions.groups.index,
        element: <GroupsView />,
    },
    {
        path: routerPaths.permissions.group.index,
        element: <GroupView />,
    },
    {
        path: routerPaths.permissions.groups.create,
        element: <CreateGroupView />,
    },
    {
        path: routerPaths.permissions.group.update,
        element: <UpdateGroupView />,
    },
]

export default groupsRoutes
