import SpaceDashboardOutlined from '@mui/icons-material/SpaceDashboardOutlined'
import { useTranslation } from 'react-i18next'

import Layout from '@/components/layout/Layout'
import PageHead from '@/components/layout/PageHead'
import routerPaths from '@/config/routerPaths'
import AuthenticatedGuard from '@/guard/AuthenticatedGuard'

const WapLayoutContent = () => {
    const { t } = useTranslation('common')

    const sidebarItems = [
        {
            href: routerPaths.wap.index,
            label: t('labels.dashboard'),
            icon: <SpaceDashboardOutlined />,
        },
        {
            href: routerPaths.wap.services.index,
            label: t('labels.services'),
            icon: <SpaceDashboardOutlined />,
        },
    ]

    const map = {
        [routerPaths.wap.index]: t('labels.wap'),
        [routerPaths.wap.services.index]: t('labels.services'),
        [routerPaths.wap.service.index]: '2024-07 (1.7.2024 - 31.7.2024)',
        [routerPaths.wap.service.editor]: t('labels.editor'),
    }

    return (
        <>
            <PageHead map={map} />

            <Layout
                breadcrumbsMap={map}
                logoUrl='/images/logos/default.png'
                sidebarItems={sidebarItems}
            />
        </>
    )
}

const WapLayout = () => {
    return (
        <AuthenticatedGuard>
            <WapLayoutContent />
        </AuthenticatedGuard>
    )
}

export default WapLayout
