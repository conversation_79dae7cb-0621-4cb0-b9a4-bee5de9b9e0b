import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import MenuItem from '@mui/material/MenuItem'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import DataGridHeader from '@/components/other/DataGridHeader'
import routerPaths from '@/config/routerPaths'

const ServicesTable = () => {
    const [t] = useTranslation('common')

    const navigate = useNavigate()
    const gridRef = useGridApiRef()

    const onUpdate = React.useCallback(() => {
        const href = createHrefWithParams(routerPaths.wap.service.editor, {
            serviceId: '1',
        })

        navigate(href)
    }, [navigate])

    const columns = React.useMemo<GridColDef<(typeof data)[0]>[]>(
        () => [
            {
                minWidth: 350,
                field: 'starts_at',
                headerName: t('labels.period'),
            },
            {
                minWidth: 350,
                field: 'object',
                headerName: t('labels.object'),
            },
            {
                minWidth: 350,
                field: 'center',
                headerName: t('labels.center'),
            },
            {
                minWidth: 350,
                field: 'contract',
                headerName: t('labels.contract'),
            },
            {
                minWidth: 350,
                field: 'count_of_positions',
                headerName: t('labels.count-of-positions'),
            },
            {
                minWidth: 350,
                field: 'count_of_errors',
                headerName: t('labels.count-of-errors'),
            },
            {
                minWidth: 200,
                field: 'involment',
                headerName: t('labels.involment'),
            },
            {
                minWidth: 200,
                field: 'effectivity',
                headerName: t('labels.effectivity'),
            },
            {
                minWidth: 200,
                field: 'work_attendance',
                headerName: t('labels.attendance'),
            },
            {
                minWidth: 200,
                field: 'billing_status',
                headerName: t('labels.billing-status'),
            },
            {
                minWidth: 200,
                field: 'work_attendance_status',
                headerName: t('labels.attendance-status'),
            },
            {
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: () => (
                    <IconButtonDropdown
                        icon={<MoreVert fontSize='small' />}
                        menuContent={
                            <MenuItem onClick={onUpdate}>
                                {t('actions.update')}
                            </MenuItem>
                        }
                    />
                ),
            },
        ],
        [onUpdate, t],
    )

    return (
        <Card sx={{ height: '100%', width: '100%' }}>
            <CardContent
                sx={{
                    p: '0 !important',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <DataGridHeader
                    search=''
                    onSearch={() => {}}
                    gridRef={gridRef}
                    actions={
                        <Button startIcon={<AddCircleOutlineOutlined />}>
                            {t('actions.add-period')}
                        </Button>
                    }
                />

                <DataGridPro
                    disableColumnPinning
                    rows={data}
                    apiRef={gridRef}
                    columns={columns}
                    sx={{ height: '100%', flex: 1 }}
                    initialState={{
                        density: 'compact',
                        pinnedColumns: {
                            right: ['actions'],
                        },
                    }}
                />
            </CardContent>
        </Card>
    )
}

const data = [
    {
        id: 19,
        object: 'Winterfell',
        center: 'Lorem Ipsum',
        contract: 'Lorem Ipsum',
        count_of_positions: 20,
        count_of_errors: 20,
        involment: 0.8,
        effectivity: 0.6,
        work_attendance: 0.5,
        billing_status: 'success',
        work_attendance_status: 'success',
        starts_at: '2024-05-21T19:16:19+00:00',
        ends_at: '2024-05-26T19:16:19+00:00',
    },
    {
        id: 50,
        object: 'Winterfell',
        center: 'Lorem Ipsum',
        contract: 'Lorem Ipsum',
        count_of_positions: 20,
        count_of_errors: 20,
        involment: 0.8,
        effectivity: 0.6,
        work_attendance: 0.5,
        billing_status: 'success',
        work_attendance_status: 'success',
        starts_at: '2024-05-21T19:16:19+00:00',
        ends_at: '2024-05-26T19:16:19+00:00',
    },
    {
        id: 59,
        object: 'Winterfell',
        center: 'Lorem Ipsum',
        contract: 'Lorem Ipsum',
        count_of_positions: 20,
        count_of_errors: 20,
        involment: 0.8,
        effectivity: 0.6,
        work_attendance: 0.5,
        billing_status: 'success',
        work_attendance_status: 'success',
        starts_at: '2024-05-21T19:16:19+00:00',
        ends_at: '2024-05-26T19:16:19+00:00',
    },
    {
        id: 73,
        object: 'Winterfell',
        center: 'Lorem Ipsum',
        contract: 'Lorem Ipsum',
        count_of_positions: 20,
        count_of_errors: 20,
        involment: 0.8,
        effectivity: 0.6,
        work_attendance: 0.5,
        billing_status: 'success',
        work_attendance_status: 'success',
        starts_at: '2024-05-21T19:16:19+00:00',
        ends_at: '2024-05-26T19:16:19+00:00',
    },
    {
        id: 75,
        object: 'Winterfell',
        center: 'Lorem Ipsum',
        contract: 'Lorem Ipsum',
        count_of_positions: 20,
        count_of_errors: 20,
        involment: 0.8,
        effectivity: 0.6,
        work_attendance: 0.5,
        billing_status: 'success',
        work_attendance_status: 'success',
        starts_at: '2024-05-21T19:16:19+00:00',
        ends_at: '2024-05-26T19:16:19+00:00',
    },
]

export default ServicesTable
