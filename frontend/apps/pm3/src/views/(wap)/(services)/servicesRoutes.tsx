import React from 'react'

import routerPaths from '@/config/routerPaths'

const ServicesView = React.lazy(() => import('./ServicesView/ServicesView'))
const ServicesEditorView = React.lazy(
    () => import('./ServiceEditorView/ServiceEditorView'),
)

const servicesRoutes = [
    {
        path: routerPaths.wap.services.index,
        element: <ServicesView />,
    },
    {
        path: routerPaths.wap.service.editor,
        element: <ServicesEditorView />,
    },
]

export default servicesRoutes
