import { DndContext, DragOverlay } from '@dnd-kit/core'
import AddOutlined from '@mui/icons-material/AddOutlined'
import PersonAddOutlined from '@mui/icons-material/PersonAddOutlined'
import PersonPinOutlined from '@mui/icons-material/PersonPinOutlined'
import ReceiptLongOutlined from '@mui/icons-material/ReceiptLongOutlined'
import SearchRounded from '@mui/icons-material/SearchRounded'
import TodayOutlined from '@mui/icons-material/TodayOutlined'
import { alpha, useTheme } from '@mui/material'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import TextField from '@mui/material/TextField'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { GridOnScrollProps, VariableSizeGrid } from 'react-window'

import { formatUserName } from '@/utils/format'

import ContentColumns from './-components/ContentColumns'
import GroupsNavigation from './-components/GroupsNavigation'
import AddPositionModal from './-components/modal/AddPositionModal'
import CreatePositionModal from './-components/modal/CreatePositionModal'
import ObjectInformation from './-components/ObjectInformation'
import UsersList from './-components/UsersList'
import {
    filterContentCell,
    filterLeftHeaderCells,
    filterTopHeaderCells,
} from './-utils/cell'
import { Cell } from './types'

const ServiceEditorView = () => {
    const { t } = useTranslation('common')

    const queryClient = useQueryClient()
    const { palette, spacing } = useTheme()

    const starts_at = dayjs().startOf('month')
    const ends_at = dayjs().endOf('month')

    const headerRef = React.useRef<HTMLDivElement | null>(null)
    const contentRef = React.useRef<VariableSizeGrid | null>(null)

    const { data } = useQuery({
        queryFn: () => {
            return demoData
        },
        queryKey: ['demo'],
    })

    const [groupId, setGroupId] = React.useState(-1)

    const [addPositionOpen, setAddPositionOpen] = React.useState(false)
    const [createPositionOpen, setCreatePositionOpen] = React.useState(false)
    const [addUsersVisibility, setAddUsersVisibility] = React.useState(false)

    const [selectedCells, setSelectedCells] = React.useState<Cell[]>([])

    const onScroll = ({
        scrollTop,
        scrollUpdateWasRequested,
    }: GridOnScrollProps) => {
        if (!scrollUpdateWasRequested) {
            headerRef.current?.scrollTo({
                top: scrollTop,
            })
        }
    }

    const onResize = () => {
        contentRef.current?.resetAfterIndices({
            columnIndex: 0,
            rowIndex: 0,
            shouldForceUpdate: false,
        })
    }

    const onToggleAddUsers = () => {
        setAddUsersVisibility((p) => !p)
    }

    const onAddGroup = () => {
        queryClient.setQueryData(['demo'], {
            ...data,
            groups: [
                ...(data?.groups || []),
                {
                    id: _.size(data?.groups) + 1,
                    group_name: 'Úklid',
                    objects: [],
                },
            ],
        })

        setSelectedCells([])
    }

    const group = React.useMemo(() => {
        return _.find(data?.groups, (group) => group.id === groupId)
    }, [groupId, data])

    const days = React.useMemo(() => {
        const daysDifference = ends_at.diff(starts_at, 'day')

        return _.times(daysDifference + 1, (dayNumber) =>
            starts_at.add(dayNumber, 'day'),
        )
    }, [ends_at, starts_at])

    const columns = React.useMemo(() => {
        return _.map(group?.objects, (object) => {
            const { shifts, object_name } = object

            const rows = _.map(days, (day) => {
                const shift = _.find(shifts, (shift) =>
                    dayjs(shift.start_at).isSame(day, 'day'),
                )

                return { person: shift ? formatUserName(shift.person) : null }
            })

            return { rows, objectName: object_name }
        })
    }, [days, group])

    const onSelectCell = (cell: Cell) => {
        setSelectedCells((prevCells) => {
            const rowsLength = days.length
            const columnsLength = columns.length

            if (cell.rowIndex === 0) {
                const filteredCells = filterTopHeaderCells(prevCells, cell)

                const selected =
                    prevCells.length - filteredCells.length === rowsLength

                if (selected) {
                    return filteredCells
                }

                const newColumnCells = _.times(rowsLength, (rowIndex) => ({
                    // Skip top header
                    rowIndex: rowIndex + 1,
                    columnIndex: cell.columnIndex,
                }))

                return _.uniqWith(
                    _.concat(prevCells, newColumnCells),
                    _.isEqual,
                )
            }

            if (cell.columnIndex === 0) {
                const filteredCells = filterLeftHeaderCells(prevCells, cell)

                const selected =
                    prevCells.length - filteredCells.length === columnsLength

                if (selected) {
                    return filteredCells
                }

                const newRowCells = _.times(columnsLength, (columnIndex) => ({
                    // Skip left header
                    columnIndex: columnIndex + 1,
                    rowIndex: cell.rowIndex,
                }))

                return _.uniqWith(_.concat(prevCells, newRowCells), _.isEqual)
            }

            const filteredCells = filterContentCell(prevCells, cell)
            const selected = prevCells.length - filteredCells.length === 1

            if (selected) {
                return filteredCells
            }

            return _.concat(prevCells, cell)
        })
    }

    React.useEffect(() => {
        const newId = _.first(data?.groups)?.id

        if (groupId === -1 && _.isNumber(newId)) {
            setGroupId(newId as number)
        }
    }, [groupId, data])

    return (
        <Stack flex={1} gap={1}>
            <ObjectInformation
                onCreatePosition={() => setCreatePositionOpen(true)}
            />

            <Stack flex={1}>
                <Tabs
                    value={1}
                    sx={{
                        width: 'max-content',
                        background: palette.grey[50],
                        borderTopLeftRadius: spacing(2),
                        borderTopRightRadius: spacing(2),
                    }}
                >
                    <Tab
                        value={1}
                        label={t('labels.service-editor')}
                        icon={<TodayOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />

                    <Tab
                        value={2}
                        label={t('labels.attendance-approval')}
                        icon={<PersonPinOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />

                    <Tab
                        value={3}
                        label={t('labels.invoice-approval')}
                        icon={<ReceiptLongOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />
                </Tabs>

                <DndContext>
                    <Stack direction='row' flex={1} gap={1}>
                        <Card
                            sx={{
                                height: '100%',
                                flex: 1,
                                borderTopLeftRadius: 0,
                            }}
                        >
                            <CardContent
                                sx={{
                                    p: '0 !important',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                }}
                            >
                                <Stack p={0.5} gap={2} direction='row'>
                                    <TextField
                                        value={''}
                                        size='small'
                                        placeholder={t('labels.search')}
                                        sx={{
                                            maxWidth: 300,
                                            mr: 'auto',

                                            '.MuiOutlinedInput-notchedOutline':
                                                {
                                                    border: 'none',
                                                },
                                        }}
                                        slotProps={{
                                            input: {
                                                startAdornment: (
                                                    <SearchRounded />
                                                ),
                                            },
                                        }}
                                        onChange={(e) => e.target.value}
                                    />

                                    <IconButton
                                        onClick={onToggleAddUsers}
                                        color={
                                            addUsersVisibility
                                                ? 'primary'
                                                : 'default'
                                        }
                                        sx={{
                                            background: addUsersVisibility
                                                ? alpha(
                                                      palette.primary.main,
                                                      0.1,
                                                  )
                                                : 'transparent',
                                        }}
                                    >
                                        <PersonAddOutlined fontSize='small' />
                                    </IconButton>
                                </Stack>

                                <GroupsNavigation
                                    groupId={groupId}
                                    setGroupId={setGroupId}
                                    groups={data?.groups || []}
                                    onAddGroup={onAddGroup}
                                />

                                <Stack direction='row' flex={1}>
                                    <ContentColumns
                                        columns={columns}
                                        days={days}
                                        onResize={onResize}
                                        onScroll={onScroll}
                                        onSelectCell={onSelectCell}
                                        selectedCells={selectedCells}
                                    />

                                    {!addUsersVisibility && (
                                        <Stack
                                            px={2}
                                            width={200}
                                            alignItems='center'
                                            justifyContent='center'
                                            bgcolor={palette.grey[100]}
                                        >
                                            <Button
                                                color='inherit'
                                                startIcon={<AddOutlined />}
                                                onClick={() =>
                                                    setAddPositionOpen(true)
                                                }
                                                sx={{
                                                    flexDirection: 'column',
                                                    paddingBottom: spacing(1),
                                                    paddingTop: spacing(1),
                                                    height: 'auto !important',
                                                    fontSize: '13px !important',
                                                    textTransform: 'none',
                                                }}
                                            >
                                                {t(
                                                    'actions.add-another-position',
                                                )}
                                            </Button>
                                        </Stack>
                                    )}
                                </Stack>
                            </CardContent>
                        </Card>

                        {addUsersVisibility && (
                            <UsersList
                                onClose={() => setAddUsersVisibility(false)}
                            />
                        )}
                    </Stack>

                    {/* Don't delete */}
                    <DragOverlay></DragOverlay>
                </DndContext>

                {addPositionOpen && (
                    <AddPositionModal
                        onClose={() => setAddPositionOpen(false)}
                    />
                )}

                {createPositionOpen && (
                    <CreatePositionModal
                        onClose={() => setCreatePositionOpen(false)}
                    />
                )}
            </Stack>
        </Stack>
    )
}

const demoData = {
    groups: [
        {
            id: 1,
            group_name: 'Security',
            objects: _.times(100, () => ({
                id: 1,
                object_name: 'Brána (1)',
                shifts: [
                    {
                        id: 1,
                        start_at: dayjs().set('date', 1),
                        person: {
                            first_name: 'Jan',
                            last_name: 'Novák',
                        },
                    },
                    {
                        id: 2,
                        start_at: dayjs().set('date', 3),
                        person: {
                            first_name: 'Jan',
                            last_name: 'Novák',
                        },
                    },
                ],
            })),
        },
        {
            id: 2,
            group_name: 'Úklid',
            objects: [
                {
                    id: 1,
                    object_name: 'Brána (1)',
                    shifts: [
                        {
                            id: 1,
                            start_at: dayjs().set('date', 1),
                            person: {
                                first_name: 'Jan',
                                last_name: 'Novák',
                            },
                        },
                    ],
                },
            ],
        },
    ],
}

export default ServiceEditorView
