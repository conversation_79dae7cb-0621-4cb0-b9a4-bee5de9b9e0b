import { useDraggable } from '@dnd-kit/core'
import Box from '@mui/material/Box'
import React from 'react'

interface DraggableItemProps {
    children: React.ReactNode
    id: string
}

const DraggableItem = ({ children, id }: DraggableItemProps) => {
    const { attributes, listeners, setNodeRef, transform } = useDraggable({
        id,
    })

    const style = transform
        ? {
              transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
          }
        : undefined

    return (
        <Box
            ref={setNodeRef}
            style={style}
            {...listeners}
            {...attributes}
            sx={{ cursor: 'grab', background: '#fff' }}
        >
            {children}
        </Box>
    )
}
export default DraggableItem
