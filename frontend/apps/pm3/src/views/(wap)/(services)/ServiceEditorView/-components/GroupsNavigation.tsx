import Add from '@mui/icons-material/Add'
import MoreVert from '@mui/icons-material/MoreVert'
import { useTheme } from '@mui/material'
import Badge from '@mui/material/Badge'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

interface GroupsNavigationProps {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    groups: any[]
    groupId: number
    onAddGroup: () => void
    setGroupId: React.SetStateAction<React.Dispatch<number>>
}

const GroupsNavigation = ({
    groupId,
    setGroupId,
    groups,
    onAddGroup,
}: GroupsNavigationProps) => {
    const [t] = useTranslation('common')

    const { typography, palette } = useTheme()

    if (groupId === -1) return <></>

    return (
        <Stack
            direction='row'
            alignItems='center'
            px={1.5}
            sx={{
                borderTop: '1px solid',
                borderTopColor: palette.divider,
            }}
        >
            <Typography mr={1.5} variant='body2' color='text.disabled'>
                {t('labels.groups')}:
            </Typography>

            <Tabs
                value={groupId}
                onChange={(__, value) => setGroupId(value)}
                sx={{
                    '.MuiTab-root': {
                        flexDirection: 'row',
                    },
                }}
            >
                {_.map(groups, (group) => (
                    <Tab
                        key={group.id}
                        value={group.id}
                        sx={{
                            ...typography.body2,
                        }}
                        label={
                            <Stack direction='row' gap={1} alignItems='center'>
                                {group.group_name}

                                <Badge
                                    color='error'
                                    badgeContent='2'
                                    variant='dot'
                                >
                                    <MoreVert
                                        sx={{
                                            fontSize: 18,
                                            color: palette.action
                                                .disabledBackground,
                                        }}
                                    />
                                </Badge>
                            </Stack>
                        }
                    />
                ))}
            </Tabs>

            <IconButton onClick={onAddGroup}>
                <Add sx={{ fontSize: 18 }} />
            </IconButton>
        </Stack>
    )
}

export default GroupsNavigation
