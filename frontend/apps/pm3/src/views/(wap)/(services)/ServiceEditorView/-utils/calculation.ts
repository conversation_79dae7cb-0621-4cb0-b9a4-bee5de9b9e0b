import React from 'react'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getCellIndicies = (child: any) => {
    return { row: child.props.rowIndex, column: child.props.columnIndex }
}

export const getCellShownIndicies = (children: React.ReactNode) => {
    let minRow = Infinity
    let maxRow = -Infinity
    let minColumn = Infinity
    let maxColumn = -Infinity

    React.Children.forEach(children, (child) => {
        const { row, column } = getCellIndicies(child)
        minRow = Math.min(minRow, row)
        maxRow = Math.max(maxRow, row)
        minColumn = Math.min(minColumn, column)
        maxColumn = Math.max(maxColumn, column)
    })

    return {
        from: {
            row: minRow,
            column: minColumn,
        },
        to: {
            row: maxRow,
            column: maxColumn,
        },
    }
}

export const sumRowsHeights = (
    index: number,
    rowHeight: (index: number) => number,
) => {
    let sum = 0

    while (index > 1) {
        sum += rowHeight(index - 1)
        index -= 1
    }

    return sum
}

export const sumColumnWidths = (
    index: number,
    columnWidth: (index: number) => number,
) => {
    let sum = 0

    while (index > 1) {
        sum += columnWidth(index - 1)
        index -= 1
    }

    return sum
}
