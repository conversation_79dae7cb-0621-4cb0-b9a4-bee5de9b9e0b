import NativeCheckbox from '@goodsailors/ui/components/inputs/NativeCheckbox'
import { alpha, useTheme } from '@mui/material'
import Stack, { StackProps } from '@mui/material/Stack'
import dayjs from 'dayjs'
import * as _ from 'lodash'
import React, { CSSProperties } from 'react'

import { isWeekend } from '@/utils/isWeekend'

import { Column } from '../../types'
import DroppableZone from '../DroppableZone'
import EmptyPersonLabel from '../label/EmptyPersonLabel'
import PersonLabel from '../label/PersonLabel'

interface ContentColumnCellProps {
    columnIndex: number
    rowIndex: number
    style: CSSProperties
    columns: Column[]
    days: dayjs.Dayjs[]
    selected: boolean
    onSelect: () => void
}

const ContentColumnCell = ({
    columnIndex,
    rowIndex,
    style,
    columns,
    days,
    onSelect,
    selected,
}: ContentColumnCellProps) => {
    const [settingsVisibility, setSettingsVisibility] = React.useState(false)

    const column = columns[columnIndex - 1]

    if (!column) return null

    const object = _.get(column.rows, rowIndex - 1)
    const day = _.get(days, rowIndex - 1)

    return (
        <ContentWrapper
            style={style}
            isWeekend={isWeekend(day)}
            onMouseEnter={() => setSettingsVisibility(true)}
            onMouseLeave={() => setSettingsVisibility(false)}
        >
            <DroppableZone
                id={`${columnIndex}-${rowIndex}`}
                pl={1.5}
                pr={1}
                height='100%'
                direction='row'
                alignItems='center'
                justifyContent='space-between'
            >
                <NativeCheckbox
                    checked={selected}
                    onChange={() => onSelect()}
                    label={
                        object.person ? (
                            <PersonLabel
                                person={object.person}
                                settingsVisibility={settingsVisibility}
                            />
                        ) : (
                            <EmptyPersonLabel
                                settingsVisibility={settingsVisibility}
                            />
                        )
                    }
                />
            </DroppableZone>
        </ContentWrapper>
    )
}

interface ContentWrapperProps extends StackProps {
    isWeekend?: boolean
}

const ContentWrapper = ({
    style,
    children,
    isWeekend = false,
    ...props
}: ContentWrapperProps) => {
    const { palette } = useTheme()

    return (
        <Stack
            {...props}
            style={style}
            alignItems='center'
            direction='row'
            sx={{
                '&:hover': {
                    backgroundColor: alpha(palette.primary.main, 0.05),
                },

                backgroundColor: isWeekend ? palette.grey[50] : 'white',

                border: '1px solid',
                borderLeft: 0,
                borderColor: palette.divider,

                '&:not(:last-child)': {
                    borderBottom: 0,
                },
            }}
        >
            {children}
        </Stack>
    )
}

export default ContentColumnCell
