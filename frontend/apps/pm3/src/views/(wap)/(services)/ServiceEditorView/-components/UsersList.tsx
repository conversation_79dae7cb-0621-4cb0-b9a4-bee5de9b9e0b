import CloseOutlined from '@mui/icons-material/CloseOutlined'
import SearchOutlined from '@mui/icons-material/SearchOutlined'
import { useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'

import DraggableItem from './DraggableItem'

interface UsersListProps {
    onClose: () => void
}

const UsersList = ({ onClose }: UsersListProps) => {
    const { t } = useTranslation('common')
    const { spacing } = useTheme()

    return (
        <Card sx={{ overflow: 'visible', maxWidth: 400, width: '100%' }}>
            <CardContent
                sx={{
                    padding: `${spacing(2)} !important`,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                }}
            >
                <Stack
                    direction='row'
                    justifyContent='space-between'
                    alignItems='center'
                >
                    <Typography fontWeight={600}>
                        {t('labels.list-of-users')}
                    </Typography>

                    <IconButton size='small' onClick={onClose}>
                        <CloseOutlined sx={{ fontSize: 20 }} />
                    </IconButton>
                </Stack>

                <Stack gap={2}>
                    <TextField select label={t('labels.object')}>
                        <MenuItem value={1}>Winterfell</MenuItem>
                    </TextField>

                    <TextField
                        label={t('labels.search')}
                        slotProps={{
                            input: {
                                endAdornment: <SearchOutlined />,
                            },
                        }}
                    />
                </Stack>

                <Box>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>{t('labels.first-name')}</TableCell>
                                <TableCell>{t('labels.workload')}</TableCell>
                                <TableCell>{t('labels.type')}</TableCell>
                                <TableCell>ID</TableCell>
                            </TableRow>
                        </TableHead>

                        <TableBody>
                            {_.map(data, (item) => (
                                <TableRow key={item.id}>
                                    <TableCell>
                                        <DraggableItem id={`${item.id}`}>
                                            <Typography variant='body2'>
                                                {item.username}
                                            </Typography>
                                        </DraggableItem>
                                    </TableCell>

                                    <TableCell>
                                        <Chip
                                            size='small'
                                            color='success'
                                            label={item.workload * 100 + '%'}
                                        />
                                    </TableCell>

                                    <TableCell>{item.type}</TableCell>
                                    <TableCell>{item.id}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </Box>
            </CardContent>
        </Card>
    )
}

const data = _.times(5, (index) => ({
    id: index,
    username: 'Petr Lihá',
    workload: 0.9,
    type: 'HPP',
}))

export default UsersList
