import * as _ from 'lodash'

import { Cell } from '../types'

export const isContentCellSelected = (selectedCells: Cell[], cell: Cell) => {
    if (selectedCells.length === 0) return false

    return _.some(
        selectedCells,
        (selectedCell) =>
            selectedCell.rowIndex === cell.rowIndex &&
            selectedCell.columnIndex === cell.columnIndex,
    )
}

export const isTopHeaderCellSelected = (
    selectedCells: Cell[],
    cell: Cell,
    rowsLength: number,
) => {
    if (selectedCells.length === 0) return false

    const filteredRows = _.filter(
        selectedCells,
        (selectedCell) => selectedCell.columnIndex === cell.columnIndex,
    )

    return rowsLength === filteredRows.length
}

export const isLeftHeaderCellSelected = (
    selectedCells: Cell[],
    cell: Cell,
    columnsLength: number,
) => {
    if (selectedCells.length === 0) return false

    const filteredColumns = _.filter(
        selectedCells,
        (selectedCell) => selectedCell.rowIndex === cell.rowIndex,
    )

    return columnsLength === filteredColumns.length
}

export const filterTopHeaderCells = (cells: Cell[], topHeaderCell: Cell) => {
    return _.filter(
        cells,
        (cell) => cell.columnIndex !== topHeaderCell.columnIndex,
    )
}

export const filterLeftHeaderCells = (cells: Cell[], leftHeaderCell: Cell) => {
    return _.filter(cells, (cell) => cell.rowIndex !== leftHeaderCell.rowIndex)
}

export const filterContentCell = (cells: Cell[], contentCell: Cell) => {
    return _.filter(
        cells,
        (cell) =>
            cell.rowIndex !== contentCell.rowIndex ||
            cell.columnIndex !== contentCell.columnIndex,
    )
}
