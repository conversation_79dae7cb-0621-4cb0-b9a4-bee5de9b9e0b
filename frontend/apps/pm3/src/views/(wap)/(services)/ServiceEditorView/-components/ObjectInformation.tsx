import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import CopyAllOutlined from '@mui/icons-material/CopyAllOutlined'
import DeleteOutlined from '@mui/icons-material/DeleteOutlined'
import HistoryOutlined from '@mui/icons-material/HistoryOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import WorkOutlineOutlined from '@mui/icons-material/WorkOutlineOutlined'
import { useTheme } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Chip from '@mui/material/Chip'
import Divider from '@mui/material/Divider'
import ListItemIcon from '@mui/material/ListItemIcon'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import { InformationRow } from '@/components/other/InformationRow'

interface ObjectInformationProps {
    onCreatePosition: () => void
}

const ObjectInformation = ({ onCreatePosition }: ObjectInformationProps) => {
    const [t] = useTranslation('common')
    const { spacing } = useTheme()

    return (
        <Card>
            <CardContent
                sx={{
                    px: 1,
                    py: `${spacing(1.5)} !important`,
                    display: 'flex',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    gap: 1.5,
                }}
            >
                <InformationRow title={t('labels.object')}>
                    <Typography variant='body2' fontWeight={700}>
                        Winterfell
                    </Typography>
                </InformationRow>

                <InformationRow title={t('labels.code-object')}>
                    <Typography variant='body2' fontWeight={700}>
                        5463/stredisko1
                    </Typography>
                </InformationRow>

                <InformationRow title={t('labels.period')}>
                    <Typography variant='body2' fontWeight={700}>
                        2024-07 (1.7.2024 - 31.7.2024)
                    </Typography>
                </InformationRow>

                <InformationRow title={t('labels.position')}>
                    <Typography variant='body2' fontWeight={700}>
                        20
                    </Typography>
                </InformationRow>

                <InformationRow title={t('labels.count-of-errors')}>
                    <Chip size='small' label='14' color='error' />
                </InformationRow>

                <InformationRow title={t('labels.involment')}>
                    <Chip size='small' label='80%' color='error' />
                </InformationRow>

                <InformationRow title={t('labels.effectivity')}>
                    <Chip size='small' label='85%' color='warning' />
                </InformationRow>

                <InformationRow title={t('labels.attendance')}>
                    <Chip size='small' label='Nelze určit' color='default' />
                </InformationRow>

                <InformationRow title={t('labels.billing-status')}>
                    <Chip size='small' label='Neaktivní' color='default' />
                </InformationRow>

                <InformationRow title={t('labels.billing-status')}>
                    <Chip size='small' label='Neschválena' color='default' />
                </InformationRow>

                <IconButtonDropdown
                    icon={<MoreVert sx={{ fontSize: 24 }} />}
                    iconButtonProps={{ sx: { ml: 'auto' } }}
                    menuContent={
                        <Stack>
                            <MenuItem onClick={onCreatePosition}>
                                <ListItemIcon>
                                    <WorkOutlineOutlined />
                                </ListItemIcon>

                                {t('actions.add-position')}
                            </MenuItem>

                            <MenuItem>
                                <ListItemIcon>
                                    <CopyAllOutlined />
                                </ListItemIcon>

                                {t('actions.duplicate')}
                            </MenuItem>

                            <MenuItem>
                                <ListItemIcon>
                                    <HistoryOutlined />
                                </ListItemIcon>

                                {t('actions.carry-over-from-prev')}
                            </MenuItem>

                            <Divider />

                            <MenuItem>
                                <ListItemIcon>
                                    <DeleteOutlined />
                                </ListItemIcon>

                                {t('actions.remove-plan')}
                            </MenuItem>
                        </Stack>
                    }
                />
            </CardContent>
        </Card>
    )
}

export default ObjectInformation
