import React from 'react'
import { VariableSizeGrid, VariableSizeGridProps } from 'react-window'

import {
    getCellIndicies,
    getCellShownIndicies,
    sumColumnWidths,
    sumRowsHeights,
} from '../-utils/calculation'
import { Cell } from '../types'

interface StickyGridProps<C extends Cell> extends VariableSizeGridProps {
    stickyCellProps: (props: C) => Omit<C, 'style' | 'rowIndex' | 'columnIndex'>
    stickyCell: (props: C) => React.ReactNode
}

interface InnerElementProps {
    children: React.ReactNode
    style: React.CSSProperties
}

const StickyGrid = <C extends Cell>({ ...props }: StickyGridProps<C>) => {
    const { stickyCell, stickyCellProps, columnWidth, rowHeight } = props

    const elementType = React.useMemo(
        () =>
            // eslint-disable-next-line react/display-name
            React.forwardRef<HTMLDivElement, InnerElementProps>(
                (props, ref) => {
                    const shownIndecies = getCellShownIndicies(props.children)

                    const children =
                        React.Children.map(props.children, (child) => {
                            const { column, row } = getCellIndicies(child)

                            if (column === 0 || row === 0) {
                                return null
                            }

                            return child
                        }) || []

                    const cellProps = stickyCellProps({
                        rowIndex: 0,
                        columnIndex: 0,
                    } as C)

                    children.push(
                        React.createElement(stickyCell, {
                            ...(cellProps as C),
                            key: '0:0',
                            rowIndex: 0,
                            columnIndex: 0,
                            style: {
                                display: 'inline-flex',
                                width: columnWidth(0),
                                height: rowHeight(0),
                                position: 'sticky',
                                top: 0,
                                left: 0,
                                zIndex: 4,
                            },
                        } as C),
                    )

                    const shownColumnsCount =
                        shownIndecies.to.column - shownIndecies.from.column

                    for (let i = 1; i <= shownColumnsCount; i += 1) {
                        const columnIndex = i + shownIndecies.from.column
                        const rowIndex = 0
                        const width = columnWidth(columnIndex)
                        const height = rowHeight(rowIndex)

                        const marginLeft =
                            i === 1
                                ? sumColumnWidths(columnIndex, columnWidth)
                                : undefined

                        const cellProps = stickyCellProps({
                            rowIndex,
                            columnIndex,
                        } as C)

                        children.push(
                            React.createElement(stickyCell, {
                                ...(cellProps as C),
                                key: `${rowIndex}:${columnIndex}`,
                                rowIndex,
                                columnIndex,
                                style: {
                                    marginLeft,
                                    display: 'inline-flex',
                                    width,
                                    height,
                                    position: 'sticky',
                                    top: 0,
                                    zIndex: 3,
                                },
                            }),
                        )
                    }

                    const shownRowsCount =
                        shownIndecies.to.row - shownIndecies.from.row

                    for (let i = 1; i <= shownRowsCount; i += 1) {
                        const columnIndex = 0
                        const rowIndex = i + shownIndecies.from.row
                        const width = columnWidth(columnIndex)
                        const height = rowHeight(rowIndex)

                        const marginTop =
                            i === 1
                                ? sumRowsHeights(rowIndex, rowHeight)
                                : undefined

                        const cellProps = stickyCellProps({
                            rowIndex,
                            columnIndex,
                        } as C)

                        children.push(
                            React.createElement(stickyCell, {
                                ...(cellProps as C),
                                key: `${rowIndex}:${columnIndex}`,
                                rowIndex,
                                columnIndex,
                                style: {
                                    marginTop,
                                    width,
                                    height,
                                    position: 'sticky',
                                    left: 0,
                                    zIndex: 2,
                                },
                            }),
                        )
                    }

                    return (
                        <div ref={ref} {...props}>
                            {children}
                        </div>
                    )
                },
            ),
        [stickyCellProps, stickyCell, columnWidth, rowHeight],
    )

    return <VariableSizeGrid {...props} innerElementType={elementType} />
}

export default StickyGrid
