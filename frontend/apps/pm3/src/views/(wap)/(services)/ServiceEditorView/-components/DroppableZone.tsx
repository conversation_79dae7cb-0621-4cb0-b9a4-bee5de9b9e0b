import { useDroppable } from '@dnd-kit/core'
import { useTheme } from '@mui/material'
import Stack, { StackProps } from '@mui/material/Stack'

interface DroppableZoneProps extends StackProps {
    id: string
}

const DroppableZone = ({ id, children, ...props }: DroppableZoneProps) => {
    const { palette } = useTheme()

    const { isOver, setNodeRef } = useDroppable({
        id,
    })

    return (
        <Stack
            {...props}
            flex={1}
            ref={setNodeRef}
            sx={{
                ...props.sx,
                border: '2px dashed',
                borderColor: isOver ? palette.primary.main : 'transparent',
            }}
        >
            {children}
        </Stack>
    )
}

export default DroppableZone
