import Stack from '@mui/material/Stack'
import dayjs from 'dayjs'
import * as _ from 'lodash'
import React from 'react'
import AutoSizer from 'react-virtualized-auto-sizer'

import { isContentCellSelected } from '../-utils/cell'
import { Cell, Column } from '../types'
import HeaderColumnCell from './cell/HeaderColumnCell'

interface HeaderColumnsProps {
    days: dayjs.Dayjs[]
    columns: Column[]
    selectedCells: Cell[]
    onSelectCell: (cell: Cell) => void
}

const HeaderColumns = React.forwardRef<HTMLDivElement, HeaderColumnsProps>(
    ({ days, columns, selectedCells, onSelectCell }, ref) => {
        return (
            <Stack width={170} flex='none'>
                <AutoSizer>
                    {({ width, height }) => (
                        <Stack
                            className='hide-scrollbar'
                            height={height}
                            width={width}
                            ref={ref}
                            sx={{
                                overflowX: 'scroll',
                                overflowY: 'hidden',
                            }}
                        >
                            {_.times(days.length + 1, (rowIndex) => {
                                const cell: Cell = { columnIndex: 0, rowIndex }
                                const selected = isContentCellSelected(
                                    selectedCells,
                                    cell,
                                )

                                return (
                                    <HeaderColumnCell
                                        days={days}
                                        key={`0-${rowIndex}`}
                                        columns={columns}
                                        rowIndex={rowIndex}
                                        columnIndex={0}
                                        selected={selected}
                                        onSelect={() => {
                                            onSelectCell(cell)
                                        }}
                                    />
                                )
                            })}
                        </Stack>
                    )}
                </AutoSizer>
            </Stack>
        )
    },
)

HeaderColumns.displayName = 'HeaderColumns'

export default HeaderColumns
