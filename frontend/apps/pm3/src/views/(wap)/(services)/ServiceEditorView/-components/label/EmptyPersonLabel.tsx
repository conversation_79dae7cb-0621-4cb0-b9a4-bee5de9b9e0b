import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import Bolt from '@mui/icons-material/Bolt'
import MoreVert from '@mui/icons-material/MoreVert'
import PersonAddOutlined from '@mui/icons-material/PersonAddOutlined'
import ListItemIcon from '@mui/material/ListItemIcon'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

interface EmptyPersonLabelProps {
    settingsVisibility: boolean
}

const EmptyPersonLabel = ({ settingsVisibility }: EmptyPersonLabelProps) => {
    const [t] = useTranslation('common')

    return (
        <Stack
            flex={1}
            direction='row'
            alignItems='center'
            justifyContent='space-between'
        >
            <Typography
                variant='caption'
                color='text.disabled'
                fontStyle='italic'
            >
                {t('labels.not-selected')}
            </Typography>

            {settingsVisibility && (
                <IconButtonDropdown
                    iconButtonProps={{
                        size: 'small',
                    }}
                    icon={<MoreVert sx={{ fontSize: 20 }} />}
                    menuContent={
                        <Stack>
                            <MenuItem>
                                <ListItemIcon>
                                    <PersonAddOutlined />
                                </ListItemIcon>

                                {t('actions.add')}
                            </MenuItem>

                            <MenuItem>
                                <ListItemIcon>
                                    <Bolt />
                                </ListItemIcon>

                                {t('actions.add-automatically')}
                            </MenuItem>
                        </Stack>
                    }
                />
            )}
        </Stack>
    )
}

export default EmptyPersonLabel
