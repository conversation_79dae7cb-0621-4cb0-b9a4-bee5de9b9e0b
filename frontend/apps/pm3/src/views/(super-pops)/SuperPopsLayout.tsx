import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import SpaceDashboardOutlined from '@mui/icons-material/SpaceDashboardOutlined'
import { useQuery } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router'

import Layout from '@/components/layout/Layout'
import PageHead from '@/components/layout/PageHead'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import AppGuard from '@/guard/AppGuard'
import AuthenticatedGuard from '@/guard/AuthenticatedGuard'
import getPopsEventRequest from '@/requests/pops/getPopsEventRequest'
import { ApplicationName } from '@/requests/responses'
import getSuperPopsDetailRequest from '@/requests/superPops/getSuperPopsDetailRequest'

import NavbarActions from './-components/NavbarActions'

const SuperPopsLayoutContent = () => {
    const { t } = useTranslation('common')

    const params = useParams()

    const superPopsId = parseParamInt(params.superPopsId)
    const eventId = parseParamInt(params.eventId)

    const { data: superPops } = useQuery({
        queryFn: () => getSuperPopsDetailRequest(superPopsId),
        queryKey: queryKeys.superPopsDetail(superPopsId).index,
        enabled: Boolean(superPopsId),
    })

    const { data: event } = useQuery({
        queryFn: () => getPopsEventRequest(eventId),
        queryKey: queryKeys.event(eventId).index,
        enabled: Boolean(eventId),
    })

    const sidebarItems = [
        {
            href: routerPaths.superPops.index,
            label: t('labels.dashboard'),
            icon: <SpaceDashboardOutlined />,
        },
    ]

    const map = {
        [routerPaths.superPops.index]: t('labels.super-pops'),
        [routerPaths.superPops.detail.index]:
            `${t('labels.super-pops-detail')} - ${superPops?.data?.id || ''}`,
        [routerPaths.superPops.event.index]:
            `${t('labels.event-detail')} - ${event?.data?.id || ''}`,
    }

    return (
        <>
            <PageHead map={map} favicon={'/images/logos/super-pops-icon.svg'} />

            <Layout
                breadcrumbsMap={map}
                sidebarItems={sidebarItems}
                actions={<NavbarActions />}
                logoUrl='/images/logos/super-pops-icon.svg'
            />
        </>
    )
}

const SuperPopsLayout = () => {
    return (
        <AuthenticatedGuard>
            <AppGuard name={ApplicationName.SUPER_POPS}>
                <SuperPopsLayoutContent />
            </AppGuard>
        </AuthenticatedGuard>
    )
}

export default SuperPopsLayout
