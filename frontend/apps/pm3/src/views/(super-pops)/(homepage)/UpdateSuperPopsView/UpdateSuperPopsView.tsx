import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { Form } from 'formik'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Navigate, useNavigate, useParams } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import { useCurrentUser } from '@/config/queryClient/queries'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import { PermissionName } from '@/requests/responses'
import getSuperPopsDetailRequest from '@/requests/superPops/getSuperPopsDetailRequest'
import updateSuperPopsRequest, {
    UpdateSuperPopsBody,
    UpdateSuperPopsBodyKeys,
} from '@/requests/superPops/updateSuperPopsRequest'
import { canAccessWithPermissions } from '@/utils/validation'

import BasicInformationStep from './-components/BasicInformationStep'

enum StepName {
    BASIC_INFORMATION,
    FACILITIES,
}

const UpdateSuperPopsView = () => {
    const navigate = useNavigate()
    const queryClient = useQueryClient()
    const { t } = useTranslation()

    const params = useParams<'superPopsId'>()
    const superPopsId = parseParamInt(params.superPopsId)

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { data: currentUser } = useCurrentUser()

    const {
        isPending: isSuperPopsPending,
        data: superPops,
        isError,
    } = useQuery({
        queryFn: () => getSuperPopsDetailRequest(superPopsId),
        queryKey: queryKeys.superPopsDetail(superPopsId).index,
    })

    const { mutate, isPending } = useMutation({
        mutationFn: (body: UpdateSuperPopsBody) =>
            updateSuperPopsRequest(superPopsId, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.superPopsDetail(superPopsId).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.superPops.index,
            })

            queryClient.invalidateQueries({
                queryKey: queryKeys.facilities.withSuperPops,
            })

            toast.success(t('texts.success-action'))

            const href = createHrefWithParams(
                routerPaths.superPops.detail.index,
                {
                    superPopsId,
                },
            )

            navigate(href)
        },
    })

    if (isSuperPopsPending) {
        return <LoadingSpinner />
    }

    if (isError) {
        return <Navigate to={routerPaths.superPops.index} />
    }

    const canUpdateSuperPops = canAccessWithPermissions(superPops.data, [
        PermissionName.PERM_SUPERPOPS_CREATE,
    ])

    if (!canUpdateSuperPops || superPops.data.is_locked) {
        const href = createHrefWithParams(routerPaths.superPops.detail.index, {
            superPopsId,
        })

        return <Navigate to={href} />
    }

    const initialValues: UpdateSuperPopsBody = {
        [UpdateSuperPopsBodyKeys.IS_LOCKED]: false,
        [UpdateSuperPopsBodyKeys.STARTS_AT]: dayjs(superPops.data.starts_at),
        [UpdateSuperPopsBodyKeys.ENDS_AT]: dayjs(superPops.data.ends_at),
        [UpdateSuperPopsBodyKeys.ACCOUNT_ID]: currentUser?.data
            ?.account_id as number,
        [UpdateSuperPopsBodyKeys.FACILITIES_IDS]: superPops.data.facilities_ids,
    }

    const validationSchema = yup.object().shape({
        [UpdateSuperPopsBodyKeys.STARTS_AT]:
            step === StepName.BASIC_INFORMATION
                ? yup
                      .mixed<dayjs.Dayjs>()
                      .required('errors.required-field')
                      .test(
                          'is-after-or-equal-to-now',
                          'errors.time-range-must-include-current-time',
                          function (value) {
                              if (!value) return true
                              return dayjs(value).isBefore(dayjs())
                          },
                      )
                : yup.mixed(),
        [UpdateSuperPopsBodyKeys.ENDS_AT]:
            step === StepName.BASIC_INFORMATION
                ? yup
                      .mixed<dayjs.Dayjs>()
                      .required('errors.required-field')
                      .test(
                          'is-greater-than-starts-at',
                          'errors.end-date-must-be-after-start-date',
                          function (value, context) {
                              const startsAt =
                                  context.parent[
                                      UpdateSuperPopsBodyKeys.STARTS_AT
                                  ]

                              if (!startsAt || !value) return true
                              return dayjs(value).isAfter(dayjs(startsAt))
                          },
                      )
                      .test(
                          'is-after-or-equal-to-now',
                          'errors.time-range-must-include-current-time',
                          function (value) {
                              if (!value) return true
                              return dayjs(value).isAfter(dayjs())
                          },
                      )
                : yup.mixed(),
        [UpdateSuperPopsBodyKeys.FACILITIES_IDS]:
            step === StepName.FACILITIES
                ? yup.array().min(1, 'errors.required-field')
                : yup.array(),
    })

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: UpdateSuperPopsBody) => {
        if (step !== StepName.FACILITIES) {
            onNext()
            return
        }

        mutate(body)
    }

    return (
        <BlockingFormik
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
            initialTouched={{ startsAt: true, endsAt: true }}
        >
            <Stack component={Form} height='100%' gap={2}>
                <Card sx={{ flex: 1 }}>
                    <CardContent
                        sx={{
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                        }}
                    >
                        <React.Suspense fallback={<LoadingSpinner />}>
                            {step === StepName.BASIC_INFORMATION && (
                                <BasicInformationStep />
                            )}

                            {step === StepName.FACILITIES && (
                                <FormFacilitiesAssigner
                                    excludeFacilitiesInSuperPops
                                    name={
                                        UpdateSuperPopsBodyKeys.FACILITIES_IDS
                                    }
                                />
                            )}
                        </React.Suspense>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent
                        sx={{
                            gap: 4,
                            display: 'flex',
                            justifyContent: 'space-between',
                        }}
                    >
                        <BackButton color='inherit' onClick={onBack} />

                        <Stepper
                            activeStep={step}
                            sx={{ flex: 1, maxWidth: 850 }}
                        >
                            <Step>
                                <StepLabel>
                                    {t('labels.basic-information')}
                                </StepLabel>
                            </Step>

                            <Step>
                                <StepLabel>{t('labels.objects')}</StepLabel>
                            </Step>
                        </Stepper>

                        <SubmitButton loading={isPending}>
                            {step === StepName.FACILITIES
                                ? t('actions.save')
                                : t('actions.continue')}
                        </SubmitButton>
                    </CardContent>
                </Card>
            </Stack>
        </BlockingFormik>
    )
}

export default UpdateSuperPopsView
