import { TextFieldProps } from '@mui/material'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import FormDateTimePicker from '@/components/form/FormDateTimePicker'
import { UpdateSuperPopsBodyKeys } from '@/requests/superPops/updateSuperPopsRequest'

const BasicInformationStep = () => {
    const { t } = useTranslation()

    return (
        <Stack gap={4}>
            <Typography fontWeight={600}>
                {t('labels.basic-information')}
            </Typography>

            <Stack gap={2}>
                <FormDateTimePicker
                    slotProps={{
                        textField: {
                            'data-testid': 'STARTS_AT',
                        } as TextFieldProps,
                    }}
                    name={UpdateSuperPopsBodyKeys.STARTS_AT}
                    label={t('labels.shift-start-superpops')}
                />

                <FormDateTimePicker
                    slotProps={{
                        textField: {
                            'data-testid': 'ENDS_AT',
                        } as TextFieldProps,
                    }}
                    name={UpdateSuperPopsBodyKeys.ENDS_AT}
                    label={t('labels.shift-end-superpops')}
                />
            </Stack>
        </Stack>
    )
}

export default BasicInformationStep
