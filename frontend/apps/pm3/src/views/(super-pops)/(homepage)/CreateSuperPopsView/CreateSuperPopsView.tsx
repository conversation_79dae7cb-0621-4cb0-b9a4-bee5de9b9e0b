import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import Stack from '@mui/material/Stack'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import * as yup from 'yup'

import BackButton from '@/components/other/BackButton'
import StepperFormik from '@/components/other/StepperFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import { useCurrentUser } from '@/config/queryClient/queries'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import createSuperPopsRequest, {
    CreateSuperPopsBody,
    CreateSuperPopsBodyKeys,
} from '@/requests/superPops/createSuperPopsRequest'

import BasicInformationStep from './-components/BasicInformationStep'

enum StepName {
    BASIC_INFORMATION,
    FACILITIES,
}

const CreateSuperPopsView = () => {
    const navigate = useNavigate()
    const queryClient = useQueryClient()
    const { t } = useTranslation()

    const [step, setStep] = React.useState(StepName.BASIC_INFORMATION)

    const { data: currentUser } = useCurrentUser()

    const { mutate, isPending } = useMutation({
        mutationFn: (body: CreateSuperPopsBody) => createSuperPopsRequest(body),
        onSuccess: async (response) => {
            toast.success(t('texts.success-action'))

            const href = createHrefWithParams(
                routerPaths.superPops.detail.index,
                {
                    superPopsId: response.data.id,
                },
            )

            navigate(href)

            queryClient.invalidateQueries({
                queryKey: queryKeys.facilities.withSuperPops,
            })

            queryClient.invalidateQueries({
                queryKey: queryKeys.superPops.index,
            })
        },
    })

    const initialValues: CreateSuperPopsBody = {
        [CreateSuperPopsBodyKeys.IS_LOCKED]: false,
        [CreateSuperPopsBodyKeys.STARTS_AT]: dayjs(),
        [CreateSuperPopsBodyKeys.ENDS_AT]: dayjs().endOf('day'),
        [CreateSuperPopsBodyKeys.ACCOUNT_ID]: currentUser?.data
            ?.account_id as number,
        [CreateSuperPopsBodyKeys.FACILITIES_IDS]: [],
    }

    const validationSchema = React.useMemo(() => {
        return yup.object().shape({
            [CreateSuperPopsBodyKeys.STARTS_AT]:
                step === StepName.BASIC_INFORMATION
                    ? yup
                          .mixed<dayjs.Dayjs>()
                          .required('errors.required-field')
                          .test(
                              'is-after-or-equal-to-now',
                              'errors.time-range-must-include-current-time',
                              function (value) {
                                  if (!value) return true
                                  return dayjs(value).isBefore(dayjs())
                              },
                          )
                    : yup.mixed(),
            [CreateSuperPopsBodyKeys.ENDS_AT]:
                step === StepName.BASIC_INFORMATION
                    ? yup
                          .mixed<dayjs.Dayjs>()
                          .required('errors.required-field')
                          .test(
                              'is-greater-than-starts-at',
                              'errors.end-date-must-be-after-start-date',
                              function (value, context) {
                                  const startsAt =
                                      context.parent[
                                          CreateSuperPopsBodyKeys.STARTS_AT
                                      ]

                                  if (!startsAt || !value) return true
                                  return dayjs(value).isAfter(dayjs(startsAt))
                              },
                          )
                          .test(
                              'is-after-or-equal-to-now',
                              'errors.time-range-must-include-current-time',
                              function (value) {
                                  if (!value) return true
                                  return dayjs(value).isAfter(dayjs())
                              },
                          )
                    : yup.mixed(),
            [CreateSuperPopsBodyKeys.FACILITIES_IDS]:
                step === StepName.FACILITIES
                    ? yup.array().min(1, 'errors.required-field')
                    : yup.array(),
        })
    }, [step])

    const onBack = () => {
        if (step === StepName.BASIC_INFORMATION) {
            navigate(-1)
        } else {
            setStep((p) => Math.max(--p, 0))
        }
    }

    const onNext = () => {
        setStep((p) => ++p)
    }

    const onSubmit = (body: CreateSuperPopsBody) => {
        if (step !== StepName.FACILITIES) {
            onNext()
            return
        }

        mutate(body)
    }

    const stepper = (
        <Stepper activeStep={step}>
            <Step>
                <StepLabel>{t('labels.basic-information')}</StepLabel>
            </Step>

            <Step>
                <StepLabel>{t('labels.objects')}</StepLabel>
            </Step>
        </Stepper>
    )

    const actions = (
        <Stack direction='row' justifyContent='space-between'>
            <BackButton color='inherit' onClick={onBack} />

            <SubmitButton loading={isPending}>
                {step === StepName.FACILITIES
                    ? t('actions.save')
                    : t('actions.continue')}
            </SubmitButton>
        </Stack>
    )

    return (
        <StepperFormik
            stepper={stepper}
            actions={actions}
            maxWidth={1100}
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
            initialTouched={{ startsAt: true, endsAt: true }}
        >
            <React.Suspense fallback={<LoadingSpinner />}>
                {step === StepName.BASIC_INFORMATION && (
                    <BasicInformationStep />
                )}

                {step === StepName.FACILITIES && (
                    <FormFacilitiesAssigner
                        excludeFacilitiesInSuperPops
                        name={CreateSuperPopsBodyKeys.FACILITIES_IDS}
                    />
                )}
            </React.Suspense>
        </StepperFormik>
    )
}

export default CreateSuperPopsView
