import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import EventIcon from '@mui/icons-material/Event'
import MapsHomeWorkOutlined from '@mui/icons-material/MapsHomeWorkOutlined'
import { useTheme } from '@mui/material'
import Stack from '@mui/material/Stack'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import { useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Navigate, useParams } from 'react-router'

import { useCurrentUser } from '@/config/queryClient/queries'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import { UserDetailResponse } from '@/requests/responses'
import getSuperPopsDetailRequest from '@/requests/superPops/getSuperPopsDetailRequest'

import Navigation from './-components/Navigation'
import { SuperPopsDetailViewTabName } from './types'

const EventsTab = React.lazy(() => import('./-tabs/EventsTab'))
const FaciliesTab = React.lazy(() => import('./-tabs/FaciliesTab'))

interface SuperPopsDetailViewContentProps {
    tab: SuperPopsDetailViewTabName
    onTab: (tab: SuperPopsDetailViewTabName) => void
}

const SuperPopsDetailViewContent = ({
    onTab,
    tab,
}: SuperPopsDetailViewContentProps) => {
    const { t } = useTranslation()
    const { palette, spacing } = useTheme()

    const params = useParams()
    const superPopsId = parseParamInt(params.superPopsId)

    const { data: currentUser } = useCurrentUser()

    const {
        isPending: isSuperPopsPending,
        data: superPops,
        isError,
    } = useQuery({
        queryFn: () => getSuperPopsDetailRequest(superPopsId),
        queryKey: queryKeys.superPopsDetail(superPopsId).index,
    })

    if (isSuperPopsPending) {
        return <LoadingSpinner />
    }

    if (isError) {
        return <Navigate to={routerPaths.superPops.index} />
    }

    return (
        <Stack width='100%' gap={1}>
            <Navigation superPops={superPops.data} />

            <Stack flex={1} overflow='hidden'>
                <Tabs
                    value={tab}
                    onChange={(__, value) => {
                        onTab(value)
                    }}
                    sx={{
                        width: 'max-content',
                        background: palette.grey[50],
                        borderTopLeftRadius: spacing(2),
                        borderTopRightRadius: spacing(2),
                    }}
                >
                    <Tab
                        data-testid='TAB_EVENTS'
                        value={SuperPopsDetailViewTabName.EVENTS}
                        label={t('labels.events')}
                        icon={<EventIcon sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />

                    <Tab
                        data-testid='TAB_OBJECTS'
                        value={SuperPopsDetailViewTabName.FACILITIES}
                        label={t('labels.objects')}
                        icon={<MapsHomeWorkOutlined sx={{ fontSize: 20 }} />}
                        iconPosition='start'
                    />
                </Tabs>

                <React.Suspense fallback={<LoadingSpinner />}>
                    {tab === SuperPopsDetailViewTabName.EVENTS && (
                        <EventsTab superPops={superPops.data} />
                    )}

                    {tab === SuperPopsDetailViewTabName.FACILITIES && (
                        <FaciliesTab
                            currentUser={
                                currentUser?.data as UserDetailResponse
                            }
                            superPops={superPops.data}
                        />
                    )}
                </React.Suspense>
            </Stack>
        </Stack>
    )
}

const SuperPopsDetailView = () => {
    return (
        <TabGuard
            defaultTab={SuperPopsDetailViewTabName.EVENTS}
            validValues={_.values(SuperPopsDetailViewTabName)}
        >
            {(props) => <SuperPopsDetailViewContent {...props} />}
        </TabGuard>
    )
}

export default SuperPopsDetailView
