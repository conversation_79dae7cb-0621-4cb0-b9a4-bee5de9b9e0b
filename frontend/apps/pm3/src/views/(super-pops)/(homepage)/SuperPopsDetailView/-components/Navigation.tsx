import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import EditOutlined from '@mui/icons-material/EditOutlined'
import LockClockOutlined from '@mui/icons-material/LockClockOutlined'
import PlayCircleOutlineOutlined from '@mui/icons-material/PlayCircleOutlineOutlined'
import { alpha, useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Chip from '@mui/material/Chip'
import Stack from '@mui/material/Stack'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Typography from '@mui/material/Typography'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import { PermissionName, SuperPopsResponse } from '@/requests/responses'
import lockSuperPopsRequest from '@/requests/superPops/lockSuperPopsRequest'
import { formatDateRange } from '@/utils/format'
import { canAccessWithPermissions } from '@/utils/validation'

interface NavigationProps {
    superPops: SuperPopsResponse
}

const Navigation = ({ superPops }: NavigationProps) => {
    const navigate = useNavigate()
    const queryClient = useQueryClient()
    const { spacing, palette } = useTheme()

    const { t } = useTranslation('common')

    const [confirmationOpen, setConfirmationOpen] = React.useState(false)

    const canUpdateSuperPops = canAccessWithPermissions(superPops, [
        PermissionName.PERM_SUPERPOPS_CREATE,
    ])

    const canLockSuperPops = canAccessWithPermissions(superPops, [
        PermissionName.PERM_SUPERPOPS_CREATE,
    ])

    const { mutate, isPending } = useMutation({
        mutationFn: () => lockSuperPopsRequest(superPops.id),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.superPops.index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.superPopsDetail(superPops.id).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.facilities.withSuperPops,
            })

            toast.success(t('texts.success-action'))
            setConfirmationOpen(false)
        },
    })

    const onUpdate = () => {
        const href = createHrefWithParams(routerPaths.superPops.detail.update, {
            superPopsId: superPops.id,
        })

        navigate(href)
    }

    const statusColor = React.useMemo(() => {
        return superPops.is_locked ? 'error' : 'success'
    }, [superPops.is_locked])

    const shiftRange = formatDateRange(superPops.starts_at, superPops.ends_at)

    return (
        <Card sx={{ flex: 'none' }}>
            <CardContent
                sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 2,
                    alignItems: 'center',
                }}
            >
                <Box
                    sx={{
                        width: 48,
                        height: 48,
                        display: 'flex',
                        borderRadius: '50%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: palette[statusColor].dark,
                        background: alpha(palette[statusColor].main, 0.15),
                    }}
                >
                    {superPops.is_locked ? (
                        <LockClockOutlined />
                    ) : (
                        <PlayCircleOutlineOutlined />
                    )}
                </Box>

                <Typography variant='h6'>
                    {formatDateRange(superPops.starts_at, superPops.ends_at)}
                </Typography>

                <Table
                    sx={{
                        mr: 4,
                        maxWidth: 300,

                        '.MuiTableCell-root': {
                            border: 'none',
                            padding: spacing(0, 2),
                        },
                    }}
                >
                    <TableHead>
                        <TableRow>
                            <TableCell>
                                {t('labels.event-category.CRITICAL')}
                            </TableCell>

                            <TableCell>
                                {t('labels.event-category.MEDIUM')}
                            </TableCell>

                            <TableCell>
                                {t('labels.event-category.LOW')}
                            </TableCell>

                            <TableCell>
                                {t('labels.event-category.NONE')}
                            </TableCell>
                        </TableRow>
                    </TableHead>

                    <TableBody>
                        <TableRow>
                            <TableCell>
                                <Chip
                                    label={superPops.count_of_critical_events}
                                    color='error'
                                />
                            </TableCell>

                            <TableCell>
                                <Chip
                                    label={superPops.count_of_medium_events}
                                    color='warning'
                                />
                            </TableCell>

                            <TableCell>
                                <Chip
                                    label={superPops.count_of_low_events}
                                    color='success'
                                />
                            </TableCell>

                            <TableCell>
                                <Chip
                                    label={superPops.count_of_internal_events}
                                    color='primary'
                                />
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>

                <Stack direction='row' gap={1} ml='auto'>
                    {canLockSuperPops && (
                        <Button
                            data-testid='LOCK_SUPERPOPS'
                            disabled={superPops.is_locked}
                            onClick={() => setConfirmationOpen(true)}
                            startIcon={<LockClockOutlined />}
                            color='error'
                            sx={{ mr: 1 }}
                        >
                            {t('actions.lock-superpops')}
                        </Button>
                    )}

                    {canUpdateSuperPops && (
                        <Button
                            data-testid='UPDATE_SUPERPOPS'
                            onClick={onUpdate}
                            size={'medium'}
                            startIcon={<EditOutlined />}
                            disabled={superPops.is_locked}
                        >
                            {t('actions.update-super-pops')}
                        </Button>
                    )}
                </Stack>
            </CardContent>

            {confirmationOpen && (
                <ConfirmationDialog
                    isLoading={isPending}
                    onConfirm={mutate}
                    sx={{
                        textAlign: 'center',

                        '.MuiPaper-root': {
                            maxWidth: 480,
                            width: '100%',
                        },
                    }}
                    confirmLabel={t('actions.lock-superpops')}
                    onClose={() => setConfirmationOpen(false)}
                >
                    <Stack alignItems='center'>
                        <Box
                            mb={3}
                            width={80}
                            height={80}
                            display='flex'
                            alignItems='center'
                            justifyContent='center'
                            borderRadius={spacing(2)}
                            bgcolor={alpha(palette.error.main, 0.15)}
                        >
                            <LockClockOutlined
                                sx={{
                                    fontSize: 48,
                                    color: palette.error.main,
                                }}
                            />
                        </Box>

                        <Typography variant='h5' mb={1}>
                            {t('texts.lock-super-pops-confirmation')}
                        </Typography>

                        <Typography fontWeight={500} variant='h6'>
                            {shiftRange}
                        </Typography>
                    </Stack>
                </ConfirmationDialog>
            )}
        </Card>
    )
}

export default Navigation
