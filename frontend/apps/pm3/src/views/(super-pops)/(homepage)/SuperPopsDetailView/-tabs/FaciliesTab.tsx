import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { Form } from 'formik'
import * as _ from 'lodash'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import FormFacilitiesAssigner from '@/components/resource/facility/FormFacilitiesAssigner'
import queryKeys from '@/config/queryClient/queryKeys'
import {
    PermissionName,
    SuperPopsResponse,
    UserDetailResponse,
} from '@/requests/responses'
import updateSuperPopsRequest, {
    UpdateSuperPopsBody,
    UpdateSuperPopsBodyKeys,
} from '@/requests/superPops/updateSuperPopsRequest'
import { canAccessWithPermissions } from '@/utils/validation'

interface FacilitiesTabProps {
    currentUser: UserDetailResponse
    superPops: SuperPopsResponse
}

const FacilitiesTab = ({ superPops, currentUser }: FacilitiesTabProps) => {
    const { t } = useTranslation('common')
    const queryClient = useQueryClient()

    const canUpdateSuperPops = canAccessWithPermissions(superPops, [
        PermissionName.PERM_SUPERPOPS_CREATE,
    ])

    const { mutate, isPending } = useMutation({
        mutationFn: (body: UpdateSuperPopsBody) =>
            updateSuperPopsRequest(superPops.id, body),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.superPopsDetail(superPops.id).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.superPops.index,
            })

            queryClient.invalidateQueries({
                queryKey: queryKeys.facilities.withSuperPops,
            })

            toast.success(t('texts.success-action'))
        },
    })

    const initialValues: UpdateSuperPopsBody = {
        [UpdateSuperPopsBodyKeys.IS_LOCKED]: superPops.is_locked,
        [UpdateSuperPopsBodyKeys.STARTS_AT]: dayjs(superPops.starts_at),
        [UpdateSuperPopsBodyKeys.ENDS_AT]: dayjs(superPops.ends_at),
        [UpdateSuperPopsBodyKeys.ACCOUNT_ID]: currentUser.account_id,
        [UpdateSuperPopsBodyKeys.FACILITIES_IDS]: superPops.facilities_ids,
    }

    const onSubmit = (body: UpdateSuperPopsBody) => {
        mutate(body)
    }

    return (
        <BlockingFormik initialValues={initialValues} onSubmit={onSubmit}>
            {({ resetForm, values }) => {
                const isSaveDisabled =
                    _.size(values[UpdateSuperPopsBodyKeys.FACILITIES_IDS]) === 0

                return (
                    <Card
                        component={Form}
                        sx={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            borderTopLeftRadius: 0,
                        }}
                    >
                        <CardContent
                            sx={{
                                flex: 1,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                            }}
                        >
                            <FormFacilitiesAssigner
                                disabled={
                                    superPops.is_locked || !canUpdateSuperPops
                                }
                                excludeFacilitiesInSuperPops
                                name={UpdateSuperPopsBodyKeys.FACILITIES_IDS}
                            />
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between' }}>
                            <Button
                                type='button'
                                color='inherit'
                                onClick={() => resetForm()}
                            >
                                {t('actions.discard-changes')}
                            </Button>

                            <SubmitButton
                                loading={isPending}
                                disabled={
                                    isSaveDisabled ||
                                    superPops.is_locked ||
                                    !canUpdateSuperPops
                                }
                            >
                                {t('actions.save-changes')}
                            </SubmitButton>
                        </CardActions>
                    </Card>
                )
            }}
        </BlockingFormik>
    )
}

export default FacilitiesTab
