import { useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Chip from '@mui/material/Chip'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'

import { LanguageName } from '@/config/i18n/i18n'
import { ChangelogVersionResponse } from '@/requests/responses'
import { formatDate } from '@/utils/format'

interface ChangelogVersionProps {
    version: ChangelogVersionResponse
}

const ChangelogVersion = ({
    version: { changes, date, version },
}: ChangelogVersionProps) => {
    const { palette, breakpoints } = useTheme()
    const { i18n } = useTranslation()

    const lang = i18n.language as LanguageName

    return (
        <Stack
            sx={{
                display: 'grid',
                gridTemplateColumns: '100%',
                pb: 1,
                mt: 1,
                [breakpoints.up('md')]: {
                    gridTemplateColumns: '200px max-content 1fr',
                    gap: 2,
                },
            }}
        >
            <Typography variant='subtitle1' mb={1}>
                {formatDate(dayjs(date, 'DD.MM.YYYY'), 'date')}
            </Typography>

            <Stack
                sx={{
                    gap: 1,
                    alignItems: 'center',
                    [breakpoints.down('md')]: {
                        display: 'none',
                    },
                }}
            >
                <Box
                    sx={{
                        width: 16,
                        height: 16,
                        background: palette.common.white,
                        border: '4px solid',
                        borderRadius: '50%',
                        borderColor: palette.primary.main,
                        [breakpoints.down('md')]: {
                            display: 'none',
                        },
                    }}
                />

                <Box
                    flex={1}
                    sx={{
                        width: '1px',
                        height: '100%',
                        background: palette.primary.main,
                    }}
                />
            </Stack>

            <Stack
                p={2}
                flex={1}
                sx={{
                    border: '1px solid',
                    borderRadius: 1,
                    borderColor: palette.divider,
                }}
            >
                <List>
                    {changes.map((change, index) => (
                        <ListItem key={index}>• {change[lang]}</ListItem>
                    ))}
                </List>

                <Chip
                    size='small'
                    label={version}
                    sx={{
                        position: 'absolute',
                        right: 1,
                        top: 1,
                    }}
                />
            </Stack>
        </Stack>
    )
}

export default ChangelogVersion
