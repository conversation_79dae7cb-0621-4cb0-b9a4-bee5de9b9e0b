import PermContactCalendarOutlinedIcon from '@mui/icons-material/PermContactCalendarOutlined'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import CardHeader from '@mui/material/CardHeader'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form } from 'formik'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import queryKeys from '@/config/queryClient/queryKeys'
import { UserDetailResponse } from '@/requests/responses'
import updateUserRequest, {
    UpdateUserBody,
    UpdateUserBodyKeys,
} from '@/requests/user/updateUserRequest'

interface SettingsFormProps {
    user: UserDetailResponse
}

const SettingsForm = ({ user }: SettingsFormProps) => {
    const { t } = useTranslation('common')
    const queryClient = useQueryClient()

    const updateUser = useMutation({
        mutationFn: (body: UpdateUserBody) => updateUserRequest(user.id, body),
        onSuccess: async (response) => {
            toast.success(t(`texts.${response.message}`))

            await queryClient.invalidateQueries({
                queryKey: queryKeys.users.current,
            })
        },
    })

    const onSubmit = (values: UpdateUserBody) => {
        updateUser.mutate(values)
    }

    const initialValues: UpdateUserBody = {
        [UpdateUserBodyKeys.FIRST_NAME]: user.first_name,
        [UpdateUserBodyKeys.LAST_NAME]: user.last_name,
        [UpdateUserBodyKeys.COMPANY_NUMBER]: user.company_identification_number,
        [UpdateUserBodyKeys.PHONE]: user.cell_phone || '',
        [UpdateUserBodyKeys.EMAIL]: user.email,
    }

    return (
        <Card
            sx={{
                display: 'flex',
                flex: 1,
                flexDirection: 'column',
                height: '100%',
            }}
        >
            <CardHeader
                title={t('labels.personal-settings')}
                subheader={user.identifier}
            />

            <BlockingFormik
                onSubmit={onSubmit}
                initialValues={initialValues}
                enableReinitialize
            >
                {({ resetForm }) => (
                    <Form
                        style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            overflowY: 'auto',
                        }}
                    >
                        <CardContent sx={{ flex: 1, overflowY: 'auto' }}>
                            <Stack gap={4} mb={4}>
                                <FormTextField
                                    label={t('labels.first-name')}
                                    name={UpdateUserBodyKeys.FIRST_NAME}
                                    fullWidth
                                />

                                <FormTextField
                                    label={t('labels.last-name')}
                                    name={UpdateUserBodyKeys.LAST_NAME}
                                    fullWidth
                                />

                                <FormTextField
                                    label={t(
                                        'labels.company-identification-number',
                                    )}
                                    name={UpdateUserBodyKeys.COMPANY_NUMBER}
                                    fullWidth
                                />
                            </Stack>

                            <Stack
                                gap={1}
                                direction='row'
                                alignItems='center'
                                sx={{ mb: 1.5 }}
                            >
                                <PermContactCalendarOutlinedIcon
                                    sx={{
                                        color: 'primary.main',
                                        fontSize: 20,
                                    }}
                                />

                                <Typography
                                    variant='body2'
                                    color={'text.primary'}
                                    sx={{ fontSize: 14, fontWeight: 600 }}
                                >
                                    {t('labels.business-contact-information')}
                                </Typography>
                            </Stack>

                            <Stack gap={4}>
                                <FormTextField
                                    label={t('labels.business-phone')}
                                    name={UpdateUserBodyKeys.PHONE}
                                    fullWidth
                                />

                                <FormTextField
                                    label={t('labels.business-email')}
                                    name={UpdateUserBodyKeys.EMAIL}
                                    fullWidth
                                />
                            </Stack>
                        </CardContent>

                        <CardActions>
                            <Button
                                size='medium'
                                type='button'
                                variant='contained'
                                color='inherit'
                                onClick={() => resetForm()}
                            >
                                {t('actions.discard-changes')}
                            </Button>

                            <SubmitButton
                                size='large'
                                variant='contained'
                                loading={updateUser.isPending}
                            >
                                {t('actions.save-changes')}
                            </SubmitButton>
                        </CardActions>
                    </Form>
                )}
            </BlockingFormik>
        </Card>
    )
}

export default SettingsForm
