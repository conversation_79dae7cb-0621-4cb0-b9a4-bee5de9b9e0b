import SecondarySidebar from '@goodsailors/ui/components/layout/SecondarySidebar'
import SecondarySidebarItem from '@goodsailors/ui/components/layout/SecondarySidebarItem'
import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'
import Container from '@mui/material/Container'
import Link from '@mui/material/Link'
import Stack from '@mui/material/Stack'
import React, { Suspense } from 'react'
import { useTranslation } from 'react-i18next'
import { Outlet } from 'react-router'

import PageHead from '@/components/layout/PageHead'
import routerPaths from '@/config/routerPaths'
import AppGuard from '@/guard/AppGuard'
import { ApplicationName } from '@/requests/responses'

const ProfileLayout = () => {
    const { t } = useTranslation('common')

    const sidebarItems = [
        {
            href: routerPaths.dashboard.profile.index,
            label: t('labels.personal-settings'),
            icon: <SettingsOutlinedIcon />,
        },
    ]

    const headMap = React.useMemo(
        () => ({
            [routerPaths.dashboard.index]: t('labels.dashboard'),
            [routerPaths.dashboard.profile.index]: t(
                'labels.personal-settings',
            ),
            [routerPaths.dashboard.profile.password]: t(
                'labels.change-password',
            ),
        }),
        [t],
    )

    return (
        <AppGuard name={ApplicationName.PROFILE}>
            <Container disableGutters sx={{ height: '100%', margin: 0 }}>
                <PageHead
                    favicon={'/images/logos/profile-icon.svg'}
                    map={headMap}
                />

                <Stack direction='row' height='100%' gap={2}>
                    <SecondarySidebar
                        sx={{ flex: 'none' }}
                        title={t('labels.settings')}
                    >
                        {sidebarItems.map((sidebarItem, i) => (
                            <Link key={i} href={sidebarItem.href}>
                                <SecondarySidebarItem
                                    isActive={
                                        location.pathname === sidebarItem.href
                                    }
                                    icon={sidebarItem.icon}
                                    label={sidebarItem.label}
                                />
                            </Link>
                        ))}
                    </SecondarySidebar>

                    <Suspense fallback={<LoadingSpinner />}>
                        <Outlet />
                    </Suspense>
                </Stack>
            </Container>
        </AppGuard>
    )
}

export default ProfileLayout
