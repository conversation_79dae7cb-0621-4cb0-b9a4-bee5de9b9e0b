import React from 'react'

import routerPaths from '@/config/routerPaths'

import ProfileLayout from './ProfileLayout'

const SettingsView = React.lazy(() => import('./ProfileView/ProfileView'))

const SettingsChangePasswordView = React.lazy(
    () => import('./ProfileChangePasswordView/ProfileChangePasswordView'),
)

const profileRoutes = [
    {
        element: <ProfileLayout />,
        children: [
            {
                path: routerPaths.dashboard.profile.index,
                element: <SettingsView />,
            },
            {
                path: routerPaths.dashboard.profile.password,
                element: <SettingsChangePasswordView />,
            },
        ],
    },
]

export default profileRoutes
