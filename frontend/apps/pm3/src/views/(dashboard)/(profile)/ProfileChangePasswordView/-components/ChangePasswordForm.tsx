import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import CardHeader from '@mui/material/CardHeader'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import { useMutation } from '@tanstack/react-query'
import { Form, FormikHelpers } from 'formik'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import * as yup from 'yup'

import FormTextField from '@/components/form/FormTextField'
import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import { UserDetailResponse } from '@/requests/responses'
import changeUserPasswordRequest, {
    ChangeUserPasswordBody,
    ChangeUserPasswordBodyKeys,
} from '@/requests/user/changePasswordRequest'

interface ChangePasswordFormValues {
    [ChangeUserPasswordBodyKeys.OLD_PASSWORD]: string
    [ChangeUserPasswordBodyKeys.NEW_PASSWORD]: string
    [AdditionalBodyKeys.REPEAT_PASSWORD]: string
}

enum AdditionalBodyKeys {
    REPEAT_PASSWORD = 'repeat_password',
}

interface ChangePasswordFormProps {
    user: UserDetailResponse
}

const ChangePasswordForm = ({ user }: ChangePasswordFormProps) => {
    const { t } = useTranslation('common')

    const [showPasswords, setShowPasswords] = React.useState({
        current: false,
        new: false,
        repeat: false,
    })

    const updateUser = useMutation({
        mutationFn: (body: ChangeUserPasswordBody) =>
            changeUserPasswordRequest(user.id, body),
    })

    const validationSchema = yup.object().shape({
        [ChangeUserPasswordBodyKeys.OLD_PASSWORD]: yup
            .string()
            .required('errors.required-field'),
        [ChangeUserPasswordBodyKeys.NEW_PASSWORD]: yup
            .string()
            .min(8, 'errors.too-short-password')
            .required('errors.required-field'),
        [AdditionalBodyKeys.REPEAT_PASSWORD]: yup
            .string()
            .required('errors.required-field')
            .oneOf(
                [yup.ref(ChangeUserPasswordBodyKeys.NEW_PASSWORD)],
                'errors.passwords-dont-match',
            ),
    })

    const onSubmit = (
        values: ChangePasswordFormValues,
        { resetForm }: FormikHelpers<ChangePasswordFormValues>,
    ) => {
        updateUser.mutate(values, {
            onSuccess: (response) => {
                toast.success(t(`texts.${response.message}`))
                resetForm()
            },
        })
    }

    const initialValues: ChangePasswordFormValues = {
        [ChangeUserPasswordBodyKeys.OLD_PASSWORD]: '',
        [ChangeUserPasswordBodyKeys.NEW_PASSWORD]: '',
        [AdditionalBodyKeys.REPEAT_PASSWORD]: '',
    }

    const onShowClick = (type: 'current' | 'new' | 'repeat') => () => {
        setShowPasswords((values) => ({
            ...values,
            [type]: !values[type],
        }))
    }

    const getPasswordIcon = (type: 'current' | 'new' | 'repeat') => (
        <IconButton
            onClick={onShowClick(type)}
            sx={{
                '&:hover': {
                    background: 'none',
                },
                '& .MuiSvgIcon-root': {
                    color: 'grey.400',
                    fontSize: 18,
                },
            }}
        >
            {showPasswords[type] ? (
                <VisibilityOffOutlinedIcon />
            ) : (
                <VisibilityOutlinedIcon />
            )}
        </IconButton>
    )

    return (
        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardHeader
                title={t('labels.change-password')}
                subheader={t('texts.change-password-description')}
            />

            <BlockingFormik
                enableReinitialize
                onSubmit={onSubmit}
                initialValues={initialValues}
                validationSchema={validationSchema}
            >
                {({ resetForm }) => (
                    <Form
                        style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                        }}
                    >
                        <CardContent
                            sx={{
                                flex: 1,
                                gap: 4,
                                display: 'flex',
                                flexDirection: 'column',
                            }}
                        >
                            <FormTextField
                                label={t('labels.current-password')}
                                name={ChangeUserPasswordBodyKeys.OLD_PASSWORD}
                                fullWidth
                                slotProps={{
                                    input: {
                                        endAdornment: (
                                            <InputAdornment position='end'>
                                                {getPasswordIcon('current')}
                                            </InputAdornment>
                                        ),
                                    },
                                }}
                                type={
                                    showPasswords.current ? 'text' : 'password'
                                }
                            />

                            <FormTextField
                                label={t('labels.new-password')}
                                name={ChangeUserPasswordBodyKeys.NEW_PASSWORD}
                                fullWidth
                                slotProps={{
                                    input: {
                                        endAdornment: (
                                            <InputAdornment position='end'>
                                                {getPasswordIcon('new')}
                                            </InputAdornment>
                                        ),
                                    },
                                }}
                                type={showPasswords.new ? 'text' : 'password'}
                            />

                            <FormTextField
                                label={t('labels.repeat-password')}
                                name={AdditionalBodyKeys.REPEAT_PASSWORD}
                                fullWidth
                                slotProps={{
                                    input: {
                                        endAdornment: (
                                            <InputAdornment position='end'>
                                                {getPasswordIcon('repeat')}
                                            </InputAdornment>
                                        ),
                                    },
                                }}
                                type={
                                    showPasswords.repeat ? 'text' : 'password'
                                }
                            />
                        </CardContent>

                        <CardActions>
                            <Button
                                size='medium'
                                type='button'
                                variant='contained'
                                color='inherit'
                                onClick={() => resetForm()}
                            >
                                {t('actions.discard-changes')}
                            </Button>

                            <SubmitButton
                                size='large'
                                variant='contained'
                                loading={updateUser.isPending}
                            >
                                {t('labels.change-password')}
                            </SubmitButton>
                        </CardActions>
                    </Form>
                )}
            </BlockingFormik>
        </Card>
    )
}

export default ChangePasswordForm
