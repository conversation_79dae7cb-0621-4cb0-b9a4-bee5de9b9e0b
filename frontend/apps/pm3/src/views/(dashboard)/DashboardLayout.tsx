import Person2Outlined from '@mui/icons-material/Person2Outlined'
import SpaceDashboardOutlined from '@mui/icons-material/SpaceDashboardOutlined'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router'

import Layout from '@/components/layout/Layout'
import { useCurrentUser } from '@/config/queryClient/queries'
import routerPaths from '@/config/routerPaths'
import AuthenticatedGuard from '@/guard/AuthenticatedGuard'

const DashboardLayoutContent = () => {
    const { t } = useTranslation('common')

    const location = useLocation()

    const { data: currentUser } = useCurrentUser()

    const logoUrl = React.useMemo(() => {
        const name = currentUser?.data.account.logo || 'default'
        return `/images/logos/${name}.svg`
    }, [currentUser])

    const sidebarItems = [
        {
            href: routerPaths.dashboard.index,
            label: t('labels.dashboard'),
            icon: <SpaceDashboardOutlined />,
        },
        {
            href: routerPaths.dashboard.profile.index,
            label: t('labels.profile'),
            icon: <Person2Outlined />,
        },
    ]

    const map = React.useMemo(
        () => ({
            [routerPaths.dashboard.index]: t('labels.dashboard'),
            [routerPaths.dashboard.profile.index]: t('labels.profile'),
            [routerPaths.dashboard.profile.password]: t(
                'labels.change-password',
            ),
        }),
        [t],
    )

    const isProfile = location.pathname.includes('profile')

    return (
        <>
            <Layout
                breadcrumbsMap={map}
                sidebarItems={sidebarItems}
                logoUrl={isProfile ? '/images/logos/profile-icon.svg' : logoUrl}
            />
        </>
    )
}

const DashboardLayout = () => {
    return (
        <AuthenticatedGuard>
            <DashboardLayoutContent />
        </AuthenticatedGuard>
    )
}

export default DashboardLayout
