import ContentRow from '@goodsailors/ui/components/other/ContentRow'
import { UploadedFile } from '@goodsailors/ui/types/fileUpload'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AccessTimeOutlined from '@mui/icons-material/AccessTimeOutlined'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import MapsHomeWorkOutlinedIcon from '@mui/icons-material/MapsHomeWorkOutlined'
import PersonOutlineOutlined from '@mui/icons-material/PersonOutlineOutlined'
import ReportGmailerrorredOutlined from '@mui/icons-material/ReportGmailerrorredOutlined'
import TodayOutlined from '@mui/icons-material/TodayOutlined'
import { useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack, { StackProps } from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form } from 'formik'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import * as yup from 'yup'

import FormFileUploadZone from '@/components/form/FormFileUploadZone'
import BackButton from '@/components/other/BackButton'
import BlockingFormik from '@/components/other/BlockingFormik'
import SubmitButton from '@/components/other/SubmitButton'
import {
    POPS_EVENT_ALLOWED_FILE_TYPE,
    POPS_EVENT_FILE_MAX_SIZE,
} from '@/config/pops'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import deleteEventAttachmentRequest from '@/requests/event/deleteEventAttachmentRequest'
import uploadEventAttachmentRequest, {
    UploadEventAttachmentBody,
    UploadEventAttachmentBodyKeys,
} from '@/requests/event/uploadEventAttachmentRequest'
import updatePopsEventRequest, {
    UpdatePopsEventBody,
    UpdatePopsEventBodyKeys,
} from '@/requests/pops/updatePopsEventRequest'
import {
    FacilityItemResponse,
    PopsEventResponse,
    PopsResponse,
    UserDetailResponse,
} from '@/requests/responses'
import { formatDate, formatDateRange, formatUserName } from '@/utils/format'
import { yupFilesValidation } from '@/utils/validation'

import FormEventDescription from '../../-components/FormEventDescription'

interface UpdatePopsEventFormProps {
    event: PopsEventResponse
    pops: PopsResponse
    facilityItem: FacilityItemResponse
    currentUser: UserDetailResponse
}

type InjectedUploadBody = Omit<UploadEventAttachmentBody, 'files'> & {
    files: UploadedFile[]
}

type Body = UpdatePopsEventBody & InjectedUploadBody

const UpdatePopsEventForm = ({
    event,
    facilityItem,
    pops,
    currentUser,
}: UpdatePopsEventFormProps) => {
    const { t } = useTranslation('common')
    const queryClient = useQueryClient()

    const { palette } = useTheme()

    const navigate = useNavigate()

    const onEventDetail = () => {
        const href = createHrefWithParams(routerPaths.pops.event.index, {
            facilityId: facilityItem.id,
            eventId: event.id,
        })

        navigate(href)
    }

    const { mutate: updatePopsEvent, isPending } = useMutation({
        mutationFn: async (body: Body) => {
            const { files, ...rest } = body

            const uploadedAttachmentsIds: (string | number)[] = []
            const filesForUpload: File[] = []

            _.forEach(files, (file) => {
                if ('arrayBuffer' in file) {
                    filesForUpload.push(file as unknown as File)
                } else {
                    uploadedAttachmentsIds.push(file.id)
                }
            })

            const attachmentsForDeletePromises = _.filter(
                event.attachments,
                (a) => {
                    return !uploadedAttachmentsIds.includes(a.id.toString())
                },
            ).map((a) => deleteEventAttachmentRequest(event.id, a.id))

            const attachmentsCount =
                _.size(event.attachments) -
                _.size(attachmentsForDeletePromises) +
                _.size(filesForUpload)

            try {
                await Promise.all(attachmentsForDeletePromises)

                if (_.size(filesForUpload) > 0) {
                    await uploadEventAttachmentRequest(event.id, {
                        files: filesForUpload,
                    })
                }
            } catch (e) {
                console.error(e)
            }

            await updatePopsEventRequest(event.id, {
                ...rest,
                attachmentsCount,
            })
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.pops.events,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.event(event.id).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.events.summary,
            })

            toast.success(t('texts.success-action'))
            onEventDetail()
        },
    })

    const initialValues: Body = React.useMemo(
        () => ({
            [UpdatePopsEventBodyKeys.DESCRIPTION]: event.description,
            [UpdatePopsEventBodyKeys.ATTACHMENTS_COUNT]: _.size(
                event.attachments,
            ),
            [UploadEventAttachmentBodyKeys.FILES]: _.map(
                event.attachments,
                (a) => ({
                    ...a,
                    url: a.thumbnail_url,
                    id: a.id.toString(),
                    type: a.mime,
                }),
            ),
        }),
        [event],
    )

    const validationSchema = yup.object().shape({
        [UpdatePopsEventBodyKeys.DESCRIPTION]: yup
            .string()
            .test('not-empty', 'errors.required-field', (value) => {
                return value !== '<p></p>'
            }),
        [UploadEventAttachmentBodyKeys.FILES]: yupFilesValidation(
            Object.keys(POPS_EVENT_ALLOWED_FILE_TYPE),
        ),
    })

    const shiftRange = React.useMemo(() => {
        const { starts_at, ends_at } = pops
        return formatDateRange(starts_at, ends_at)
    }, [pops])

    const onSubmit = (body: Body) => {
        updatePopsEvent(body)
    }

    return (
        <BlockingFormik
            enableReinitialize
            onSubmit={onSubmit}
            initialValues={initialValues}
            validationSchema={validationSchema}
        >
            <Stack gap={2} component={Form}>
                <Card sx={{ flex: 1, overflow: 'hidden' }}>
                    <CardContent
                        sx={{
                            display: 'flex',
                            height: '100%',
                            gap: 3,
                        }}
                    >
                        <Stack
                            gap={4}
                            pr={3}
                            flex={2}
                            overflow='auto'
                            borderRight={1}
                            borderColor={palette.divider}
                        >
                            <Typography
                                gap={1}
                                display='flex'
                                fontWeight={600}
                                alignItems='center'
                            >
                                <ReportGmailerrorredOutlined color='primary' />
                                {t('labels.update-event')}
                            </Typography>

                            <FormEventDescription
                                name={UpdatePopsEventBodyKeys.DESCRIPTION}
                            />

                            <FormFileUploadZone
                                multiple
                                slotProps={{
                                    container: {
                                        'data-testid': 'EVENT_FILE',
                                    } as StackProps,
                                }}
                                maxSize={POPS_EVENT_FILE_MAX_SIZE}
                                name={UploadEventAttachmentBodyKeys.FILES}
                                accept={POPS_EVENT_ALLOWED_FILE_TYPE}
                                label={t('texts.file-info-upload')}
                                description='PDF, JPG, DOC, ... (max. 10MB)'
                            />
                        </Stack>

                        <Box flex={1} pl={3} overflow={'auto'}>
                            <Typography
                                mb={3}
                                gap={1}
                                display='flex'
                                alignItems='center'
                                fontWeight={600}
                            >
                                <InfoOutlined color='primary' />
                                {t('labels.basic-information')}
                            </Typography>

                            <Stack gap={1}>
                                <ContentRow
                                    icon={<MapsHomeWorkOutlinedIcon />}
                                    title={t('labels.object')}
                                    content={`${facilityItem.name}`}
                                />

                                <ContentRow
                                    icon={<PersonOutlineOutlined />}
                                    title={t('labels.author')}
                                    content={formatUserName(currentUser)}
                                />

                                <ContentRow
                                    icon={<TodayOutlined />}
                                    title={t('labels.shift-range')}
                                    content={shiftRange}
                                />

                                <ContentRow
                                    icon={<AccessTimeOutlined />}
                                    title={t('labels.event-time')}
                                    content={formatDate(event.event_date_time)}
                                />
                            </Stack>
                        </Box>
                    </CardContent>
                </Card>

                <Card sx={{ flex: 'none' }}>
                    <CardContent sx={{ display: 'flex' }}>
                        <BackButton
                            size='medium'
                            color='inherit'
                            onClick={onEventDetail}
                        />

                        <SubmitButton
                            size='medium'
                            loading={isPending}
                            variant='contained'
                            sx={{ ml: 'auto' }}
                        >
                            {t('actions.update-event')}
                        </SubmitButton>
                    </CardContent>
                </Card>
            </Stack>
        </BlockingFormik>
    )
}

export default UpdatePopsEventForm
