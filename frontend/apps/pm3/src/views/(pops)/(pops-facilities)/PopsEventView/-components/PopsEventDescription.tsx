import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import React from 'react'
import { useTranslation } from 'react-i18next'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'

interface PopsEventDescriptionProps {
    eventDescription: string
}

const PopsEventDescription = ({
    eventDescription,
}: PopsEventDescriptionProps) => {
    const { t } = useTranslation()
    const contentRef = React.useRef<HTMLDivElement>(null)

    const [exitUrl, setExitUrl] = React.useState<string | null>(null)

    const handleLinkClick = (event: React.MouseEvent<HTMLDivElement>) => {
        const target = event.target as HTMLElement

        if (target.tagName === 'A') {
            event.preventDefault()
            const href = target.getAttribute('href')

            if (href) {
                setExitUrl(href)
            }
        }
    }

    const handleCancel = () => {
        setExitUrl(null)
    }

    const handleContinue = () => {
        if (exitUrl) {
            window.open(exitUrl)
        }

        setExitUrl(null)
    }

    return (
        <>
            <Box
                ref={contentRef}
                onClick={handleLinkClick}
                dangerouslySetInnerHTML={{ __html: eventDescription }}
                sx={{ wordBreak: 'break-all' }}
            />

            {exitUrl && (
                <ConfirmationDialog
                    onClose={handleCancel}
                    onConfirm={handleContinue}
                    confirmLabel={t('actions.leave-website')}
                >
                    <Typography>{t('texts.leave-website-confirm')}</Typography>
                </ConfirmationDialog>
            )}
        </>
    )
}

export default PopsEventDescription
