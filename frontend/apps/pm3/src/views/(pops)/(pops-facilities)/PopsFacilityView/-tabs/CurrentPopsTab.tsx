import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import HighlightOffOutlined from '@mui/icons-material/HighlightOffOutlined'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTranslation } from 'react-i18next'

import {
    FacilityResponse,
    PermissionName,
    PopsResponse,
    UserDetailResponse,
} from '@/requests/responses'
import { useAppStore } from '@/store/store'
import { canAccessWithPermissions } from '@/utils/validation'

import EventsTable from '../../-components/EventsTable'
import PopsDetailCard from '../../-components/PopsDetailCard'
import { isFacilityLastPopsActive } from '../../-utils/validation'

interface CurrentPopsTabProps {
    facility: FacilityResponse
    currentUser: UserDetailResponse
}

const CurrentPopsTab = ({ facility, currentUser }: CurrentPopsTabProps) => {
    const { t } = useTranslation('common')

    const openCreatePopsDialog = useAppStore.use.openCreatePopsDialog()

    const isActive = isFacilityLastPopsActive(facility)

    const canCreate = canAccessWithPermissions(facility.facility, [
        PermissionName.PERM_POPS_CREATE,
    ])

    if (isActive) {
        const pops = facility.last_pops as PopsResponse

        return (
            <Stack direction={'row'} gap={1} flex={1} overflow='hidden'>
                <Stack width={300}>
                    <PopsDetailCard
                        pops={pops}
                        currentUser={currentUser}
                        facilityItem={facility.facility}
                    />
                </Stack>

                <Stack width='calc(100% - 300px)' display='flex'>
                    <EventsTable
                        pops={pops}
                        currentUser={currentUser}
                        facilityItem={facility.facility}
                    />
                </Stack>
            </Stack>
        )
    }

    return (
        <Card data-testid='ACTIVITY_TAB'>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box
                    width={48}
                    height={48}
                    display='flex'
                    alignItems='center'
                    justifyContent='center'
                    sx={{
                        borderRadius: '50%',
                        background: (theme) => theme.palette.action.selected,
                    }}
                >
                    <HighlightOffOutlined />
                </Box>

                <Stack mr='auto'>
                    <Typography color='text.secondary'>
                        {t('labels.inactive-pops')}
                    </Typography>

                    <Typography color='text.secondary' variant='body2'>
                        {t('texts.pops-needs-created')}
                    </Typography>
                </Stack>

                {canCreate && (
                    <Button
                        data-testid='CREATE_POPS'
                        startIcon={<AddCircleOutlineOutlined />}
                        onClick={() => openCreatePopsDialog(facility.facility)}
                    >
                        {t('actions.create-pops')}
                    </Button>
                )}
            </CardContent>
        </Card>
    )
}

export default CurrentPopsTab
