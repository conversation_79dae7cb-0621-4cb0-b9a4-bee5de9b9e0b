import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import Stack from '@mui/material/Stack'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import * as _ from 'lodash'
import { Navigate, useParams } from 'react-router'

import { POPS_REFETCH_INTERVAL } from '@/config/pops'
import { useCurrentUser } from '@/config/queryClient/queries'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import TabGuard from '@/guard/TabGuard'
import getFacilityRequest from '@/requests/facility/getFacilityRequest'

import EventsTable from '../-components/EventsTable'
import ActivityCheckDialog from './-components/ActivityCheckDialog'
import LockedPopsTable from './-components/LockedPopsTable'
import Navigation from './-components/Navigation'
import CurrentPopsTab from './-tabs/CurrentPopsTab'
import { PopsFacilityViewTabName } from './types'

interface PopsFacilityViewContentProps {
    tab: PopsFacilityViewTabName
    onTab: (tab: PopsFacilityViewTabName) => void
}

const PopsFacilityViewContent = ({
    onTab,
    tab,
}: PopsFacilityViewContentProps) => {
    const params = useParams<'facilityId'>()
    const facilityId = parseParamInt(params.facilityId)

    const { data: currentUser, isPending: isUserPending } = useCurrentUser()

    const {
        data: facility,
        isPending: isFacilityPending,
        isError,
    } = useQuery({
        queryFn: () => getFacilityRequest(facilityId),
        queryKey: queryKeys.facility(facilityId).index,
        enabled: Boolean(facilityId),
        placeholderData: keepPreviousData,
        refetchInterval: POPS_REFETCH_INTERVAL,
    })

    const isLoading = isUserPending || isFacilityPending

    if (isLoading) {
        return <LoadingSpinner />
    }

    if (isError) {
        return <Navigate to={routerPaths.pops.index} />
    }

    if (!currentUser?.data) return <></>

    return (
        <>
            <Stack width='100%' gap={1}>
                <Navigation tab={tab} onTab={onTab} facility={facility.data} />

                {tab === PopsFacilityViewTabName.CURRENT_POPS && (
                    <CurrentPopsTab
                        currentUser={currentUser.data}
                        facility={facility.data}
                    />
                )}

                {tab === PopsFacilityViewTabName.LOCKED_POPS && (
                    <LockedPopsTable
                        currentUser={currentUser.data}
                        facility={facility.data}
                    />
                )}

                {tab === PopsFacilityViewTabName.EVENTS && facility && (
                    <EventsTable
                        currentUser={currentUser.data}
                        facilityItem={facility.data.facility}
                    />
                )}
            </Stack>

            <ActivityCheckDialog />
        </>
    )
}

const PopsFacilityView = () => {
    return (
        <TabGuard
            defaultTab={PopsFacilityViewTabName.CURRENT_POPS}
            validValues={_.values(PopsFacilityViewTabName)}
        >
            {(props) => <PopsFacilityViewContent {...props} />}
        </TabGuard>
    )
}

export default PopsFacilityView
