import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Divider from '@mui/material/Divider'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import routerPaths from '@/config/routerPaths'
import { FacilityItemResponse } from '@/requests/responses'

interface NavigationProps {
    facility: FacilityItemResponse
}

const Navigation = ({ facility }: NavigationProps) => {
    const { t } = useTranslation('common')

    const navigate = useNavigate()

    const onBackClick = React.useCallback(() => {
        const href = createHrefWithParams(routerPaths.pops.facility.index, {
            facilityId: String(facility.id),
        })

        navigate(href)
    }, [navigate, facility])

    return (
        <Card
            sx={{
                width: '100%',
            }}
        >
            <CardHeader
                title={
                    <Box display={'flex'} alignItems={'center'}>
                        <ChevronLeftIcon
                            sx={{
                                fontSize: 22,
                                mr: 1,
                                cursor: 'pointer',
                                color: ({ palette }) => palette.blueGrey['300'],
                            }}
                            onClick={onBackClick}
                        />

                        <Stack direction='row' alignItems='center' gap={2}>
                            <div>{t('labels.pops-detail')}</div>
                            <Divider flexItem={true} orientation='vertical' />
                            <Typography>{facility.name}</Typography>
                        </Stack>
                    </Box>
                }
            />
        </Card>
    )
}

export default Navigation
