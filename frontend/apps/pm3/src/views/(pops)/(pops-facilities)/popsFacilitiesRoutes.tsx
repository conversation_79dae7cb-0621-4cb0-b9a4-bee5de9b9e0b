import React from 'react'

import routerPaths from '@/config/routerPaths'

const PopsFacilitiesView = React.lazy(
    () => import('./PopsFacilitiesView/PopsFacilitiesView'),
)
const PopsFacilityView = React.lazy(
    () => import('./PopsFacilityView/PopsFacilityView'),
)
const UpdatePopsEventView = React.lazy(
    () => import('./UpdatePopsEventView/UpdatePopsEventView'),
)
const CreatePopsEventView = React.lazy(
    () => import('./CreatePopsEventView/CreatePopsEventView'),
)
const PopsDetailView = React.lazy(
    () => import('./PopsDetailView/PopsDetailView'),
)
const PopsEventView = React.lazy(() => import('./PopsEventView/PopsEventView'))

const popsFacilitiesRoutes = [
    {
        path: routerPaths.pops.facilities.index,
        element: <PopsFacilitiesView />,
    },
    {
        path: routerPaths.pops.facility.index,
        element: <PopsFacilityView />,
    },
    {
        path: routerPaths.pops.event.index,
        element: <PopsEventView />,
    },
    {
        path: routerPaths.pops.popsDetail.index,
        element: <PopsDetailView />,
    },
    {
        path: routerPaths.pops.facility.createEvent,
        element: <CreatePopsEventView />,
    },
    {
        path: routerPaths.pops.event.update,
        element: <UpdatePopsEventView />,
    },
]

export default popsFacilitiesRoutes
