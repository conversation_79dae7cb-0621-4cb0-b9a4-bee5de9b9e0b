import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { Navigate, useParams } from 'react-router'

import { POPS_REFETCH_INTERVAL } from '@/config/pops'
import { useCurrentUser } from '@/config/queryClient/queries'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import getFacilityRequest from '@/requests/facility/getFacilityRequest'
import { PopsResponse } from '@/requests/responses'

import { isFacilityLastPopsActive } from '../-utils/validation'
import CreatePopsEventForm from './-components/CreatePopsEventForm'
import { CreatePopsEventViewParams } from './types'

const CreatePopsEventView = () => {
    const params = useParams<keyof CreatePopsEventViewParams>()
    const facilityId = parseParamInt(params.facilityId)

    const { data: currentUser, isPending: isUserPending } = useCurrentUser()

    const { data: facility, isPending: isFacilityPending } = useQuery({
        queryFn: () => getFacilityRequest(facilityId),
        queryKey: queryKeys.facility(facilityId).index,
        enabled: Boolean(facilityId),
        placeholderData: keepPreviousData,
        refetchInterval: POPS_REFETCH_INTERVAL,
    })

    if (isFacilityPending || isUserPending) {
        return <LoadingSpinner />
    }

    if (facility && isFacilityLastPopsActive(facility.data) && currentUser) {
        return (
            <CreatePopsEventForm
                currentUser={currentUser.data}
                facilityItem={facility.data.facility}
                pops={facility.data.last_pops as PopsResponse}
            />
        )
    }

    const href = createHrefWithParams(routerPaths.pops.facility.index, {
        facilityId: facilityId,
    })

    return <Navigate to={href} />
}

export default CreatePopsEventView
