import ButtonDropdown from '@goodsailors/ui/components/other/ButtonDropdown'
import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import useDebouncedValue from '@goodsailors/ui/hooks/useDebouncedValue'
import useGridFilter from '@goodsailors/ui/hooks/useGridFilter'
import useGridOperators from '@goodsailors/ui/hooks/useGridOperators'
import useGridPagination from '@goodsailors/ui/hooks/useGridPagination'
import useGridSort from '@goodsailors/ui/hooks/useGridSort'
import createHrefWithParams from '@goodsailors/ui/utils/createHrefWithParams'
import AddCircleOutlineOutlined from '@mui/icons-material/AddCircleOutlineOutlined'
import AttachFileOutlined from '@mui/icons-material/AttachFileOutlined'
import FileUploadOutlined from '@mui/icons-material/FileUploadOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import { DataGridPro, GridColDef, useGridApiRef } from '@mui/x-data-grid-pro'
import {
    keepPreviousData,
    useInfiniteQuery,
    useMutation,
    useQuery,
} from '@tanstack/react-query'
import fileDownload from 'js-file-download'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

import CellLink from '@/components/other/CellLink'
import DataGridHeader from '@/components/other/DataGridHeader'
import EventCategoryChip from '@/components/other/EventCategoryChip'
import { gridFilterMap } from '@/config/gridFilter'
import { POPS_REFETCH_INTERVAL } from '@/config/pops'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import usePersistTable from '@/hooks/usePersistTable'
import exportPopsRequest, {
    ExportPopsTargetName,
} from '@/requests/pops/exportPopsRequest'
import getPopsEventsRequest, {
    GetPopsEventsParams,
} from '@/requests/pops/getPopsEventsRequest'
import {
    EventCategorySeverityName,
    FacilityItemResponse,
    PermissionName,
    PopsEventResponse,
    PopsResponse,
    UserDetailResponse,
} from '@/requests/responses'
import getUsersRequest, {
    GetUsersParams,
} from '@/requests/user/getUsersRequest'
import createRequestParams from '@/utils/createRequestParams'
import { formatDate, formatUserName } from '@/utils/format'
import { canAccessWithPermissions } from '@/utils/validation'

import { isPopsEventUpdatable } from '../-utils/validation'

interface EventsTableProps {
    currentUser: UserDetailResponse
    pops?: PopsResponse
    eventDate?: string
    facilityItem: FacilityItemResponse
}

const EventsTable = ({
    pops,
    currentUser,
    eventDate,
    facilityItem,
}: EventsTableProps) => {
    const { t } = useTranslation('common')

    const gridRef = useGridApiRef()
    const navigate = useNavigate()

    const [search, setSearch, debouncedSearch] = useDebouncedValue<string>('')

    const [filter, filterModel, setFilterModel] = useGridFilter(gridFilterMap)

    const [pagination, paginationModel, setPaginationModel] =
        useGridPagination()
    const [sort, sortModel, setSortModel] = useGridSort()

    const {
        gridEnumOperators,
        gridStringOperators,
        gridDateOperators,
        gridAsyncAutocompleteOperators,
    } = useGridOperators()

    const facilityId = facilityItem.id

    const params = React.useMemo(() => {
        const value: GetPopsEventsParams = {
            sort,
            eventDate,
            filter: [],
            pagination,
            isLocked: false,
            facilityId: facilityId,
            search: debouncedSearch,
            accountId: currentUser.account_id,
        }

        if (pops) {
            value.popsId = pops.id
        }

        _.forEach(filter, (filterItem) => {
            if (
                filterItem.field === 'hasAttachment' &&
                filterItem.value !== undefined
            ) {
                value.hasAttachment = Boolean(filterItem.value)
            } else {
                value.filter?.push(filterItem)
            }
        })

        return createRequestParams<GetPopsEventsParams>(value)
    }, [
        currentUser.account_id,
        debouncedSearch,
        eventDate,
        facilityId,
        filter,
        pagination,
        pops,
        sort,
    ])

    const canUpdateEvent = canAccessWithPermissions(facilityItem, [
        PermissionName.PERM_POPS_EVENTS_EDIT,
    ])

    const canExport = canAccessWithPermissions(facilityItem, [
        PermissionName.PERM_POPS_EVENTS_EXPORT,
    ])

    const canCreateEvent = canAccessWithPermissions(facilityItem, [
        PermissionName.PERM_POPS_CREATE,
    ])

    const { data: events, isFetching } = useQuery({
        queryFn: () => getPopsEventsRequest(params),
        queryKey: [...queryKeys.pops.events, params],
        placeholderData: keepPreviousData,
        refetchInterval: POPS_REFETCH_INTERVAL,
    })

    const { mutate: exportPops, isPending: exportPopsPending } = useMutation({
        mutationFn: (target: ExportPopsTargetName) => {
            if (!pops) {
                throw Error('Pops is null')
            }

            return exportPopsRequest(target, params)
        },
        onSuccess: (response, target) => {
            fileDownload(response, `pops-export.${target}`)
        },
    })

    const onDetail = React.useCallback(
        (event: PopsEventResponse) => {
            const href = createHrefWithParams(routerPaths.pops.event.index, {
                facilityId: String(facilityId),
                eventId: event.id,
            })

            navigate(href)
        },
        [facilityId, navigate],
    )

    const onCreate = React.useCallback(() => {
        const href = createHrefWithParams(
            routerPaths.pops.facility.createEvent,
            {
                facilityId: String(facilityId),
            },
        )

        navigate(href)
    }, [facilityId, navigate])

    const onUpdate = React.useCallback(
        (event: PopsEventResponse) => {
            const href = createHrefWithParams(routerPaths.pops.event.update, {
                facilityId: String(facilityId),
                eventId: event.id,
            })

            navigate(href)
        },
        [facilityId, navigate],
    )

    const [usersSearch, setUsersSearch, usersDebounceSearch] =
        useDebouncedValue<string>('')

    const {
        data: users,
        isLoading: isUsersLoading,
        isFetchingNextPage: isUsersFetchingNextPage,
        fetchNextPage: fetchUsersNextPage,
        hasNextPage: hasUsersNextPage,
    } = useInfiniteQuery({
        queryKey: [...queryKeys.permissionTemplates.index, usersDebounceSearch],
        queryFn: ({ pageParam = 0 }) => {
            const params = createRequestParams<GetUsersParams>({
                pagination: {
                    limit: 10,
                    offset: pageParam,
                },
                search: usersDebounceSearch || undefined,
            })

            return getUsersRequest(params)
        },
        initialPageParam: 0,
        getNextPageParam: (lastPage, allPages) => {
            const totalLoaded = allPages.reduce(
                (count, page) => count + page.list.length,
                0,
            )
            return totalLoaded < lastPage.total_count ? totalLoaded : undefined
        },
    })

    const usersOptions = React.useMemo(() => {
        if (!users?.pages) return []

        const options = users.pages.flatMap((page) =>
            page.list.map((user) => ({
                id: user.id,
                label: formatUserName(user),
            })),
        )

        return options
    }, [users])

    const columns = React.useMemo<GridColDef<PopsEventResponse>[]>(
        () => [
            {
                minWidth: 200,
                field: 'eventDateTime',
                headerName: t('labels.event-time'),
                sortable: false,
                filterOperators: gridDateOperators(),
                renderCell: ({ row }) => {
                    const href = createHrefWithParams(
                        routerPaths.pops.event.index,
                        {
                            facilityId: String(facilityId),
                            eventId: row.id,
                        },
                    )
                    return (
                        <CellLink
                            label={formatDate(row.event_date_time)}
                            to={href}
                        />
                    )
                },
            },
            {
                minWidth: 200,
                field: 'eventCategory.name',
                headerName: t('labels.event'),
                sortable: false,
                filterOperators: gridStringOperators(),
                valueGetter: (__, row) => row.event_category?.label,
            },
            {
                minWidth: 150,
                field: 'eventCategory.severity',
                sortable: false,
                filterOperators: gridEnumOperators(
                    _.values(EventCategorySeverityName).map((value) => ({
                        value,
                        label: t(`labels.event-category.${value}`),
                    })),
                ),
                headerName: t('labels.category'),
                valueGetter: (__, row) => row.event_category.severity,
                renderCell: ({ row }) => {
                    return (
                        <EventCategoryChip
                            size='small'
                            name={row.event_category.severity}
                        />
                    )
                },
            },
            {
                minWidth: 200,
                field: 'description',
                filterOperators: gridStringOperators(),
                headerName: t('labels.description'),
                renderCell: ({ row }) => (
                    <Box
                        dangerouslySetInnerHTML={{
                            __html: row.description || '',
                        }}
                    />
                ),
            },
            {
                minWidth: 100,
                align: 'center',
                field: 'hasAttachment',
                type: 'boolean',
                sortable: false,
                headerName: t('labels.attaches'),
                renderCell: ({ row }) => (
                    <Stack height='100%' justifyContent='center'>
                        <AttachFileOutlined
                            fontSize='small'
                            sx={{
                                color: ({ palette }) =>
                                    _.size(row.attachments) > 0
                                        ? palette.primary.main
                                        : palette.blueGrey['100'],
                            }}
                        />
                    </Stack>
                ),
            },
            {
                minWidth: 200,
                field: 'insertedBy.id',
                headerName: t('labels.author'),
                sortable: false,
                filterOperators: gridAsyncAutocompleteOperators({
                    loading: isUsersLoading,
                    onSearch: setUsersSearch,
                    search: usersSearch,
                    options: usersOptions,
                    getNextPage: fetchUsersNextPage,
                    hasNextPage: hasUsersNextPage,
                    loadingNextPage: isUsersFetchingNextPage,
                    itemSize: 40,
                }),
                valueGetter: (__, row) => formatUserName(row.inserted_by),
            },
            {
                field: 'actions',
                type: 'actions',
                resizable: false,
                headerName: t('labels.actions'),
                renderCell: ({ row }) => {
                    const canUpdate =
                        pops &&
                        canUpdateEvent &&
                        !pops.is_locked &&
                        isPopsEventUpdatable(pops, row)

                    return (
                        <IconButtonDropdown
                            icon={<MoreVert fontSize='small' />}
                            menuContent={({ onClose }) => (
                                <Stack>
                                    {canUpdate && (
                                        <MenuItem
                                            data-testid='MENU_UPDATE'
                                            onClick={() => {
                                                onClose()
                                                onUpdate(row)
                                            }}
                                        >
                                            {t('actions.update-event')}
                                        </MenuItem>
                                    )}

                                    <MenuItem
                                        data-testid='MENU_SHOW'
                                        onClick={() => onDetail(row)}
                                    >
                                        {t('actions.show-detail')}
                                    </MenuItem>
                                </Stack>
                            )}
                        />
                    )
                },
            },
        ],
        [
            t,
            gridDateOperators,
            gridStringOperators,
            gridEnumOperators,
            gridAsyncAutocompleteOperators,
            isUsersLoading,
            setUsersSearch,
            usersSearch,
            usersOptions,
            fetchUsersNextPage,
            hasUsersNextPage,
            isUsersFetchingNextPage,
            facilityId,
            pops,
            canUpdateEvent,
            onUpdate,
            onDetail,
        ],
    )

    usePersistTable(
        gridRef,
        routerPaths.pops.facility.index + facilityItem.id + (pops?.id || ''),
    )

    return (
        <Card sx={{ height: '100%', width: '100%' }}>
            <CardContent
                sx={{
                    p: '0 !important',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <DataGridHeader
                    search={search}
                    onSearch={setSearch}
                    gridRef={gridRef}
                    actions={
                        <>
                            {pops && !pops.is_locked && canCreateEvent && (
                                <Button
                                    data-testid='ADD_EVENT'
                                    onClick={onCreate}
                                    startIcon={<AddCircleOutlineOutlined />}
                                >
                                    {t('actions.add-event')}
                                </Button>
                            )}

                            {pops && canExport && (
                                <ButtonDropdown
                                    data-testid='EXPORT_BUTTON'
                                    loading={exportPopsPending}
                                    startIcon={<FileUploadOutlined />}
                                    menuContent={
                                        <Stack>
                                            <MenuItem
                                                onClick={() =>
                                                    exportPops(
                                                        ExportPopsTargetName.PDF,
                                                    )
                                                }
                                            >
                                                {t('actions.export-to-target', {
                                                    target: 'PDF',
                                                })}
                                            </MenuItem>

                                            <MenuItem
                                                onClick={() =>
                                                    exportPops(
                                                        ExportPopsTargetName.XLS,
                                                    )
                                                }
                                            >
                                                {t('actions.export-to-target', {
                                                    target: 'XLS',
                                                })}
                                            </MenuItem>
                                        </Stack>
                                    }
                                >
                                    {t('actions.export')}
                                </ButtonDropdown>
                            )}
                        </>
                    }
                />

                <DataGridPro
                    disableColumnPinning
                    loading={isFetching}
                    pagination
                    apiRef={gridRef}
                    columns={columns}
                    paginationMode='server'
                    sortingMode='server'
                    filterMode='server'
                    sx={{ height: 400 }}
                    paginationModel={paginationModel}
                    filterModel={filterModel}
                    onPaginationModelChange={(model) =>
                        setPaginationModel(model)
                    }
                    onFilterModelChange={(model) => setFilterModel(model)}
                    sortModel={sortModel}
                    onSortModelChange={(model) => setSortModel(model)}
                    rows={events?.list || []}
                    rowCount={events?.total_count || 0}
                    initialState={{
                        density: 'compact',
                        pinnedColumns: {
                            right: ['actions'],
                        },
                    }}
                />
            </CardContent>
        </Card>
    )
}

export default EventsTable
