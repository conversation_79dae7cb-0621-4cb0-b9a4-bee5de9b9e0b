import PieChartWithLabel from '@goodsailors/ui/components/charts/PieChartWithLabel'
import IconButtonDropdown from '@goodsailors/ui/components/other/IconButtonDropdown'
import LockClockOutlined from '@mui/icons-material/LockClockOutlined'
import MoreVert from '@mui/icons-material/MoreVert'
import PlayCircleOutlineOutlined from '@mui/icons-material/PlayCircleOutlineOutlined'
import { alpha, IconButtonProps, useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Chip from '@mui/material/Chip'
import Divider from '@mui/material/Divider'
import MenuItem from '@mui/material/MenuItem'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as _ from 'lodash'
import React from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import queryKeys from '@/config/queryClient/queryKeys'
import { useLastActivity } from '@/hooks/useLastActivity'
import getPopsEventsSummaryRequest, {
    GetPopsEventsSummaryParams,
} from '@/requests/pops/getPopsEventsSummaryRequest'
import lockPopsRequest from '@/requests/pops/lockPopsRequest'
import {
    EventCategorySeverityName,
    FacilityItemResponse,
    PermissionName,
    PopsEventsSummaryResponse,
    PopsResponse,
    UserDetailResponse,
} from '@/requests/responses'
import { useAppStore } from '@/store/store'
import createRequestParams from '@/utils/createRequestParams'
import { formatDateRange, formatUserName } from '@/utils/format'
import { canAccessWithPermissions } from '@/utils/validation'

interface PopsDetailCardProps {
    currentUser: UserDetailResponse
    pops: PopsResponse
    facilityItem: FacilityItemResponse
}

const PopsDetailCard = ({
    currentUser: user,
    pops,
    facilityItem,
}: PopsDetailCardProps) => {
    const { t } = useTranslation('common')

    const queryClient = useQueryClient()
    const { palette, spacing } = useTheme()

    const openUpdatePopsDialog = useAppStore.use.openUpdatePopsDialog()

    const lastActivity = useLastActivity(pops.last_pops_event?.event_date_time)

    const [confirmationOpen, setConfirmationOpen] = React.useState(false)

    const shiftRange = formatDateRange(pops.starts_at, pops.ends_at)

    const canUpdatePops = canAccessWithPermissions(facilityItem, [
        PermissionName.PERM_POPS_EDIT,
    ])

    const canLockPops = canAccessWithPermissions(facilityItem, [
        PermissionName.PERM_POPS_EDIT,
    ])

    const { mutate, isPending: isDeletePending } = useMutation({
        mutationFn: () => lockPopsRequest(pops.id),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: queryKeys.facility(facilityItem.id).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.popsDetail(pops.id).index,
            })

            await queryClient.invalidateQueries({
                queryKey: queryKeys.pops.index,
            })

            toast.success(t('texts.success-action'))
            setConfirmationOpen(false)
        },
    })

    const getPopsEventsSummaryParams =
        createRequestParams<GetPopsEventsSummaryParams>({
            popsId: pops.id,
            accountId: user.account_id,
        })

    const { data: summary, isPending } = useQuery({
        queryFn: () => getPopsEventsSummaryRequest(getPopsEventsSummaryParams),
        queryKey: [...queryKeys.events.summary, getPopsEventsSummaryParams],
    })

    const statusColor = React.useMemo(() => {
        return pops.is_locked ? 'error' : 'success'
    }, [pops.is_locked])

    const data: { label: string; value: number; color: string }[] =
        React.useMemo(() => {
            return _.map(summary?.data, (item: PopsEventsSummaryResponse) => {
                const severity = item.type.name
                const label = t(`labels.event-category.${severity}`)
                let color = palette.primary.main

                if (severity === EventCategorySeverityName.MEDIUM) {
                    color = palette.warning.main
                }

                if (severity === EventCategorySeverityName.CRITICAL) {
                    color = palette.error.main
                }

                if (severity === EventCategorySeverityName.LOW) {
                    color = palette.success.main
                }

                return {
                    value: item.total,
                    label,
                    color,
                    labelMarkType: 'square',
                }
            })
        }, [t, summary, palette])

    const eventsSum = React.useMemo(() => {
        return data.reduce((acc: number, item) => acc + item.value, 0)
    }, [data])

    return (
        <Card sx={{ height: '100%', flex: 'none', overflow: 'auto' }}>
            <CardContent
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <Stack direction='row' justifyContent='space-between'>
                    <Stack direction='row' alignItems='center' gap={2}>
                        <Box
                            width={48}
                            height={48}
                            display='flex'
                            alignItems='center'
                            justifyContent='center'
                            sx={{
                                borderRadius: '50%',
                                color: palette[statusColor].dark,
                                background: alpha(
                                    palette[statusColor].main,
                                    0.15,
                                ),
                            }}
                        >
                            {pops.is_locked ? (
                                <LockClockOutlined />
                            ) : (
                                <PlayCircleOutlineOutlined />
                            )}
                        </Box>

                        <Stack>
                            <Typography color={palette[statusColor].main}>
                                {pops.is_locked
                                    ? t('labels.inactive-pops')
                                    : t('labels.active-pops')}
                            </Typography>

                            <Typography variant='body2' color={'text.primary'}>
                                {formatUserName(user)}
                            </Typography>
                        </Stack>
                    </Stack>

                    {canUpdatePops && !pops.is_locked && (
                        <IconButtonDropdown
                            iconButtonProps={
                                {
                                    sx: { p: 0 },
                                    'data-testid': 'POPS_ACTIONS',
                                } as IconButtonProps
                            }
                            icon={<MoreVert fontSize='small' />}
                            menuContent={({ onClose }) => (
                                <MenuItem
                                    data-testid='UPDATE_POPS'
                                    onClick={() => {
                                        openUpdatePopsDialog(facilityItem, pops)
                                        onClose()
                                    }}
                                >
                                    {t('actions.update-pops')}
                                </MenuItem>
                            )}
                        />
                    )}
                </Stack>

                <Divider sx={{ my: 3 }} />

                <Box mb={3}>
                    <Typography mb={1}>{t('labels.shift-range')}</Typography>
                    <Chip size='small' label={shiftRange} />
                </Box>

                {lastActivity && (
                    <Box>
                        <Typography mb={1}>
                            {t('labels.last-activity')}
                        </Typography>

                        <Chip size='small' label={lastActivity} />
                    </Box>
                )}

                <Divider sx={{ my: 3 }} />

                {data.length > 0 && (
                    <Box>
                        <Typography mb={2} variant='body2'>
                            {t('labels.events')}
                        </Typography>

                        <PieChartWithLabel
                            sx={{
                                '.PieChartLabel': {
                                    fontSize: 20,
                                },
                            }}
                            series={[
                                {
                                    data,
                                    innerRadius: 50,
                                    outerRadius: 80,
                                    highlightScope: {
                                        fade: 'global',
                                    },
                                },
                            ]}
                            slotProps={{
                                loadingOverlay: {
                                    message: t('labels.loading'),
                                },
                                noDataOverlay: { message: '' },
                            }}
                            colors={data.map((item) => item.color)}
                            loading={isPending}
                            width={250}
                            height={120}
                            label={`${eventsSum}`}
                        />

                        <Divider sx={{ my: 3 }} />
                    </Box>
                )}

                {!pops.is_locked && canLockPops && (
                    <Button
                        color='error'
                        data-testid='LOCK_BUTTON'
                        onClick={() => setConfirmationOpen(true)}
                        sx={{
                            width: 'max-content',
                            mx: 'auto',
                            mt: 'auto',
                        }}
                        startIcon={<LockClockOutlined />}
                    >
                        {t('actions.lock-pops')}
                    </Button>
                )}

                {confirmationOpen && (
                    <ConfirmationDialog
                        isLoading={isDeletePending}
                        onConfirm={mutate}
                        sx={{
                            textAlign: 'center',

                            '.MuiPaper-root': {
                                maxWidth: 480,
                                width: '100%',
                            },
                        }}
                        confirmLabel={t('actions.lock-pops')}
                        onClose={() => setConfirmationOpen(false)}
                    >
                        <Stack alignItems='center'>
                            <Box
                                mb={3}
                                width={80}
                                height={80}
                                display='flex'
                                alignItems='center'
                                justifyContent='center'
                                borderRadius={spacing(2)}
                                bgcolor={alpha(palette.error.main, 0.15)}
                            >
                                <LockClockOutlined
                                    sx={{
                                        fontSize: 48,
                                        color: palette.error.main,
                                    }}
                                />
                            </Box>

                            <Typography variant='h5' mb={1}>
                                {t('texts.lock-pops-confirmation')}
                            </Typography>

                            <Typography fontWeight={500} variant='h6'>
                                {shiftRange}
                            </Typography>
                        </Stack>
                    </ConfirmationDialog>
                )}
            </CardContent>
        </Card>
    )
}

export default PopsDetailCard
