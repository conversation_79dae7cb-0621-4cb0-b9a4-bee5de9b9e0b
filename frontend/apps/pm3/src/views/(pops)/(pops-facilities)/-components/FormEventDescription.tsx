import { FormControlProps } from '@mui/material'
import Document from '@tiptap/extension-document'
import Link from '@tiptap/extension-link'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'
import { useTranslation } from 'react-i18next'

import FormTextFieldEditor from '@/components/form/FormTextFieldEditor'
import { useAppConfig } from '@/config/queryClient/queries'

interface FormEventDescriptionProps {
    name: string
}

const FormEventDescription = ({
    name,
    ...props
}: FormEventDescriptionProps) => {
    const { t } = useTranslation()

    const { data: appConfig } = useAppConfig()
    const allowedOrigins = appConfig?.data.allowed_origins || []

    return (
        <FormTextFieldEditor
            {...props}
            name={name}
            slotProps={{
                control: {
                    'data-testid': 'EVENT_DESCRIPTION',
                } as FormControlProps,
            }}
            label={t('labels.event-description')}
            dependencies={[allowedOrigins]}
            extensions={[
                Document,
                Text,
                Paragraph,
                Link.configure({
                    openOnClick: true,
                    autolink: true,
                    linkOnPaste: true,
                    defaultProtocol: 'https',
                    protocols: ['http', 'https'],
                    isAllowedUri: (url, ctx) => {
                        try {
                            const parsedUrl = url.includes(':')
                                ? new URL(url)
                                : new URL(`${ctx.defaultProtocol}://${url}`)

                            if (!ctx.defaultValidate(parsedUrl.href)) {
                                return false
                            }

                            const disallowedProtocols = [
                                'ftp',
                                'file',
                                'mailto',
                            ]

                            const protocol = parsedUrl.protocol.replace(':', '')

                            if (disallowedProtocols.includes(protocol)) {
                                return false
                            }

                            const allowedProtocols = ctx.protocols.map((p) =>
                                typeof p === 'string' ? p : p.scheme,
                            )

                            if (!allowedProtocols.includes(protocol)) {
                                return false
                            }

                            const domain = parsedUrl.hostname
                            return allowedOrigins.includes(domain)
                        } catch {
                            return false
                        }
                    },
                    shouldAutoLink: (url) => {
                        try {
                            const parsedUrl = url.includes(':')
                                ? new URL(url)
                                : new URL(`https://${url}`)

                            const domain = parsedUrl.hostname
                            return allowedOrigins.includes(domain)
                        } catch {
                            return false
                        }
                    },
                }),
            ]}
        />
    )
}

export default FormEventDescription
