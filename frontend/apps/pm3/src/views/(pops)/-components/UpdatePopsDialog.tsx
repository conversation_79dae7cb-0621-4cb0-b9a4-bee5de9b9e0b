import DialogHeader from '@goodsailors/ui/components/other/DialogHeader'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import { useTranslation } from 'react-i18next'

import { useCurrentUser } from '@/config/queryClient/queries'
import { FacilityItemResponse, PopsResponse } from '@/requests/responses'

import UpsertPopsForm from './UpdatePopsForm'

interface UpdatePopsDialogProps {
    facilityItem: FacilityItemResponse
    onClose: () => void
    pops: PopsResponse
}

const UpdatePopsDialog = ({
    onClose,
    facilityItem,
    pops,
}: UpdatePopsDialogProps) => {
    const { t } = useTranslation('common')

    const { data: currentUser } = useCurrentUser()

    return (
        <Dialog
            open={true}
            onClose={onClose}
            sx={{
                '.MuiPaper-root': {
                    maxWidth: 480,
                    width: '100%',
                },
            }}
        >
            <DialogHeader title={t('actions.update-pops')} onClose={onClose} />

            <DialogContent>
                {currentUser?.data && (
                    <UpsertPopsForm
                        currentUser={currentUser.data}
                        facilityItem={facilityItem}
                        onCancel={onClose}
                        onSucess={onClose}
                        pops={pops}
                    />
                )}
            </DialogContent>
        </Dialog>
    )
}
export default UpdatePopsDialog
