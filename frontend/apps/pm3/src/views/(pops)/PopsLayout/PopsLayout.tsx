import parseParamInt from '@goodsailors/ui/utils/parseParamInt'
import MapsHomeWorkOutlined from '@mui/icons-material/MapsHomeWorkOutlined'
import { useQuery } from '@tanstack/react-query'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router'

import Layout from '@/components/layout/Layout'
import PageHead from '@/components/layout/PageHead'
import queryKeys from '@/config/queryClient/queryKeys'
import routerPaths from '@/config/routerPaths'
import AppGuard from '@/guard/AppGuard'
import AuthenticatedGuard from '@/guard/AuthenticatedGuard'
import getFacilityRequest from '@/requests/facility/getFacilityRequest'
import getPopsDetailRequest from '@/requests/pops/getPopsDetailRequest'
import getPopsEventRequest from '@/requests/pops/getPopsEventRequest'
import { ApplicationName } from '@/requests/responses'
import { useAppStore } from '@/store/store'

import CreatePopsDialog from '../-components/CreatePopsDialog'
import NavbarActions from '../-components/NavbarActions'
import UpdatePopsDialog from '../-components/UpdatePopsDialog'
import useFacilityPopsSSE from '../-sse/useFacilityPopsSSE'
import usePopsEventsSSE from '../-sse/usePopsEventsSSE'

const PopsLayoutContent = () => {
    const { t } = useTranslation('common')

    const createPopsDialog = useAppStore.use.createPopsDialog()
    const closeCreatePopsDialog = useAppStore.use.closeCreatePopsDialog()

    const updatePopsDialog = useAppStore.use.updatePopsDialog()
    const closeUpdatePopsDialog = useAppStore.use.closeUpdatePopsDialog()

    const params = useParams()

    const facilityId = parseParamInt(params.facilityId)
    const popsId = parseParamInt(params.popsId)
    const eventId = parseParamInt(params.eventId)

    const { data: facility } = useQuery({
        queryFn: () => getFacilityRequest(facilityId),
        queryKey: queryKeys.facility(facilityId).index,
        enabled: Boolean(facilityId),
    })

    const { data: pops } = useQuery({
        queryFn: () => getPopsDetailRequest(popsId),
        queryKey: queryKeys.popsDetail(popsId).index,
        enabled: Boolean(popsId),
    })

    const { data: event } = useQuery({
        queryFn: () => getPopsEventRequest(eventId),
        queryKey: queryKeys.event(eventId).index,
        enabled: Boolean(eventId),
    })

    useFacilityPopsSSE(facility?.data || null)
    usePopsEventsSSE(pops?.data || facility?.data.last_pops || null)

    const sidebarItems = [
        {
            href: routerPaths.pops.facilities.index,
            label: t('labels.objects'),
            icon: <MapsHomeWorkOutlined />,
        },
    ]

    const map = React.useMemo(
        () => ({
            [routerPaths.pops.index]: t('labels.pops'),
            [routerPaths.pops.facilities.index]: t('labels.objects'),
            [routerPaths.pops.facility.createEvent]: t('labels.create-event'),
            '/pops/facilities/:facilityId':
                facility?.data?.facility?.name || '',
            [routerPaths.pops.popsDetail.index]:
                `${t('labels.pops-detail')} - ${pops?.data?.id || ''}`,
            [routerPaths.pops.event.index]:
                `${t('labels.event-detail')} - ${event?.data?.id || ''}`,
        }),
        [t, facility, pops, event],
    )

    return (
        <>
            <PageHead map={map} favicon={'/images/logos/pops-icon.svg'} />

            <Layout
                breadcrumbsMap={map}
                sidebarItems={sidebarItems}
                actions={<NavbarActions />}
                logoUrl='/images/logos/pops-icon.svg'
            />

            {createPopsDialog && (
                <CreatePopsDialog
                    onClose={closeCreatePopsDialog}
                    facilityItem={createPopsDialog.facilityItem}
                />
            )}

            {updatePopsDialog && (
                <UpdatePopsDialog
                    pops={updatePopsDialog.pops}
                    onClose={closeUpdatePopsDialog}
                    facilityItem={updatePopsDialog.facilityItem}
                />
            )}
        </>
    )
}

const PopsLayout = () => {
    return (
        <AuthenticatedGuard>
            <AppGuard name={ApplicationName.POPS}>
                <PopsLayoutContent />
            </AppGuard>
        </AuthenticatedGuard>
    )
}

export default PopsLayout
