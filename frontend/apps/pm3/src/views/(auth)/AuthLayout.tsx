import LoadingSpinner from '@goodsailors/ui/components/other/LoadingSpinner'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import { Suspense } from 'react'
import { useTranslation } from 'react-i18next'
import { Outlet } from 'react-router'

import PageHead from '@/components/layout/PageHead'
import routerPaths from '@/config/routerPaths'
import UnauthenticatedGuard from '@/guard/UnauthenticatedGuard'

const AuthLayout = () => {
    const { t } = useTranslation('common')

    const headMap = {
        [routerPaths.auth.login]: t('labels.login'),
    }

    return (
        <UnauthenticatedGuard>
            <PageHead map={headMap} />

            <Box minHeight={'100vh'} height={'100%'}>
                <Container maxWidth='xs'>
                    <Suspense fallback={<LoadingSpinner />}>
                        <Outlet />
                    </Suspense>
                </Container>
            </Box>
        </UnauthenticatedGuard>
    )
}

export default AuthLayout
