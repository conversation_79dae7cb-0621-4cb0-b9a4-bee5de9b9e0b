import React from 'react'

import routerPaths from '@/config/routerPaths'

import AuthLayout from './AuthLayout'

const LoginView = React.lazy(() => import('./LoginView/LoginView'))

const authRoutes = [
    {
        element: <AuthLayout />,
        children: [
            {
                path: routerPaths.auth.login,
                element: <LoginView />,
            },
        ],
    },
]

export default authRoutes
