import PersonIcon from '@mui/icons-material/PersonOutlined'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import InputAdornment from '@mui/material/InputAdornment'
import Stack from '@mui/material/Stack'
import { useTranslation } from 'react-i18next'

import FormTextField from '@/components/form/FormTextField'
import SwitchLanguage from '@/components/layout/SwitchLanguage'
import { AuthTokenBodyKeys } from '@/requests/auth/authTokenRequest'

interface IdentifierStepProps {
    isPending: boolean
}

const IdentifierStep = ({ isPending }: IdentifierStepProps) => {
    const { t } = useTranslation('common')

    return (
        <>
            <FormTextField
                data-testid='USERNAME'
                name={AuthTokenBodyKeys.IDENTIFIER}
                label={t('labels.your-email-or-id')}
                autoFocus
                autoComplete={'m2cIdentifierLogin'}
                slotProps={{
                    input: {
                        endAdornment: (
                            <InputAdornment position='end'>
                                <PersonIcon
                                    sx={{
                                        color: 'primary.main',
                                        fontSize: 20,
                                    }}
                                />
                            </InputAdornment>
                        ),
                    },
                }}
            />

            <Button
                fullWidth
                data-testid='SUBMIT'
                variant='contained'
                sx={{ mt: 4 }}
                type={'submit'}
                loading={isPending}
            >
                {t('actions.continue')}
            </Button>

            <Divider
                sx={{
                    mt: 4,
                }}
            />

            <Stack
                pt={2}
                direction='row'
                alignItems='center'
                width='max-content'
                justifyContent='space-between'
            >
                <SwitchLanguage />
            </Stack>
        </>
    )
}

export default IdentifierStep
