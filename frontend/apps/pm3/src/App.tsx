import Typography from '@mui/material/Typography'
import React from 'react'
import { useTranslation } from 'react-i18next'
import {
    createBrowserRouter,
    Navigate,
    Outlet,
    RouterProvider,
    useBlocker,
} from 'react-router'

import ConfirmationDialog from '@/components/dialog/ConfirmationDialog'
import PopsEmergencyDialog from '@/components/dialog/PopsEmergencyDialog'
import PopsEmergencyGroupDialog from '@/components/dialog/PopsEmergencyGroupDialog'
import { DATA_GRID_STATE_STORE } from '@/config/common'
import routerPaths from '@/config/routerPaths'
import { useAppStore } from '@/store/store'
import authRoutes from '@/views/(auth)/authRoutes'
import codeListsRoutes from '@/views/(code-lists)/codeListsRoutes'
import dashboardRoutes from '@/views/(dashboard)/dashboardRoutes'
import permissionsRoutes from '@/views/(permissions)/permissionsRoutes'
import popsRoutes from '@/views/(pops)/popsRoutes'
import superPopsRoutes from '@/views/(super-pops)/superPopsRoutes'
import wapRoutes from '@/views/(wap)/wapRoutes'

const Layout = () => {
    const { t } = useTranslation('common')
    const routingBlocked = useAppStore.use.routingBlocked()
    const setRoutingBlocked = useAppStore.use.setRoutingBlocked()

    // Shared with super-pops and pops
    const popsEmergencyDialog = useAppStore.use.popsEmergencyDialog()
    const closePopsEmergencyDialog = useAppStore.use.closePopsEmergencyDialog()

    const popsEmergencyGroupDialog = useAppStore.use.popsEmergencyGroupDialog()
    const closePopsEmergencyGroupDialog =
        useAppStore.use.closePopsEmergencyGroupDialog()

    const blocker = useBlocker(({ currentLocation, nextLocation }) => {
        return (
            routingBlocked && currentLocation.pathname !== nextLocation.pathname
        )
    })

    const onCancel = () => {
        blocker.reset?.()
    }

    const onContinue = () => {
        setRoutingBlocked(false)
        blocker.proceed?.()
    }

    // If version is not equal reset saved data grid state
    React.useEffect(() => {
        const key = '_pm_version'
        const version = import.meta.env.VITE_VERSION
        const localVersion = localStorage.getItem(key)

        if (version !== localVersion) {
            localStorage.setItem(DATA_GRID_STATE_STORE, '{}')
        }

        localStorage.setItem(key, version)
    }, [])

    return (
        <>
            <Outlet />

            {blocker.state === 'blocked' && (
                <ConfirmationDialog
                    onClose={onCancel}
                    onConfirm={onContinue}
                    confirmLabel={t('actions.continue-without-changes')}
                >
                    <Typography>{t('texts.leave-unsaved-confirm')}</Typography>
                </ConfirmationDialog>
            )}

            {popsEmergencyDialog && (
                <PopsEmergencyDialog onClose={closePopsEmergencyDialog} />
            )}

            {popsEmergencyGroupDialog && (
                <PopsEmergencyGroupDialog
                    onClose={closePopsEmergencyGroupDialog}
                />
            )}
        </>
    )
}

const router = createBrowserRouter([
    {
        element: <Layout />,
        children: [
            ...dashboardRoutes,
            ...codeListsRoutes,
            ...popsRoutes,
            ...superPopsRoutes,
            ...authRoutes,
            ...wapRoutes,
            ...permissionsRoutes,
        ],
    },
    {
        path: '*',
        element: <Navigate to={routerPaths.dashboard.index} />,
    },
])

const App = () => {
    React.useEffect(() => {
        const preloadErrorListener = () => {
            window.location.reload()
        }

        window.addEventListener('vite:preloadError', preloadErrorListener)

        return () => {
            window.removeEventListener(
                'vite:preloadError',
                preloadErrorListener,
            )
        }
    }, [])

    return <RouterProvider router={router} />
}

export default App
