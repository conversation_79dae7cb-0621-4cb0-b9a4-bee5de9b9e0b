const queryKeys = {
    common: {
        changelog: ['changelog'],
        geocoding: ['geocoding'],
        appConfig: ['appConfig'],
    },
    users: {
        index: ['users'],
        current: ['current-user'],
    },
    facilities: {
        index: ['facilities'],
        withSuperPops: ['facilities-with-super-pops'],
        facilitiesByPermission: ['facilities-by-permission'],
    },
    facilityGroups: {
        index: ['facility-groups'],
    },
    currencies: {
        index: ['currencies'],
    },
    currency: (currencyId: number) => ({
        index: ['currency', currencyId],
    }),
    projects: {
        index: ['projects'],
    },
    project: (projectId: number) => ({
        index: ['project', projectId],
    }),
    companies: {
        index: ['companies'],
    },
    company: (companyId: number) => ({
        index: ['company', companyId],
    }),
    facilityGroup: (facilityGroupId: number) => ({
        index: ['facility-group', facilityGroupId],
    }),
    contracts: {
        index: ['contracts'],
    },
    contract: (contractId: number) => ({
        index: ['contract', contractId],
    }),
    units: {
        index: ['units'],
    },
    unit: (unitId: number) => ({
        index: ['unit', unitId],
    }),
    customers: {
        index: ['customers'],
    },
    customer: (customerId: number) => ({
        index: ['customer', customerId],
    }),
    tenants: {
        index: ['tenants'],
    },
    tenant: (tenantId: number) => ({
        index: ['tenant', tenantId],
    }),
    legislations: {
        index: ['legislations'],
    },
    legislation: (legislationId: number) => ({
        index: ['legislation', legislationId],
    }),
    tenantGroups: {
        index: ['tenant-groups'],
    },
    tenantGroup: (tenantGroupId: number) => ({
        index: ['tenant-group', tenantGroupId],
    }),
    facility: (facilityId: number) => ({
        index: ['facility', facilityId],
        tenantGroups: ['facility', facilityId, 'tenant-groups'],
        favoriteEventCategories: [
            'facility',
            facilityId,
            'favorite-event-categories',
        ],
    }),
    pops: {
        index: ['pops'],
        events: ['pops-events'],
        alerts: ['pops-alerts'],
    },
    popsDetail: (popsId: number) => ({
        index: ['popsDetail', popsId],
    }),
    events: {
        categoryTree: ['events-category-tree'],
        categories: ['events-categories'],
        summary: ['events-summary'],
    },
    event: (eventId: number) => ({
        index: ['event', eventId],
    }),
    superPops: {
        index: ['super-pops'],
    },
    superPopsDetail: (superPopsId: number) => ({
        index: ['superPopsDetail', superPopsId],
        events: ['superPopsDetail', superPopsId, 'events'],
    }),
    permissions: {
        index: ['permissions'],
    },
    permissionGroups: {
        index: ['permission-groups'],
    },
    permissionGroup: (groupId: number) => ({
        index: ['permission-group', groupId],
    }),
    permissionTemplates: {
        index: ['permission-templates'],
    },
    permissionTemplate: (templateId: number) => ({
        index: ['permission-template', templateId],
    }),
}

export default queryKeys
