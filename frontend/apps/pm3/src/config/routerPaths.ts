const routerPaths = {
    auth: {
        login: '/login',
    },
    dashboard: {
        index: '/dashboard',
        profile: {
            index: '/profile',
            password: '/profile/change-password',
        },
    },
    superPops: {
        index: '/super-pops',
        create: '/super-pops/create',
        detail: {
            index: '/super-pops/:superPopsId',
            update: '/super-pops/:superPopsId/update',
        },
        event: {
            index: '/super-pops/:superPopsId/facilities/:facilityId/events/:eventId',
        },
    },
    pops: {
        index: '/pops',
        facilities: {
            index: '/pops/facilities',
        },
        facility: {
            index: '/pops/facilities/:facilityId',
            createEvent: '/pops/facilities/:facilityId/create-event',
        },
        event: {
            index: '/pops/facilities/:facilityId/events/:eventId',
            update: '/pops/facilities/:facilityId/events/:eventId/update',
        },
        popsDetail: {
            index: '/pops/facilities/:facilityId/pops-detail/:popsId',
        },
    },
    wap: {
        index: '/wap',
        services: {
            index: '/wap/services',
        },
        service: {
            index: '/wap/services/:serviceId',
            editor: '/wap/services/:serviceId/editor',
        },
    },
    permissions: {
        index: '/permissions',
        groups: {
            index: '/permissions/groups',
            create: '/permissions/groups/create',
        },
        group: {
            index: '/permissions/groups/:groupId',
            update: '/permissions/groups/:groupId/update',
        },
        templates: {
            index: '/permissions/templates',
            create: '/permissions/templates/create',
        },
        template: {
            index: '/permissions/templates/:templateId',
            update: '/permissions/templates/:templateId/update',
        },
    },
    codeLists: {
        index: '/code-lists',
        events: {
            index: '/code-lists/events',
        },
        facilities: {
            index: '/code-lists/facilities',
            create: '/code-lists/facilities/create',
        },
        facility: {
            index: '/code-lists/facilities/:facilityId',
        },
        facilityGroups: {
            index: '/code-lists/facility-groups',
            create: '/code-lists/facility-groups/create',
        },
        facilityGroup: {
            index: '/code-lists/facility-groups/:facilityGroupId',
            update: '/code-lists/facility-groups/:facilityGroupId/update',
        },
        currencies: {
            index: '/code-lists/currencies',
            create: '/code-lists/currencies/create',
        },
        currency: {
            index: '/code-lists/currencies/:currencyId',
            update: '/code-lists/currencies/:currencyId/update',
        },
        units: {
            index: '/code-lists/units',
            create: '/code-lists/units/create',
        },
        unit: {
            index: '/code-lists/units/:unitId',
            update: '/code-lists/units/:unitId/update',
        },
        projects: {
            index: '/code-lists/projects',
            create: '/code-lists/projects/create',
        },
        project: {
            index: '/code-lists/projects/:projectId',
            update: '/code-lists/projects/:projectId/update',
        },
        companies: {
            index: '/code-lists/companies',
            create: '/code-lists/companies/create',
        },
        company: {
            index: '/code-lists/companies/:companyId',
            update: '/code-lists/companies/:companyId/update',
        },
        contracts: {
            index: '/code-lists/contracts',
            create: '/code-lists/contracts/create',
        },
        contract: {
            index: '/code-lists/contracts/:contractId',
            update: '/code-lists/contracts/:contractId/update',
        },
        customers: {
            index: '/code-lists/customers',
            create: '/code-lists/customers/create',
        },
        customer: {
            index: '/code-lists/customers/:customerId',
            update: '/code-lists/customers/:customerId/update',
        },
        tenants: {
            index: '/code-lists/tenants',
            create: '/code-lists/tenants/create',
        },
        tenant: {
            index: '/code-lists/tenants/:tenantId',
            update: '/code-lists/tenants/:tenantId/update',
        },
        legislations: {
            index: '/code-lists/legislations',
            create: '/code-lists/legislations/create',
        },
        legislation: {
            index: '/code-lists/legislations/:legislationId',
            update: '/code-lists/legislations/:legislationId/update',
        },
        tenantGroups: {
            index: '/code-lists/tenant-groups',
            create: '/code-lists/tenant-groups/create',
        },
        tenantGroup: {
            index: '/code-lists/tenant-groups/:tenantGroupId',
            update: '/code-lists/tenant-groups/:tenantGroupId/update',
        },
    },
}

export default routerPaths
