import axios from 'axios'
import toast from 'react-hot-toast'

import i18n from '../i18n/i18n'
import routerPaths from '../routerPaths'
import { onErrorMessage } from './helpers'

export const restClient = (withCredentials = true) => {
    const client = axios.create({
        baseURL: `${import.meta.env.VITE_API_URL}`,
        timeout: 10000,
        headers: {
            'Content-Type': 'application/json',
            'Accept-Language': i18n.language,
        },
        withCredentials,
    })

    client.interceptors.request.use(
        (config) => {
            return config
        },
        (error) => {
            return Promise.reject(error)
        },
    )

    client.interceptors.response.use(
        (response) => response,
        async (error) => {
            const prevRequest = error?.config
            const status = error?.response?.status

            const isAuthPath =
                window.location.pathname === routerPaths.auth.login

            if (
                (status === 401 || status === 403) &&
                !prevRequest?.sent &&
                !isAuthPath
            ) {
                prevRequest.sent = true

                try {
                    await axios.post('/api/v1/auth/token/refresh')
                } catch {
                    window.location.href =
                        routerPaths.auth.login +
                        '?redirect=' +
                        window.location.href
                }

                return client(prevRequest)
            }

            if (status !== 401 && status !== 403) {
                const errorMessage = onErrorMessage(error)
                toast.error(errorMessage)
            }

            return Promise.reject(error)
        },
    )

    return client
}
