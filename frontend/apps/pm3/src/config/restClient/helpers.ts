import axios from 'axios'
import * as _ from 'lodash'

type ApiError = {
    message: string
}

function isApiErrorResponse(res: ApiError): res is ApiError {
    return res && 'message' in res
}

export const onErrorMessage = (error: unknown) => {
    if (!axios.isAxiosError(error)) {
        return 'Unknown error'
    }

    if (!error.response) {
        return error.message
    }

    if (!isApiErrorResponse(error.response.data)) {
        return error.message
    }

    const singleError = _.get(error.response.data, 'errors[0].message')
    return singleError || error.response.data.message
}
