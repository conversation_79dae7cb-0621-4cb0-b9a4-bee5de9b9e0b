import TranslationProvider from '@goodsailors/ui/providers/TranslationProvider'
import { themeOptions } from '@goodsailors/ui/theme/themeOptions'
import { createTheme } from '@mui/material'
import {
    csCZ as csCZRoot,
    deDE as deDERoot,
    enUS as enUSRoot,
} from '@mui/material/locale'
import { ThemeProvider } from '@mui/material/styles'
import {
    csCZ as csCZGrid,
    deDE as deDEGrid,
    enUS as enUSGrid,
} from '@mui/x-data-grid/locales'
import { LocalizationProvider } from '@mui/x-date-pickers-pro'
import { AdapterDayjs } from '@mui/x-date-pickers-pro/AdapterDayjs'
import {
    csCZ as csCZDate,
    deDE as deDEDate,
    enUS as enUSDate,
} from '@mui/x-date-pickers-pro/locales'
import * as _ from 'lodash'
import React from 'react'
import { useTranslation } from 'react-i18next'

import LinkWrapper from '@/components/other/LinkWrapper'
import { LanguageName } from '@/config/i18n/i18n'

interface DesignSystemProviderProps {
    children?: React.ReactNode
}

const DesignSystemProvider = ({ children }: DesignSystemProviderProps) => {
    const { i18n } = useTranslation()

    const locales = React.useMemo(() => {
        const language = i18n.language as LanguageName

        if (language === 'cs') {
            return [csCZRoot, csCZGrid, csCZDate]
        } else if (language === 'de') {
            return [deDERoot, deDEGrid, deDEDate]
        } else {
            return [enUSRoot, enUSGrid, enUSDate]
        }
    }, [i18n.language])

    const theme = React.useMemo(
        () =>
            createTheme(
                _.set(
                    themeOptions,
                    'components.MuiLink.defaultProps.component',
                    LinkWrapper,
                ),
                ...locales,
            ),
        [locales],
    )

    return (
        <LocalizationProvider
            dateAdapter={AdapterDayjs}
            adapterLocale={i18n.language}
        >
            <TranslationProvider i18n={i18n}>
                <ThemeProvider theme={theme}>{children}</ThemeProvider>
            </TranslationProvider>
        </LocalizationProvider>
    )
}

export default DesignSystemProvider
