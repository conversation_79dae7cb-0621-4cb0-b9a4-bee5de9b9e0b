import _ from 'lodash'
import qs from 'qs'

import {
    FilterParams,
    FilterTypeName,
    PaginationParams,
    SearchParams,
    SortParams,
} from '@/requests/params'

const createRequestParams = <
    O extends FilterParams &
        SortParams &
        PaginationParams &
        SearchParams &
        object,
>(
    values: O,
): string => {
    let paramsValues = _.omit(values, [
        'sort',
        'filter',
        'pagination',
        'search',
    ])

    const sort = values.sort
    const search = values.search
    const pagination = values.pagination
    const filter = _.filter(values.filter || [], (item) => {
        return item.value !== undefined && item.value !== null
    })

    if (search) {
        paramsValues = { ...paramsValues, search }
    }

    if (sort && sort.sortBy) {
        paramsValues = { ...paramsValues, ...sort }
    }

    if (pagination) {
        paramsValues = { ...paramsValues, ...pagination }
    }

    if (filter) {
        _.forEach(filter, (filterItem) => {
            const value = _.get(filterItem, 'value')

            if (_.isArray(value) && filterItem) {
                _.set(filterItem, 'value', _.join(value, ','))
            }

            if (typeof value === 'boolean' && filterItem.type === undefined) {
                filterItem.type = FilterTypeName.MATCH
            }
        })

        paramsValues = { ...paramsValues, filter }
    }

    const params = qs.stringify(paramsValues, { encode: false })

    return '?' + params.toString()
}

export default createRequestParams
