import dayjs from 'dayjs'
import * as _ from 'lodash'

import { LanguageName } from '@/config/i18n/i18n'
import { AttachmentResponse } from '@/requests/responses'

export const formatEventCategoryName = <
    C extends { label: string; code: string },
>(
    category: C,
) => {
    return `${category.label} (${category.code})`
}

// TODO: Write better types
export const formatTreeData = <
    I extends { children: I[] },
    R extends { children: R[] },
>(
    data: I[],
    formatTreeItem: (item: I) => R,
): R[] => {
    return data.map((item) => {
        const formattedItem = formatTreeItem(item)

        if (_.size(formattedItem.children) > 0) {
            return {
                ...formattedItem,
                children: formatTreeData<I, R>(
                    (formattedItem.children || []) as unknown as I[],
                    formatTreeItem,
                ),
            }
        }

        return formattedItem
    }) as R[]
}

export const formatDateRange = (startsAt: string, endsAt: string) => {
    const dStartsAt = dayjs(startsAt)
    const dEndsAt = dayjs(endsAt)

    const isSameDay = dStartsAt.isSame(dEndsAt, 'day')

    if (isSameDay) {
        return `${formatDate(dStartsAt, 'date')}, ${formatDate(dStartsAt, 'time')} - ${formatDate(dEndsAt, 'time')}`
    }

    return `${formatDate(dStartsAt)} - ${formatDate(dEndsAt)}`
}

export const formatUserName = <
    U extends { first_name: string; last_name: string },
>(
    user: U | undefined,
) => {
    if (!user) return ''

    return `${user.last_name || ''} ${user.first_name || ''} `
}

export const formatUserInitials = <
    U extends { first_name: string; last_name: string },
>(
    user: U | undefined,
) => {
    if (!user) return ''

    const { first_name, last_name } = user
    const firstInitial = first_name?.charAt(0).toUpperCase()
    const lastInitial = last_name?.charAt(0).toUpperCase()

    return firstInitial + lastInitial
}

export const formatDate = (
    value: string | Date | dayjs.Dayjs | null | undefined,
    target:
        | 'date-starting-day-name'
        | 'date-with-time'
        | 'short-date'
        | 'date'
        | 'time' = 'date-with-time',
) => {
    if (!value) return ''
    const locale = dayjs.locale() as LanguageName

    const map: { [l in LanguageName]: { [t in typeof target]: string } } = {
        cs: {
            'date-with-time': 'DD.MM.YYYY HH:mm',
            'date-starting-day-name': 'dddd DD.MM.YYYY',
            'short-date': 'DD.MM',
            date: 'DD.MM.YYYY',
            time: 'HH:mm',
        },
        en: {
            'date-with-time': 'MM/DD/YYYY h:mm A',
            'date-starting-day-name': 'dddd MM/DD/YYYY',
            'short-date': 'MM/DD',
            date: 'MM/DD/YYYY',
            time: 'h:mm A',
        },
        de: {
            'date-with-time': 'DD.MM.YYYY HH:mm',
            'date-starting-day-name': 'dddd DD.MM.YYYY',
            'short-date': 'DD.MM',
            date: 'DD.MM.YYYY',
            time: 'HH:mm',
        },
    }

    return dayjs(value).format(map[locale][target])
}

export const formatAddress = <
    A extends {
        country?: string
        city?: string
        street?: string
        postal_code?: string
    },
>(
    address: A,
) => {
    const pieces = [
        address.country,
        address.city,
        address.street,
        address.postal_code,
    ]

    return pieces.filter(Boolean).join(', ')
}

export const formatAttachmentUrl = (attachment: AttachmentResponse) => {
    return import.meta.env.VITE_API_URL + '/' + attachment.url
}
