{"name": "@goodsailors/pm3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "env-cmd -x vite --port=\\$PORT --host", "build": "tsc && vite build", "lint": "eslint", "lint:fix": "eslint --fix", "lint:config": "eslint --inspect-config", "prettier:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "preview": "vite preview", "serve": "env-cmd -x serve -s dist --listen=\\$PORT"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@goodsailors/ui": "3.22.1", "@mui/icons-material": "7.0.2", "@mui/material": "7.0.2", "@mui/x-charts": "^8.0.0", "@mui/x-data-grid": "^8.0.0", "@mui/x-data-grid-pro": "8.0.0", "@mui/x-date-pickers-pro": "^8.0.0", "@mui/x-license": "^8.0.0", "@mui/x-tree-view-pro": "^8.1.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.74.4", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/react": "^2.12.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "env-cmd": "^10.1.0", "eventsource": "^3.0.7", "file-saver": "^2.0.5", "formik": "^2.4.6", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "js-file-download": "^0.4.12", "lodash": "^4.17.21", "qs": "^6.14.0", "react": "19.1.0", "react-color": "^2.19.3", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-helmet": "^6.1.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-router": "^7.5.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "use-sound": "^5.0.0", "vite-tsconfig-paths": "^5.1.4", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/qs": "^6.9.18", "@types/react": "19.1.2", "@types/react-color": "^3.0.13", "@types/react-dom": "19.1.2", "@types/react-helmet": "^6.1.11", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.4.1", "dotenv": "^16.5.0", "eslint-plugin-mui-path-imports": "^0.0.15", "eslint-plugin-paths": "^1.1.0", "vite": "^6.3.2"}}