FROM node:20-alpine AS base
WORKDIR /app
RUN npm install -g pnpm serve

FROM base AS build
RUN apk add --no-cache --virtual .build-deps alpine-sdk python3
COPY . /app
ARG APP=pm3
RUN --mount=type=secret,id=npmrc,target=/root/.npmrc pnpm install
ENV APP=$APP

# map build-time variables here
ARG VITE_VERSION
ENV VITE_VERSION=$VITE_VERSION

ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL

ARG VITE_MAP_KEY
ENV VITE_MAP_KEY=$VITE_MAP_KEY

ARG VITE_SSE_TOKEN
ENV VITE_SSE_TOKEN=$VITE_SSE_TOKEN

ARG VITE_SSE_URL
ENV VITE_SSE_URL=$VITE_SSE_URL

ARG VITE_HIDE_CODE_LISTS
ENV VITE_HIDE_CODE_LISTS=$VITE_HIDE_CODE_LISTS

ARG VITE_HIDE_WAP
ENV VITE_HIDE_WAP=$VITE_HIDE_WAP

ARG VITE_HIDE_PERMISSIONS
ENV VITE_HIDE_PERMISSIONS=$VITE_HIDE_PERMISSIONS

ARG VITE_MUI_LICENSE
ENV VITE_MUI_LICENSE=$VITE_MUI_LICENSE

RUN cp apps/$APP/.env.example apps/$APP/.env \
    && pnpm $APP build

FROM base
COPY --from=build /app /app
ARG APP=pm3
ENV APP=$APP
CMD ["pnpm", "pm3", "serve"]