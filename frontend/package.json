{"name": "goodsailors", "version": "1.0.0", "description": "goodsailors monorepo", "type": "module", "scripts": {"pm3": "pnpm --filter @goodsailors/pm3", "lint:config": "eslint --inspect-config"}, "private": true, "keywords": [], "author": "", "devDependencies": {"@eslint/compat": "^1.2.8", "@eslint/js": "^9.25.1", "@types/eslint__js": "^9.14.0", "@types/node": "^22.14.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "prettier": "^3.5.3", "tsx": "^4.19.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}}