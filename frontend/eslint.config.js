import { fixupPluginRules } from '@eslint/compat'
import eslint from '@eslint/js'
import eslintConfigPrettier from 'eslint-config-prettier/flat'
import importPlugin from 'eslint-plugin-import'
import react from 'eslint-plugin-react'
import reactHooks from 'eslint-plugin-react-hooks'
import simpleImportSort from 'eslint-plugin-simple-import-sort'
import globals from 'globals'
import typescript from 'typescript-eslint'

export default [
    {
        files: ['**/*.ts', '**/*.tsx', '**/*.js'],
    },
    eslintConfigPrettier,
    react.configs.flat.recommended,
    eslint.configs.recommended,
    ...typescript.configs.recommended,
    {
        languageOptions: {
            ecmaVersion: 'latest',
            globals: {
                ...globals.browser,
                ...globals.node,
            },
        },
        plugins: {
            import: importPlugin,
            'simple-import-sort': simpleImportSort,
            'react-hooks': fixupPluginRules(reactHooks),
        },
        settings: {
            react: {
                version: 'detect',
            },
        },
        rules: {
            ...reactHooks.configs.recommended.rules,
            'simple-import-sort/imports': 'error',
            'import/newline-after-import': 'error',
            'react/react-in-jsx-scope': 'off',
            'react-hooks/exhaustive-deps': 'error',
            'react/jsx-newline': [
                'error',
                { prevent: true, allowMultilines: true },
            ],
        },
    },
]
