# Pm\Legislation\Controller\LegislationController::getDetailAction
GET /api/v1/legislation/{id}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Legislation\HttpResponse\LegislationResponse
        id: int
        name: string
        active: bool
        currency: { # Pm\Currency\HttpResponse\CurrencyResponse
            id: int
            name: string
            abbr: string
        }
    }
    code: null
    message: null
}
