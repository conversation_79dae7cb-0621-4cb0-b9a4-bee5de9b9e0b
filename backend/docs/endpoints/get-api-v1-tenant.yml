# Pm\Tenant\Controller\TenantController::getListAction
GET /api/v1/tenant

Input Query parameters:
{ # Pm\Tenant\Input\TenantGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Tenant\Validator\TenantSortFields
        id
        name
        phone
        email
        active
        read_only
        facility
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Tenant\HttpResponse\TenantResponse
        id: int
        name: string|null
        phone: string
        email: string|null
        active: bool|null
        account: { # Pm\User\HttpResponse\AccountHttpResponse
            id: int
            name: string
            logo: string|null
            active: bool
        }
        facility: { # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
            id: int
            name: string
            code: string
        }
        read_only: bool
    })
    total_count: int
    code: null
}
