# Pm\Facility\Controller\FacilityController::getAction
# Get Facility by ID
GET /api/v1/facility/{facilityId}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Facility\HttpResponse\FacilityWithPopsHttpResponse
        facility: { # Pm\Facility\HttpResponse\FacilityHttpResponse
            id: int
            permissions: list(string)
            name: string
            code: string
            is_active: bool
            is_pops_alert: bool
            pops_alert_interval: int|null
            created_at: datetime|null
            updated_at: datetime|null
            attachment_resolution: string
            company: { # Pm\Company\HttpResponse\CompanyResponse
                id: int
                name: string
                active: bool
                abbr: string|null
                code: string|null
                cid: string|null
                note: string|null
                country: string|null
                city: string|null
                street: string|null
                postal_code: string|null
                coordinates: string|null
                original_response: {
                    [string]: unknown (mixed)
                }|null
                contact_email: string|null
            }|null
            contract_id: int|null
            contract_name: string|null
            project_id: int|null
            project_name: string|null
            legislation_id: int|null
            legislation_name: string|null
            unit_id: int|null
            street: string|null
            city: string|null
            postal_code: string|null
            country: string|null
            customer_name: string|null
            customer_street: string|null
            customer_city: string|null
            customer_postal_code: string|null
            customer_country: string|null
            read_only: bool
        }
        last_pops: { # Pm\Pops\HttpResponse\PopsHttpResponse
            id: int
            starts_at: datetime
            ends_at: datetime
            is_locked: bool
            facility_id: int
            facility_name: string
            facility_code: string
            is_send_attachment: bool
            last_pops_event: { # Pm\Pops\HttpResponse\PopsEventHttpResponse
                attachments: list({ # Pm\Pops\HttpResponse\PopsEventAttachmentHttpResponse
                    id: int
                    path: string
                    mime: string
                    name: string
                    size: int|null
                    created_at: datetime|null
                    url: string
                    thumbnail_url: string
                })
                id: int
                pops_id: int
                pops: { # Pm\Pops\HttpResponse\PopsHttpResponseSimple
                    id: int
                    starts_at: datetime
                    ends_at: datetime
                    is_locked: bool
                    facility_id: int
                    facility_name: string
                    facility_code: string
                }|null
                facility_id: int
                facility_name: string
                facility_code: string
                description: string
                event_date_time: datetime
                created_at: datetime|null
                inserted_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
                    id: int
                    personal_no: string|null
                    identifier: string
                    first_name: string|null
                    last_name: string|null
                }|null
                event_category: { # Pm\EventCategory\HttpResponse\EventCategoryResponse
                    id: int
                    code: string
                    label: string
                    severity: string
                    is_hidden: bool
                    parent_id: int|null
                }
                attachments_count: int
                attachments_received_count: int
            }|null
            highest_pops_event_priority: string|null
            count_of_critical_events: int
            count_of_medium_events: int
            count_of_internal_events: int
            count_of_low_events: int
        }|null
    }
    code: null
    message: null
}
