# Pm\Tenant\Controller\TenantGroupController::getTenantGroupPaginatedByFacilityAction
GET /api/v1/tenant-group/facility/{facilityId}

Input Query parameters:
{ # Pm\Tenant\Input\TenantGroupGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Tenant\Validator\TenantGroupSortFields
        id
        name
        active
        facility
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list(unknown (mixed))
    total_count: int
    code: null
}
