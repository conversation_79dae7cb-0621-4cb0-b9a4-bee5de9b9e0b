# Pm\Subject\Controller\SubjectController::getListAction
GET /api/v1/subject

Input Query parameters:
{ # Pm\Subject\Input\SubjectGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Subject\Validator\SubjectSortFields
        id
        firstName
        lastName
        gid
        email
        phone
        active
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Subject\HttpResponse\SubjectResponse
        id: int
        first_name: string
        last_name: string
        gid: string|null
        title_before_name: string|null
        title_after_name: string|null
        email: string|null
        phone: string|null
        active: bool
    })
    total_count: int
    code: null
}
