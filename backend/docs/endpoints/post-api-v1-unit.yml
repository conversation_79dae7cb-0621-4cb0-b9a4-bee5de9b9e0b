# Pm\Unit\Controller\UnitController::createUnitAction
POST /api/v1/unit

Input Body:
{ # Pm\Unit\Input\UnitCreateUpdateInput
    name: string
    code: string
    active: bool
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Unit\HttpResponse\UnitResponse
        id: int
        name: string
        code: string
        active: bool
    }
    code: int
    message: string
}
