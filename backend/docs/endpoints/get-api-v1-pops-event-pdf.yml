# Pm\Pops\Controller\PopsEventController::getPdfListAction
GET /api/v1/pops-event-pdf

Input Query parameters:
{ # Pm\Pops\Input\PopsEventGetInput
    accountId: int|null
    popsId: int|null
    facilityId: int|null
    eventDate: datetime|null
    sortBy: enum( # Pm\Pops\Validator\PopsEventSortFields
        id
        created_at
        event_date_time
        description
        inserted_by
        event_category.label
        event_category.type
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    limit: int|null
    offset: int|null
    search: string|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
    hasAttachment: bool|null
}

Output:
unknown (Symfony\Component\HttpFoundation\Response)
