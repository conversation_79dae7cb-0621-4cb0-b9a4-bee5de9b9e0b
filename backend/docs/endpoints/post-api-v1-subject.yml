# Pm\Subject\Controller\SubjectController::createSubjectAction
POST /api/v1/subject

Input Body:
{ # Pm\Subject\Input\SubjectCreateUpdateInput
    firstName: string
    lastName: string
    gid: string|null
    titleBeforeName: string|null
    titleAfterName: string|null
    email: string|null
    phone: string|null
    active: bool
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Subject\HttpResponse\SubjectResponse
        id: int
        first_name: string
        last_name: string
        gid: string|null
        title_before_name: string|null
        title_after_name: string|null
        email: string|null
        phone: string|null
        active: bool
    }
    code: int
    message: string
}
