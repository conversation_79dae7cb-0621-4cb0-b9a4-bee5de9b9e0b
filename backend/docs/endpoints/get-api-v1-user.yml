# Pm\User\Controller\UserController::getDetailOfLoggedUser
GET /api/v1/user

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\User\HttpResponse\UserHttpResponse
        id: int
        personal_no: string|null
        identifier: string
        first_name: string|null
        last_name: string|null
        created_at: datetime|null
        updated_at: datetime|null
        account_id: int
        account: { # Pm\User\HttpResponse\AccountHttpResponse
            id: int
            name: string
            logo: string|null
            active: bool
        }
        read_only: bool
        allowed_permissions: list(string)
        allowed_applications: list(string)
    }
    code: null
    message: null
}
