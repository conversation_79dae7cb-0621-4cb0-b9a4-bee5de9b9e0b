# Pm\Tenant\Controller\TenantController::createTenantAction
POST /api/v1/tenant

Input Body:
{ # Pm\Tenant\Input\TenantCreateUpdateInput
    name: string
    phone: string
    email: string|null
    active: bool|null
    facilityId: int|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Tenant\HttpResponse\TenantResponse
        id: int
        name: string|null
        phone: string
        email: string|null
        active: bool|null
        account: { # Pm\User\HttpResponse\AccountHttpResponse
            id: int
            name: string
            logo: string|null
            active: bool
        }
        facility: { # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
            id: int
            name: string
            code: string
        }
        read_only: bool
    }
    code: int
    message: string
}
