# Pm\Contract\Controller\ContractController::getListAction
GET /api/v1/contract

Input Query parameters:
{ # Pm\Contract\Input\ContractGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Contract\Validator\ContractSortFields
        id
        name
        code
        active
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Contract\HttpResponse\ContractResponse
        id: int
        name: string
        code: string
        active: bool
    })
    total_count: int
    code: null
}
