# Pm\SuperPops\Controller\SuperPopsController::getListXlsAction
GET /api/v1/super-pops/{id}/pops-event-xls

Input Query parameters:
{ # Pm\SuperPops\Input\SuperPopsEventGetInput
    eventDate: datetime|null
    sortBy: enum( # Pm\Pops\Validator\PopsEventSortFields
        id
        created_at
        event_date_time
        description
        inserted_by
        event_category.label
        event_category.type
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    limit: int|null
    offset: int|null
    search: string|null
    filter: {
        [int]: { # Pm\Core\Input\FilterInput
            field: string
            value: unknown (mixed)|null
            type: enum( # Pm\Core\Enum\FilterTypeEnum
                match
                startsWith
                endsWith
                contains
                greaterThan
                between
                lessThan
                in
                notIn
            )
            operator: enum( # Pm\Core\Enum\OperatorTypeEnum
                AND
                OR
            )|null
        }
    }|null
}

Output:
unknown (Yectep\PhpSpreadsheetBundle\PhpSpreadsheet\Response\XlsxResponse)
