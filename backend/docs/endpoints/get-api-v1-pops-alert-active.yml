# Pm\Pops\Controller\PopsAlertController::getActiveAlertsAction
GET /api/v1/pops-alert/active

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Pops\HttpResponse\PopsAlertHttpResponse
        id: int
        pops_id: int
        created_at: datetime|null
        starts_at: datetime
        ends_at: datetime
        facility_id: int
    })
    total_count: int
    code: null
}
