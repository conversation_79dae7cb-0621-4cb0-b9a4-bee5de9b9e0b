# Pm\Account\Controller\AccountController::createAccountAction
POST /api/v1/create-account

Input Body:
{ # Pm\Account\Input\AccountCreateUpdateInput
    name: string
    users: {
        [int]: { # Pm\User\Input\UserCreateUpdateInput
            email: string
            password: string
            role: string
            permissions: {
                [int]: enum( # Pm\Permission\Enum\PermissionEnum
                    PERM_PERAMBULATION_SHOW
                    PERM_PERAMBULATION_PLAN_SHOW
                    PERM_PERAMBULATION_ROUTE_SHOW
                    PERM_PERAMBULATION_CHECKPOINT_SHOW
                    PERM_PERAMBULATION_USER_SHOW
                    PERM_PERAMBULATION_TERMINAL_SHOW
                    PERM_PERAMBULATION_CONDITIONAL_EVENT_SHOW
                    PERM_PERAMBULATION_CONDITIONAL_EVENT_DELETE
                    PERM_PERAMBULATION_REPORT_SHOW
                    PERM_PERAMBULATION_REPORT_DELETE
                    PERM_PERAMBULATION_REPORTING_SHOW
                    PERM_PERAMBULATION_REPORTING_DELETE
                    PERM_PERAMBULATION_FAILURE_SHOW
                    PERM_PERAMBULATION_FAILURE_DELETE
                    PERM_PERAMBULATION_ALARM_SHOW
                    PERM_PERAMBULATION_ALARM_DELETE
                    PERM_PERAMBULATION_GPS_SHOW
                    PERM_PERAMBULATION_TERMINAL_VIDEO_SHOW
                    PERM_PERAMBULATION_TERMINAL_VIDEO_CALL
                    PERM_PERAMBULATION_TERMINAL_VIDEO_CREATE
                    PERM_PERAMBULATION_TERMINAL_VIDEO_DELETE
                    PERM_PERAMBULATION_TERMINAL_FILE_SHOW
                    PERM_PERAMBULATION_TERMINAL_FILE_DELETE
                    PERM_PERAMBULATION_END
                    PERM_PERAMBULATION_CHECKPOINT_ALL_SHOW
                    PERM_PERAMBULATION_CHECKPOINT_ALL_EDIT
                    PERM_PERAMBULATION_CHECKPOINT_ALL_DELETE
                    PERM_PERAMBULATION_TERMINAL_ALL_SHOW
                    PERM_PERAMBULATION_TERMINAL_ALL_EDIT
                    PERM_PERAMBULATION_TERMINAL_ALL_DELETE
                    PERM_COMMON
                    PERM_OBJECTS
                    PERM_OBJECT_GROUP_SELECT
                    PERM_PANIC_RECEIVE
                    PERM_POSITION_SECRET_SHOW
                    PERM_DISABLE_AUTOLOGOUT
                    PERM_DISABLE_CONTROL_PRESENCE
                    PERM_SUBJECT_HIRE
                    PERM_PERIOD_RESTRICTED
                    PERM_IS_CONTACT
                    PERM_M2CDESK_EXCLUDE_GROUP
                    PERM_OBJECT_CREATE
                    PERM_OBJECT_DELETE
                    PERM_OBJECT_EDIT
                    PERM_OBJECT_EDIT_ALL
                    PERM_OBJECT_EDIT_DOCUMENTATION
                    PERM_OBJECT_EDIT_MODE
                    PERM_OBJECT_EDIT_POPS_ACTIVITY
                    PERM_OBJECT_ACTIVATE
                    PERM_OBJECT_SHOW
                    PERM_POPS_CREATE
                    PERM_POPS_EDIT
                    PERM_POPS_SHOW
                    PERM_POPS_EVENT_EDIT
                    PERM_POPS_EVENT_TIME
                    PERM_POPS_TOP_20
                    PERM_POPS_SET_SEND_ATTACHMENT
                    PERM_MULTIPOPS_CREATE
                    PERM_MULTIPOPS_EDIT
                    PERM_MULTIPOPS_SHOW
                    PERM_MULTIPOPS_EVENT_CREATE
                    PERM_MULTIPOPS_EVENT_EDIT
                    PERM_MULTIPOPS_EVENT_SHOW
                    PERM_MULTIPOPS_SET_SEND_ATTACHMENT
                    PERM_ABSENCE_CREATE
                    PERM_ABSENCE_EDIT
                    PERM_ABSENCE_DELETE
                    PERM_ABSENCE_SHOW
                    PERM_ABSENCE_TYPE_CREATE
                    PERM_ABSENCE_TYPE_EDIT
                    PERM_ABSENCE_TYPE_DELETE
                    PERM_ABSENCE_TYPE_SHOW
                    PERM_AGENC_RATE_CREATE
                    PERM_AGENC_RATE_EDIT
                    PERM_AGENC_RATE_DELETE
                    PERM_AGENC_RATE_SHOW
                    PERM_AGENCY_CREATE
                    PERM_AGENCY_EDIT
                    PERM_AGENCY_DELETE
                    PERM_AGENCY_SHOW
                    PERM_AGENCY_SERVICE_SHOW
                    PERM_AGENCY_SERVICE_DETAIL_SHOW
                    PERM_AGENCY_SUBJECT_CREATE
                    PERM_AGENCY_SUBJECT_EDIT
                    PERM_AGENCY_SUBJECT_DELETE
                    PERM_AGENCY_SUBJECT_SHOW
                    PERM_ATTENDANCE_END_CREATE
                    PERM_ATTENDANCE_END_EDIT
                    PERM_ATTENDANCE_END_SHOW
                    PERM_ATTENDANCE_NO_PLAN_SHOW
                    PERM_ATTENDANCE_NOT_APPROVED_SHOW
                    PERM_ATTENDANCE_NOT_APPROVED_EXPORT
                    PERM_ATTENDANCE_RUNNING_EDIT
                    PERM_ATTENDANCE_RUNNING_SHOW
                    PERM_ATTENDANCE_START_CREATE
                    PERM_ATTENDANCE_START_EDIT
                    PERM_ATTENDANCE_START_DELETE
                    PERM_ATTENDANCE_START_CREATE_SERVICE
                    PERM_ATTENDANCE_START_SHOW
                    PERM_ATTENDANCE_START_CHECK_SHOW
                    PERM_ATTENDANCE_SHOW
                    PERM_ATTENDANCE_EDIT
                    PERM_BILLING_DOCUMENT_ATTENDANCE_SHOW
                    PERM_BILLING_DOCUMENT_SERVICE_SHOW
                    PERM_BILLING_DOCUMENT_ANNEX_SHOW
                    PERM_BILLING_DOCUMENT_SUBJECT_SHOW
                    PERM_BILLING_DOCUMENT_ATTENDANCE_SUBJECT_SHOW
                    PERM_BILLING_DOCUMENT_EXPORT
                    PERM_BILLING_DOCUMENT_GROUP_ATTENDANCE_SHOW
                    PERM_BILLING_DOCUMENT_GROUP_SERVICE_SHOW
                    PERM_BILLING_DOCUMENT_ANNEX_GROUP_SHOW
                    PERM_BILLING_DOCUMENT_GROUP_EXPORT
                    PERM_ATTENDANCE_LENGTH_EXPORT
                    PERM_ATTENDANCE_LENGTH_SHOW
                    PERM_CALENDAR_ADMIN
                    PERM_CALENDAR_ADMIN_EDIT
                    PERM_CITY_CREATE
                    PERM_CITY_EDIT
                    PERM_CITY_DELETE
                    PERM_CITY_SHOW
                    PERM_COMPANY_CREATE
                    PERM_COMPANY_EDIT
                    PERM_COMPANY_DELETE
                    PERM_COMPANY_SHOW
                    PERM_COMPANY_READ
                    PERM_COMPANY_WRITE
                    PERM_CONTRACT_CREATE
                    PERM_CONTRACT_EDIT
                    PERM_CONTRACT_DELETE
                    PERM_CONTRACT_SHOW
                    PERM_CONTRACT_READ
                    PERM_CONTRACT_WRITE
                    PERM_CONTRACT_RATE_CREATE
                    PERM_CONTRACT_RATE_EDIT
                    PERM_CONTRACT_RATE_DELETE
                    PERM_CONTRACT_RATE_SHOW
                    PERM_COUNTRY_CREATE
                    PERM_COUNTRY_EDIT
                    PERM_COUNTRY_DELETE
                    PERM_COUNTRY_SHOW
                    PERM_CURRENCY_CREATE
                    PERM_CURRENCY_EDIT
                    PERM_CURRENCY_DELETE
                    PERM_CURRENCY_SHOW
                    PERM_CURRENCY_READ
                    PERM_CURRENCY_WRITE
                    PERM_CUSTOMERS_CREATE
                    PERM_CUSTOMERS_EDIT
                    PERM_CUSTOMERS_DELETE
                    PERM_CUSTOMERS_SHOW
                    PERM_CUSTOMERS_EDIT_PASSWORD
                    PERM_CUSTOMER_READ
                    PERM_CUSTOMER_WRITE
                    PERM_DIMENSION_CREATE
                    PERM_DIMENSION_EDIT
                    PERM_DIMENSION_DELETE
                    PERM_DIMENSION_SHOW
                    PERM_DIMENSION_LEVEL_CREATE
                    PERM_DIMENSION_LEVEL_EDIT
                    PERM_DIMENSION_LEVEL_DELETE
                    PERM_DIMENSION_LEVEL_SHOW
                    PERM_EVENT_CREATE
                    PERM_EVENT_EDIT
                    PERM_EVENT_ACTIVATE
                    PERM_EVENT_HIDE
                    PERM_EVENT_DELETE
                    PERM_EVENT_SHOW
                    PERM_INDIVIDUAL_RATE_CREATE
                    PERM_INDIVIDUAL_RATE_EDIT
                    PERM_INDIVIDUAL_RATE_DELETE
                    PERM_INDIVIDUAL_RATE_SHOW
                    PERM_LOG
                    PERM_LOGIN_LOG_SHOW
                    PERM_LOGIN_LOG_EXPORT
                    PERM_CUSTOMER_LOGIN_LOG_SHOW
                    PERM_CUSTOMER_LOGIN_LOG_EXPORT
                    PERM_LEGISLATION_READ
                    PERM_LEGISLATION_WRITE
                    PERM_LOGS_DATA_SHOW
                    PERM_LOGS_DATA_EXPORT
                    PERM_LOGS_EMERGENCY_SHOW
                    PERM_LOGS_EMERGENCY_EXPORT
                    PERM_LOGS_ERRORS_SHOW
                    PERM_LOGS_ERRORS_EXPORT
                    PERM_LOGS_REPORTING_SHOW
                    PERM_LOGS_REPORTING_EXPORT
                    PERM_LOGS_ALL_SHOW
                    PERM_LOGS_ALL_DELETE
                    PERM_LOGS_ALL_EXPORT
                    PERM_NEWS_PM_CREATE
                    PERM_NEWS_PM_SHOW
                    PERM_NEWS_OPM_CREATE
                    PERM_NEWS_OPM_SHOW
                    PERM_OBJECT_GROUP_CREATE
                    PERM_OBJECT_GROUP_EDIT
                    PERM_OBJECT_GROUP_DELETE
                    PERM_OBJECT_GROUP_SHOW
                    PERM_PANIC_BUTTON
                    PERM_PANIC_BUTTON_TENANT
                    PERM_PERIOD_CREATE
                    PERM_PERIOD_EDIT
                    PERM_PERIOD_ACTIVATE
                    PERM_PERIOD_DELETE
                    PERM_PERIOD_SHOW
                    PERM_PERIOD_COPY_SHOW
                    PERM_PERIOD_COPY_CREATE
                    PERM_PERIOD_COPY_EXPORT
                    PERM_PERIODIC_PAYMENT_CREATE
                    PERM_PERIODIC_PAYMENT_EDIT
                    PERM_PERIODIC_PAYMENT_APPROBATE
                    PERM_PERIODIC_PAYMENT_DELETE
                    PERM_PERIODIC_PAYMENT_UNBLOCK
                    PERM_PERIODIC_PAYMENT_SHOW
                    PERM_PERMISSION_GROUP_ALLSUBJECT
                    PERM_PERMISSION_SUBJECT
                    PERM_PERMISSION_OBJECT
                    PERM_PERSON_CREATE
                    PERM_PERSON_EDIT
                    PERM_PERSON_DELETE
                    PERM_PERSON_PRIVATE_SHOW
                    PERM_PERSON_CUSTOMER_SHOW
                    PERM_PERSON_ADDRESS_SHOW
                    PERM_PERSON_BONUS_SHOW
                    PERM_PERSON_SHOW
                    PERM_SUBJECT_ACCESS_PASSWORD
                    PERM_SUBJECT_ACCESS_PASSWORD_CUSTOMER
                    PERM_PERSONS_RATE_CREATE
                    PERM_PERSONS_RATE_EDIT
                    PERM_PERSONS_RATE_DELETE
                    PERM_PERSONS_RATE_SHOW
                    PERM_POPSLIST_EDIT
                    PERM_POPSLIST_EXPORT
                    PERM_POPSLIST_SHOW
                    PERM_POPS_EVENTS_EDIT
                    PERM_POPS_EVENTS_DELETE
                    PERM_POPS_EVENTS_TRANSLATE
                    PERM_POPS_EVENTS_FOR_CUSTOMER
                    PERM_POPS_EVENTS_EXPORT
                    PERM_POPS_EVENTS_SHOW
                    PERM_MULTIPOPSLIST_STATS_EDIT
                    PERM_MULTIPOPSLIST_STATS_EXPORT
                    PERM_MULTIPOPSLIST_STATS_SHOW
                    PERM_OBJECT_POPS_EDIT
                    PERM_OBJECT_POPS_DELETE
                    PERM_OBJECT_POPS_EXPORT
                    PERM_OBJECT_READ
                    PERM_OBJECT_WRITE
                    PERM_EVENTS_OBJECT_EDIT
                    PERM_EVENTS_OBJECT_DELETE
                    PERM_EVENTS_OBJECT_TRANSLATE
                    PERM_EVENTS_OBJECT_FOR_CUSTOMER
                    PERM_EVENTS_OBJECT_EXPORT
                    PERM_EVENTS_OBJECT_SHOW
                    PERM_MULTIPOPSLIST_EDIT
                    PERM_MULTIPOPSLIST_EXPORT
                    PERM_MULTIPOPSLIST_SHOW
                    PERM_MULTIPOPSLIST_ADD_PAST_EVENT
                    PERM_POSITION_RATE_CREATE
                    PERM_POSITION_RATE_EDIT
                    PERM_POSITION_RATE_DELETE
                    PERM_POSITION_RATE_SHOW
                    PERM_PRODUCT_CREATE
                    PERM_PRODUCT_EDIT
                    PERM_PRODUCT_ACTIVATE
                    PERM_PRODUCT_DELETE
                    PERM_PRODUCT_SHOW
                    PERM_REPORTING_CREATE
                    PERM_REPORTING_EDIT
                    PERM_REPORTING_DELETE
                    PERM_REPORTING_SHOW
                    PERM_REPORTING_TEMPLATE_CREATE
                    PERM_REPORTING_TEMPLATE_EDIT
                    PERM_REPORTING_TEMPLATE_DELETE
                    PERM_REPORTING_TEMPLATE_SHOW
                    PERM_ROLE_CREATE
                    PERM_ROLE_EDIT
                    PERM_ROLE_EDIT_DEPARTMENT
                    PERM_ROLE_SHOW_DEPARTMENT
                    PERM_ROLE_EDIT_MANAGERIAL_ALLOWANCE
                    PERM_ROLE_SHOW_MANAGERIAL_ALLOWANCE
                    PERM_ROLE_ACTIVATE
                    PERM_ROLE_DELETE
                    PERM_ROLE_SHOW
                    PERM_ROLE_GROUP_CREATE
                    PERM_ROLE_GROUP_EDIT
                    PERM_ROLE_GROUP_DELETE
                    PERM_ROLE_GROUP_SHOW
                    PERM_SERVICE_ATTENDANCE_CREATE
                    PERM_SERVICE_ATTENDANCE_EDIT
                    PERM_SERVICE_ATTENDANCE_APPROVE
                    PERM_SERVICE_ATTENDANCE_DELETE
                    PERM_SERVICE_ATTENDANCE_UNBLOCK
                    PERM_SERVICE_ATTENDANCE_SHOW
                    PERM_SERVICE_BILLING_CREATE
                    PERM_SERVICE_BILLING_EDIT
                    PERM_SERVICE_BILLING_APPROVE
                    PERM_SERVICE_BILLING_DELETE
                    PERM_SERVICE_BILLING_UNBLOCK
                    PERM_SERVICE_BILLING_UPDATE_RATES
                    PERM_SERVICE_BILLING_UPDATE_PRODUCTS
                    PERM_SERVICE_BILLING_SHOW_RATE
                    PERM_SERVICE_BILLING_SHOW
                    PERM_SERVICE_EDIT_EDIT
                    PERM_SERVICE_EDIT_SHOW
                    PERM_SERVICE_CREATE_CREATE
                    PERM_SERVICE_CREATE_TRAINING
                    PERM_SERVICE_TEMPLATE_CREATE
                    PERM_SERVICE_TEMPLATE_EDIT
                    PERM_SERVICE_TEMPLATE_DELETE
                    PERM_SERVICE_TEMPLATE_SHOW
                    PERM_SUBJECT_ACCESS_DELETE
                    PERM_SUBJECT_ACCESS_SHOW
                    PERM_SUBJECT_GROUP_CREATE
                    PERM_SUBJECT_GROUP_EDIT
                    PERM_SUBJECT_GROUP_DELETE
                    PERM_SUBJECT_GROUP_SHOW
                    PERM_UNIT_READ
                    PERM_UNIT_WRITE
                    PERM_FLUCTUATION_SHOW
                    PERM_FLUCTUATION_EXPORT
                    PERM_FLUCTUATION_DELETE
                    PERM_CD_PROFIT_SHOW
                    PERM_OBJECT_CD_PROFIT_EXPORT
                    PERM_ATTENDANCE_POPS_CHECK_EXPORT
                    PERM_ATTENDANCE_POPS_CHECK_SHOW
                    PERM_ARCHIVES_SERVICE_SHOW
                    PERM_ARCHIVES_SERVICE_DELETE
                    PERM_ARCHIVES_SERVICE_EXPORT
                    PERM_ARCHIVES_ATTENDANCE_SHOW
                    PERM_ARCHIVES_ATTENDANCE_DELETE
                    PERM_ARCHIVES_ATTENDANCE_EXPORT
                    PERM_ARCHIVES_POPS_SHOW
                    PERM_ARCHIVES_POPS_DELETE
                    PERM_ARCHIVES_POPS_EXPORT
                    PERM_ARCHIVES_POPS_EVENT_NOOCCUR_SHOW
                    PERM_ARCHIVES_MULTIPOPS_SHOW
                    PERM_ARCHIVES_MULTIPOPS_DELETE
                    PERM_ARCHIVES_MULTIPOPS_EXPORT
                    PERM_ARCHIVES_LOG_SHOW
                    PERM_ARCHIVES_LOG_DELETE
                    PERM_ARCHIVES_SETTINGS
                    PERM_MESSAGE_SEND
                    PERM_MESSAGE_SEND_PERM_TEMPLATE
                    PERM_NAVISION_XLS_EXPORT
                    PERM_NAVISION_XLS_OBJECT_EXPORT
                    PERM_PASS_AUTO
                    PERM_SEND_CONTROL_MESSAGE
                    PERM_DISCIPLINARY_MEASURE_CREATE
                    PERM_DISCIPLINARY_MEASURE_DELETE
                    PERM_DISCIPLINARY_MEASURE_EXPORT
                    PERM_DISCIPLINARY_MEASURE_SHOW
                    PERM_TENANT_CREATE
                    PERM_TENANT_EDIT
                    PERM_TENANT_DELETE
                    PERM_TENANT_SHOW
                    PERM_TENANT_READ
                    PERM_TENANT_WRITE
                    PERM_TENANT_GROUP_READ
                    PERM_TENANT_GROUP_WRITE
                    PERM_SUGGESTION_SHOW
                    PERM_SUGGESTION_EDIT
                    PERM_SUGGESTION_GROUP_SHOW
                    PERM_SUGGESTION_GROUP_EDIT
                    PERM_SUGGESTION_NOTIFICATION
                    PERM_SUGGESTION_NOTIFICATION_EMAIL
                    PERM_POOL_SHOW
                    PERM_POOL_RATE_SHOW
                    PERM_DOCUMENTATION_SHOW
                    PERM_DOCUMENTATION_OBJECT_SHOW
                    PERM_DOCUMENTATION_BOZP_SHOW
                    PERM_DOCUMENTATION_EDIT
                    PERM_DOCUMENTATION_DELETE
                    PERM_DOCUMENTATION_OBJECT_DELETE
                    PERM_DOCUMENTATION_BOZP_DELETE
                    PERM_DOCUMENTATION_M2CSOL_SHOW
                    PERM_DOCUMENTATION_M2CSOL_EDIT
                    PERM_DOCUMENTATION_M2CSOL_DELETE
                    PERM_DOCUMENTATION_M2CSOL_OBJECT_DELETE
                    PERM_DOCUMENTATION_M2CSOL_BOZP_DELETE
                    PERM_DOCUMENTATION_INTERNAL_SHOW
                    PERM_DOCUMENTATION_INTERNAL_DELETE
                    PERM_OVERTIME_CONTROL_SHOW
                    PERM_OVERTIME_CONTROL_EXPORT
                    PERM_OVERTIME_CONTROL_SETTING_SHOW
                    PERM_OVERTIME_CONTROL_SETTING_CREATE
                    PERM_OVERTIME_CONTROL_SETTING_EDIT
                    PERM_OVERTIME_CONTROL_SETTING_EDIT_FULL
                    PERM_OVERTIME_CONTROL_SETTING_DELETE
                    PERM_OPM_POPS_SHOW
                    PERM_EVENT_CATEGORY_SHOW
                    PERM_EVENT_CATEGORY_CREATE
                    PERM_EVENT_CATEGORY_EDIT
                    PERM_EVENT_CATEGORY_DELETE
                    PERM_SUPERPOPS_CREATE
                    PERM_SUPERPOPS_SHOW
                    PERM_PROJECT_READ
                    PERM_PROJECT_WRITE
                    PERM_PERMISSION_MANAGE
                    PERM_USER_WRITE
                    PERM_USER_READ
                    PERM_SUBJECT_READ
                    PERM_SUBJECT_WRITE
                    PERM_SUBJECT_GROUP_READ
                    PERM_SUBJECT_GROUP_WRITE
                )
            }
            facilityName: string
            active: bool
        }
    }
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Account\HttpResponse\AccountHttpResponse
        id: int
        name: string
        is_active: bool
    }
    code: int
    message: string
}
