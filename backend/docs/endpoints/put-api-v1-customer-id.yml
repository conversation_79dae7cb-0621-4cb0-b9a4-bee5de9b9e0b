# Pm\Customer\Controller\CustomerController::updateCustomerAction
PUT /api/v1/customer/{id}

Input Body:
{ # Pm\Customer\Input\CustomerCreateUpdateInput
    name: string
    cid: string
    country: string|null
    city: string|null
    street: string|null
    postalCode: string|null
    coordinates: string|null
    facilities: list(int)|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Customer\HttpResponse\CustomerResponse
        id: int
        name: string
        cid: string
        country: string|null
        city: string|null
        street: string|null
        postal_code: string|null
        coordinates: string|null
        facilities: list({ # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
            id: int
            name: string
            code: string
        })
    }
    code: int
    message: string
}
