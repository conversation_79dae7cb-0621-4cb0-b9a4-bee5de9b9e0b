# Pm\Subject\Controller\SubjectGroupController::getDetailAction
GET /api/v1/subject-group/{id}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Subject\HttpResponse\SubjectGroupResponse
        id: int
        name: string
        active: bool|null
        subjects: list({ # Pm\Subject\HttpResponse\SubjectResponse
            id: int
            first_name: string
            last_name: string
            gid: string|null
            title_before_name: string|null
            title_after_name: string|null
            email: string|null
            phone: string|null
            active: bool
        })
    }
    code: null
    message: null
}
