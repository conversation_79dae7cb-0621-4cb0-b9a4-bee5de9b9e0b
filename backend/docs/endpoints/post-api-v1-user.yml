# Pm\User\Controller\UserController::createUserAction
POST /api/v1/user

Input Body:
{ # Pm\User\Input\UserCreateInput
    identifier: string
    password: string
    role: string
    email: string|null
    firstName: string|null
    lastName: string|null
    rc: string|null
    title: string|null
    taxNo: string|null
    position: string|null
    locale: string|null
    cellPhone: string|null
    active: bool
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\User\HttpResponse\UserIntermediateHttpResponse
        id: int
        personal_no: string|null
        identifier: string
        first_name: string|null
        last_name: string|null
        email: string|null
        rc: string|null
        title: string|null
        tax_no: string|null
        position: string|null
        locale: string|null
        cell_phone: string|null
        active: bool
        read_only: bool
    }
    code: int
    message: string
}
