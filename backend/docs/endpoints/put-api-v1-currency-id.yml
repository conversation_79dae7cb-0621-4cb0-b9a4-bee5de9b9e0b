# Pm\Currency\Controller\CurrencyController::updateCurrencyAction
PUT /api/v1/currency/{id}

Input Body:
{ # Pm\Currency\Input\CurrencyCreateUpdateInput
    name: string
    abbr: string
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Currency\HttpResponse\CurrencyResponse
        id: int
        name: string
        abbr: string
    }
    code: int
    message: string
}
