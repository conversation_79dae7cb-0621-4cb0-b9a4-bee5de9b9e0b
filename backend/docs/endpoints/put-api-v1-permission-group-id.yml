# Pm\Permission\Controller\PermissionGroupController::updatePermissionGroupAction
PUT /api/v1/permission-group/{id}

Input Body:
{ # Pm\Permission\Input\PermissionGroupUpdateInput
    name: string|null
    usage: string|null
    users: {
        [int]: int
    }|null
    facilities: {
        [int]: int
    }|null
    permissionTemplates: {
        [int]: int
    }|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Permission\HttpResponse\PermissionGroupHttpResponse
        id: int
        name: string
        usage: string|null
        permission_templates: list({ # Pm\Permission\HttpResponse\PermissionTemplateHttpResponse
            id: int
            name: string
            usage: string
            created_at: datetime|null
            updated_at: datetime|null
            inserted_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
                id: int
                personal_no: string|null
                identifier: string
                first_name: string|null
                last_name: string|null
            }|null
            updated_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
                id: int
                personal_no: string|null
                identifier: string
                first_name: string|null
                last_name: string|null
            }|null
            permissions: list(string)
        })
        users: list({ # Pm\User\HttpResponse\UserBasicHttpResponse
            id: int
            personal_no: string|null
            identifier: string
            first_name: string|null
            last_name: string|null
        })
        facilities: list({ # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
            id: int
            name: string
            code: string
        })
        created_at: datetime|null
        updated_at: datetime|null
        inserted_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
            id: int
            personal_no: string|null
            identifier: string
            first_name: string|null
            last_name: string|null
        }|null
        updated_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
            id: int
            personal_no: string|null
            identifier: string
            first_name: string|null
            last_name: string|null
        }|null
    }
    code: int
    message: string
}
