# Pm\Legislation\Controller\LegislationController::createLegislationAction
POST /api/v1/legislation

Input Body:
{ # Pm\Legislation\Input\LegislationCreateUpdateInput
    name: string
    currencyId: int
    active: bool
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Legislation\HttpResponse\LegislationResponse
        id: int
        name: string
        active: bool
        currency: { # Pm\Currency\HttpResponse\CurrencyResponse
            id: int
            name: string
            abbr: string
        }
    }
    code: int
    message: string
}
