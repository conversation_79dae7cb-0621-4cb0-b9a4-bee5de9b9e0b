# Pm\Company\Controller\CompanyController::createCompanyAction
POST /api/v1/company

Input Body:
{ # Pm\Company\Input\CompanyCreateUpdateInput
    name: string
    active: bool
    abbr: string
    code: string
    cid: string
    note: string|null
    country: string|null
    city: string|null
    street: string|null
    postalCode: string|null
    coordinates: string|null
    originalResponse: {
        [string]: unknown (mixed)
    }|null
    contactEmail: string|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Company\HttpResponse\CompanyResponse
        id: int
        name: string
        active: bool
        abbr: string|null
        code: string|null
        cid: string|null
        note: string|null
        country: string|null
        city: string|null
        street: string|null
        postal_code: string|null
        coordinates: string|null
        original_response: {
            [string]: unknown (mixed)
        }|null
        contact_email: string|null
    }
    code: int
    message: string
}
