# Pm\Auth\Controller\AuthController::postInitAction
POST /api/v1/auth/init

Input Body:
{ # Pm\Auth\Input\AuthTokenInit
    identifier: string # minLength: 4, maxLength: 255
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Auth\HttpResponse\AuthInitResponse
        type: string
        roles: list(string)
        endpoint: string
    }
    code: null
    message: null
}
