# Pm\Contract\Controller\ContractController::createContractAction
POST /api/v1/contract

Input Body:
{ # Pm\Contract\Input\ContractCreateUpdateInput
    name: string
    code: string
    active: bool
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Contract\HttpResponse\ContractResponse
        id: int
        name: string
        code: string
        active: bool
    }
    code: int
    message: string
}
