# Pm\Pops\Controller\PopsEventController::getListAction
GET /api/v1/pops-event

Input Query parameters:
{ # Pm\Pops\Input\PopsEventGetInput
    accountId: int|null
    popsId: int|null
    facilityId: int|null
    eventDate: datetime|null
    sortBy: enum( # Pm\Pops\Validator\PopsEventSortFields
        id
        created_at
        event_date_time
        description
        inserted_by
        event_category.label
        event_category.type
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    limit: int|null
    offset: int|null
    search: string|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
    hasAttachment: bool|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Pops\HttpResponse\PopsEventHttpResponse
        attachments: list({ # Pm\Pops\HttpResponse\PopsEventAttachmentHttpResponse
            id: int
            path: string
            mime: string
            name: string
            size: int|null
            created_at: datetime|null
            url: string
            thumbnail_url: string
        })
        id: int
        pops_id: int
        pops: { # Pm\Pops\HttpResponse\PopsHttpResponseSimple
            id: int
            starts_at: datetime
            ends_at: datetime
            is_locked: bool
            facility_id: int
            facility_name: string
            facility_code: string
        }|null
        facility_id: int
        facility_name: string
        facility_code: string
        description: string
        event_date_time: datetime
        created_at: datetime|null
        inserted_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
            id: int
            personal_no: string|null
            identifier: string
            first_name: string|null
            last_name: string|null
        }|null
        event_category: { # Pm\EventCategory\HttpResponse\EventCategoryResponse
            id: int
            code: string
            label: string
            severity: string
            is_hidden: bool
            parent_id: int|null
        }
        attachments_count: int
        attachments_received_count: int
    })
    total_count: int
    code: null
}
