# Pm\Pops\Controller\PopsEventController::getAction
GET /api/v1/pops-event/{id}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Pops\HttpResponse\PopsEventHttpResponse
        attachments: list({ # Pm\Pops\HttpResponse\PopsEventAttachmentHttpResponse
            id: int
            path: string
            mime: string
            name: string
            size: int|null
            created_at: datetime|null
            url: string
            thumbnail_url: string
        })
        id: int
        pops_id: int
        pops: { # Pm\Pops\HttpResponse\PopsHttpResponseSimple
            id: int
            starts_at: datetime
            ends_at: datetime
            is_locked: bool
            facility_id: int
            facility_name: string
            facility_code: string
        }|null
        facility_id: int
        facility_name: string
        facility_code: string
        description: string
        event_date_time: datetime
        created_at: datetime|null
        inserted_by: { # Pm\User\HttpResponse\UserBasicHttpResponse
            id: int
            personal_no: string|null
            identifier: string
            first_name: string|null
            last_name: string|null
        }|null
        event_category: { # Pm\EventCategory\HttpResponse\EventCategoryResponse
            id: int
            code: string
            label: string
            severity: string
            is_hidden: bool
            parent_id: int|null
        }
        attachments_count: int
        attachments_received_count: int
    }
    code: null
    message: null
}
