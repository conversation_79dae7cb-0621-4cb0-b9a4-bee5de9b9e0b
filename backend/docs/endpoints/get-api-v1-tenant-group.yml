# Pm\Tenant\Controller\TenantGroupController::getListAction
GET /api/v1/tenant-group

Input Query parameters:
{ # Pm\Tenant\Input\TenantGroupGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Tenant\Validator\TenantGroupSortFields
        id
        name
        active
        facility
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Tenant\HttpResponse\TenantGroupResponse
        id: int
        name: string
        active: bool|null
        facility_id: int|null
        read_only: bool
        tenants: list({ # Pm\Tenant\HttpResponse\TenantResponse
            id: int
            name: string|null
            phone: string
            email: string|null
            active: bool|null
            account: { # Pm\User\HttpResponse\AccountHttpResponse
                id: int
                name: string
                logo: string|null
                active: bool
            }
            facility: { # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
                id: int
                name: string
                code: string
            }
            read_only: bool
        })
    })
    total_count: int
    code: null
}
