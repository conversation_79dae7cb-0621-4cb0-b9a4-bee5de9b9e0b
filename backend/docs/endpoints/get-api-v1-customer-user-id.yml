# Pm\Customer\Controller\CustomerUserController::getDetailAction
GET /api/v1/customer-user/{id}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Customer\HttpResponse\CustomerUserResponse
        id: int
        customer_id: int
        customer: { # Pm\Customer\HttpResponse\CustomerSimpleHttpResponse
            id: int
            name: string
            cid: string
            country: string|null
            city: string|null
            street: string|null
            postal_code: string|null
            coordinates: string|null
        }
        user_id: int
        user: { # Pm\User\HttpResponse\UserSimpleHttpResponse
            id: int
            personal_no: string|null
            identifier: string
            first_name: string|null
            last_name: string|null
            email: string|null
            read_only: bool
        }
    }
    code: null
    message: null
}
