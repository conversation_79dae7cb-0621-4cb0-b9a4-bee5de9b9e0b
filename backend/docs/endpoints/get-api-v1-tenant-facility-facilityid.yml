# Pm\Tenant\Controller\TenantController::getTenantPaginatedByFacilityAction
GET /api/v1/tenant/facility/{facilityId}

Input Query parameters:
{ # Pm\Tenant\Input\TenantGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Tenant\Validator\TenantSortFields
        id
        name
        phone
        email
        active
        read_only
        facility
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list(unknown (mixed))
    total_count: int
    code: null
}
