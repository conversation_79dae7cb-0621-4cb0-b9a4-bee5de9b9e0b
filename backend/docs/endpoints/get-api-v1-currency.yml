# Pm\Currency\Controller\CurrencyController::getListAction
GET /api/v1/currency

Input Query parameters:
{ # Pm\Currency\Input\CurrencyGetInput
    search: string|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Currency\Validator\CurrencySortFields
        id
        name
        abbr
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Currency\HttpResponse\CurrencyResponse
        id: int
        name: string
        abbr: string
    })
    total_count: int
    code: null
}
