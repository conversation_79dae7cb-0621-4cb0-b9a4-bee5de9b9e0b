# Pm\Permission\Controller\PermissionController::getListAction
# Get list of permissions
GET /api/v1/permission

Input Query parameters:
{ # Pm\Permission\Input\PermissionGetInput
    search: string|null
    filter: list({ # Pm\Core\Input\FilterInput
        field: string
        value: unknown (mixed)|null
        type: enum( # Pm\Core\Enum\FilterTypeEnum
            match
            startsWith
            endsWith
            contains
            greaterThan
            between
            lessThan
            in
            notIn
        )
        operator: enum( # Pm\Core\Enum\OperatorTypeEnum
            AND
            OR
        )|null
    })|null
    limit: int|null
    offset: int|null
    sortBy: enum( # Pm\Permission\Validator\PermissionSortFields
        code
        application
    )|null
    sortMethod: enum( # Pm\Core\Enum\Sort
        ASC
        DESC
    )|null
}

Output:
{ # Pm\Core\Controller\SuccessCountableOutput
    status: int
    list: list({ # Pm\Permission\HttpResponse\PermissionHttpResponse
        code: string
        name: string
        description: string|null
        application: { # Pm\Permission\HttpResponse\PermissionApplicationHttpResponse
            code: string
            name: string
        }
    })
    total_count: int
    code: null
}
