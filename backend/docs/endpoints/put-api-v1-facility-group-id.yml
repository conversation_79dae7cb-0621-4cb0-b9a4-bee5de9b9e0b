# Pm\Facility\Controller\FacilityGroupController::updateFacilityGroupAction
PUT /api/v1/facility-group/{id}

Input Body:
{ # Pm\Facility\Input\FacilityGroupUpdateInput
    name: string|null
    facilities: {
        [int]: int
    }|null
    usage: string|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Facility\HttpResponse\FacilityGroupHttpResponse
        id: int
        name: string
        usage: string|null
        created_at: datetime|null
        updated_at: datetime|null
        account: { # Pm\User\HttpResponse\AccountHttpResponse
            id: int
            name: string
            logo: string|null
            active: bool
        }
        read_only: bool
        facilities: list({ # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
            id: int
            name: string
            code: string
        })
    }
    code: int
    message: string
}
