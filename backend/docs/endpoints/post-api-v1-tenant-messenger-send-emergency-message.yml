# Pm\Connector\Controller\TenantMessengerController::sendEmergencyMessageAction
POST /api/v1/tenant-messenger/send-emergency-message

Input Body:
{ # Pm\Connector\Input\EmergencyMessageInput
    message: string
    tenantIds: list(int)
    tenantGroupIds: list(int)
    facilityId: int|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: unknown (Pm\Connector\HttpResponse\EmergencyMessagesOutput)
    code: null
    message: null
}
