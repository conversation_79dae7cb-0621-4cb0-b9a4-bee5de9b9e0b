# Pm\Tenant\Controller\TenantGroupController::updateTenantGroupAction
PUT /api/v1/tenant-group/{id}

Input Body:
{ # Pm\Tenant\Input\TenantGroupCreateUpdateInput
    name: string
    active: bool|null
    tenants: list(int)|null
}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: { # Pm\Tenant\HttpResponse\TenantGroupResponse
        id: int
        name: string
        active: bool|null
        facility_id: int|null
        read_only: bool
        tenants: list({ # Pm\Tenant\HttpResponse\TenantResponse
            id: int
            name: string|null
            phone: string
            email: string|null
            active: bool|null
            account: { # Pm\User\HttpResponse\AccountHttpResponse
                id: int
                name: string
                logo: string|null
                active: bool
            }
            facility: { # Pm\Facility\HttpResponse\FacilitySimpleHttpResponse
                id: int
                name: string
                code: string
            }
            read_only: bool
        })
    }
    code: int
    message: string
}
