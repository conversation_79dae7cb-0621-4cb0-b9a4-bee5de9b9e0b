# Pm\Facility\Controller\FacilityController::getListFacilitiesByPermissionAction
# Get list of facilities filtered by permission
GET /api/v1/facilities-by-permission/{permission}

Output:
{ # Pm\Core\Controller\SuccessOutput
    status: int
    data: list({ # Pm\Facility\HttpResponse\FacilityHttpResponse
        id: int
        permissions: list(string)
        name: string
        code: string
        is_active: bool
        is_pops_alert: bool
        pops_alert_interval: int|null
        created_at: datetime|null
        updated_at: datetime|null
        attachment_resolution: string
        company: { # Pm\Company\HttpResponse\CompanyResponse
            id: int
            name: string
            active: bool
            abbr: string|null
            code: string|null
            cid: string|null
            note: string|null
            country: string|null
            city: string|null
            street: string|null
            postal_code: string|null
            coordinates: string|null
            original_response: {
                [string]: unknown (mixed)
            }|null
            contact_email: string|null
        }|null
        contract_id: int|null
        contract_name: string|null
        project_id: int|null
        project_name: string|null
        legislation_id: int|null
        legislation_name: string|null
        unit_id: int|null
        street: string|null
        city: string|null
        postal_code: string|null
        country: string|null
        customer_name: string|null
        customer_street: string|null
        customer_city: string|null
        customer_postal_code: string|null
        customer_country: string|null
        read_only: bool
    })
    code: null
    message: null
}
