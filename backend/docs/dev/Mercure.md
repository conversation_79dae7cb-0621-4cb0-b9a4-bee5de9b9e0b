# Mercure Hub

## Setup
**Env vars:**\
MERCURE_URL=http://localhost:4000/.well-known/mercure \
The private URL of the Mercure hub, used to publishing events.

MERCURE_PUBLIC_URL=https://localhost:4001/.well-known/mercure \
The public URL of the Mercure hub, used for subscribing events.

MERCURE_JWT_SECRET=xxxx\
The secret used to sign the JWTs

**Docker:**\
Base docker service `mercure` is configured in `docker-compose.yml`.\
The Development configuration in `docker-compose.override.yaml` enables `Testing debug tool`

## Publishing events
On backend we use `Pm\Core\SSE\Publisher` to publish any event.\
Every events category has to be published in separate topic, e.g. any `Book` in topic `books`

## Subscribing events
We currently do not subscribe to any topics on backend.

## Testing debug tool
Go to [UI url](http://localhost:4001/.well-known/mercure/ui/#)

1. In settings part fill in your local `HUB Url` (e.g. http://localhost:4001/.well-known/mercure) and \
encoded JWT token from .env file (MERCURE_JWT_TOKEN) to field `JWT`. If you need to change your secret, generate new token by clicking \
on "Create token", you will be redirected to `jwt.io`
2. In Subscribe part edit `Topics to get updates for` (you can just put `"*"` to subscribe all topics)
3. Now subscribing and publishing on debug tool should work. Try to subscribe any topic and when any event will be published in this topic,\
you can see it immediately under the Subscribe part.

## Documentation:
- https://symfony.com/doc/current/mercure.html
- https://mercure.rocks/docs
