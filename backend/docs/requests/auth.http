## Create a new authentication token

POST http://localhost:8080/api/v1/auth/token
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "pAsSw0rD"
}

###

## Validate authentication token

GET http://localhost:8080/api/v1/auth/token/validate
Content-Type: application/json
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***********************************************************************************************************************.UPNhBcTWNNuwQgc9s2NYj2plEf3OaULhWLhillqz8BKSy_LN9u2Af4ew7m1ht_VNxvIBMoHpAD5LUpxAuh2duhT2WZjmjdas9l5EOtvxBrstAiFDjqZCU_BmD8fERYQfaPuT4Z3Fm_mOmCDbm_lhqy3V6yJGY0yMIh93Vi7ti1hpkRjaW304gMfCZMvnCEiHfiu-pAkj8hZ2ZJfp0UdIIuKiRX3ry3dQj_TcyuegaU0r_nge5-3b-xfLZ-efcjkrFltl5fcpW5_ELFw3JIJtzPHXmj7WrPYEy2NzsLTXUVnG9KJcOcNBJ1_W38E7h8xAWrjH2ZRkN1m6oxaMzVrttQ

###
