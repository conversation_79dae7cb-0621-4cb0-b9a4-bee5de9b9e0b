services:
  db:
    container_name: pm-db
    image: "postgres:15-alpine"
    restart: always
    environment:
      - POSTGRES_DB=pm
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - ./.data/db-local:/var/lib/postgresql/data
    ports:
      - '55432:5432'

  db-test:
    container_name: pm-db-test
    image: "postgres:15-alpine"
    restart: always
    environment:
      - POSTGRES_DB=pm-test
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - ./.data/db-local-test:/var/lib/postgresql/data
    ports:
      - '55433:5432'

  webserver:
    build:
      context: .
      dockerfile: docker/nginx/Dockerfile
    container_name: pm-webserver
    working_dir: /application
    volumes:
      - .:/application
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites/:/etc/nginx/sites-available
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d
    ports:
      - "8080:80"
    links:
      - backend
    depends_on:
      - backend
    restart: always

  backend:
    env_file:
      - ./.env.local
    build:
      context: .
      dockerfile: docker/php-fpm/Dockerfile
      args:
        APP_ENV: ${APP_ENV:-prod}
    container_name: pm-php-fpm
    working_dir: /application
    volumes:
      - .:/application
      - ./docker/php-fpm/php-ini-overrides.prod.ini:/etc/php/8.2/fpm/conf.d/99-overrides.ini
      - ./docker/php-fpm/php-ini-overrides.prod.ini:/usr/local/etc/php/conf.d/99-overrides.ini
    extra_hosts:
      - "host.docker.internal:host-gateway"
    expose:
      - 9000
    depends_on:
      - mercure

  rabbit:
    image: rabbitmq:3-management
    container_name: pm-rabbit
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_DEFAULT_VHOST}
    ports:
      - "5672:5672"
      - "15672:15672"

  redis:
    image: "redis:latest"
    restart: always
    container_name: pm-redis
    ports:
      - 6379:6379

  mercure:
    image: dunglas/mercure
    restart: unless-stopped
    container_name: pm-mercure
    environment:
      SERVER_NAME: ':4000'
      JWT_KEY: ${MERCURE_PUBLISHER_JWT_KEY}
      DEMO: 0
      ALLOW_ANONYMOUS: 0
      MERCURE_PUBLISHER_JWT_KEY: ${MERCURE_PUBLISHER_JWT_KEY}
      MERCURE_SUBSCRIBER_JWT_KEY: ${MERCURE_SUBSCRIBER_JWT_KEY}
      PUBLISH_ALLOWED_ORIGINS: '*'
      CORS_ALLOWED_ORIGINS: '*'
      DEBUG: 0
      MERCURE_EXTRA_DIRECTIVES: |
        cors_origins *
    ports:
      - '4001:4000'
    volumes:
      - ./.data/mercure:/data
      - ./docker/mercure/config:/config


networks:
  default:
    driver: bridge
    name: pm-network
