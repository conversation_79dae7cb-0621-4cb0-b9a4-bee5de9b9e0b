<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250520072849 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER INDEX idx_f287fd8b979b1ad6 RENAME TO IDX_105994B2979B1AD6');
        $this->addSql('ALTER INDEX idx_f287fd8b2576e0fd RENAME TO IDX_105994B22576E0FD');
        $this->addSql('ALTER INDEX idx_f287fd8b166d1f9c RENAME TO IDX_105994B2166D1F9C');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6956883F5E237E06 ON currency (name)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6956883F4D4901 ON currency (abbr)');
        $this->addSql('ALTER INDEX permission_group_uniq RENAME TO UNIQ_BB4729B65E237E06');
        $this->addSql('ALTER INDEX uniq_8f6c6eac5e237e06 RENAME TO UNIQ_DCBB0C535E237E06');
        $this->addSql('ALTER INDEX uniq_8f6c6eac77153098 RENAME TO UNIQ_DCBB0C5377153098');
        $this->addSql('ALTER TABLE tenant DROP CONSTRAINT fk_99ab231ca7014910');
        $this->addSql('ALTER TABLE tenant DROP CONSTRAINT fk_99ab231cdff2bbb0');
        $this->addSql('DROP INDEX idx_4e59c462dff2bbb0');
        $this->addSql('ALTER TABLE tenant ADD active BOOLEAN DEFAULT true NOT NULL');
        $this->addSql('ALTER TABLE tenant DROP tenant_group_id');
        $this->addSql('ALTER TABLE tenant ALTER facility_id SET NOT NULL');
        $this->addSql('ALTER TABLE tenant ALTER phone DROP NOT NULL');
        $this->addSql('ALTER TABLE tenant_group DROP CONSTRAINT fk_7d252216a7014910');
        $this->addSql('ALTER TABLE tenant_group DROP CONSTRAINT fk_7d2522169b6b5fba');
        $this->addSql('ALTER TABLE tenant_group ADD active BOOLEAN DEFAULT NULL');
        $this->addSql('ALTER TABLE tenant_group ALTER facility_id SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER INDEX IDX_105994B2979B1AD6 RENAME TO idx_f287fd8b979b1ad6');
        $this->addSql('ALTER INDEX IDX_105994B22576E0FD RENAME TO idx_f287fd8b2576e0fd');
        $this->addSql('ALTER INDEX IDX_105994B2166D1F9C RENAME TO idx_f287fd8b166d1f9c');
        $this->addSql('DROP INDEX UNIQ_6956883F5E237E06');
        $this->addSql('DROP INDEX UNIQ_6956883F4D4901');
        $this->addSql('ALTER INDEX UNIQ_BB4729B65E237E06 RENAME TO permission_group_uniq');
        $this->addSql('ALTER INDEX UNIQ_DCBB0C535E237E06 RENAME TO uniq_8f6c6eac5e237e06');
        $this->addSql('ALTER INDEX UNIQ_DCBB0C5377153098 RENAME TO uniq_8f6c6eac77153098');
        $this->addSql('ALTER TABLE tenant ADD CONSTRAINT fk_99ab231ca7014910 FOREIGN KEY (facility_id) REFERENCES facility (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant ADD CONSTRAINT fk_99ab231cdff2bbb0 FOREIGN KEY (tenant_group_id) REFERENCES tenant_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_4e59c462dff2bbb0 ON tenant (tenant_group_id)');
        $this->addSql('ALTER TABLE tenant DROP active');
        $this->addSql('ALTER TABLE tenant ADD tenant_group_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE tenant ALTER facility_id DROP NOT NULL');
        $this->addSql('ALTER TABLE tenant ALTER phone SET NOT NULL');
        $this->addSql('ALTER TABLE tenant_group ADD CONSTRAINT fk_7d252216a7014910 FOREIGN KEY (facility_id) REFERENCES facility (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant_group ADD CONSTRAINT fk_7d2522169b6b5fba FOREIGN KEY (parent_id) REFERENCES tenant_group (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant_group DROP active');
        $this->addSql('ALTER TABLE tenant_group ALTER facility_id DROP NOT NULL');
    }

}
