<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250602110822 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE customer_user_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE customer_user (id INT NOT NULL, customer_id INT NOT NULL, user_id INT NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D902723E9395C3F3 ON customer_user (customer_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D902723EA76ED395 ON customer_user (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX customer_user_unique ON customer_user (customer_id, user_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_user ADD CONSTRAINT FK_D902723E9395C3F3 FOREIGN KEY (customer_id) REFERENCES customer (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_user ADD CONSTRAINT FK_D902723EA76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP SEQUENCE customer_user_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_user DROP CONSTRAINT FK_D902723E9395C3F3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE customer_user DROP CONSTRAINT FK_D902723EA76ED395
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE customer_user
        SQL);
    }

}
