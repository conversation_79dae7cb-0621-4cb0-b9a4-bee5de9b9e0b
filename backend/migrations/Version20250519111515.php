<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250519111515 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER INDEX uniq_legislation_name RENAME TO UNIQ_1CF18AE35E237E06;');
        $this->addSql('ALTER INDEX uniq_legislation_code RENAME TO UNIQ_1CF18AE377153098;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER INDEX uniq_1cf18ae377153098 RENAME TO uniq_legislation_code;');
        $this->addSql('ALTER INDEX uniq_1cf18ae35e237e06 RENAME TO uniq_legislation_name;');
    }

}
