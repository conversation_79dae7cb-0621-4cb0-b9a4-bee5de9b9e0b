<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250519073046 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql("
            INSERT INTO permission (code, application, description)
            VALUES
                ('PERM_OBJECT_READ', 'code-list', 'PERM_OBJECT_READ'),
                ('PERM_OBJECT_WRITE', 'code-list', 'PERM_OBJECT_WRITE')
            ON CONFLICT (code) DO NOTHING
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("
            DELETE FROM permission
            WHERE code IN ('PERM_OBJECT_READ', 'PERM_OBJECT_WRITE')
        ");
    }

}
