<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250527110726 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE facility ADD legislation_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE facility ADD CONSTRAINT FK_105994B2C633199E FOREIGN KEY (legislation_id) REFERENCES legislation (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_105994B2C633199E ON facility (legislation_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE facility DROP CONSTRAINT FK_105994B2C633199E
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_105994B2C633199E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE facility DROP legislation_id
        SQL);
    }

}
