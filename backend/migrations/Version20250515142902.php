<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250515142902 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE SEQUENCE legislation_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE legislation (id INT NOT NULL, name VARCHAR(255) NOT NULL,currency_id INT DEFAULT NULL, code VARCHAR(50) NOT NULL, active BOOLEAN NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_LEGISLATION_NAME ON legislation (name)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_LEGISLATION_CODE ON legislation (code)');
        $this->addSql('CREATE INDEX IDX_1CF18AE338248176 ON legislation (currency_id)');
        $this->addSql('ALTER TABLE legislation ADD CONSTRAINT FK_1CF18AE338248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql("
            INSERT INTO permission (code, application, description)
            VALUES
                ('PERM_LEGISLATION_READ', 'code-list', 'PERM_LEGISLATION_READ'),
                ('PERM_LEGISLATION_WRITE', 'code-list', 'PERM_LEGISLATION_WRITE')
            ON CONFLICT (code) DO NOTHING
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE legislation_id_seq CASCADE');
        $this->addSql('ALTER TABLE legislation DROP CONSTRAINT FK_1CF18AE338248176');
        $this->addSql('DROP TABLE legislation');
        $this->addSql("
            DELETE FROM permission
            WHERE code IN ('PERM_LEGISLATION_READ', 'PERM_LEGISLATION_WRITE')
        ");
    }

}
