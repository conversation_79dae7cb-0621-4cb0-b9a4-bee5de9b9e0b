<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250602064848 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE facility ADD unit_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE facility ADD CONSTRAINT FK_105994B2F8BD700D FOREIGN KEY (unit_id) REFERENCES unit (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_105994B2F8BD700D ON facility (unit_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX facility_unit_contract_unique ON facility (unit_id, contract_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE facility DROP CONSTRAINT FK_105994B2F8BD700D
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_105994B2F8BD700D
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX facility_unit_contract_unique
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE facility DROP unit_id
        SQL);
    }

}
