<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250416121938 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql("
            INSERT INTO permission (code, application, description) VALUES
            ('PERM_SUPERPOPS_SHOW', 'super-pops', 'From migration'),
            ('PERM_SUPERPOPS_LOCK', 'super-pops', 'From migration'),
            ('PERM_SUPERPOPS_CREATE', 'super-pops', 'From migration'),
            ('PERM_SUPERPOPS_EDIT', 'super-pops', 'From migration')
            ON CONFLICT (code) DO NOTHING
        ");
    }

    public function down(Schema $schema): void
    {
        // Remove permissions manually added to template through UI
        $this->addSql("
        DELETE FROM permission_template_permission WHERE permission IN (
            'PERM_SUPERPOPS_SHOW',
            'PERM_SUPERPOPS_LOCK',
            'PERM_SUPERPOPS_CREATE',
            'PERM_SUPERPOPS_EDIT'
        )
    ");

        $this->addSql("
        DELETE FROM permission WHERE code IN (
            'PERM_SUPERPOPS_SHOW',
            'PERM_SUPERPOPS_LOCK',
            'PERM_SUPERPOPS_CREATE',
            'PERM_SUPERPOPS_EDIT'
        )
    ");
    }

}
