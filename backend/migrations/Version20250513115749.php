<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250513115749 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE SEQUENCE IF NOT EXISTS tenant_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE IF NOT EXISTS tenant_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE IF NOT EXISTS tenant (id INT NOT NULL, facility_id INT NOT NULL, account_id INT NOT NULL, name VARCHAR(255) NOT NULL, phone VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, active BOOLEAN DEFAULT true NOT NULL, original_db_id VARCHAR(50) DEFAULT NULL, migrated_completely BOOLEAN DEFAULT false NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX IF NOT EXISTS UNIQ_4E59C4626D0FE5C8 ON tenant (original_db_id)');
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_4E59C462A7014910 ON tenant (facility_id)');
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_4E59C4629B6B5FBA ON tenant (account_id)');
        $this->addSql('CREATE TABLE IF NOT EXISTS tenant_group (id INT NOT NULL, facility_id INT NOT NULL, account_id INT NOT NULL, name VARCHAR(255) NOT NULL, active BOOLEAN DEFAULT NULL, original_db_id VARCHAR(50) DEFAULT NULL, migrated_completely BOOLEAN DEFAULT false NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX IF NOT EXISTS UNIQ_7250A8F16D0FE5C8 ON tenant_group (original_db_id)');
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_7250A8F1A7014910 ON tenant_group (facility_id)');
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_7250A8F19B6B5FBA ON tenant_group (account_id)');
        $this->addSql('CREATE TABLE IF NOT EXISTS tenant_group_tenant_m2n (tenant_group_id INT NOT NULL, tenant_id INT NOT NULL, PRIMARY KEY(tenant_group_id, tenant_id))');
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_4565DE41DFF2BBB0 ON tenant_group_tenant_m2n (tenant_group_id)');
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_4565DE419033212A ON tenant_group_tenant_m2n (tenant_id)');
        $this->addSql('ALTER TABLE tenant DROP CONSTRAINT IF EXISTS FK_4E59C462A7014910');
        $this->addSql('ALTER TABLE tenant ADD CONSTRAINT FK_4E59C462A7014910 FOREIGN KEY (facility_id) REFERENCES facility (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant DROP CONSTRAINT IF EXISTS FK_4E59C4629B6B5FBA');
        $this->addSql('ALTER TABLE tenant ADD CONSTRAINT FK_4E59C4629B6B5FBA FOREIGN KEY (account_id) REFERENCES account (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant_group DROP CONSTRAINT IF EXISTS FK_7250A8F1A7014910');
        $this->addSql('ALTER TABLE tenant_group ADD CONSTRAINT FK_7250A8F1A7014910 FOREIGN KEY (facility_id) REFERENCES facility (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant_group DROP CONSTRAINT IF EXISTS FK_7250A8F19B6B5FBA');
        $this->addSql('ALTER TABLE tenant_group ADD CONSTRAINT FK_7250A8F19B6B5FBA FOREIGN KEY (account_id) REFERENCES account (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant_group_tenant_m2n DROP CONSTRAINT IF EXISTS FK_4565DE41DFF2BBB0');
        $this->addSql('ALTER TABLE tenant_group_tenant_m2n ADD CONSTRAINT FK_4565DE41DFF2BBB0 FOREIGN KEY (tenant_group_id) REFERENCES tenant_group (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tenant_group_tenant_m2n DROP CONSTRAINT IF EXISTS FK_4565DE419033212A');
        $this->addSql('ALTER TABLE tenant_group_tenant_m2n ADD CONSTRAINT FK_4565DE419033212A FOREIGN KEY (tenant_id) REFERENCES tenant (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE IF EXISTS tenant_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE IF EXISTS tenant_group_id_seq CASCADE');
        $this->addSql('ALTER TABLE tenant DROP CONSTRAINT FK_4E59C462A7014910');
        $this->addSql('ALTER TABLE tenant DROP CONSTRAINT FK_4E59C4629B6B5FBA');
        $this->addSql('ALTER TABLE tenant_group DROP CONSTRAINT FK_7250A8F1A7014910');
        $this->addSql('ALTER TABLE tenant_group DROP CONSTRAINT FK_7250A8F19B6B5FBA');
        $this->addSql('ALTER TABLE tenant_group_tenant_m2n DROP CONSTRAINT FK_4565DE41DFF2BBB0');
        $this->addSql('ALTER TABLE tenant_group_tenant_m2n DROP CONSTRAINT FK_4565DE419033212A');
        $this->addSql('DROP TABLE tenant');
        $this->addSql('DROP TABLE tenant_group');
        $this->addSql('DROP TABLE tenant_group_tenant_m2n');
    }

}
