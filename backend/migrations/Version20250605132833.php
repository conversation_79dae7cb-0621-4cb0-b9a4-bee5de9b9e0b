<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250605132833 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE subject_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE subject_group_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE subject (id INT NOT NULL, first_name VARCHAR(255) NOT NULL, last_name VA<PERSON>HA<PERSON>(255) NOT NULL, gid VARCHAR(255) DEFAULT NULL, title_before_name VARCHAR(255) DEFAULT NULL, title_after_name VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, phone VARCHAR(255) DEFAULT NULL, active BOOLEAN DEFAULT true NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE subject_group (id INT NOT NULL, name VARCHAR(255) NOT NULL, active BOOLEAN DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE subject_group_subject_m2n (subject_group_id INT NOT NULL, subject_id INT NOT NULL, PRIMARY KEY(subject_group_id, subject_id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6E361338C629992F ON subject_group_subject_m2n (subject_group_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6E36133823EDC87 ON subject_group_subject_m2n (subject_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE subject_group_subject_m2n ADD CONSTRAINT FK_6E361338C629992F FOREIGN KEY (subject_group_id) REFERENCES subject_group (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE subject_group_subject_m2n ADD CONSTRAINT FK_6E36133823EDC87 FOREIGN KEY (subject_id) REFERENCES subject (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);

        $this->addSql("
            INSERT INTO permission (code, application)
            VALUES
                ('PERM_SUBJECT_READ', 'code-list'),
                ('PERM_SUBJECT_WRITE', 'code-list'),
                ('PERM_SUBJECT_GROUP_READ', 'code-list'),
                ('PERM_SUBJECT_GROUP_WRITE', 'code-list')
            ON CONFLICT (code) DO NOTHING
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP SEQUENCE subject_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            DROP SEQUENCE subject_group_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE subject_group_subject_m2n DROP CONSTRAINT FK_6E361338C629992F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE subject_group_subject_m2n DROP CONSTRAINT FK_6E36133823EDC87
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE subject
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE subject_group
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE subject_group_subject_m2n
        SQL);
        $this->addSql("
            DELETE FROM permission
            WHERE code IN ('PERM_SUBJECT_READ', 'PERM_SUBJECT_WRITE', 'PERM_SUBJECT_GROUP_READ', 'PERM_SUBJECT_GROUP_WRITE')
        ");
    }

}
