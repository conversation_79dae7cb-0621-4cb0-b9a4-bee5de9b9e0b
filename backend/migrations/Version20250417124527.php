<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250417124527 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX favorite_event_category_unique_fk');
        $this->addSql('CREATE UNIQUE INDEX favorite_event_category_unique_fk ON favorite_event_category (facility_id, event_category_id, account_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX favorite_event_category_unique_fk');
        $this->addSql('CREATE UNIQUE INDEX favorite_event_category_unique_fk ON favorite_event_category (facility_id, event_category_id)');
    }

}
