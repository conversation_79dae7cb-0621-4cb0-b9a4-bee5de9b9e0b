<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250514170000 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        // Drop department column
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS department');
    }

    public function down(Schema $schema): void
    {
        // Add back department column
        $this->addSql('ALTER TABLE facility ADD COLUMN department VARCHAR(255) DEFAULT NULL');
    }

}
