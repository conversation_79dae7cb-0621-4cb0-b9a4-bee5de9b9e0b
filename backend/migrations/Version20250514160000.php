<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250514160000 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        // Drop removed columns
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS documentation_bozp');
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS documentation_object');
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS object_space');
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS track_pops_activity');
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS pops_activity_interval');
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS max_attachment_width');
        $this->addSql('ALTER TABLE facility DROP COLUMN IF EXISTS max_attachment_height');
    }

    public function down(Schema $schema): void
    {
        // Add back removed columns
        $this->addSql('ALTER TABLE facility ADD COLUMN documentation_bozp VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD COLUMN documentation_object VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD COLUMN object_space BOOLEAN NOT NULL DEFAULT FALSE');
        $this->addSql('ALTER TABLE facility ADD COLUMN track_pops_activity BOOLEAN NOT NULL DEFAULT FALSE');
        $this->addSql('ALTER TABLE facility ADD COLUMN pops_activity_interval INT DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD COLUMN max_attachment_width INT DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD COLUMN max_attachment_height INT DEFAULT NULL');
    }

}
