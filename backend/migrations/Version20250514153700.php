<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250514153700 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        // Add company relationship
        $this->addSql('ALTER TABLE facility ADD company_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD CONSTRAINT FK_F287FD8B979B1AD6 FOREIGN KEY (company_id) REFERENCES company (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F287FD8B979B1AD6 ON facility (company_id)');

        // Add contract relationship
        $this->addSql('ALTER TABLE facility ADD contract_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD CONSTRAINT FK_F287FD8B2576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F287FD8B2576E0FD ON facility (contract_id)');

        // Add project relationship
        $this->addSql('ALTER TABLE facility ADD project_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD CONSTRAINT FK_F287FD8B166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_F287FD8B166D1F9C ON facility (project_id)');

        // Add department field
        $this->addSql('ALTER TABLE facility ADD department VARCHAR(255) DEFAULT NULL');

        // Add address fields
        $this->addSql('ALTER TABLE facility ADD street VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD city VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD postal_code VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD country VARCHAR(50) DEFAULT NULL');

        // Add customer fields
        $this->addSql('ALTER TABLE facility ADD customer_name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD customer_street VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD customer_city VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD customer_postal_code VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE facility ADD customer_country VARCHAR(50) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Drop customer fields
        $this->addSql('ALTER TABLE facility DROP customer_name');
        $this->addSql('ALTER TABLE facility DROP customer_street');
        $this->addSql('ALTER TABLE facility DROP customer_city');
        $this->addSql('ALTER TABLE facility DROP customer_postal_code');
        $this->addSql('ALTER TABLE facility DROP customer_country');

        // Drop address fields
        $this->addSql('ALTER TABLE facility DROP street');
        $this->addSql('ALTER TABLE facility DROP city');
        $this->addSql('ALTER TABLE facility DROP postal_code');
        $this->addSql('ALTER TABLE facility DROP country');

        // Drop department field
        $this->addSql('ALTER TABLE facility DROP department');

        // Drop project relationship
        $this->addSql('ALTER TABLE facility DROP CONSTRAINT FK_F287FD8B166D1F9C');
        $this->addSql('DROP INDEX IDX_F287FD8B166D1F9C');
        $this->addSql('ALTER TABLE facility DROP project_id');

        // Drop contract relationship
        $this->addSql('ALTER TABLE facility DROP CONSTRAINT FK_F287FD8B2576E0FD');
        $this->addSql('DROP INDEX IDX_F287FD8B2576E0FD');
        $this->addSql('ALTER TABLE facility DROP contract_id');

        // Drop company relationship
        $this->addSql('ALTER TABLE facility DROP CONSTRAINT FK_F287FD8B979B1AD6');
        $this->addSql('DROP INDEX IDX_F287FD8B979B1AD6');
        $this->addSql('ALTER TABLE facility DROP company_id');
    }

}
