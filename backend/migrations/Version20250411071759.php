<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250411071759 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE permission SET application = 'permissions' WHERE code LIKE 'PERM_PERMISSION_%'");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("UPDATE permission SET application = 'code-list'  WHERE code LIKE 'PERM_PERMISSION_%'");
    }

}
