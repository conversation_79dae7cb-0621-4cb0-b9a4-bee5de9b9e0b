<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250410091535 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE SEQUENCE favorite_event_category_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE favorite_event_category (id INT NOT NULL, facility_id INT NOT NULL, event_category_id INT NOT NULL, account_id INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, original_db_id VARCHAR(50) DEFAULT NULL, migrated_completely BOOLEAN DEFAULT false NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_4CCB09436D0FE5C8 ON favorite_event_category (original_db_id)');
        $this->addSql('CREATE INDEX IDX_4CCB0943A7014910 ON favorite_event_category (facility_id)');
        $this->addSql('CREATE INDEX IDX_4CCB0943B9CF4E62 ON favorite_event_category (event_category_id)');
        $this->addSql('CREATE INDEX IDX_4CCB09439B6B5FBA ON favorite_event_category (account_id)');
        $this->addSql('CREATE UNIQUE INDEX favorite_event_category_unique_fk ON favorite_event_category (facility_id, event_category_id)');
        $this->addSql('COMMENT ON COLUMN favorite_event_category.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN favorite_event_category.updated_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE favorite_event_category ADD CONSTRAINT FK_4CCB0943A7014910 FOREIGN KEY (facility_id) REFERENCES facility (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE favorite_event_category ADD CONSTRAINT FK_4CCB0943B9CF4E62 FOREIGN KEY (event_category_id) REFERENCES event_category (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE favorite_event_category ADD CONSTRAINT FK_4CCB09439B6B5FBA FOREIGN KEY (account_id) REFERENCES account (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE favorite_event_category_id_seq CASCADE');
        $this->addSql('ALTER TABLE favorite_event_category DROP CONSTRAINT FK_4CCB0943A7014910');
        $this->addSql('ALTER TABLE favorite_event_category DROP CONSTRAINT FK_4CCB0943B9CF4E62');
        $this->addSql('ALTER TABLE favorite_event_category DROP CONSTRAINT FK_4CCB09439B6B5FBA');
        $this->addSql('DROP TABLE favorite_event_category');
    }

}
