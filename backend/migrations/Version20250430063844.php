<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250430063844 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE SEQUENCE contract_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE contract (id INT NOT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(50) NOT NULL, active BOOLEAN NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_E98F28595E237E06 ON contract (name)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_E98F285977153098 ON contract (code)');
        $this->addSql("
            INSERT INTO permission (code, application, description)
            VALUES
                ('PERM_CONTRACT_READ', 'code-list', 'PERM_CONTRACT_READ'),
                ('PERM_CONTRACT_WRITE', 'code-list', 'PERM_CONTRACT_WRITE')
            ON CONFLICT (code) DO NOTHING
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE contract_id_seq CASCADE');
        $this->addSql('DROP TABLE contract');
        $this->addSql("
            DELETE FROM permission
            WHERE code IN ('PERM_CONTRACT_READ', 'PERM_CONTRACT_WRITE')
        ");
    }

}
