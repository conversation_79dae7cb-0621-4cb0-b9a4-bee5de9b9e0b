<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250428122930 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
        WITH DuplicateNames AS (
            SELECT id, name, ROW_NUMBER() OVER (PARTITION BY name ORDER BY id) AS row_num
            FROM permission_group
        )
        UPDATE permission_group
        SET name = CONCAT(permission_group.name, '_', DuplicateNames.row_num)
        FROM DuplicateNames
        WHERE permission_group.id = DuplicateNames.id
        AND DuplicateNames.row_num > 1;
        SQL,);

        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX permission_group_uniq ON permission_group (name);
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX permission_group_uniq;
        SQL);
    }

}
