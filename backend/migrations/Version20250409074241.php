<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250409074241 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
        WITH DuplicateNames AS (
            SELECT id, name, ROW_NUMBER() OVER (PARTITION BY name ORDER BY id) AS row_num
            FROM permission_template
        )
        UPDATE permission_template
        SET name = CONCAT(permission_template.name, '_', DuplicateNames.row_num)
        FROM DuplicateNames
        WHERE permission_template.id = DuplicateNames.id
        AND DuplicateNames.row_num > 1;
    SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_6258CA355E237E06 ON permission_template (name)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_6258CA355E237E06 ON permission_template
        SQL);
    }

}
