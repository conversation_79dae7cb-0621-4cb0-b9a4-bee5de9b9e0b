<?php

declare(strict_types = 1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250423091152 extends AbstractMigration
{

    public function up(Schema $schema): void
    {
        $accounts = $this->connection->fetchAllAssociative('SELECT id FROM account');
        foreach ($accounts as $account) {
            $accountId = (int) $account['id'];
            $internalCategoryId = $this->connection->fetchOne('SELECT id FROM event_category WHERE account_id = ? AND code = ?', [$accountId, '239']);
            $userId = (int) $this->connection->fetchOne('SELECT id FROM "user" ORDER BY id ASC LIMIT 1');
            if ($internalCategoryId !== false && (int) $internalCategoryId > 0) {
                $id = (int) $this->connection->fetchOne('SELECT nextval(\'event_category_id_seq\')');
                $this->addSql(
                    'INSERT INTO event_category (
                                id, parent_category_id, account_id,
                                label, code, is_active,
                                created_at, updated_at, original_db_id,
                                migrated_completely, inserted_by,
                                updated_by, is_hidden, severity
                            ) VALUES (
                                ?, ?, ?,
                                ?, ?, true,
                                now(), now(), null,
                                true, ?, ?,
                                true, ?
                            )',
                    [
                        $id,
                        (int) $internalCategoryId,
                        $accountId,
                        'Kontrola pozornosti provedena úspěšně',
                        '1000',
                        $userId,
                        $userId,
                        'NONE',
                    ],
                );

                $this->addSql(
                    'INSERT INTO ext_translations (
                                    locale, object_class, field, foreign_key, content
                                ) VALUES (
                                    ?, ?, ?, ?, ?
                                ) ON CONFLICT (locale, object_class, field, foreign_key) DO NOTHING',
                    [
                        'cs',
                        'Pm\EventCategory\Entity\EventCategory',
                        'label',
                        $id,
                        'Kontrola pozornosti provedena úspěšně',
                    ],
                );
            }

        }
    }

    public function down(Schema $schema): void
    {
    }

}
