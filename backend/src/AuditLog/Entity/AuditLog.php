<?php

declare(strict_types = 1);

namespace Pm\AuditLog\Entity;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Index;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Pm\AuditLog\Enum\AuditLogAction;
use Pm\AuditLog\Enum\AuditLogActionType;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Exception\LogicException;
use Pm\User\Entity\User;

#[Table(name: 'audit_log')]
#[Index(columns: ['action'])]
#[Index(columns: ['entity_class', 'entity_id'])]
#[Index(columns: ['created_at'])]
#[Entity]
class AuditLog extends BaseEntity
{

    #[Column(type: AuditLogActionType::NAME, length: 32, nullable: false)]
    private ?AuditLogAction $action = null;

    #[Column(name: 'entity_class', length: 100, nullable: true)]
    private ?string $entityClass = null;

    #[Column(name: 'entity_id', type: Types::INTEGER, nullable: true)]
    private ?int $entityId = null;

    /**
     * @var array<string, mixed>|null
     */
    #[Column(type: Types::JSON, nullable: true)]
    private ?array $data = null;

    #[ManyToOne(targetEntity: User::class)]
    #[JoinColumn(name: 'created_by', nullable: true)]
    private ?User $createdBy = null;

    #[Column]
    protected ?DateTimeImmutable $createdAt = null;

    public function getId(): int
    {
        if ($this->id === null) {
            throw new LogicException('Cannot get ID when it\'s null.'); // TODO: Should be removed because of Doctrine issue
        }

        return $this->id;
    }

    public function hasId(): bool
    {
        return $this->id !== null;
    }

    public function getStringId(): string
    {
        return (string) $this->id;
    }

    public function getAction(): ?AuditLogAction
    {
        return $this->action;
    }

    public function setAction(AuditLogAction $action): void
    {
        $this->action = $action;
    }

    public function getEntityClass(): ?string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): void
    {
        $this->entityClass = $entityClass;
    }

    public function getEntityId(): ?int
    {
        return $this->entityId;
    }

    public function setEntityId(?int $entityId): void
    {
        $this->entityId = $entityId;
    }

    /** @return array<string, mixed>|null */
    public function getData(): ?array
    {
        return $this->data;
    }

    /**
     * @param array<string,mixed>|null $data
     */
    public function setData(?array $data): void
    {
        $this->data = $data;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): void
    {
        $this->createdBy = $createdBy;
    }

    public function getCreatedAt(): ?DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

}
