<?php

declare(strict_types = 1);

namespace Pm\AuditLog\Repository;

use Doctrine\ORM\EntityManager;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Pm\AuditLog\Entity\AuditLog;
use Pm\User\Entity\User;
use Pm\User\Repository\UserRepository;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\User\UserInterface;
use function gmdate;

class AuditLogRepository
{

    public function __construct(
        private readonly EntityManager $entityManager,
        private readonly Security $security,
        private readonly UserRepository $userRepository,
    )
    {
    }

    /**
     * @throws JsonException
     */
    public function saveLog(AuditLog $entity): void
    {
        $conn = $this->entityManager->getConnection();
        $createdAt = ($entity->getCreatedAt() !== null) ? $entity->getCreatedAt()->format('Y-m-d H:i:s') : gmdate('Y-m-d H:i:s');

        $params = [
            'action' => $entity->getAction()?->value,
            'entity_class' => $entity->getEntityClass(),
            'entity_id' => $entity->getEntityId() ?? null,
            'data' => Json::encode($entity->getData()),
            'created_at' => $createdAt,
        ];

        if ($entity->getCreatedBy() === null) {
            $entity->setCreatedBy($this->getUser($this->security->getUser()));
        }

        if ($entity->getCreatedBy() !== null) {
            $params['created_by'] = $entity->getCreatedBy()->getId();
        }

        $conn->executeQuery('INSERT INTO audit_log (id, action, entity_class, entity_id, data, created_at' . (isset($params['created_by']) ? ', created_by' : '') . ') values (nextval(\'audit_log_id_seq\'), :action, :entity_class , :entity_id, :data, :created_at' . (isset($params['created_by']) ? ', :created_by' : '') . ')', $params);
    }

    public function getUser(?UserInterface $user): ?User
    {
        if ($user !== null) {
            return $this->userRepository->findByIdentifier($user->getUserIdentifier());
        }

        return null;
    }

}
