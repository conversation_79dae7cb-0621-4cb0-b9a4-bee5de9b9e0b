<?php

declare(strict_types = 1);

namespace Pm\AuditLog\Service;

use BackedEnum;
use DateTimeImmutable;
use DateTimeInterface;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Types\Type;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\MappingException;
use Doctrine\ORM\PersistentCollection;
use Pm\AuditLog\Entity\AuditLog;
use Pm\AuditLog\Enum\AuditLogAction;
use Pm\AuditLog\Repository\AuditLogRepository;
use Pm\Core\Entity\BaseEntity;
use Psr\Log\LoggerInterface;
use ReflectionClass;
use Symfony\Bundle\SecurityBundle\Security;
use function base64_encode;
use function count;
use function is_object;
use function is_resource;
use function is_string;
use function method_exists;
use function number_format;
use function rewind;
use function str_replace;
use function stream_get_contents;

class AuditLogService
{

    /**
     * @var array<int, mixed>
     */
    private array $changes = [];

    public function __construct(
        private readonly AuditLogConfiguration $auditConfiguration,
        private readonly Security $security,
        private readonly AuditLogRepository $auditLogRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerInterface $logger,
    )
    {
    }

    public function resetChangeset(): void
    {
        $this->changes = [];
    }

    public function collectScheduledInsertions(): void
    {
        $uow = $this->entityManager->getUnitOfWork();

        foreach ($uow->getScheduledEntityInsertions() as $entity) {
            if ($this->auditConfiguration->isAudited($entity)) {
                $this->changes[] = [
                    'action' => AuditLogAction::INSERT,
                    'entity' => $entity,
                    'data' => [],
                ];
            }
        }
    }

    public function collectScheduledUpdates(): void
    {
        $uow = $this->entityManager->getUnitOfWork();
        foreach ($uow->getScheduledEntityUpdates() as $entity) {
            if ($this->auditConfiguration->isAudited($entity)) {
                $changeSet = $uow->getEntityChangeSet($entity);
                $diff = $this->diff($entity, $changeSet);
                $this->changes[] = [
                    'action' => AuditLogAction::UPDATE,
                    'entity' => $entity,
                    'data' => $diff,
                ];
            }
        }
    }

    public function collectScheduledDeletions(): void
    {
        $uow = $this->entityManager->getUnitOfWork();
        foreach ($uow->getScheduledEntityDeletions() as $entity) {
            if ($this->auditConfiguration->isAudited($entity)) {
                $uow->initializeObject($entity);
                $id = null;
                if ($entity instanceof BaseEntity) {
                    $id = $entity->getId();
                }
                $changes = ['old' => [], 'new' => []];
                $entityClassName = $entity::class;
                foreach ((array) $entity as $fieldName => $value) {
                    $realFieldName = str_replace(["\x00*\x00", "\x00{$entityClassName}\x00"], '', $fieldName);
                    if (is_object($value)) {
                        if (method_exists($value, '__toString')) {
                            $realValue = (string) $value;
                        } elseif ($value instanceof DateTimeInterface) {
                            $realValue = $value->format('r');
                        } elseif ($value instanceof PersistentCollection) {
                            continue;
                        } elseif ($value instanceof BackedEnum) {
                            $realValue = $value->value;
                        } elseif ((new ReflectionClass($value))->getShortName() === 'LazyObjectState') {
                            continue;
                        } else {
                            $realValue = $value::class . '#' . $this->id($value);
                        }
                    } else {
                        $realValue = $value;
                    }
                    $changes['old'][$realFieldName] = $realValue;
                    $changes['new'][$realFieldName] = null;
                }
                $this->changes[] = [
                    'action' => AuditLogAction::DELETE,
                    'entity' => $entity,
                    'id' => $id,
                    'data' => $changes,
                ];
            }
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    public function collectScheduledCollectionUpdates(): void
    {
        $uow = $this->entityManager->getUnitOfWork();
        foreach ($uow->getScheduledCollectionUpdates() as $collection) {
            if ($this->auditConfiguration->isAudited($collection->getOwner())) {
                foreach ($collection->getInsertDiff() as $entity) {
                    $this->collectionDiff($entity, $collection, AuditLogAction::ASSOCIATE);
                }

                foreach ($collection->getDeleteDiff() as $entity) {
                    $this->collectionDiff($entity, $collection, AuditLogAction::DISSOCIATE);
                }
            }
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    public function collectScheduledCollectionDeletions(): void
    {
        $uow = $this->entityManager->getUnitOfWork();

        foreach ($uow->getScheduledCollectionDeletions() as $collection) {
            if ($this->auditConfiguration->isAudited($collection->getOwner())) {
                foreach ($collection->toArray() as $entity) {
                    $this->collectionDiff($entity, $collection, AuditLogAction::DISSOCIATE);
                }
            }
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function collectionDiff(mixed $entity, PersistentCollection $collection, AuditLogAction $type): void
    {
        if ($this->auditConfiguration->isAudited($entity)) {
            $diff = [
                'source' => $this->summarize($collection->getOwner()),
                'target' => $this->summarize($entity),
            ];
            $mapping = $collection->getMapping();
            if (isset($mapping['joinTable']['name'])) {
                $diff['table'] = $mapping['joinTable']['name'];
            }
            $this->changes[] = [
                'action' => $type,
                'entity' => $collection->getOwner(),
                'data' => $diff,
            ];
        }
    }

    /**
     * @param array<string, array{mixed, mixed}|PersistentCollection> $ch
     * @return array{old: array{}, new: array{}}|array{old: non-empty-array<string, mixed>, new: non-empty-array<string, mixed>}
     *
     * @throws Exception
     * @throws MappingException
     */
    public function diff(object $entity, array $ch): array
    {
        $meta = $this->entityManager->getClassMetadata($entity::class);
        $diff = ['old' => [], 'new' => []];
        foreach ($ch as $fieldName => [$old, $new]) {
            $o = null;
            $n = null;

            $isAudited = $this->auditConfiguration->isAuditedField($entity, $fieldName);

            if (!isset($meta->embeddedClasses[$fieldName]) && $meta->hasField($fieldName)) {
                $mapping = $meta->fieldMappings[$fieldName];
                $type = Type::getType($mapping['type']);
                $o = $this->value($type, $old, $mapping);
                $n = $this->value($type, $new, $mapping);
            } elseif ($meta->hasAssociation($fieldName) && $meta->isSingleValuedAssociation($fieldName)) {
                $o = $this->summarize($old);
                $n = $this->summarize($new);
            }

            if ($o !== $n) {
                if ($isAudited) {
                    $diff['new'][$fieldName] = $n;
                    $diff['old'][$fieldName] = $o;
                } else {
                    $this->logger->info('AuditManager property changed but not auditable: ' . $fieldName . ': ' . $n . ' => ' . $o);
                }
            }
        }

        return $diff;
    }

    /**
     * @return array<string, mixed>|null
     *
     * @throws Exception
     * @throws MappingException
     */
    public function summarize(?object $entity = null, mixed $id = null): ?array
    {
        if ($entity === null) {
            return null;
        }
        $this->entityManager->getUnitOfWork()->initializeObject($entity); // ensure that proxies are initialized
        $meta = $this->entityManager->getClassMetadata($entity::class);
        $pkName = $meta->getSingleIdentifierFieldName();

        $pkValue = $id ?? $this->id($entity);
        if (method_exists($entity, '__toString')) {
            $label = (string) $entity;
        } else {
            $label = $entity::class . '#' . $pkValue;
        }

        return [
            'label' => $label,
            'class' => $meta->name,
            'table' => $meta->getTableName(),
            $pkName => $pkValue,
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    public function id(object $entity): mixed
    {
        $meta = $this->entityManager->getClassMetadata($entity::class);
        $pk = $meta->getSingleIdentifierFieldName();
        if (isset($meta->fieldMappings[$pk])) {
            $type = Type::getType($meta->fieldMappings[$pk]['type']);
            return $this->value($type, $meta->getReflectionProperty($pk)->getValue($entity));
        }
        // Primary key is not part of fieldMapping
        // @see https://github.com/DamienHarper/DoctrineAuditBundle/issues/40
        // @see https://www.doctrine-project.org/projects/doctrine-orm/en/latest/tutorials/composite-primary-keys.html#identity-through-foreign-entities
        // We try to get it from associationMapping (will throw a MappingException if not available)
        $targetEntity = $meta->getReflectionProperty($pk)->getValue($entity);
        $mapping = $meta->getAssociationMapping($pk);
        $meta = $this->entityManager->getClassMetadata($mapping['targetEntity']);
        $pk = $meta->getSingleIdentifierFieldName();
        $type = Type::getType($meta->fieldMappings[$pk]['type']);

        return $this->value($type, $meta->getReflectionProperty($pk)->getValue($targetEntity));
    }

    public function processChanges(): void
    {
        foreach ($this->changes as $entityChanges) {
            $action = $entityChanges['action'];
            $entity = $entityChanges['entity'];
            $class = $entity::class;
            if (!is_string($class)) {
                continue;
            }
            if (isset($entityChanges['id'])) {
                $entityId = $entityChanges['id'];
            } else {
                $entityId = $this->id($entity); /** @TODO: Strict getId() exists? */
            }
            $data = $entityChanges['data'];

            $auditLogEntity = new AuditLog();
            $auditLogEntity->setCreatedBy($this->auditLogRepository->getUser($this->security->getUser()));
            $auditLogEntity->setAction($action);
            $auditLogEntity->setEntityId($entityId);
            $auditLogEntity->setEntityClass($class);
            $auditLogEntity->setData($data ?? []);
            $auditLogEntity->setCreatedAt(new DateTimeImmutable('now'));

            if (count($data) > 0 || $action === AuditLogAction::INSERT || $action === AuditLogAction::DELETE) {
                // save audit log
                $this->auditLogRepository->saveLog($auditLogEntity);
            }
        }
    }

    public function getAuditConfiguration(): AuditLogConfiguration
    {
        return $this->auditConfiguration;
    }

    /**
     * @param array<string, mixed> $mapping
     *
     * @throws Exception
     */
    private function value(Type $type, mixed $value, array $mapping = []): mixed
    {
        if ($value === null) {
            return null;
        }
        $platform = $this->entityManager->getConnection()->getDatabasePlatform();
        switch ($type->getName()) {
            case Types::DECIMAL:
                if (count($mapping) > 0) {
                    $convertedValue = number_format((float) $value, $mapping['scale'], '.', '');
                    break;
                }
            // no break
            case Types::BIGINT:
                $convertedValue = (string) $value;
                break;
            case Types::INTEGER:
            case Types::SMALLINT:
                $convertedValue = (int) $value;
                break;
            case Types::FLOAT:
            case Types::BOOLEAN:
                $convertedValue = $type->convertToPHPValue($value, $platform);
                break;
            case Types::BLOB:
                if (is_resource($value)) {
                    $streamValue = stream_get_contents($value);
                    if ($streamValue === false) {
                        return null;
                    }
                    $convertedValue = base64_encode($streamValue);
                    rewind($value);
                } else {
                    $convertedValue = base64_encode((string) $value);
                }
                break;
            default:
                $convertedValue = $type->convertToDatabaseValue($value, $platform);
        }

        return $convertedValue;
    }

}
