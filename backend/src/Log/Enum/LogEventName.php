<?php

declare(strict_types = 1);

namespace Pm\Log\Enum;

use BackedEnum;

/**
 * @implements BackedEnum<string>
 */
enum LogEventName: string
{

    case UserCreated = 'user_created';
    case UserLoggedIn = 'user_logged_in';
    case UserHashUpgraded = 'user_hash_upgraded';
    case UserPasswordChanged = 'user_password_changed';
    case UserProfileUpdated = 'user_profile_updated';

    case PopsCreated = 'pops_created';
    case PopsUpdated = 'pops_updated';
    case PopsLocked = 'pops_locked';

    case SuperPopsCreated = 'super_pops_created';
    case SuperPopsUpdated = 'super_pops_updated';
    case SuperPopsLocked = 'super_pops_locked';

    case AccountCreated = 'account_created';

}
