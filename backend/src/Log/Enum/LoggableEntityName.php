<?php

declare(strict_types = 1);

namespace Pm\Log\Enum;

use BackedEnum;

/**
 * @implements BackedEnum<string>
 */
enum LoggableEntityName: string
{

    case User = 'user';
    case Facility = 'facility';
    case FacilityStats = 'facilityStats';
    case FacilityInfo = 'facility_info';
    case Country = 'city';
    case City = 'country';
    case PermissionApplication = 'permission_application';
    case Permission = 'permission';
    case PermissionTemplate = 'permission_template';
    case PermissionTemplatePermission = 'permission_template_permission';
    case PermissionGroup = 'permission_group';
    case Pops = 'pops';
    case PopsStats = 'pops_stats';
    case PopsEvent = 'pops_event';
    case Account = 'account';
    case MobileSetting = 'mobile_setting';
    case MobileInfo = 'mobile_info';

}
