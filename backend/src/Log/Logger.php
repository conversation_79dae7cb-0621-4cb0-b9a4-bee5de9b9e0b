<?php

declare(strict_types = 1);

namespace Pm\Log;

use Psr\Log\LoggerInterface;
use Throwable;

class Logger
{

    public function __construct(private readonly LoggerInterface $logger)
    {
    }

    /**
     * @param mixed[] $context
     */
    public function logInfoMessage(string $message, array $context = []): void
    {
        $this->logger->info($message, $context);
    }

    /**
     * @param mixed[] $context
     */
    public function logErrorMessage(string $message, array $context = []): void
    {
        $this->logger->error($message, $context);
    }

    public function logInfoException(Throwable $exception): void
    {
        $this->logger->info($exception->getMessage());
    }

    public function logErrorException(Throwable $exception): void
    {
        $this->logger->error($exception->getMessage());
    }

}
