<?php

declare(strict_types = 1);

namespace Pm\Log\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Pm\Core\Exception\LogicException;
use Pm\Log\Enum\LoggableEntityName;
use Pm\Log\Enum\LoggableEntityNameType;

#[Entity]
#[Table(name: 'auth_log_entity')]
#[UniqueConstraint(name: 'UNIQ_ENTITY__FOREIGN_KEY__LOG', columns: ['entity', 'foreign_key', 'log_id'])]
class LogEntity
{

    #[Id]
    #[Column(type: Types::BIGINT, nullable: false)]
    #[GeneratedValue(strategy: 'SEQUENCE')]
    protected ?string $id = null;

    #[Column(type: LoggableEntityNameType::NAME, nullable: false)]
    private LoggableEntityName $entity;

    #[Column(type: Types::STRING, nullable: true)]
    private ?string $foreignKey = null;

    /**
     * todo drop this variable, is dropped when entity is loaded from database
     */
    private ?LoggableEntity $loggableEntity = null;

    #[ManyToOne(targetEntity: Log::class, inversedBy: 'entities')]
    #[JoinColumn(nullable: false)]
    private Log $log;

    public function __construct(
        LoggableEntity $entity,
        Log $log,
    )
    {
        $this->entity = $entity->getLoggableName();
        $this->loggableEntity = $entity;

        if ($entity->hasId()) {
            $this->foreignKey = $entity->getStringId();
        }
        $this->log = $log;
    }

    public function getEntity(): LoggableEntityName
    {
        return $this->entity;
    }

    public function hasForeignKey(): bool
    {
        return $this->foreignKey !== null;
    }

    public function getForeignKey(): string
    {
        if ($this->foreignKey === null) {
            throw new LogicException('No foreign key for set.');
        }

        return $this->foreignKey;
    }

    public function getLoggableEntity(): LoggableEntity
    {
        if ($this->loggableEntity === null) {
            throw new LogicException('No loggable entity set.');
        }

        return $this->loggableEntity;
    }

    public function updateForeignKey(): void
    {
        if ($this->foreignKey !== null) {
            throw new LogicException('Foreign key already set.');
        }

        if ($this->loggableEntity === null) {
            throw new LogicException('No loggable entity set.');
        }

        $this->foreignKey = $this->loggableEntity->getStringId();
    }

}
