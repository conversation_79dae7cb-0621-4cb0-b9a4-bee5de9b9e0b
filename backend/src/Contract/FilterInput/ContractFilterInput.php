<?php

declare(strict_types = 1);

namespace Pm\Contract\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class ContractFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_ID,
        self::FILTER_ACTIVE,
        self::FILTER_NAME,
        self::FILTER_CODE,
    ];

    final public const FILTER_ID = 'id';
    final public const FILTER_ACTIVE = 'active';
    final public const FILTER_NAME = 'name';
    final public const FILTER_CODE = 'code';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID => Types::INTEGER,
            self::FILTER_ACTIVE => Types::BOOLEAN,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
