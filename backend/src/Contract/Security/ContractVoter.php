<?php

declare(strict_types = 1);

namespace Pm\Contract\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Permission\Enum\PermissionEnum;

readonly class ContractVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canRead(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_CONTRACT_READ)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canWrite(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_CONTRACT_WRITE)) {
            return;
        }

        throw new AclException();
    }

}
