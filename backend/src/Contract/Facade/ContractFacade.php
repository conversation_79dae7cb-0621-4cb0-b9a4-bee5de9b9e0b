<?php

declare(strict_types = 1);

namespace Pm\Contract\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Contract\Entity\Contract;
use Pm\Contract\HttpResponse\ContractResponse;
use Pm\Contract\Input\ContractCreateUpdateInput;
use Pm\Contract\Input\ContractGetInput;
use Pm\Contract\Repository\ContractRepository;
use Pm\Contract\Security\ContractVoter;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Core\Validator\EntityValidator;
use function count;

class ContractFacade
{

    public const PAGE_MAX_LIMIT = 100;

    public function __construct(
        private readonly ContractVoter $contractVoter,
        private readonly EntityManager $entityManager,
        private readonly ContractRepository $contractRepository,
        private readonly EntityValidator $entityValidator,
    )
    {
    }

    /**
     * @return PaginatedCollection<ContractResponse>
     *
     * @throws AclException
     */
    public function getContractPaginatedListResponse(ContractGetInput $contractGetInput): PaginatedCollection
    {
        $contractList = $this->contractRepository->getContractList(
            $contractGetInput,
        );

        $this->contractVoter->canRead();

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($contractList));
        foreach ($contractList as $contract) {
            $responseList[] = new ContractResponse($contract);
        }

        return $responseList;
    }

    /**
     * @throws InvalidInputException
     * @throws AclException
     */
    public function getContract(int $id): ContractResponse
    {
        $this->contractVoter->canRead();

        $contract = $this->contractRepository->get($id);

        return new ContractResponse($contract);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function createContract(ContractCreateUpdateInput $contractCreate): ContractResponse
    {
        $this->contractVoter->canWrite();

        /** @var Contract $contract */
        $contract = $this->entityManager->wrapInTransaction(function () use ($contractCreate): Contract {
            $contract = new Contract(
                $contractCreate->name,
                $contractCreate->code,
                $contractCreate->active,
            );

            $this->entityValidator->validateEntity($contract);

            $this->entityManager->persist($contract);

            return $contract;
        });

        return new ContractResponse($contract);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function updateContract(int $id, ContractCreateUpdateInput $contractUpdate): ContractResponse
    {
        $this->contractVoter->canWrite();
        $contract = $this->contractRepository->get($id);

        /** @var Contract $contract */
        $contract = $this->entityManager->wrapInTransaction(function () use ($contract, $contractUpdate): Contract {
            $contract->setName($contractUpdate->name);
            $contract->setCode($contractUpdate->code);
            $contract->setActive($contractUpdate->active);

            $this->entityValidator->validateEntity($contract);

            return $contract;
        });

        return new ContractResponse($contract);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function deleteContract(int $id): void
    {
        $this->contractVoter->canWrite();
        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $contract = $this->contractRepository->get($id);

            $this->entityManager->remove($contract);
        });
    }

}
