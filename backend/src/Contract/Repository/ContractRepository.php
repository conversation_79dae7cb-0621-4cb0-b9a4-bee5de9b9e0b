<?php

declare(strict_types = 1);

namespace Pm\Contract\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Contract\Entity\Contract;
use Pm\Contract\FilterInput\ContractFilterInput;
use Pm\Contract\Input\ContractGetInput;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Repository\BaseRepository;
use Pm\Tools\SortingHelper;

class ContractRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): Contract
    {
        $contract = $this->entityManager->createQueryBuilder()
            ->select('c')
            ->from(Contract::class, 'c')
            ->where('c.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($contract === null) {
            throw new InvalidInputException("Contract with ID {$id} not found.");
        }

        return $contract;
    }

    /**
     * @return Paginator<Contract>
     */
    public function getContractList(
        ContractGetInput $contractGetInput,
    ): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('c')
            ->from(Contract::class, 'c');

        if ($contractGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('c.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->orWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('c.code'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $contractGetInput->search . '%');
        }

        $this->applyCriteria(
            null,
            $queryBuilder,
            new ContractFilterInput($contractGetInput->filter),
            $contractGetInput->limit,
            $contractGetInput->offset,
            SortingHelper::sanitizeSortBy($contractGetInput->sortBy, 'c', 'name'),
            SortingHelper::sanitizeSortMethod($contractGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    public function existsByNameOrCode(string $name, string $code): bool
    {
        $queryBuilder = $this->entityManager->createQueryBuilder();
        $queryBuilder->select('1')
            ->from(Contract::class, 'c')
            ->where('c.name = :name')
            ->orWhere('c.code = :code')
            ->setParameter('name', $name)
            ->setParameter('code', $code)
            ->setMaxResults(1);

        return (bool) $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findOneByNameOrCode(string $name, string $code): ?Contract
    {
        return $this->entityManager->createQueryBuilder()
            ->select('c')
            ->from(Contract::class, 'c')
            ->where('c.name = :name OR c.code = :code')
            ->setParameter('name', $name)
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
