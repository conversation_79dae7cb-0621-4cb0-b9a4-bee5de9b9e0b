<?php

declare(strict_types = 1);

namespace Pm\Company\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class CompanyFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_ID,
        self::FILTER_ACTIVE,
        self::FILTER_NAME,
        self::FILTER_CODE,
        self::FILTER_ABBR,
        self::FILTER_CID,
        self::FILTER_NOTE,
        self::FILTER_COUNTRY,
        self::FILTER_CITY,
        self::FILTER_STREET,
        self::FILTER_POSTAL_CODE,
        self::FILTER_CONTACT_EMAIL,
    ];

    final public const FILTER_ID = 'id';
    final public const FILTER_ACTIVE = 'active';
    final public const FILTER_NAME = 'name';
    final public const FILTER_CODE = 'code';
    final public const FILTER_ABBR = 'abbr';
    final public const FILTER_CID = 'cid';
    final public const FILTER_NOTE = 'note';
    final public const FILTER_COUNTRY = 'country';
    final public const FILTER_CITY = 'city';
    final public const FILTER_STREET = 'street';
    final public const FILTER_POSTAL_CODE = 'postalCode';
    final public const FILTER_CONTACT_EMAIL = 'contactEmail';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID => Types::INTEGER,
            self::FILTER_ACTIVE => Types::BOOLEAN,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
