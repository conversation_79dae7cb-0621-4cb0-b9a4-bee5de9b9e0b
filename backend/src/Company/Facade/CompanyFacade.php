<?php

declare(strict_types = 1);

namespace Pm\Company\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Company\Entity\Company;
use Pm\Company\HttpResponse\CompanyResponse;
use Pm\Company\Input\CompanyCreateUpdateInput;
use Pm\Company\Input\CompanyGetInput;
use Pm\Company\Repository\CompanyRepository;
use Pm\Company\Security\CompanyVoter;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Core\Validator\EntityValidator;
use function count;

class CompanyFacade
{

    public function __construct(
        private readonly CompanyVoter $companyVoter,
        private readonly EntityManager $entityManager,
        private readonly CompanyRepository $companyRepository,
        private readonly EntityValidator $entityValidator,
    )
    {
    }

    /**
     * @return PaginatedCollection<CompanyResponse>
     *
     * @throws AclException
     */
    public function getCompanyPaginatedListResponse(CompanyGetInput $companyGetInput): PaginatedCollection
    {
        $companyList = $this->companyRepository->getCompanyList($companyGetInput);

        $this->companyVoter->canRead();

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($companyList));
        foreach ($companyList as $company) {
            $responseList[] = new CompanyResponse($company);
        }

        return $responseList;
    }

    /**
     * @throws InvalidInputException
     * @throws AclException
     */
    public function getCompany(int $id): CompanyResponse
    {
        $this->companyVoter->canRead();

        $company = $this->companyRepository->get($id);

        return new CompanyResponse($company);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function createCompany(CompanyCreateUpdateInput $companyCreate): CompanyResponse
    {
        $this->companyVoter->canWrite();

        /** @var Company $company */
        $company = $this->entityManager->wrapInTransaction(function () use ($companyCreate): Company {
            $company = new Company(
                $companyCreate->name,
                $companyCreate->active,
                $companyCreate->abbr,
                $companyCreate->code,
                $companyCreate->cid,
                $companyCreate->note,
                $companyCreate->country,
                $companyCreate->city,
                $companyCreate->street,
                $companyCreate->postalCode,
                $companyCreate->coordinates,
                $companyCreate->originalResponse,
                $companyCreate->contactEmail,
            );

            $this->entityValidator->validateEntity($company);

            $this->entityManager->persist($company);

            return $company;
        });

        return new CompanyResponse($company);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function updateCompany(int $id, CompanyCreateUpdateInput $input): CompanyResponse
    {
        $this->companyVoter->canWrite();

        $company = $this->companyRepository->get($id);

        /** @var Company $company */
        $company = $this->entityManager->wrapInTransaction(function () use ($company, $input): Company {
            $company->setName($input->name);
            $company->setAbbr($input->abbr);
            $company->setCode($input->code);
            $company->setCid($input->cid);
            $company->setActive($input->active);
            $company->setNote($input->note);
            $company->setCountry($input->country);
            $company->setCity($input->city);
            $company->setStreet($input->street);
            $company->setPostalCode($input->postalCode);
            $company->setCoordinates($input->coordinates);
            $company->setOriginalResponse($input->originalResponse);
            $company->setContactEmail($input->contactEmail);

            $this->entityValidator->validateEntity($company);

            return $company;
        });

        return new CompanyResponse($company);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function deleteCompany(int $id): void
    {
        $this->companyVoter->canWrite();

        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $company = $this->companyRepository->get($id);

            $this->entityManager->remove($company);
        });
    }

}
