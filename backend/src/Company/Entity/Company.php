<?php

declare(strict_types = 1);

namespace Pm\Company\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Table;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\UniqueValidatableEntity;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[Entity]
#[Table(name: 'company')]
#[UniqueEntity(fields: ['name'], message: 'validation.field.unique')]
#[UniqueEntity(fields: ['abbr'], message: 'validation.field.unique')]
#[UniqueEntity(fields: ['code'], message: 'validation.field.unique')]
class Company extends BaseEntity implements UniqueValidatableEntity
{

    public function __construct(

        #[Column(type: Types::STRING, length: 255, unique: true, nullable: false)]
        private string $name,

        #[Column(type: Types::BOOLEAN, nullable: false)]
        private bool $active,

        #[Column(type: Types::STRING, length: 10, unique: true, nullable: false)]
        private string $abbr,

        #[Column(type: Types::STRING, length: 20, unique: true, nullable: false)]
        private string $code,

        #[Column(type: Types::STRING, length: 20, nullable: false)]
        private string $cid,

        #[Column(type: Types::TEXT, nullable: true)]
        private ?string $note = null,

        #[Column(type: Types::STRING, length: 255, nullable: true)]
        private ?string $country = null,

        #[Column(type: Types::STRING, length: 255, nullable: true)]
        private ?string $city = null,

        #[Column(type: Types::STRING, length: 255, nullable: true)]
        private ?string $street = null,

        #[Column(type: Types::STRING, length: 20, nullable: true)]
        private ?string $postalCode = null,

        #[Column(type: Types::STRING, nullable: true)]
        private ?string $coordinates = null,

        /**
         * @var array<string, mixed>|null
         */
        #[Column(type: Types::JSON, nullable: true)]
        private ?array $originalResponse = null,

        #[Column(type: Types::STRING, length: 255, nullable: true)]
        private ?string $contactEmail = null,

    )
    {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getAbbr(): string
    {
        return $this->abbr;
    }

    public function setAbbr(string $abbr): void
    {
        $this->abbr = $abbr;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getCid(): string
    {
        return $this->cid;
    }

    public function setCid(string $cid): void
    {
        $this->cid = $cid;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function setNote(?string $note): void
    {
        $this->note = $note;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): void
    {
        $this->country = $country;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): void
    {
        $this->city = $city;
    }

    public function getStreet(): ?string
    {
        return $this->street;
    }

    public function setStreet(?string $street): void
    {
        $this->street = $street;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getCoordinates(): ?string
    {
        return $this->coordinates;
    }

    public function setCoordinates(?string $coordinates): void
    {
        $this->coordinates = $coordinates;
    }

    /** @return array<string, mixed>|null */
    public function getOriginalResponse(): ?array
    {
        return $this->originalResponse;
    }

    /**
     * @param array<string,mixed>|null $originalResponse
     */
    public function setOriginalResponse(?array $originalResponse): void
    {
        $this->originalResponse = $originalResponse;
    }

    public function getContactEmail(): ?string
    {
        return $this->contactEmail;
    }

    public function setContactEmail(?string $contactEmail): void
    {
        $this->contactEmail = $contactEmail;
    }

}
