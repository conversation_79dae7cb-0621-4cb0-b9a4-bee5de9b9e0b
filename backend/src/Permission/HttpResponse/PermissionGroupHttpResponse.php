<?php

declare(strict_types = 1);

namespace Pm\Permission\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\Facility\HttpResponse\FacilitySimpleHttpResponse;
use Pm\Permission\Entity\PermissionGroup;
use Pm\User\HttpResponse\UserBasicHttpResponse;

class PermissionGroupHttpResponse implements ApiOutput
{

    public readonly int $id;

    public readonly string $name;

    public readonly ?string $usage;

    /**
     * @var array<PermissionTemplateHttpResponse>
     */
    public readonly array $permissionTemplates;

    /**
     * @var array<UserBasicHttpResponse>
     */
    public readonly array $users;

    /**
     * @var array<FacilitySimpleHttpResponse>
     */
    public readonly array $facilities;

    public readonly ?DateTimeImmutable $createdAt;

    public readonly ?DateTimeImmutable $updatedAt;

    public readonly ?UserBasicHttpResponse $insertedBy;

    public readonly ?UserBasicHttpResponse $updatedBy;

    public function __construct(
        PermissionGroup $permissionGroup,
    )
    {
        $this->id = $permissionGroup->getId();
        $this->name = $permissionGroup->getName();
        $this->usage = $permissionGroup->getUsage();

        $permissionTemplates = $permissionGroup->getPermissionTemplates();
        $permissionTemplatesArray = [];
        foreach ($permissionTemplates as $permissionTemplate) {
            $permissionTemplatesArray[] = new PermissionTemplateHttpResponse($permissionTemplate);
        }
        $this->permissionTemplates = $permissionTemplatesArray;

        $users = [];
        foreach ($permissionGroup->getUsers() as $user) {
            $users[] = new UserBasicHttpResponse($user);
        }
        $this->users = $users;

        $facilities = [];
        foreach ($permissionGroup->getFacilities() as $facility) {
            $facilities[] = new FacilitySimpleHttpResponse($facility);
        }
        $this->facilities = $facilities;

        $this->createdAt = $permissionGroup->getCreatedAt();
        $this->updatedAt = $permissionGroup->getUpdatedAt();
        $this->insertedBy = $permissionGroup->getInsertedBy() !== null ? new UserBasicHttpResponse($permissionGroup->getInsertedBy()) : null;
        $this->updatedBy = $permissionGroup->getUpdatedBy() !== null ? new UserBasicHttpResponse($permissionGroup->getUpdatedBy()) : null;
    }

}
