<?php

declare(strict_types = 1);

namespace Pm\Permission\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\User\HttpResponse\UserBasicHttpResponse;

readonly class PermissionTemplateHttpResponse implements ApiOutput
{

    public int $id;

    public string $name;

    public string $usage;

    public ?DateTimeImmutable $createdAt;

    public ?DateTimeImmutable $updatedAt;

    public ?UserBasicHttpResponse $insertedBy;

    public ?UserBasicHttpResponse $updatedBy;

    /**
     * @var array<string>
     */
    public array $permissions;

    public function __construct(
        PermissionTemplate $permissionTemplate,
    )
    {
        $this->id = $permissionTemplate->getId();
        $this->name = $permissionTemplate->getName();
        $this->usage = $permissionTemplate->getUsage();
        $permissions = [];
        foreach ($permissionTemplate->getPermissions() as $permission) {
            $permissions[] = $permission->getCode()->value;
        }
        $this->permissions = $permissions;
        $this->createdAt = $permissionTemplate->getCreatedAt();
        $this->updatedAt = $permissionTemplate->getUpdatedAt();
        $this->insertedBy = $permissionTemplate->getInsertedBy() !== null ? new UserBasicHttpResponse($permissionTemplate->getInsertedBy()) : null;
        $this->updatedBy = $permissionTemplate->getUpdatedBy() !== null ? new UserBasicHttpResponse($permissionTemplate->getUpdatedBy()) : null;
    }

}
