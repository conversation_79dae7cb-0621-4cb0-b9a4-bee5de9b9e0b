<?php

declare(strict_types = 1);

namespace Pm\Permission\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Permission\Facade\PermissionTemplateFacade;
use Pm\Permission\Validator\PermissionTemplateSortFields;

readonly class PermissionTemplateGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?string $search = null,
        public ?array $filter = null,
        public ?int $limit = PermissionTemplateFacade::PAGE_MAX_LIMIT,
        public ?int $offset = 0,
        public ?PermissionTemplateSortFields $sortBy = null,
        public ?Sort $sortMethod = null,
    )
    {
    }

}
