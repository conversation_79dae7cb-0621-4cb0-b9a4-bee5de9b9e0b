<?php

declare(strict_types = 1);

namespace Pm\Permission\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

readonly class PermissionGroupCreateInput implements Input
{

    /**
     * @param array<int, int> $users
     * @param array<int, int> $facilities
     * @param array<int, int> $permissionTemplates
     */
    public function __construct(
        #[NotBlank]
        #[Length(min: 1, max: 255)]
        public string $name,
        public string $usage,
        public array $users,
        public array $facilities,
        public array $permissionTemplates,
    )
    {
    }

}
