<?php

declare(strict_types = 1);

namespace Pm\Permission\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

readonly class PermissionTemplateCreateInput implements Input
{

    /**
     * @param array<int, string> $permissions
     */
    public function __construct(
        #[NotBlank]
        #[Length(min: 1, max: 255)]
        public string $name,
        public string $usage,
        public array $permissions,
    )
    {
    }

}
