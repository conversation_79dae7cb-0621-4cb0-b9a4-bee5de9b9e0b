<?php

declare(strict_types = 1);

namespace Pm\Permission\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Permission\Facade\PermissionFacade;
use Pm\Permission\Validator\PermissionSortFields;

readonly class PermissionGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?string $search = null,
        public ?array $filter = null,
        public ?int $limit = PermissionFacade::PAGE_MAX_LIMIT,
        public ?int $offset = 0,
        public ?PermissionSortFields $sortBy = null,
        public ?Sort $sortMethod = null,
    )
    {
    }

}
