<?php

declare(strict_types = 1);

namespace Pm\Permission\Exception;

use Pm\Core\Exception\RuntimeException;
use Pm\Core\Http\ResponseStatusCode;

class NoPermissionApplicationFoundException extends RuntimeException
{

    public static function byCode(string $code): self
    {
        return new self("No permissionApplication ##[{$code}]# found.", code: ResponseStatusCode::NOT_FOUND_404);
    }

}
