<?php

declare(strict_types = 1);

namespace Pm\Permission\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Permission\Exception\NoPermissionApplicationFoundException;
use Pm\Permission\Facade\PermissionFacade;
use Pm\Permission\HttpResponse\PermissionHttpResponse;
use Pm\Permission\Input\PermissionGetInput;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;

#[Tag(name: 'Permission')]
class PermissionController extends BaseController
{

    public function __construct(
        private readonly PermissionFacade $permissionFacade,
    )
    {
    }

    /**
     *  Get list of permissions
     *
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoPermissionApplicationFoundException
     */
    #[ListResponse(PermissionHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/permission')]
    public function getListAction(
        #[MapQueryString]
        ?PermissionGetInput $permissionGetInput,
    ): SuccessCountableOutput
    {
        $permissionList = $this->permissionFacade->getPermissionPaginatorListResponse(
            $permissionGetInput ?? new PermissionGetInput(),
        );

        /**
         * @var list<PermissionHttpResponse> $arr
         */
        $arr = $permissionList->getArrayCopy();
        return new SuccessCountableOutput($arr, $permissionList->getTotalCount());
    }

}
