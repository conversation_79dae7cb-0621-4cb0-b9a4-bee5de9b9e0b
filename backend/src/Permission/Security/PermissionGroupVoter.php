<?php

declare(strict_types = 1);

namespace Pm\Permission\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Permission\Entity\PermissionGroup;
use Pm\Permission\Enum\PermissionEnum;

readonly class PermissionGroupVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canView(PermissionGroup $permissionGroup): void
    {
        $this->canAccessByAccountId($permissionGroup->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canCreate(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdate(PermissionGroup $permissionGroup): void
    {
        $this->canAccessByAccountId($permissionGroup->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canDelete(PermissionGroup $permissionGroup): void
    {
        $this->canAccessByAccountId($permissionGroup->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

}
