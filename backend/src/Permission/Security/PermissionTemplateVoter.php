<?php

declare(strict_types = 1);

namespace Pm\Permission\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\Permission\Enum\PermissionEnum;

readonly class PermissionTemplateVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canView(PermissionTemplate $permissionTemplate): void
    {
        $this->canAccessByAccountId($permissionTemplate->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canCreate(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdate(PermissionTemplate $permissionTemplate): void
    {
        $this->canAccessByAccountId($permissionTemplate->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canDelete(PermissionTemplate $permissionTemplate): void
    {
        $this->canAccessByAccountId($permissionTemplate->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_PERMISSION_MANAGE)) {
            return;
        }

        throw new AclException();
    }

}
