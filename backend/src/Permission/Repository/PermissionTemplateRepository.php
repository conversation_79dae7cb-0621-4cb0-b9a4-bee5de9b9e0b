<?php

declare(strict_types = 1);

namespace Pm\Permission\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Account\Entity\Account;
use Pm\Core\Enum\Sort;
use Pm\Core\Repository\BaseRepository;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Permission\Exception\NoPermissionTemplateFoundException;
use Pm\Permission\FilterInput\PermissionTemplateFilterInput;
use Pm\Permission\Input\PermissionTemplateGetInput;
use Pm\Permission\Validator\PermissionTemplateSortFields;
use Pm\Tools\SortingHelper;

class PermissionTemplateRepository extends BaseRepository
{

    /**
     * @throws NoPermissionTemplateFoundException
     */
    public function get(int $id): PermissionTemplate
    {
        $permissionTemplate = $this->entityManager->createQueryBuilder()
            ->select('pt')
            ->from(PermissionTemplate::class, 'pt')
            ->andWhere('pt.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($permissionTemplate === null) {
            throw NoPermissionTemplateFoundException::byId($id);
        }

        return $permissionTemplate;
    }

    /**
     * @return Paginator<PermissionTemplate>
     */
    public function getPermissionTemplateList(
        PermissionTemplateGetInput $permissionTemplateGetInput,
        ?Account $account = null,
    ): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('pt')
            ->from(PermissionTemplate::class, 'pt')
            ->leftJoin('pt.permissions', 'permissions');

        if ($permissionTemplateGetInput->search !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('pt.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->orWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('pt.usage'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $permissionTemplateGetInput->search . '%');
        }

        $this->applyCriteria(
            $account,
            $queryBuilder,
            new PermissionTemplateFilterInput($permissionTemplateGetInput->filter),
            $permissionTemplateGetInput->limit,
            $permissionTemplateGetInput->offset,
            SortingHelper::sanitizeSortBy($permissionTemplateGetInput->sortBy, 'pt', PermissionTemplateSortFields::Name->value),
            SortingHelper::sanitizeSortMethod($permissionTemplateGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    /**
     * @return PermissionTemplate[]
     */
    public function getTemplatesByPermission(PermissionEnum $permissionEnum): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('pt')
            ->from(PermissionTemplate::class, 'pt')
            ->innerJoin('pt.permissions', 'p')
            ->where('p.code = :permission')
            ->setParameter('permission', $permissionEnum)
            ->getQuery()
            ->getResult();
    }

    public function existsByName(string $name): bool
    {
        $result = $this->entityManager->createQueryBuilder()
            ->select('pt')
            ->from(PermissionTemplate::class, 'pt')
            ->where('pt.name = :name')
            ->setParameter('name', $name)
            ->getQuery()
            ->getOneOrNullResult();

        return $result !== null;
    }

}
