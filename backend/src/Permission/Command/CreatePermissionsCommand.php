<?php

declare(strict_types = 1);

namespace Pm\Permission\Command;

use Pm\Core\Command\BaseCommand;
use Pm\Core\Exception\InvalidInputException;
use Pm\Permission\Facade\CreatePermissionsFacade;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CreatePermissionsCommand extends BaseCommand
{

    public function __construct(private readonly CreatePermissionsFacade $createPermissionsFacade)
    {
        parent::__construct(null);
    }

    /**
     * @throws InvalidInputException
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output,
    ): int
    {
        $this->createPermissionsFacade->createPermissions();

        $this->output->writeln('Done.');
        return Command::SUCCESS;
    }

    public static function getDefaultName(): string
    {
        return 'permission:create';
    }

}
