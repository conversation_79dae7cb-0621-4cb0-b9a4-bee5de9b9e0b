<?php

declare(strict_types = 1);

namespace Pm\Permission\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\InverseJoinColumn;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\JoinTable;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\OrderBy;
use Doctrine\ORM\Mapping\Table;
use Pm\Account\Entity\Account;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\BlameableInsertEntity;
use Pm\Core\Entity\Traits\BlameableUpdateEntity;
use Pm\Core\Entity\Traits\OriginalDbEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\Log\Entity\LoggableEntity;
use Pm\Log\Enum\LoggableEntityName;
use Pm\User\Entity\User;

#[Table(name: 'permission_template')]
#[Entity]
class PermissionTemplate extends BaseEntity implements LoggableEntity
{

    use TimestampableEntity;
    use BlameableInsertEntity;
    use BlameableUpdateEntity;
    use OriginalDbEntity;

    #[Column(type: Types::STRING, length: 255, unique: true, nullable: false)]
    private string $name;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private string $usage;

    /**
     * @var Collection<int, Permission>
     */
    #[ManyToMany(targetEntity: Permission::class, inversedBy: 'permissionTemplates')]
    #[JoinTable(name: 'permission_template_permission')]
    #[JoinColumn(name: 'permission_template_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[InverseJoinColumn(name: 'permission', referencedColumnName: 'code')]
    #[OrderBy(['code' => 'ASC'])]
    private Collection $permissions;

    /**
     * @var Collection<int, User>
     */
    #[JoinTable(name: 'permission_template_user_m2n')]
    #[JoinColumn(name: 'permission_template_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[InverseJoinColumn(name: 'user_id', referencedColumnName: 'id', unique: false, onDelete: 'CASCADE')]
    #[ManyToMany(targetEntity: User::class, inversedBy: 'permissionTemplates', cascade: ['persist'], orphanRemoval: true)]
    private Collection $users;

    /**
     * @var Collection<int, PermissionGroup>
    */
    #[ManytoMany(targetEntity: PermissionGroup::class, mappedBy: 'permissionTemplates')]
    private Collection $permissionGroups;

    public function __construct(
        string $name,
        string $usage,
        Account $account,
        ?string $originalDbId = null,
        bool $migratedCompletely = false,
    )
    {
        $this->name = $name;
        $this->usage = $usage;
        $this->account = $account;
        $this->permissions = new ArrayCollection();
        $this->users = new ArrayCollection();
        $this->originalDbId = $originalDbId;
        $this->migratedCompletely = $migratedCompletely;
        $this->permissionGroups = new ArrayCollection();
    }

    public function getLoggableName(): LoggableEntityName
    {
        return LoggableEntityName::PermissionTemplate;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getUsage(): string
    {
        return $this->usage;
    }

    public function setUsage(string $usage): void
    {
        $this->usage = $usage;
    }

    /**
     * @return Collection<int, Permission>
     */
    public function getPermissions(): Collection
    {
        return $this->permissions;
    }

    public function addPermission(Permission $permission): void
    {
        if (!$this->permissions->contains($permission)) {
            $this->permissions->add($permission);
            $permission->addPermissionTemplate($this);
        }
    }

    public function removePermission(Permission $permission): void
    {
        if ($this->permissions->removeElement($permission)) {
            $permission->removePermissionTemplate($this);
        }
    }

    public function clearPermissions(): void
    {
        $this->permissions->clear();
    }

    public function addUser(User $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users->add($user);
            $user->addPermissionTemplate($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, PermissionGroup>
     */
    public function getPermissionGroups(): Collection
    {
        return $this->permissionGroups;
    }

    /**
     * @param Collection<int, PermissionGroup> $permissionGroups
     */
    public function setPermissionGroups(Collection $permissionGroups): void
    {
        $this->permissionGroups = $permissionGroups;
    }

    public function addPermissionGroup(PermissionGroup $permissionGroup): void
    {
        if (!$this->permissionGroups->contains($permissionGroup)) {
            $this->permissionGroups->add($permissionGroup);
            $permissionGroup->addPermissionTemplate($this);
        }
    }

    public function removePermissionGroup(PermissionGroup $permissionGroup): void
    {
        if ($this->permissionGroups->removeElement($permissionGroup)) {
            $permissionGroup->removePermissionTemplate($this);
        }
    }

}
