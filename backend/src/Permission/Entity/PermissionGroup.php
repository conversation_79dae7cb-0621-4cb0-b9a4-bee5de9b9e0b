<?php

declare(strict_types = 1);

namespace Pm\Permission\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\InverseJoinColumn;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\JoinTable;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\Table;
use Pm\Account\Entity\Account;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\BlameableInsertEntity;
use Pm\Core\Entity\Traits\BlameableUpdateEntity;
use Pm\Core\Entity\Traits\OriginalDbEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\Facility\Entity\Facility;
use Pm\Log\Entity\LoggableEntity;
use Pm\Log\Enum\LoggableEntityName;
use Pm\User\Entity\User;

#[Table(name: 'permission_group')]
#[Entity]
class PermissionGroup extends BaseEntity implements LoggableEntity
{

    use TimestampableEntity;
    use BlameableInsertEntity;
    use BlameableUpdateEntity;
    use OriginalDbEntity;

    #[Column(type: Types::STRING, length: 255, unique: true, nullable: false)]
    private string $name;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $usage;

    /**
     * @var Collection<int, User>
     */
    #[JoinTable(name: 'permission_group_user_m2n')]
    #[JoinColumn(name: 'permission_group_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[InverseJoinColumn(name: 'user_id', referencedColumnName: 'id', unique: false, onDelete: 'CASCADE')]
    #[ManyToMany(targetEntity: User::class, inversedBy: 'permissionGroups')]
    private Collection $users;

    /**
     * @var Collection<int, Facility>
     */
    #[JoinTable(name: 'permission_group_facility_m2n')]
    #[JoinColumn(name: 'permission_group_id', referencedColumnName: 'id')]
    #[InverseJoinColumn(name: 'facility_id', referencedColumnName: 'id')]
    #[ManyToMany(targetEntity: Facility::class, inversedBy: 'permissionGroups')]
    private Collection $facilities;

    /**
     * @var Collection<int, PermissionTemplate>
     */
    #[ManyToMany(targetEntity: PermissionTemplate::class, inversedBy: 'permissionGroups')]
    #[JoinTable(name: 'permission_groups_templates_m2n')]
    private Collection $permissionTemplates;

    public function __construct(
        string $name,
        Account $account,
        ?string $usage = null,
        ?string $originalDbId = null,
        bool $migratedCompletely = false,
    )
    {
        $this->name = $name;
        $this->account = $account;
        $this->usage = $usage;
        $this->users = new ArrayCollection();
        $this->facilities = new ArrayCollection();
        $this->originalDbId = $originalDbId;
        $this->migratedCompletely = $migratedCompletely;
        $this->permissionTemplates = new ArrayCollection();
    }

    public function getLoggableName(): LoggableEntityName
    {
        return LoggableEntityName::PermissionGroup;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getUsage(): ?string
    {
        return $this->usage;
    }

    public function setUsage(?string $usage): void
    {
        $this->usage = $usage;
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function hasUser(User $user): bool
    {
        foreach ($this->users as $s) {
            if ($s->getId() === $user->getId()) {
                return true;
            }
        }
        return false;
    }

    public function addUser(User $user): void
    {
        if (!$this->users->contains($user)) {
            $this->users->add($user);
        }
    }

    public function clearUsers(): void
    {
        $this->users->clear();
    }

    /**
     * @return Collection<int, Facility>
     */
    public function getFacilities(): Collection
    {
        return $this->facilities;
    }

    public function addFacility(Facility $facility): void
    {
        if (!$this->facilities->contains($facility)) {
            $this->facilities->add($facility);
        }
    }

    public function clearFacilities(): void
    {
        $this->facilities->clear();
    }

    /**
     * @return Collection<int, PermissionTemplate>
     */
    public function getPermissionTemplates(): Collection
    {
        return $this->permissionTemplates;
    }

    /**
     * @param Collection<int, PermissionTemplate> $permissionTemplates
     */
    public function setPermissionTemplates(Collection $permissionTemplates): void
    {
        $this->permissionTemplates = $permissionTemplates;
    }

    public function addPermissionTemplate(PermissionTemplate $permissionTemplate): void
    {
        if (!$this->permissionTemplates->contains($permissionTemplate)) {
            $this->permissionTemplates->add($permissionTemplate);
            $permissionTemplate->addPermissionGroup($this);
        }
    }

    public function removePermissionTemplate(PermissionTemplate $permissionTemplate): void
    {
        if ($this->permissionTemplates->removeElement($permissionTemplate)) {
            $permissionTemplate->removePermissionGroup($this);
        }
    }

}
