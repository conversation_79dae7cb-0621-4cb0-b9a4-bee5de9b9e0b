<?php

declare(strict_types = 1);

namespace Pm\Legislation\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Legislation\Facade\LegislationFacade;
use Pm\Legislation\Validator\LegislationSortFields;

class LegislationGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public readonly ?string $search = null,
        public readonly ?int $limit = LegislationFacade::PAGE_MAX_LIMIT,
        public readonly ?int $offset = 0,
        public readonly ?LegislationSortFields $sortBy = null,
        public readonly ?Sort $sortMethod = null,
        public readonly ?array $filter = null,
    )
    {
    }

}
