<?php

declare(strict_types = 1);

namespace Pm\Legislation\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Repository\BaseRepository;
use Pm\Legislation\Entity\Legislation;
use Pm\Legislation\FilterInput\LegislationFilterInput;
use Pm\Legislation\Input\LegislationGetInput;
use Pm\Tools\SortingHelper;

class LegislationRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): Legislation
    {
        $legislation = $this->entityManager->createQueryBuilder()
            ->select('l')
            ->from(Legislation::class, 'l')
            ->where('l.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($legislation === null) {
            throw new InvalidInputException("Legislation with ID {$id} not found.");
        }

        return $legislation;
    }

    /**
     * @return Paginator<Legislation>
     */
    public function getLegislationList(
        LegislationGetInput $legislationGetInput,
    ): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('l')
            ->from(Legislation::class, 'l');

        if ($legislationGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('l.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $legislationGetInput->search . '%');
        }

        $this->applyCriteria(
            null,
            $queryBuilder,
            new LegislationFilterInput($legislationGetInput->filter),
            $legislationGetInput->limit,
            $legislationGetInput->offset,
            SortingHelper::sanitizeSortBy($legislationGetInput->sortBy, 'l', 'name'),
            SortingHelper::sanitizeSortMethod($legislationGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    public function existsByName(string $name): bool
    {
        $queryBuilder = $this->entityManager->createQueryBuilder();
        $queryBuilder->select('1')
            ->from(Legislation::class, 'l')
            ->where('l.name = :name')
            ->setParameter('name', $name)
            ->setMaxResults(1);

        return (bool) $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findOneByName(string $name): ?Legislation
    {
        return $this->entityManager->createQueryBuilder()
            ->select('l')
            ->from(Legislation::class, 'l')
            ->where('l.name = :name')
            ->setParameter('name', $name)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
