<?php

declare(strict_types = 1);

namespace Pm\Legislation\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Legislation\Facade\LegislationFacade;
use Pm\Legislation\HttpResponse\LegislationResponse;
use Pm\Legislation\Input\LegislationCreateUpdateInput;
use Pm\Legislation\Input\LegislationGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Legislation')]
class LegislationController extends BaseController
{

    public function __construct(
        private readonly LegislationFacade $legislationFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(LegislationResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/legislation')]
    public function getListAction(
        #[MapQueryString]
        ?LegislationGetInput $legislationGetInput,
    ): SuccessCountableOutput
    {
        $legislationList = $this->legislationFacade->getLegislationPaginatedListResponse(
            $legislationGetInput ?? new LegislationGetInput(),
        );

        /**
         * @var list<LegislationResponse> $list
         */
        $list = $legislationList->getArrayCopy();
        return new SuccessCountableOutput($list, $legislationList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(LegislationResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/legislation/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $legislation = $this->legislationFacade->getLegislation($id);

        return new SuccessOutput($legislation);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(LegislationResponse::class, 'legislation-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/legislation')]
    public function createLegislationAction(
        #[MapRequestPayload]
        LegislationCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->legislationFacade->createLegislation($request),
            Response::HTTP_CREATED,
            'legislation-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(LegislationResponse::class, 'legislation-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/legislation/{id}')]
    public function updateLegislationAction(
        int $id,
        #[MapRequestPayload]
        LegislationCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->legislationFacade->updateLegislation($id, $request),
            Response::HTTP_OK,
            'legislation-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'legislation-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/legislation/{id}')]
    public function deleteLegislationAction(int $id): SuccessOutput
    {
        $this->legislationFacade->deleteLegislation($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'legislation-deleted',
        );
    }

}
