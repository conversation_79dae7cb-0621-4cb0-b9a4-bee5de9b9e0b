<?php

declare(strict_types = 1);

namespace Pm\Legislation\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Currency\Entity\Currency;
use Pm\Legislation\Entity\Legislation;
use Pm\Legislation\HttpResponse\LegislationResponse;
use Pm\Legislation\Input\LegislationCreateUpdateInput;
use Pm\Legislation\Input\LegislationGetInput;
use Pm\Legislation\Repository\LegislationRepository;
use Pm\Legislation\Security\LegislationVoter;
use function count;

class LegislationFacade
{

    public const PAGE_MAX_LIMIT = 100;

    public function __construct(
        private readonly LegislationVoter $legislationVoter,
        private readonly EntityManager $entityManager,
        private readonly LegislationRepository $legislationRepository,
    )
    {
    }

    /**
     * @return PaginatedCollection<LegislationResponse>
     *
     * @throws AclException
     */
    public function getLegislationPaginatedListResponse(LegislationGetInput $legislationGetInput): PaginatedCollection
    {
        $this->legislationVoter->canRead();
        $legislationList = $this->legislationRepository->getLegislationList(
            $legislationGetInput,
        );

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($legislationList));
        foreach ($legislationList as $legislation) {
            $responseList[] = new LegislationResponse($legislation);
        }

        return $responseList;
    }

    /**
     * @throws InvalidInputException
     * @throws AclException
     */
    public function getLegislation(int $id): LegislationResponse
    {
        $this->legislationVoter->canRead();

        $legislation = $this->legislationRepository->get($id);

        return new LegislationResponse($legislation);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function createLegislation(LegislationCreateUpdateInput $legislationCreate): LegislationResponse
    {
        $this->legislationVoter->canWrite();

        if ($this->legislationRepository->existsByName($legislationCreate->name)) {
            throw new InvalidInputException('Legislation with the same name already exists.');
        }

        /** @var Legislation $legislation */
        $legislation = $this->entityManager->wrapInTransaction(function () use ($legislationCreate): Legislation {
            $currencyId = $legislationCreate->currencyId;
            $currency = $this->entityManager->find(Currency::class, $currencyId);
            if ($currency === null) {
                throw new InvalidInputException('Currency not found.');
            }
            $legislation = new Legislation(
                $legislationCreate->name,
                $legislationCreate->active,
                $currency,
            );

            $this->entityManager->persist($legislation);

            return $legislation;
        });

        return new LegislationResponse($legislation);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function updateLegislation(int $id, LegislationCreateUpdateInput $legislationUpdate): LegislationResponse
    {
        $this->legislationVoter->canWrite();

        $legislation = $this->legislationRepository->get($id);

        $existingLegislation = $this->legislationRepository->findOneByName($legislationUpdate->name);

        if ($existingLegislation !== null && $existingLegislation->getId() !== $id) {
            throw new InvalidInputException('Legislation with the same name already exists.');
        }

        /** @var Legislation $legislation */
        $legislation = $this->entityManager->wrapInTransaction(function () use ($legislation, $legislationUpdate): Legislation {
            $legislation->setName($legislationUpdate->name);
            $legislation->setActive($legislationUpdate->active);

            $currencyId = $legislationUpdate->currencyId;
            $currency = $this->entityManager->find(Currency::class, $currencyId);
            if ($currency === null) {
                throw new InvalidInputException('Currency not found.');
            }
            $legislation->setCurrency($currency);

            return $legislation;
        });

        return new LegislationResponse($legislation);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function deleteLegislation(int $id): void
    {
        $this->legislationVoter->canWrite();
        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $legislation = $this->legislationRepository->get($id);

            $this->entityManager->remove($legislation);
        });
    }

}
