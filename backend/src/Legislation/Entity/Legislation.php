<?php

declare(strict_types = 1);

namespace Pm\Legislation\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Pm\Core\Entity\BaseEntity;
use Pm\Currency\Entity\Currency;

#[Entity]
#[Table(name: 'legislation')]
class Legislation extends BaseEntity
{

    #[Column(type: Types::STRING, length: 255, unique: true, nullable: false)]
    private string $name;

    #[Column(type: Types::BOOLEAN, nullable: false)]
    private bool $active;

    #[ManyToOne(targetEntity: Currency::class, inversedBy: 'legislations')]
    #[JoinColumn(name: 'currency_id', referencedColumnName: 'id', nullable: false)]
    private Currency $currency;

    public function __construct(string $name, bool $active, Currency $currency)
    {
        $this->setName($name);
        $this->setActive($active);
        $this->setCurrency($currency);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): void
    {
        $this->currency = $currency;
    }

}
