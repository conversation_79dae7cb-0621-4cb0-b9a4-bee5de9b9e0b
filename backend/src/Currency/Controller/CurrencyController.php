<?php

declare(strict_types = 1);

namespace Pm\Currency\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Currency\Facade\CurrencyFacade;
use Pm\Currency\HttpResponse\CurrencyResponse;
use Pm\Currency\Input\CurrencyCreateUpdateInput;
use Pm\Currency\Input\CurrencyGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

class CurrencyController extends BaseController
{

    public function __construct(
        private readonly CurrencyFacade $currencyFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[Get(path: '/api/v1/currency')]
    public function getListAction(
        #[MapQueryString]
        ?CurrencyGetInput $currencyGetInput,
    ): SuccessCountableOutput
    {
        $currencyList = $this->currencyFacade->getCurrencyPaginatedListResponse(
            $currencyGetInput ?? new CurrencyGetInput(),
        );

        /**
         * @var list<CurrencyResponse> $list
         */
        $list = $currencyList->getArrayCopy();
        return new SuccessCountableOutput($list, $currencyList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Get(path: '/api/v1/currency/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $currency = $this->currencyFacade->getCurrency($id);

        return new SuccessOutput($currency);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Post(path: '/api/v1/currency')]
    public function createCurrencyAction(
        #[MapRequestPayload]
        CurrencyCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->currencyFacade->createCurrency($request),
            Response::HTTP_CREATED,
            'currency-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Put(path: '/api/v1/currency/{id}')]
    public function updateCurrencyAction(
        int $id,
        #[MapRequestPayload]
        CurrencyCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->currencyFacade->updateCurrency($id, $request),
            Response::HTTP_OK,
            'currency-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Delete(path: '/api/v1/currency/{id}')]
    public function deleteCurrencyAction(int $id): SuccessOutput
    {
        $this->currencyFacade->deleteCurrency($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'currency-deleted',
        );
    }

}
