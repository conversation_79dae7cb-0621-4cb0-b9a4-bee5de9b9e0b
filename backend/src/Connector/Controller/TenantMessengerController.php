<?php

declare(strict_types = 1);

namespace Pm\Connector\Controller;

use FOS\RestBundle\Controller\Annotations\Post;
use OpenApi\Attributes\Tag;
use Pm\Connector\Facade\EmergencyMessageFacade;
use Pm\Connector\Input\EmergencyMessageInput;
use Pm\Core\Controller\BaseController;
use Pm\Connector\HttpResponse\EmergencyMessagesOutput;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Facility\Exception\NoFacilityFoundException;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'TenantMessenger')]
class TenantMessengerController extends BaseController
{

    public function __construct(
        private readonly EmergencyMessageFacade $emergencyMessageFacade,
    )
    {
    }

    /**
     * @throws NoFacilityFoundException
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(EmergencyMessagesOutput::class, 'emergency-message-sent')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Post('/api/v1/tenant-messenger/send-emergency-message')]
    public function sendEmergencyMessageAction(
        #[MapRequestPayload]
        EmergencyMessageInput $input,
    ): SuccessOutput
    {
        $output = $this->emergencyMessageFacade->sendEmergencyMessages($input);

        return new SuccessOutput($output);
    }

}
