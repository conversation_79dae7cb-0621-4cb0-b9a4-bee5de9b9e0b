<?php

declare(strict_types = 1);

namespace Pm\Location\Repository;

use Doctrine\ORM\EntityManager;
use Pm\Location\Entity\City;
use Pm\Pops\Exception\NoCityFoundException;

class CityRepository
{

    public function __construct(
        private readonly EntityManager $entityManager,
    )
    {
    }

    /**
     * @throws NoCityFoundException
     */
    public function get(int $id): City
    {
        $city = $this->entityManager->createQueryBuilder()
            ->select('c')
            ->from(City::class, 'c')
            ->andWhere('c.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($city === null) {
            throw NoCityFoundException::byId($id);
        }

        return $city;
    }

}
