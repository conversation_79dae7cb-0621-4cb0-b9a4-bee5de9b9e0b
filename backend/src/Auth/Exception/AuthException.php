<?php

declare(strict_types = 1);

namespace Pm\Auth\Exception;

use Pm\Core\Exception\RuntimeException;
use Pm\Core\Http\ResponseStatusCode;

class AuthException extends RuntimeException
{

    private function __construct(string $message)
    {
        parent::__construct($message, null, ResponseStatusCode::UNAUTHORIZED_401);
    }

    public static function invalidCredentials(): self
    {
        return new self('Invalid credentials.');
    }

}
