<?php

declare(strict_types = 1);

namespace Pm\Auth\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Http\Event\LogoutEvent;

class LogOutSubscriber implements EventSubscriberInterface
{

    /**
     * @return array<string, string>
     */
    public static function getSubscribedEvents(): array
    {
        return [LogoutEvent::class => 'onLogout'];
    }

    public function onLogout(LogoutEvent $event): void
    {
        $response = $event->getResponse();
        $response?->headers->clearCookie('BEARER');
        $response?->headers->clearCookie('refresh_token');
    }

}
