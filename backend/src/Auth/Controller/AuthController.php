<?php

declare(strict_types = 1);

namespace Pm\Auth\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use OpenApi\Attributes\Tag;
use Pm\Auth\Facade\AuthFacade;
use Pm\Auth\HttpResponse\AuthInitResponse;
use Pm\Auth\Input\AuthTokenInit;
use Pm\Auth\Input\AuthTokenInput;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\Controller\SuccessOutput;
use Pm\User\Entity\User;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

#[Tag(name: 'Authentication')]
class AuthController extends BaseController
{

    private const AUTH_TOKEN_API_PATH = '/api/v1/auth/token';

    public function __construct(
        private readonly AuthFacade $authFacade,
    )
    {
    }

    #[DetailResponse(AuthInitResponse::class)]
    #[Post(path: '/api/v1/auth/init')]
    public function postInitAction(
        #[MapRequestPayload]
        AuthTokenInit $authTokenInput,
    ): SuccessOutput
    {
        $authInitResponse = $this->authFacade->getAuthInit($authTokenInput, self::AUTH_TOKEN_API_PATH);

        return new SuccessOutput($authInitResponse);
    }

    /**
     * Token is returned in the Set-Cookie header.
     */
    #[Post(path: self::AUTH_TOKEN_API_PATH)]
    public function postTokenAction(
        #[MapRequestPayload]
        AuthTokenInput $authTokenInput,
    ): Response
    {
        return new Response('', 204, [
            'Set-Cookie' => 'refresh_token=xxx; path=/; httponly | BEARER=xxx; path=/; httponly',
        ]);
    }

    #[Post(path: '/api/v1/auth/token/refresh')]
    public function postTokenRefreshAction(
        #[CurrentUser]
        ?User $user,
    ): SuccessOutput
    {
        return new SuccessOutput([], 204);
    }

    #[Get(path: '/api/v1/auth/logout')]
    public function logoutAction(): Response
    {
        return new Response(null, Response::HTTP_ACCEPTED);
    }

}
