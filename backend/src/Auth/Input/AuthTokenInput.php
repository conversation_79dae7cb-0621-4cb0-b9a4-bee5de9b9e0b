<?php

declare(strict_types = 1);

namespace Pm\Auth\Input;

use OpenApi\InputMapper\Input;
use SensitiveParameter;
use ShipMonk\InputMapper\Compiler\Validator\String\AssertStringLength;

class AuthTokenInput implements Input
{

    public function __construct(
        #[AssertStringLength(min: 4, max: 255)]
        public readonly string $identifier,
        #[AssertStringLength(min: 4, max: 255)]
        #[SensitiveParameter]
        public readonly string $password,
    )
    {
    }

}
