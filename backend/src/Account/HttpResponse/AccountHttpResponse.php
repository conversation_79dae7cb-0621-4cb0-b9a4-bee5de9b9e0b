<?php

declare(strict_types = 1);

namespace Pm\Account\HttpResponse;

use Pm\Account\Entity\Account;
use Pm\Core\Controller\ApiOutput;

readonly class AccountHttpResponse implements ApiOutput
{

    public int $id;

    public string $name;

    public bool $isActive;

    public function __construct(Account $account)
    {
        $this->id = $account->getId();
        $this->name = $account->getName();
        $this->isActive = $account->isActive();
    }

}
