<?php

declare(strict_types = 1);

namespace Pm\Account\Input;

use OpenApi\InputMapper\Input;
use Pm\User\Input\UserCreateUpdateInput;
use function get_object_vars;

readonly class AccountCreateUpdateInput implements Input
{

    /**
     * @param array<int, UserCreateUpdateInput> $users
     */
    public function __construct(
        private string $name,
        private array $users,
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return array<int, UserCreateUpdateInput>
     */
    public function getUsers(): array
    {
        return $this->users;
    }

}
