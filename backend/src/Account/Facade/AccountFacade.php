<?php

declare(strict_types = 1);

namespace Pm\Account\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Account\Entity\Account;
use Pm\Account\HttpResponse\AccountHttpResponse;
use Pm\Account\Input\AccountCreateUpdateInput;
use Pm\Account\Repository\AccountRepository;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Security\Security;
use Pm\Log\Service\LogService;
use Pm\Permission\Entity\PermissionGroup;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\Pops\Log\Event\AccountCreatedLog;
use Pm\User\Enum\UserRole;
use Pm\User\Facade\UserFacade;
use Symfony\Component\Validator\Exception\ValidationFailedException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use function count;

class AccountFacade
{

    public function __construct(
        private readonly EntityManager $entityManager,
        private readonly UserFacade $userFacade,
        private readonly AccountRepository $accountRepository,
        private readonly ValidatorInterface $validator,
        private readonly LogService $logService,
        private readonly Security $security,
    )
    {
    }

    public function createAccount(AccountCreateUpdateInput $accountCreate): AccountHttpResponse
    {
        /** @var Account $account */
        $account = $this->entityManager->wrapInTransaction(function () use ($accountCreate): Account {
            $accountName = $accountCreate->getName();
            $account = $this->accountRepository->findByName($accountName);

            if ($account instanceof Account) {
                throw new InvalidInputException('Account with given name already exists.');
            }

            $account = new Account($accountName);

            $this->entityManager->persist($account);

            $users = $accountCreate->getUsers();
            foreach ($users as $user) {
                /**
                 * first we need validate all users
                 */
                $validationErrors = $this->validator->validate($user);
                if (count($validationErrors) > 0) {
                    throw new ValidationFailedException('Validation failed.', $validationErrors);
                }
            }
            $incrementalNumber = 1;
            foreach ($users as $user) {
                $permissionGroup = new PermissionGroup('Permission Group ' . $incrementalNumber, $account);
                $this->entityManager->persist($permissionGroup);
                $permissionTemplate = new PermissionTemplate('Permission Template ' . $incrementalNumber, 'PT usage', $account);
                $this->entityManager->persist($permissionTemplate);
                $userRole = UserRole::from($user->getRole());
                $userEntity = $this->userFacade->createUser($user->getEmail(), $userRole, $user->getPassword(), $user->isActive(), $account);
                $permissions = $user->getPermissions();

                $facilityName = $user->getFacilityName();

                $this->userFacade->setPermissions($userEntity, $account, $permissions, $facilityName, $permissionTemplate, $permissionGroup);
                $incrementalNumber++;
            }

            $user = $this->security->getUser();

            $this->logService->log(new AccountCreatedLog('Account Created', $user, $accountCreate->toArray()));

            return $account;
        });

        return new AccountHttpResponse($account);
    }

}
