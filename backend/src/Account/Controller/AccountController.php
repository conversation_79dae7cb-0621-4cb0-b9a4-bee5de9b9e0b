<?php

declare(strict_types = 1);

namespace Pm\Account\Controller;

use FOS\RestBundle\Controller\Annotations\Post;
use Pm\Account\Facade\AccountFacade;
use Pm\Account\Input\AccountCreateUpdateInput;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\OptionalEndpoint;
use Pm\Core\Controller\SuccessOutput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

class AccountController extends BaseController
{

    public function __construct(
        private readonly AccountFacade $accountFacade,
    )
    {
    }

    #[OptionalEndpoint]
    #[Post(path: '/api/v1/create-account')]
    public function createAccountAction(
        #[MapRequestPayload]
        AccountCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->accountFacade->createAccount($request),
            Response::HTTP_CREATED,
            'account-created',
        );
    }

}
