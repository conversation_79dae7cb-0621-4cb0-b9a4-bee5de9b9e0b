<?php

declare(strict_types = 1);

namespace Pm\Notification\Enum;

use Override;
use Pm\Core\Doctrine\StringEnumType;

class NotificationTypeType extends StringEnumType
{

    final public const NAME = 'notification_type';

    #[Override]
    public function getEnumClass(): string
    {
        return NotificationType::class;
    }

    #[Override]
    public function getName(): string
    {
        return self::NAME;
    }

}
