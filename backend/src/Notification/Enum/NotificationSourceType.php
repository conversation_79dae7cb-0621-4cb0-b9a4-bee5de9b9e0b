<?php

declare(strict_types = 1);

namespace Pm\Notification\Enum;

use Override;
use Pm\Core\Doctrine\StringEnumType;

class NotificationSourceType extends StringEnumType
{

    final public const NAME = 'notification_source';

    #[Override]
    public function getEnumClass(): string
    {
        return NotificationSource::class;
    }

    #[Override]
    public function getName(): string
    {
        return self::NAME;
    }

}
