<?php

declare(strict_types = 1);

namespace Pm\Notification\Enum;

use Override;
use Pm\Core\Doctrine\IntEnumType;

class NotificationPriorityType extends IntEnumType
{

    final public const NAME = 'notification_priority';

    #[Override]
    public function getEnumClass(): string
    {
        return NotificationPriority::class;
    }

    #[Override]
    public function getName(): string
    {
        return self::NAME;
    }

}
