<?php

declare(strict_types = 1);

namespace Pm\Notification\Consumer;

use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use Override;
use PhpAmqpLib\Message\AMQPMessage;
use Pm\Core\Consumer\BaseConsumer;
use Pm\Core\Exception\InvalidInputException;
use Pm\Log\Logger;
use Pm\Notification\Exception\GatewaySendFailedException;
use Pm\Notification\Exception\GatewaySendFailedTooManyTimesException;
use Pm\Notification\Exception\NotificationNotFoundException;
use Pm\Notification\Service\SendService;

class EmailNotificationConsumer extends BaseConsumer
{

    public function __construct(
        private readonly Logger $logger,
        private readonly SendService $sendService,
    )
    {
    }

    #[Override]
    public function execute(AMQPMessage $msg): int
    {
        try {
            $args = $this->getInputArgs($msg);
            $this->sendService->sendEmailNotification($args);

            return ConsumerInterface::MSG_ACK;

        } catch (NotificationNotFoundException $e) {
            $this->logger->logInfoMessage($e->getMessage());
            return ConsumerInterface::MSG_REJECT;

        } catch (InvalidInputException $e) {
            $this->logger->logErrorException($e);
            return ConsumerInterface::MSG_REJECT;

            // @phpstan-ignore-next-line
        } catch (GatewaySendFailedTooManyTimesException $e) {
            // reject message if failed to send too many times
            $this->logger->logErrorException($e);
            return ConsumerInterface::MSG_REJECT;

            // @phpstan-ignore-next-line
        } catch (GatewaySendFailedException $e) {
            $this->logger->logErrorException($e);
            return ConsumerInterface::MSG_REJECT_REQUEUE;

        }
    }

}
