<?php

declare(strict_types = 1);

namespace Pm\Notification\EventSubscriber;

use Doctrine\ORM\EntityManager;
use Override;
use Pm\Log\Logger;
use Pm\Notification\Entity\Notification;
use Pm\Notification\Exception\GatewaySendFailedException;
use Pm\Notification\Exception\GatewaySendFailedTooManyTimesException;
use Pm\Notification\Notifier\Notification\FirebaseMessage;
use Pm\Notification\Notifier\Notification\SmsNotification;
use RuntimeException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Notifier\Event\FailedMessageEvent;
use Symfony\Component\Notifier\Message\SmsMessage;
use function is_int;

class FailedMessageEventListener implements EventSubscriberInterface
{

    public function __construct(
        private readonly int $maxSendAttempts,
        private readonly EntityManager $entityManager,
        private readonly Logger $logger,
    )
    {
    }

    /**
     * @return array<string, string|array{0: string, 1: int}|list<array{0: string, 1?: int}>>
     */
    #[Override]
    public static function getSubscribedEvents(): array
    {
        return [
            FailedMessageEvent::class => [
                ['onFailedMessageEvent', 10],
            ],
        ];
    }

    /**
     * @throws GatewaySendFailedException
     * @throws GatewaySendFailedTooManyTimesException
     */
    public function onFailedMessageEvent(FailedMessageEvent $event): void
    {
        $transport = $event->getMessage()->getTransport();

        /**
         * @var ?Notification $notification
         */
        $notification = null;

        switch ($transport) {
            case 'smsservice':
                /**
                 * @var SmsMessage $smsMessage
                 */
                $smsMessage = $event->getMessage();
                /**
                 * @var SmsNotification $smsNotification;
                 */
                $smsNotification = $smsMessage->getNotification();

                $notification = $smsNotification->getNotificationEntity();

                break;
            case 'firebase':
                /**
                 * @var FirebaseMessage $firebaseNotification
                 */
                $firebaseNotification = $event->getMessage();

                $notification = $firebaseNotification->getNotificationEntity();

                break;
        }

        if (!$notification instanceof Notification) {
            throw new RuntimeException('Missing Notification.');
        }

        $this->entityManager->wrapInTransaction(
            static function () use ($notification): void {
                $notification->sendFailed();
            },
        );

        $this->logger->logErrorMessage('Notifier Message Send Failed: #[' . $event->getError()->getMessage() . ']#');

        if (is_int($notification->getSendAttempts()) && $notification->getSendAttempts() >= $this->maxSendAttempts) {
            throw new GatewaySendFailedTooManyTimesException('Notification message has failed to send on the gateway (#[' . $notification->getType()->value . ']#) more times than allowed.', $event->getError());
        }

        throw new GatewaySendFailedException('Failed to send #[' . ($transport ?? '') . ']# notification on Gateway: #[' . $notification->getType()->value . ']# response code: #[' . $event->getError()->getCode() . ']#', $event->getError());
    }

}
