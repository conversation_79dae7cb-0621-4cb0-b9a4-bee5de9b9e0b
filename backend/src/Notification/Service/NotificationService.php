<?php

declare(strict_types = 1);

namespace Pm\Notification\Service;

use Doctrine\ORM\EntityManager;
use Pm\Core\Validator\Constraints\PhoneNumber as PhoneNumberConstraint;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;
use Pm\Log\Logger;
use Pm\Mobile\Entity\MobileSetting;
use Pm\Mobile\Repository\MobileSettingRepository;
use Pm\Notification\Entity\Notification;
use Pm\Notification\Enum\NotificationPriority;
use Pm\Notification\Enum\NotificationSource;
use Pm\Notification\Enum\NotificationType;
use Pm\Notification\Producer\EmailNotificationProducer;
use Pm\Notification\Producer\PushNotificationProducer;
use Pm\Notification\Producer\SmsNotificationProducer;
use Pm\Pops\Entity\PopsEvent;
use Pm\Reporting\Entity\ReportingSetting;
use Pm\Reporting\Repository\ReportingSettingRepository;
use Symfony\Component\Validator\Constraints\Email as EmailConstraint;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use function is_string;

class NotificationService
{

    public function __construct(
        private readonly Logger $logger,
        private readonly EntityManager $entityManager,
        private readonly ReportingSettingRepository $reportingSettingRepository,
        private readonly EmailNotificationProducer $emailNotificationProducer,
        private readonly SmsNotificationProducer $smsNotificationProducer,
        private readonly PushNotificationProducer $pushNotificationProducer,
        private readonly MobileSettingRepository $mobileSettingRepository,
        private readonly ValidatorInterface $validator,
    )
    {
    }

    public function popsEventCreated(PopsEvent $popsEvent): void
    {
        $this->entityManager->wrapInTransaction(function () use ($popsEvent): void {
            // find out who is interested in notification
            $reportingSettings = $this->reportingSettingRepository->findReportingForUsers(
                $popsEvent->getPops()->getFacility(),
                $popsEvent->getEventCategory()->getCode(),
            );
            // Reporting - email + sms + notification
            foreach ($reportingSettings as $reportingSetting) {
                // bit stored description of which method [mail,sms,push] notification to be send
                $sendingType = $reportingSetting->getSendingType();

                // resolve notification priority based on category type
                $priority = match ($popsEvent->getEventCategory()->getSeverity()) {
                    EventCategorySeverityEnum::MEDIUM => NotificationPriority::MEDIUM,
                    EventCategorySeverityEnum::CRITICAL => NotificationPriority::CRITICAL,
                    EventCategorySeverityEnum::LOW => NotificationPriority::LOW,
                    EventCategorySeverityEnum::NONE => NotificationPriority::LOW,
                };

                // prepare notification to be queued in rabbit MQ
                if (($sendingType & ReportingSetting::SENDING_TYPE_EMAIL) === ReportingSetting::SENDING_TYPE_EMAIL) {
                    $user = $reportingSetting->getReporting()->getUser();

                    // validate email
                    $emailConstraint = new EmailConstraint();
                    $errors = $this->validator->validate($user->getEmail(), $emailConstraint);
                    if (!is_string($user->getEmail()) && $errors->count() > 0) {
                        // invalid email
                        $this->logger->logErrorMessage("Notifier: Can not send 'EMAIL' notification. Invalid Email for User: " . $user->getId());
                        return;
                    }

                    $notification = new Notification(NotificationSource::POPS_EVENT, NotificationType::EMAIL, $priority, $reportingSetting->getReporting()->getUser(), $popsEvent);
                    $this->entityManager->persist($notification);
                    $this->emailNotificationProducer->sendMessage($notification);
                }

                if (($sendingType & ReportingSetting::SENDING_TYPE_SMS) === ReportingSetting::SENDING_TYPE_SMS) {
                    $user = $reportingSetting->getReporting()->getUser();

                    // validate phone number
                    $phoneNumberConstraint = new PhoneNumberConstraint();
                    $errors = $this->validator->validate($user->getCellPhone(), $phoneNumberConstraint);
                    if (!is_string($user->getCellPhone()) && $errors->count() > 0) {
                        // invalid phone number
                        $this->logger->logErrorMessage("Notifier: Can not send 'SMS' notification. Invalid Phone Number for User: " . $reportingSetting->getReporting()->getUser()->getId());
                        return;
                    }

                    $notification = new Notification(NotificationSource::POPS_EVENT, NotificationType::SMS, $priority, $reportingSetting->getReporting()->getUser(), $popsEvent);
                    $this->entityManager->persist($notification);
                    $this->smsNotificationProducer->sendMessage($notification);
                }

                if (($sendingType & ReportingSetting::SENDING_TYPE_NOTIFICATION) === ReportingSetting::SENDING_TYPE_NOTIFICATION) {
                    // check mobile notification settings
                    $mobileSetting = $this->mobileSettingRepository->findByUser($reportingSetting->getReporting()->getUser());

                    // validate User has mobile notification setup
                    // mobile settings from pm2, check user has selected to receive objects notifications
                    if ($mobileSetting instanceof MobileSetting && $mobileSetting->getObjects() === true) {
                        $notification = new Notification(NotificationSource::POPS_EVENT, NotificationType::SMS, $priority, $reportingSetting->getReporting()->getUser(), $popsEvent);
                        $this->entityManager->persist($notification);
                        $this->pushNotificationProducer->sendMessage($notification);
                    } else {
                        $this->logger->logInfoMessage("Notifier: Can not send 'PUSH' notification. User does not have mobile configuration. User: " . $reportingSetting->getReporting()->getUser()->getId());
                    }
                }
            }
        });
    }

}
