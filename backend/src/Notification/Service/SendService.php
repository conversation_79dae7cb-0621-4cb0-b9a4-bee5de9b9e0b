<?php

declare(strict_types = 1);

namespace Pm\Notification\Service;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Input\InputArgs;
use Pm\Log\Logger;
use Pm\Notification\Enum\NotificationSource;
use Pm\Notification\Exception\GatewaySendFailedException;
use Pm\Notification\Exception\GatewaySendFailedTooManyTimesException;
use Pm\Notification\Exception\NotificationNotFoundException;
use Pm\Notification\Notifier\Notification\SmsNotification;
use Pm\Notification\Repository\NotificationRepository;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Notifier\NotifierInterface;
use Symfony\Component\Notifier\Recipient\Recipient;
use function is_int;
use function is_string;

class SendService
{

    public function __construct(
        private readonly int $maxSendAttempts,
        private readonly Logger $logger,
        private readonly EntityManager $entityManager,
        private readonly ContentService $contentService,
        private readonly NotificationRepository $notificationRepository,
        private readonly MailerInterface $mailer,
        private readonly NotifierInterface $notifier,
        // phpcs:ignore Squiz.PHP.CommentedOutCode.Found
        // private readonly ChatterInterface $chatter,
        // private readonly MobileInfoRepository $mobileInfoRepository,
    )
    {
    }

    /**
     * @throws NotificationNotFoundException
     * @throws InvalidInputException
     */
    public function sendEmailNotification(InputArgs $inputArgs): void
    {
        $notificationId = $inputArgs->getInt('id');

        $this->entityManager->wrapInTransaction(function () use ($notificationId): void {
            $notification = $this->notificationRepository->getById($notificationId);

            $emailTo = $notification->getUser()->getEmail();
            if ($emailTo === null) {
                throw new InvalidInputException('Invalid Email.');
            }

            $email = $this->contentService->popsEventCreatedEmail($notification->getPopsEvent());

            $email->to($emailTo);

            try {
                $this->mailer->send($email);
            } catch (TransportExceptionInterface $e) {
                $notification->sendFailed();
                $this->entityManager->flush();
                $this->logger->logErrorMessage('Email Notification Send Failed: #[' . $e->getMessage() . ']#');

                if (is_int($notification->getSendAttempts()) && $notification->getSendAttempts() >= $this->maxSendAttempts) {
                    throw new GatewaySendFailedTooManyTimesException('Email Notification send failed too many times (#[' . $notification->getType()->value . ']#).', $e);
                }
                throw new GatewaySendFailedException('Failed to send email notification: #[' . $e->getMessage() . ']#', $e);
            }

            $notification->setSentAt(new DateTimeImmutable());

            $this->logger->logInfoMessage("Notifier: '" . $notification->getSource()->value . "' Email notification sent to: " . $notification->getUser()->getId());
        });
    }

    /**
     * @throws InvalidInputException
     * @throws NotificationNotFoundException
     */
    public function sendSmsNotification(InputArgs $inputArgs): void
    {
        $notificationId = $inputArgs->getInt('id');
        $notification = $this->notificationRepository->getById($notificationId);

        $phoneNumberFormatted = $notification->getPhoneNumberFormatted();
        if (!is_string($phoneNumberFormatted)) {
            throw new InvalidInputException('Invalid Phone Number.');
        }

        switch ($notification->getSource()) {
            case NotificationSource::POPS_EVENT:
                // get localized formatted message content
                $content = $this->contentService->popsEventCreatedSmsMessage($notification->getPopsEvent());
                break;
            default:
                throw new InvalidInputException('Undefined Source Type: #[' . $notification->getSource()->value . ']#');
        }

        // send message to mobile gateway
        $this->notifier->send(new SmsNotification($notification, $content), new Recipient('', $phoneNumberFormatted));

        $this->logger->logInfoMessage("Notifier: '" . $notification->getSource()->value . "' Sms notification sent to: " . $notification->getUser()->getId());
    }

    /**
     * @throws InvalidInputException
     * @throws NotificationNotFoundException
     * @phpcsSuppress Squiz.PHP.CommentedOutCode.Found
     */
    public function sendPushNotification(InputArgs $inputArgs): void
    {
        // phpcs:ignore Squiz.PHP.CommentedOutCode.Found
        /*
        $notificationId = $inputArgs->getInt('id');

        $notification = $this->notificationRepository->getById($notificationId);

        $mobileInfo = $this->mobileInfoRepository->findByUser($notification->getUser());

        if (!$mobileInfo instanceof MobileInfo) {
            throw new InvalidInputException('User does not have MobileInfo registered.');
        }

        // create and store mobilenotification
        $options = new FirebaseNotification($mobileInfo->getRegistrationToken(), []);

        $options->title($notification->getPopsEvent()->getEventCategory()->getLabel())
            ->clickAction('OPEN_' . strtoupper($notification->getSource()->value))
            // -> badge - unread count of mobilenotification //can not be implemented until sync to mobile app is updated to pm3 api
            ->data([
                'severity' => $notification->getPopsEvent()->getEventCategory()->getSeverity()->value,
                'type' => $notification->getSource()->value,
                'linkedId' => $notification->getPopsEvent()->getId(),// for pm2 app this should be synced pops_event.id not pops_event
            ]);

        $chatMessage = new FirebaseMessage($notification, $this->contentService->popsEventCreatedPushMessage($notification->getPopsEvent()), $options);

        $this->chatter->send($chatMessage);

        $this->logger->logInfoMessage("Notifier: '" . $notification->getSource()->value . "' Push notification sent to: " . $notification->getUser()->getId());
        */
    }

}
