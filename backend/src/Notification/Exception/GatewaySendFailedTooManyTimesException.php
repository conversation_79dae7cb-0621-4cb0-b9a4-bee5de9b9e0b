<?php

declare(strict_types = 1);

namespace Pm\Notification\Exception;

use Pm\Core\Exception\RuntimeException;
use Pm\Core\Http\ResponseStatusCode;
use Throwable;

class GatewaySendFailedTooManyTimesException extends RuntimeException
{

    public function __construct(
        string $message = '',
        ?Throwable $previous = null,
        int $code = ResponseStatusCode::BAD_REQUEST_400,
    )
    {
        parent::__construct($message, $previous, $code);
    }

}
