<?php

declare(strict_types = 1);

namespace Pm\User\HttpRequest;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\User\Facade\UserFacade;
use Pm\User\Validator\UserSortFields;

readonly class UserInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?int $offset = 0,
        public ?int $limit = UserFacade::PAGE_MAX_LIMIT,
        public ?array $filter = null,
        public ?Sort $sortMethod = null,
        public ?UserSortFields $sortBy = null,
        public ?string $search = null,
    )
    {
    }

}
