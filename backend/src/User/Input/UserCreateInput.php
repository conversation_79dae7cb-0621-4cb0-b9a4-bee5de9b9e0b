<?php

declare(strict_types = 1);

namespace Pm\User\Input;

use OpenApi\InputMapper\Input;
use Pm\User\Enum\UserRole;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;
use function get_object_vars;

readonly class UserCreateInput implements Input
{

    public function __construct(
        #[NotBlank(message: 'validation.field.required')]
        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private string $identifier,

        #[NotBlank(message: 'validation.field.required')]
        #[Type('string')]
        private string $password,

        #[NotBlank(message: 'validation.field.required')]
        #[Choice(callback: [UserRole::class, 'values'], message: 'validation.field.invalid_choice')]
        private string $role,

        #[Email(message: 'validation.email.invalid')]
        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $email = null,

        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $firstName = null,

        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $lastName = null,

        #[Length(max: 12, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $rc = null,

        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $title = null,

        #[Length(max: 45, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $taxNo = null,

        #[Length(max: 100, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $position = null,

        #[Length(max: 10, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $locale = null,

        #[Length(max: 45, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        private ?string $cellPhone = null,

        #[Type('bool')]
        private bool $active = true,
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function getIdentifier(): string
    {
        return $this->identifier;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function getRc(): ?string
    {
        return $this->rc;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getTaxNo(): ?string
    {
        return $this->taxNo;
    }

    public function getPosition(): ?string
    {
        return $this->position;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function getCellPhone(): ?string
    {
        return $this->cellPhone;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

}
