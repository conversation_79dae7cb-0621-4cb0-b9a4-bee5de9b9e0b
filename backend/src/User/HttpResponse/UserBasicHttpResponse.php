<?php

declare(strict_types = 1);

namespace Pm\User\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\User\Entity\User;
use ShipMonk\InputMapper\Compiler\Validator\String\AssertStringLength;

readonly class UserBasicHttpResponse implements ApiOutput
{

    public int $id;

    #[AssertStringLength(max: 255)]
    public ?string $personalNo;

    #[AssertStringLength(max: 255)]
    public string $identifier;

    #[AssertStringLength(max: 255)]
    public ?string $firstName;

    #[AssertStringLength(max: 255)]
    public ?string $lastName;

    public function __construct(User $user)
    {
        $this->id = $user->getId();
        $this->identifier = $user->getIdentifier();
        $this->firstName = $user->getFirstname();
        $this->lastName = $user->getLastName();
        $this->personalNo = $user->getPersonalNo();
    }

}
