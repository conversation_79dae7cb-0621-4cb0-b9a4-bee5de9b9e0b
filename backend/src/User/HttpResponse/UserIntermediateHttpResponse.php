<?php

declare(strict_types = 1);

namespace Pm\User\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\User\Entity\User;
use ShipMonk\InputMapper\Compiler\Validator\String\AssertStringLength;

readonly class UserIntermediateHttpResponse extends UserBasicHttpResponse implements ApiOutput
{

    #[AssertStringLength(max: 255)]
    public ?string $email;

    #[AssertStringLength(max: 12)]
    public ?string $rc;

    #[AssertStringLength(max: 255)]
    public ?string $title;

    #[AssertStringLength(max: 45)]
    public ?string $taxNo;

    #[AssertStringLength(max: 100)]
    public ?string $position;

    #[AssertStringLength(max: 10)]
    public ?string $locale;

    #[AssertStringLength(max: 45)]
    public ?string $cellPhone;

    public bool $active;

    public bool $readOnly;

    public function __construct(User $user)
    {
        parent::__construct($user);
        $this->email = $user->getEmail();
        $this->rc = $user->getRc();
        $this->title = $user->getTitle();
        $this->taxNo = $user->getTaxNo();
        $this->position = $user->getPosition();
        $this->locale = $user->getLocale();
        $this->cellPhone = $user->getCellPhone();
        $this->active = $user->isActive();
        $this->readOnly = $user->getOriginalDbId() !== null;
    }

}
