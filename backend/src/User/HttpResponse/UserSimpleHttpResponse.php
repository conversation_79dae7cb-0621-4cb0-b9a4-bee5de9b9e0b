<?php

declare(strict_types = 1);

namespace Pm\User\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\User\Entity\User;
use ShipMonk\InputMapper\Compiler\Validator\String\AssertStringLength;

readonly class UserSimpleHttpResponse extends UserBasicHttpResponse implements ApiOutput
{

    #[AssertStringLength(max: 255)]
    public ?string $email;

    public bool $readOnly;

    public function __construct(User $user)
    {
        parent::__construct($user);
        $this->email = $user->getEmail();
        $this->readOnly = $user->getOriginalDbId() !== null;
    }

}
