<?php

declare(strict_types = 1);

namespace Pm\User\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\User\Entity\User;
use Pm\User\Exception\NoUserFoundException;
use Pm\User\Facade\UserFacade;
use Pm\User\HttpRequest\ChangePasswordRequest;
use Pm\User\HttpRequest\UserInput;
use Pm\User\HttpRequest\UserProfilePutRequest;
use Pm\User\HttpResponse\UserSimpleHttpResponse;
use Pm\User\Input\UserCreateInput;
use Pm\User\Service\UserHttpResponseCreator;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

class UserController extends BaseController
{

    public function __construct(
        private readonly UserFacade $userFacade,
        private readonly UserHttpResponseCreator $userHttpResponseCreator,
    )
    {
    }

    /**
     * Get list of users
     *
     * @tags User
     * @throws InvalidInputException
     * @throws AclException
     */
    #[Get(path: '/api/v1/users')]
    public function getListAction(
        #[MapQueryString]
        ?UserInput $userInput,
    ): SuccessCountableOutput
    {
        if ($userInput === null) {
            $userInput = new UserInput();
        }
        /**
         * retrieve paginated list of facilities
         * @var PaginatedCollection<UserSimpleHttpResponse> $usersList
         */
        $usersList = $this->userFacade->getUserPaginatedListResponse($userInput);

        /**
         * @var list<UserSimpleHttpResponse> $list
         */
        $list = $usersList->getArrayCopy();
        return new SuccessCountableOutput($list, $usersList->getTotalCount());
    }

    /**
     * @throws AclException
     */
    #[Get(path: '/api/v1/user')]
    public function getDetailOfLoggedUser(
        #[CurrentUser]
        User $user,
    ): SuccessOutput
    {
        $user = $this->userFacade->getUser($user);

        return new SuccessOutput(
            $this->userHttpResponseCreator->create($user),
        );
    }

    /**
     * @throws AclException
     * @throws NoUserFoundException
     */
    #[Get(path: '/api/v1/user/{id}')]
    public function getDetailAction(int $id): SuccessOutput
    {
        $user = $this->userFacade->getUserById($id);

        return new SuccessOutput(
            $this->userHttpResponseCreator->create($user),
        );
    }

    /**
     * @tags User
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Post(path: '/api/v1/user')]
    public function createUserAction(
        #[MapRequestPayload]
        UserCreateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->userFacade->createUserFromInput($request),
            Response::HTTP_CREATED,
            'user-created',
        );
    }

    /**
     * @throws AclException
     * @throws NoUserFoundException
     * @throws InvalidInputException
     */
    #[Post(path: '/api/v1/user/{id}/password')]
    public function updatePasswordAction(
        int $id,
        #[MapRequestPayload]
        ChangePasswordRequest $changePassword,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->userFacade->updatePassword($id, $changePassword),
            Response::HTTP_OK,
            'password-updated',
        );
    }

    /**
     * @throws AclException
     * @throws NoUserFoundException
     */
    #[Put(path: '/api/v1/user/{id}')]
    public function updateUserAction(
        int $id,
        #[MapRequestPayload]
        UserProfilePutRequest $userProfileUpdate,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->userFacade->updateUser($id, $userProfileUpdate),
            Response::HTTP_OK,
            'user-profile-updated',
        );
    }

}
