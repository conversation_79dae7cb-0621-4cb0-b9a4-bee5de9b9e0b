<?php

declare(strict_types = 1);

namespace Pm\User\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Doctrine\ORM\Mapping\UniqueConstraint;
use InvalidArgumentException;
use Pm\Account\Entity\Account;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\BlameableInsertEntity;
use Pm\Core\Entity\Traits\BlameableUpdateEntity;
use Pm\Core\Entity\Traits\OriginalDbEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\Core\Entity\UniqueValidatableEntity;
use Pm\Core\Exception\LogicException;
use Pm\Log\Entity\LoggableEntity;
use Pm\Log\Enum\LoggableEntityName;
use Pm\Permission\Entity\PermissionGroup;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\User\Enum\UserRole;
use Pm\User\Enum\UserRoleType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use function trim;

#[Entity]
#[Table(name: '`user`')]
#[UniqueConstraint(name: 'user_unique_identifier', columns: ['identifier'], options: ['where' => '(identifier IS NOT NULL)'])]
#[UniqueEntity(fields: ['identifier'], message: 'validation.field.unique')]
#[UniqueEntity(fields: ['email'], message: 'validation.field.unique', ignoreNull: true)]
class User extends BaseEntity implements PasswordAuthenticatedUserInterface, UserInterface, LoggableEntity, UniqueValidatableEntity
{

    use BlameableInsertEntity;
    use BlameableUpdateEntity;
    use OriginalDbEntity;
    use TimestampableEntity;

    public const SUBJECT_TYPE_USER = 0;
    public const SUBJECT_TYPE_AGENCY = 1;
    public const SUBJECT_TYPE_CUSTOMER = 2;

    /**
     * @var Collection<int, PermissionGroup> $permissionGroups
     */
    #[ManyToMany(targetEntity: PermissionGroup::class, mappedBy: 'users')]
    private Collection $permissionGroups;

    /**
     * @var Collection<int, PermissionTemplate>
     */
    #[ManyToMany(targetEntity: PermissionTemplate::class, mappedBy: 'users', cascade: ['persist'])]
    private Collection $permissionTemplates;

    #[Column(type: Types::STRING, length: 50, nullable: false, options: ['default' => 'ROLE_USER'])]
    private string $permissionRole;

    #[Column(type: Types::STRING, length: 100, nullable: true)]
    private ?string $password;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $personalNo = null;

    #[Column(type: Types::STRING, length: 100, nullable: true)]
    private ?string $passwordCustomer = null;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $firstName = null;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $lastName = null;

    #[Column(type: Types::STRING, length: 12, nullable: true)]
    private ?string $rc = null;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $title = null;

    #[Column(type: Types::STRING, length: 45, nullable: true)]
    private ?string $taxNo = null;

    #[Column(type: Types::STRING, length: 100, nullable: true)]
    private ?string $position = null;

    #[Column(type: Types::STRING, length: 10, nullable: true)]
    private ?string $locale = null;

    /**
     * 0 = user
     * 1 = agency
     * 2 = customer
     */
    #[Column(type: Types::INTEGER, nullable: true)]
    private ?int $subjectType = null;

    #[Column(type: Types::BOOLEAN, nullable: false, options: ['default' => false])]
    private bool $isContact = false;

    #[Column(type: Types::STRING, nullable: false)]
    private string $identifier;

    #[Column(type: UserRoleType::NAME, nullable: false, options: ['default' => UserRole::USER])]
    private UserRole $role;

    #[Column(type: Types::BOOLEAN, nullable: false, options: ['default' => false])]
    private bool $active;

    /**
     * The original user password hash from PM2
     */
    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $pm2Password = null;

    /**
     * The original customer password hash from PM2
     */
    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $pm2PasswordCustomer = null;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $email = null;

    #[Column(type: Types::STRING, length: 45, nullable: true)]
    private ?string $cellPhone = null;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $externalId = null;

    #[ManyToOne(targetEntity: Account::class, inversedBy: 'users')]
    #[JoinColumn(nullable: false)]
    protected Account $account;

    /**
     * @var array<string>|null
     */
    protected ?array $permissionsCache = null;

    /**
     * @var array<int, array<string>>|null
     */
    protected ?array $facilityPermissionsCache = null;

    public function __construct(string $identifier, UserRole $role, string $password, string $permissionRole = 'ROLE_USER')
    {
        $this->permissionGroups = new ArrayCollection();
        $this->permissionTemplates = new ArrayCollection();
        $this->permissionRole = $permissionRole;
        if ($identifier === '') {
            throw new InvalidArgumentException('The user identifier cannot be empty.');
        }
        $this->identifier = $identifier;
        $this->role = $role;
        $this->password = $password;
        $this->active = true;
    }

    public function getLoggableName(): LoggableEntityName
    {
        return LoggableEntityName::User;
    }

    /**
     * @return list<string>
     */
    public function getRoles(): array
    {
        return [$this->role->name];
    }

    public function eraseCredentials(): void
    {
    }

    public function getUserIdentifier(): string
    {
        if ($this->identifier === '') {
            throw new LogicException('The user identifier cannot be empty.');
        }
        return $this->identifier;
    }

    public function getIdentifier(): string
    {
        return $this->identifier;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(?string $password): void
    {
        $this->password = $password;
    }

    public function getPasswordCustomer(): ?string
    {
        return $this->passwordCustomer;
    }

    public function setPasswordCustomer(?string $passwordCustomer): void
    {
        $this->passwordCustomer = $passwordCustomer;
    }

    public function getFirstname(): ?string
    {
        return $this->firstName;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function getRc(): ?string
    {
        return $this->rc;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getTaxNo(): ?string
    {
        return $this->taxNo;
    }

    public function getPosition(): ?string
    {
        return $this->position;
    }

    public function getPersonalNo(): ?string
    {
        return $this->personalNo;
    }

    public function setPersonalNo(?string $personalNo): void
    {
        $this->personalNo = $personalNo;
    }

    public function setFirstName(?string $firstName): void
    {
        $this->firstName = $firstName;
    }

    public function setLastName(?string $lastName): void
    {
        $this->lastName = $lastName;
    }

    public function setRc(?string $rc): void
    {
        $this->rc = $rc;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function setTaxNo(?string $taxNo): void
    {
        $this->taxNo = $taxNo;
    }

    public function setPosition(?string $position): void
    {
        $this->position = $position;
    }

    public function getPermissionRole(): string
    {
        return $this->permissionRole;
    }

    public function setPermissionRole(string $permissionRole): void
    {
        $this->permissionRole = $permissionRole;
    }

    public function getSubjectType(): ?int
    {
        return $this->subjectType;
    }

    public function setSubjectType(?int $subjectType): void
    {
        $this->subjectType = $subjectType;
    }

    public function getIsContact(): bool
    {
        return $this->isContact;
    }

    public function setIsContact(bool $isContact): void
    {
        $this->isContact = $isContact;
    }

    /**
     * @return array<int, PermissionGroup>
     */
    public function getPermissionGroups(): array
    {
        return $this->permissionGroups->toArray();
    }

    /**
     * @return array<int, PermissionTemplate>
     */
    public function getPermissionTemplates(): array
    {
        return $this->permissionTemplates->toArray();
    }

    public function addPermissionTemplate(PermissionTemplate $permissionTemplate): void
    {
        if (!$this->permissionTemplates->contains($permissionTemplate)) {
            $this->permissionTemplates->add($permissionTemplate);
            $permissionTemplate->addUser($this);
        }
    }

    public function getFullName(): string
    {
        $fullName = $this->getFirstname() ?? '';
        $fullName .= ' ' . ($this->getLastName() ?? '');

        return trim($fullName);
    }

    public function enable(): void
    {
        $this->active = true;
    }

    public function disable(): void
    {
        $this->active = false;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function getRole(): UserRole
    {
        return $this->role;
    }

    public function setRole(UserRole $role): void
    {
        $this->role = $role;
    }

    public function getPm2Password(): ?string
    {
        return $this->pm2Password;
    }

    public function setPm2Password(?string $pm2Password): void
    {
        $this->pm2Password = $pm2Password;
    }

    public function setIdentifier(string $identifier): void
    {
        $this->identifier = $identifier;
    }

    public function getPm2PasswordCustomer(): ?string
    {
        return $this->pm2PasswordCustomer;
    }

    public function setPm2PasswordCustomer(?string $pm2PasswordCustomer): void
    {
        $this->pm2PasswordCustomer = $pm2PasswordCustomer;
    }

    public function fromLegacyUser(): self
    {
        if ($this->getPersonalNo() === null) {
            throw new InvalidArgumentException('Personal number is required for user.');
        }
        if ($this->getPassword() === null) {
            throw new InvalidArgumentException('User password is required.');
        }

        $this->enable();
        $this->setPm2Password($this->getPassword());
        $this->setRole(UserRole::USER);
        $this->setIdentifier((string) $this->getPersonalNo());

        return $this;
    }

    public function fromLegacyCustomer(): self
    {
        if ($this->getEmail() === null || $this->getEmail() === '') {
            throw new InvalidArgumentException('Customer email is required.');
        }
        if ($this->getPasswordCustomer() === null) {
            throw new InvalidArgumentException('Customer password is required.');
        }

        $this->enable();
        $this->setPm2PasswordCustomer((string) $this->getPasswordCustomer());
        $this->setPassword($this->getPasswordCustomer());
        $this->setRole(UserRole::CUSTOMER);
        $this->setIdentifier((string) $this->getEmail());

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getCellPhone(): ?string
    {
        return $this->cellPhone;
    }

    public function setCellPhone(?string $cellPhone): void
    {
        $this->cellPhone = $cellPhone;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(?string $locale): void
    {
        $this->locale = $locale;
    }

    /**
     * @return array<string>|null
     */
    public function getPermissionsCache(): ?array
    {
        return $this->permissionsCache;
    }

    /**
     * @param array<string>|null $permissionsCache
     */
    public function setPermissionsCache(?array $permissionsCache): void
    {
        $this->permissionsCache = $permissionsCache;
    }

    /**
     * @return array<int, array<string>>|null
     */
    public function getFacilityPermissionsCache(): ?array
    {
        return $this->facilityPermissionsCache;
    }

    /**
     * @param array<int, array<string>>|null $facilityPermissionsCache
     */
    public function setFacilityPermissionsCache(?array $facilityPermissionsCache): void
    {
        $this->facilityPermissionsCache = $facilityPermissionsCache;
    }

}
