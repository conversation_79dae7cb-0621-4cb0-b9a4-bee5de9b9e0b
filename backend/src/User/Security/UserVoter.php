<?php

declare(strict_types = 1);

namespace Pm\User\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Core\Security\Security;
use Pm\Facility\Entity\Facility;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Permission\Facade\PermissionFacade;
use Pm\Permission\Repository\PermissionRepository;
use Pm\User\Entity\User;

readonly class UserVoter extends BaseVoter
{

    public function __construct(
        Security $security,
        private PermissionFacade $permissionFacade,
        PermissionRepository $permissionRepository,
    )
    {
        parent::__construct($security, $permissionRepository);
    }

    /**
     * @throws AclException
     */
    public function canViewUserDetail(User $user): void
    {
        if ($this->canAccessUser($user) || $this->hasPermission(PermissionEnum::PERM_USER_READ)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdatePassword(User $user): void
    {
        if ($this->canAccessUser($user) || $this->hasPermission(PermissionEnum::PERM_USER_WRITE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdateUser(User $user): void
    {
        if ($this->canAccessUser($user) || $this->hasPermission(PermissionEnum::PERM_USER_WRITE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canCreate(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_USER_WRITE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function hasPermissionForFacility(Facility $facility, PermissionEnum $permissionEnum): bool
    {
        if ($this->permissionFacade->hasPermissionForFacility($this->getUser(), $facility, $permissionEnum)) {
            return true;
        }

        throw new AclException();
    }

}
