<?php

declare(strict_types = 1);

namespace Pm\Facility\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\Facility\Entity\FacilityGroup;
use Pm\User\HttpResponse\AccountHttpResponse;

readonly class FacilityGroupHttpResponse implements ApiOutput
{

    public int $id;

    public string $name;

    public ?string $usage;

    public ?DateTimeImmutable $createdAt;

    public ?DateTimeImmutable $updatedAt;

    public AccountHttpResponse $account;

    public bool $readOnly;

    /**
     * @var array<FacilitySimpleHttpResponse>
     */
    public array $facilities;

    public function __construct(FacilityGroup $facilityGroup)
    {
        $this->id = $facilityGroup->getId();
        $this->name = $facilityGroup->getName();
        $this->usage = $facilityGroup->getUsage();
        $this->createdAt = $facilityGroup->getCreatedAt();
        $this->updatedAt = $facilityGroup->getUpdatedAt();
        $this->account = new AccountHttpResponse($facilityGroup->getAccount());
        $this->readOnly = $facilityGroup->getOriginalDbId() !== null;

        $facilities = [];
        foreach ($facilityGroup->getFacilities() as $facility) {
            $facilities[] = new FacilitySimpleHttpResponse($facility);
        }
        $this->facilities = $facilities;
    }

}
