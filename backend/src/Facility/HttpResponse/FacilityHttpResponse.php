<?php

declare(strict_types = 1);

namespace Pm\Facility\HttpResponse;

use DateTimeImmutable;
use Pm\Company\HttpResponse\CompanyResponse;
use Pm\Core\Controller\ApiOutput;
use Pm\Core\Service\AttachmentService;
use Pm\Facility\Entity\Facility;
use Pm\Permission\Enum\PermissionEnum;
use Pm\User\Entity\User;
use function array_column;
use function in_array;

class FacilityHttpResponse implements ApiOutput
{

    public readonly int $id;

    /**
     * @var array<string>
     */
    public readonly array $permissions;

    public readonly string $name;

    public readonly string $code;

    public readonly bool $isActive;

    public readonly bool $isPopsAlert;

    public readonly ?int $popsAlertInterval;

    public readonly ?DateTimeImmutable $createdAt;

    public readonly ?DateTimeImmutable $updatedAt;

    public readonly string $attachmentResolution;

    public readonly ?CompanyResponse $company;

    public readonly ?int $contractId;

    public readonly ?string $contractName;

    public readonly ?int $projectId;

    public readonly ?string $projectName;

    public readonly ?int $legislationId;

    public readonly ?string $legislationName;

    public readonly ?int $unitId;

    public readonly ?string $street;

    public readonly ?string $city;

    public readonly ?string $postalCode;

    public readonly ?string $country;

    public readonly ?string $customerName;

    public readonly ?string $customerStreet;

    public readonly ?string $customerCity;

    public readonly ?string $customerPostalCode;

    public readonly ?string $customerCountry;

    public readonly bool $readOnly;

    public function __construct(Facility $facility, User $user)
    {
        $this->id = $facility->getId();

        $permissions = [];
        $enabledPermissions = array_column(PermissionEnum::cases(), 'name');
        foreach ($facility->getPermissionGroups() as $permissionGroup) {
            if (
                $permissionGroup->hasUser($user)
                &&
                !$permissionGroup->getPermissionTemplates()->isEmpty()

            ) {
                foreach ($permissionGroup->getPermissionTemplates() as $permissionTemplate) {
                    foreach ($permissionTemplate->getPermissions() as $permission) {
                        $p = $permission->getCode()->value;
                        if (in_array($p, $enabledPermissions, true) && !in_array($p, $permissions, true)) {
                            $permissions[] = $p;
                        }
                    }
                }
            }
        }
        $this->permissions = $permissions;
        $this->name = $facility->getName();
        $this->code = $facility->getCode();
        $this->isActive = $facility->isActive();
        $this->isPopsAlert = $facility->isIsPopsAlert();
        $this->popsAlertInterval = $facility->getPopsAlertInterval();
        $this->createdAt = $facility->getCreatedAt();
        $this->updatedAt = $facility->getUpdatedAt();

        $this->attachmentResolution = $facility->getAttachmentResolution() ?? AttachmentService::MAX_DIMENSIONS;

        $company = $facility->getCompany();
        $this->company = $company !== null ? new CompanyResponse($company) : null;

        $contract = $facility->getContract();
        $this->contractId = $contract?->getId();
        $this->contractName = $contract?->getName();

        $project = $facility->getProject();
        $this->projectId = $project?->getId();
        $this->projectName = $project?->getName();

        $legislation = $facility->getLegislation();
        $this->legislationId = $legislation?->getId();
        $this->legislationName = $legislation?->getName();

        $unit = $facility->getUnit();
        $this->unitId = $unit?->getId();

        $this->street = $facility->getStreet();
        $this->city = $facility->getCity();
        $this->postalCode = $facility->getPostalCode();
        $this->country = $facility->getCountry();

        $this->customerName = $facility->getCustomerName();
        $this->customerStreet = $facility->getCustomerStreet();
        $this->customerCity = $facility->getCustomerCity();
        $this->customerPostalCode = $facility->getCustomerPostalCode();
        $this->customerCountry = $facility->getCustomerCountry();

        $this->readOnly = $facility->getOriginalDbId() !== null;
    }

}
