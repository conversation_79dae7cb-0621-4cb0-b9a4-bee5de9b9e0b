<?php

declare(strict_types = 1);

namespace Pm\Facility\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Facility\Entity\Facility;

class FacilitySimpleHttpResponse implements ApiOutput
{

    public readonly int $id;

    public readonly string $name;

    public readonly string $code;

    public function __construct(Facility $facility)
    {
        $this->id = $facility->getId();
        $this->name = $facility->getName();
        $this->code = $facility->getCode();
    }

}
