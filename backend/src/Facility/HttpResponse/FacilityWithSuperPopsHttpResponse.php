<?php

declare(strict_types = 1);

namespace Pm\Facility\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Facility\Entity\Facility;
use Pm\Pops\HttpResponse\PopsHttpResponse;
use Pm\SuperPops\HttpResponse\SuperPopsSimpleHttpResponse;

readonly class FacilityWithSuperPopsHttpResponse implements ApiOutput
{

    public int $id;

    public string $name;

    public string $code;

    public function __construct(Facility $facility, public ?SuperPopsSimpleHttpResponse $superPopsLinked = null, public ?PopsHttpResponse $lastPops = null)
    {
        $this->id = $facility->getId();
        $this->name = $facility->getName();
        $this->code = $facility->getCode();
    }

}
