<?php

declare(strict_types = 1);

namespace Pm\Facility\Repository;

use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Account\Entity\Account;
use Pm\Core\Enum\Sort;
use Pm\Core\Repository\BaseRepository;
use Pm\Facility\Entity\FacilityGroup;
use Pm\Facility\Exception\NoFacilityGroupFoundException;
use Pm\Facility\FilterInput\FacilityGroupFilterInput;
use Pm\Facility\Input\FacilityGroupInput;
use Pm\Facility\Validator\FacilityGroupSortFields;
use Pm\Tools\SortingHelper;

class FacilityGroupRepository extends BaseRepository
{

    /**
     * @throws NoFacilityGroupFoundException
     */
    public function getById(int $id): FacilityGroup
    {
        $facilityGroup = $this->entityManager->createQueryBuilder()
            ->select('fg')
            ->from(FacilityGroup::class, 'fg')
            ->andWhere('fg.id = :id')
            ->setParameter('id', $id)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
        if ($facilityGroup === null) {
            throw NoFacilityGroupFoundException::byId($id);
        }

        return $facilityGroup;
    }

    /**
     * @param array<int> $idList
     * @return array<FacilityGroup>
     */
    public function getByIdList(array $idList): array
    {
        $queryBuilder = $this->entityManager->createQueryBuilder();

        return $queryBuilder->select('fg')
            ->from(FacilityGroup::class, 'fg')
            ->andWhere($queryBuilder->expr()->in('fg.id', ':idList'))
            ->setParameter('idList', $idList)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Paginator<FacilityGroup>
     */
    public function findAllMatchingPaginated(FacilityGroupInput $facilityGroupInput, ?Account $account): Paginator
    {
        $queryBuilder = $this->getQueryBuilder();

        if ($facilityGroupInput->search !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('fg.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->orWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('fg.usage'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $facilityGroupInput->search . '%');
        }

        $this->applyCriteria(
            $account,
            $queryBuilder,
            new FacilityGroupFilterInput($facilityGroupInput->filter),
            $facilityGroupInput->limit,
            $facilityGroupInput->offset,
            SortingHelper::sanitizeSortBy($facilityGroupInput->sortBy, 'fg', FacilityGroupSortFields::Name->value),
            SortingHelper::sanitizeSortMethod($facilityGroupInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    private function getQueryBuilder(): QueryBuilder
    {
        return $this->entityManager->createQueryBuilder()
            ->select('fg')
            ->from(FacilityGroup::class, 'fg');
    }

}
