<?php

declare(strict_types = 1);

namespace Pm\Facility\Controller;

use Doctrine\ORM\NonUniqueResultException;
use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\DeleteConstraintException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Facility\Exception\NoFacilityFoundException;
use Pm\Facility\Facade\FacilityFacade;
use Pm\Facility\HttpResponse\FacilityWithPopsHttpResponse;
use Pm\Facility\HttpResponse\FacilityWithSuperPopsHttpResponse;
use Pm\Facility\Input\FacilityCreateInput;
use Pm\Facility\Input\FacilityInput;
use Pm\Facility\Input\FacilityUpdateInput;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Pops\Exception\NoSuperPopsFoundException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Facility')]
class FacilityController extends BaseController
{

    public function __construct(
        private readonly FacilityFacade $facilityFacade,
    )
    {
    }

    /**
     * Get Facility by ID
     *
     * @throws AclException
     * @throws NoFacilityFoundException
     */
    #[DetailResponse(FacilityHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/facility/{facilityId}')]
    public function getAction(
        int $facilityId,
    ): SuccessOutput
    {
        $facilityResponse = $this->facilityFacade->get($facilityId);

        return new SuccessOutput($facilityResponse);
    }

    /**
     * Get list of facilities - use logged user for permissions limiting listed items
     *
     * @throws InvalidInputException
     * @throws AclException
     */
    #[ListResponse(FacilityWithPopsHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/facility')]
    public function getListAction(
        #[MapQueryString]
        ?FacilityInput $facilityInput,
    ): SuccessCountableOutput
    {
        if ($facilityInput === null) {
            $facilityInput = new FacilityInput();
        }
        /**
         * retrieve paginated list of facilities
         * @var PaginatedCollection<FacilityWithPopsHttpResponse> $facilityList
         */
        $facilityList = $this->facilityFacade->getFacilityPaginatedListResponse($facilityInput);

        /**
         * @var list<FacilityWithPopsHttpResponse> $list
         */
        $list = $facilityList->getArrayCopy();
        return new SuccessCountableOutput($list, $facilityList->getTotalCount());
    }

    /**
     * Get list of facilities filtered by permission
     *
     * @throws InvalidInputException
     * @throws AclException
     */
    #[ListResponse(FacilitySimpleHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/facilities-by-permission/{permission}')]
    public function getListFacilitiesByPermissionAction(
        PermissionEnum $permission,
    ): SuccessOutput
    {
        $facilityList = $this->facilityFacade->getFacilityListByPermission($permission);

        return new SuccessOutput($facilityList);
    }

    /**
     * Get list of facilities - with linked super pops
     *
     * @throws InvalidInputException
     * @throws AclException
     * @throws NonUniqueResultException
     * @throws NoSuperPopsFoundException
     */
    #[ListResponse(FacilityWithSuperPopsHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/facility-with-super-pops')]
    public function getSimpleListAction(
        #[MapQueryString]
        ?FacilityInput $facilityInput,
    ): SuccessCountableOutput
    {
        if ($facilityInput === null) {
            $facilityInput = new FacilityInput();
        }
        /**
         * retrieve paginated list of facilities
         * @var PaginatedCollection<FacilityWithSuperPopsHttpResponse> $facilityList
         */
        $facilityList = $this->facilityFacade->getFacilityWithSuperPopsPaginatedListResponse($facilityInput);

        /**
         * @var list<FacilityWithSuperPopsHttpResponse> $list
         */
        $list = $facilityList->getArrayCopy();
        return new SuccessCountableOutput($list, $facilityList->getTotalCount());
    }

    /**
     * Create a new facility
     *
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(FacilityHttpResponse::class, 'facility-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/facility')]
    public function createFacilityAction(
        #[MapRequestPayload]
        FacilityCreateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->facilityFacade->createFacilityFromInput($request),
            Response::HTTP_CREATED,
            'facility-created',
        );
    }

    /**
     * Update an existing facility
     *
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityFoundException
     */
    #[DetailResponse(FacilityHttpResponse::class, 'facility-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/facility/{id}')]
    public function updateFacilityAction(
        int $id,
        #[MapRequestPayload]
        FacilityUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->facilityFacade->updateFacility($id, $request),
            Response::HTTP_OK,
            'facility-updated',
        );
    }

    /**
     * @throws NoFacilityFoundException
     * @throws AclException
     * @throws DeleteConstraintException
     */
    #[DeletedResponse(message: 'facility-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/facility/{id}')]
    public function deleteFacilityAction(int $id): SuccessOutput
    {
        $this->facilityFacade->deleteFacility($id);

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'facility-deleted',
        );
    }

}
