<?php

declare(strict_types = 1);

namespace Pm\Facility\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class FacilityGroupFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_NAME,
        self::FILTER_ID,
        self::FILTER_USAGE,
    ];

    final public const FILTER_NAME = 'name';
    final public const FILTER_USAGE = 'usage';
    final public const FILTER_ID = 'id';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID => Types::INTEGER,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
