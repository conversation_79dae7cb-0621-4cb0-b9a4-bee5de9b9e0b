<?php

declare(strict_types = 1);

namespace Pm\Facility\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class FacilityFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_NAME,
        self::FILTER_ID,
        self::FILTER_PERMISSION_USER,
        self::FILTER_STARTS_AT,
        self::FILTER_ENDS_AT,
        self::FILTER_COUNT_OF_CRITICAL_EVENTS,
        self::FILTER_COUNT_OF_MEDIUM_EVENTS,
        self::FILTER_COUNT_OF_LOW_EVENTS,
        self::FILTER_COUNT_OF_INTERNAL_EVENTS,
    ];

    final public const FILTER_NAME = 'name';
    final public const FILTER_ID = 'id';

    /**
     * filter facilities by user_id, where user belongs to permission group with permission PermissionEnum::PERM_OBJECT_SHOW
     */
    final public const FILTER_PERMISSION_USER = 'permissionUser.id';


    final public const FILTER_STARTS_AT = 'last_pops.startsAt';
    final public const FILTER_ENDS_AT = 'last_pops.endsAt';


    final public const FILTER_COUNT_OF_CRITICAL_EVENTS = 'last_pops_stats.criticalEventsCount';
    final public const FILTER_COUNT_OF_MEDIUM_EVENTS = 'last_pops_stats.mediumEventsCount';
    final public const FILTER_COUNT_OF_LOW_EVENTS = 'last_pops_stats.lowEventsCount';
    final public const FILTER_COUNT_OF_INTERNAL_EVENTS = 'last_pops_stats.internalEventsCount';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID => Types::INTEGER,
            self::FILTER_PERMISSION_USER => Types::INTEGER,
            self::FILTER_STARTS_AT => Types::DATE_IMMUTABLE,
            self::FILTER_ENDS_AT => Types::DATE_IMMUTABLE,
            self::FILTER_COUNT_OF_CRITICAL_EVENTS => Types::INTEGER,
            self::FILTER_COUNT_OF_LOW_EVENTS => Types::INTEGER,
            self::FILTER_COUNT_OF_MEDIUM_EVENTS => Types::INTEGER,
            self::FILTER_COUNT_OF_INTERNAL_EVENTS => Types::INTEGER,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
