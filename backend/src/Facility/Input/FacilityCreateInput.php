<?php

declare(strict_types = 1);

namespace Pm\Facility\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

readonly class FacilityCreateInput implements Input
{

    public function __construct(
        #[NotBlank]
        #[Length(min: 1, max: 255)]
        public string $name,

        #[NotBlank]
        #[Length(min: 1, max: 255)]
        public string $code,

        public ?int $companyId = null,

        public ?int $contractId = null,
        public ?int $projectId = null,
        public ?int $legislationId = null,
        public ?int $unitId = null,

        #[Length(max: 255)]
        public ?string $street = null,

        #[Length(max: 255)]
        public ?string $city = null,

        #[Length(max: 20)]
        public ?string $postalCode = null,

        #[Length(max: 50)]
        public ?string $country = null,

        #[Length(max: 255)]
        public ?string $customerName = null,

        #[Length(max: 255)]
        public ?string $customerStreet = null,

        #[Length(max: 255)]
        public ?string $customerCity = null,

        #[Length(max: 20)]
        public ?string $customerPostalCode = null,

        #[Length(max: 50)]
        public ?string $customerCountry = null,

        public bool $isActive = true,
        public bool $isPopsAlert = false,
        public ?int $popsAlertInterval = null,

        public ?string $attachmentResolution = null,


    )
    {
    }

}
