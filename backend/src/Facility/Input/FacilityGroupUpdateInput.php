<?php

declare(strict_types = 1);

namespace Pm\Facility\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;

readonly class FacilityGroupUpdateInput implements Input
{

    /**
     * @param array<int, int>|null $facilities
     */
    public function __construct(
        #[Length(min: 1, max: 255)]
        public ?string $name,
        public ?array $facilities,
        public ?string $usage = null,
    )
    {
    }

}
