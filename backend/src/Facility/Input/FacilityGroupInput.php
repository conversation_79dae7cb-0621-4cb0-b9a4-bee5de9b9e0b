<?php

declare(strict_types = 1);

namespace Pm\Facility\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Facility\Facade\FacilityGroupFacade;
use Pm\Facility\Validator\FacilityGroupSortFields;

readonly class FacilityGroupInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?int $offset = 0,
        public ?int $limit = FacilityGroupFacade::PAGE_MAX_LIMIT,
        public ?array $filter = null,
        public ?Sort $sortMethod = null,
        public ?FacilityGroupSortFields $sortBy = null,
        public ?string $search = null,
    )
    {
    }

}
