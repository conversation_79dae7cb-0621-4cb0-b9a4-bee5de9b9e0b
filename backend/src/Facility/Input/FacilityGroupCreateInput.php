<?php

declare(strict_types = 1);

namespace Pm\Facility\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

readonly class FacilityGroupCreateInput implements Input
{

    /**
     * @param array<int, int> $facilities
     */
    public function __construct(
        #[NotBlank]
        #[Length(min: 1, max: 255)]
        public string $name,
        public array $facilities,
        public ?string $usage = null,
    )
    {
    }

}
