<?php

declare(strict_types = 1);

namespace Pm\Facility\Entity;

use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\OriginalDbEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\EventCategory\Entity\EventCategory;

#[Table(name: 'favorite_event_category')]
#[UniqueConstraint(name: 'favorite_event_category_unique_fk', columns: ['facility_id', 'event_category_id', 'account_id'])]
#[Entity]
class FavoriteEventCategory extends BaseEntity
{

    use TimestampableEntity;
    use OriginalDbEntity;

    #[ManyToOne(targetEntity: Facility::class, inversedBy: 'favorites')]
    #[JoinColumn(nullable: false)]
    private Facility $facility;

    #[ManyToOne(targetEntity: EventCategory::class)]
    #[JoinColumn(nullable: false)]
    private EventCategory $eventCategory;

    public function __construct(Facility $facility, EventCategory $eventCategory)
    {
        $this->facility = $facility;
        $this->eventCategory = $eventCategory;
    }

    public function getFacility(): Facility
    {
        return $this->facility;
    }

    public function setFacility(Facility $facility): void
    {
        $this->facility = $facility;
    }

    public function getEventCategory(): EventCategory
    {
        return $this->eventCategory;
    }

    public function setEventCategory(EventCategory $eventCategory): void
    {
        $this->eventCategory = $eventCategory;
    }

}
