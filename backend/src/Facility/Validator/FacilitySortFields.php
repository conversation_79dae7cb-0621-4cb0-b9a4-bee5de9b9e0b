<?php

declare(strict_types = 1);

namespace Pm\Facility\Validator;

use BackedEnum;

/**
 * @implements BackedEnum<string>
 */
enum FacilitySortFields: string
{

    case Id = 'id';
    case CreatedAt = 'created_at';
    case Name = 'name';
    case CriticalEventsCount = 'last_pops_stats.critical_events_count';
    case MediumEventsCount = 'last_pops_stats.medium_events_count';
    case LowEventsCount = 'last_pops_stats.low_events_count';
    case InternalEventsCount = 'last_pops_stats.internal_events_count';

}
