<?php

declare(strict_types = 1);

namespace Pm\Core\Http;

use Pm\Core\Controller\ApiOutput;
use Pm\Core\Entity\Observer\EntityObserver;
use Pm\Core\Entity\Observer\ObservedEntityInterface;
use Pm\Core\Exception\LogicException;
use Pm\Pops\Entity\Pops;
use Pm\Pops\Entity\PopsEvent;
use Pm\Pops\HttpResponse\PopsEventAttachmentObserverResponse;
use Pm\Pops\HttpResponse\PopsEventObserverResponse;
use Pm\Pops\HttpResponse\PopsObserverResponse;

readonly class ObserverResponseCreatorFactory
{

    /**
     * @throws LogicException
     */
    public function create(string $class, ObservedEntityInterface $entity, string $event, ?string $deletedOriginalDbId = null): ApiOutput
    {
        if ($event === EntityObserver::EVENT_DELETE_ATTACHMENT) {
            return $this->getPopsEventAttachmentObserverResponse($entity, $deletedOriginalDbId);
        }

        return match ($class) {
            Pops::class => $this->getPopsObserverResponse($entity),
            PopsEvent::class => $this->getPopsEventObserverResponse($entity),
            default => throw new LogicException('Unsupported entity.'),
        };
    }

    private function getPopsObserverResponse(ObservedEntityInterface $entity): PopsObserverResponse
    {
        if (!$entity instanceof Pops) {
            throw new LogicException('Unsupported entity for PopsObserverResponse.');
        }

        return new PopsObserverResponse($entity);
    }

    /**
     * @throws LogicException
     */
    private function getPopsEventObserverResponse(ObservedEntityInterface $entity): PopsEventObserverResponse
    {
        if (!$entity instanceof PopsEvent) {
            throw new LogicException('Unsupported entity for PopsEventObserverResponse.');
        }

        return new PopsEventObserverResponse($entity);
    }

    /**
     * @throws LogicException
     */
    public function getPopsEventAttachmentObserverResponse(
        ObservedEntityInterface $entity,
        ?string $deletedOriginalDbId = null,
    ): PopsEventAttachmentObserverResponse
    {
        if (!$entity instanceof PopsEvent) {
            throw new LogicException('Unsupported entity for PopsEventAttachmentObserverResponse.');
        }

        return new PopsEventAttachmentObserverResponse($entity, $deletedOriginalDbId);
    }

}
