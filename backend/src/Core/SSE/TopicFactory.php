<?php

declare(strict_types = 1);

namespace Pm\Core\SSE;

use InvalidArgumentException;
use Pm\Pops\Entity\Pops;
use Pm\Pops\Entity\PopsEvent;
use Pm\SuperPops\Entity\SuperPops;

readonly class TopicFactory
{

    public function create(object $entity): string
    {
        if ($entity instanceof PopsEvent) {
            return 'pops/' . $entity->getPops()->getId() . '/events';
        }
        if ($entity instanceof Pops) {
            return 'facility/' . $entity->getFacility()->getId() . '/pops';
        }
        if ($entity instanceof SuperPops) {
            return 'superpops/' . $entity->getId() . '/events';
        }

        throw new InvalidArgumentException('Invalid entity type.');
    }

}
