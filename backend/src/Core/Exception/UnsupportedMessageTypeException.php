<?php

declare(strict_types = 1);

namespace Pm\Core\Exception;

use Symfony\Component\Notifier\Exception\LogicException;
use Symfony\Component\Notifier\Message\MessageInterface;
use function get_debug_type;
use function sprintf;

class UnsupportedMessageTypeException extends LogicException
{

    public function __construct(string $transport, string $supported, MessageInterface $given)
    {
        $message = sprintf(
            'The "#[%s]#" transport only supports instances of "#[%s]#" (instance of "#[%s]#" given).',
            $transport,
            $supported,
            get_debug_type($given),
        );

        parent::__construct($message);
    }

}
