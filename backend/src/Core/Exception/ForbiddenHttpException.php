<?php

declare(strict_types = 1);

namespace Pm\Core\Exception;

use Pm\Core\Http\ResponseStatusCode;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Throwable;

class ForbiddenHttpException extends BadRequestHttpException
{

    public function __construct(string $message, Throwable $previous)
    {
        parent::__construct($message, $previous, ResponseStatusCode::FORBIDDEN_403);
    }

}
