<?php

declare(strict_types = 1);

namespace Pm\Core\Exception;

use Pm\Core\Http\ResponseStatusCode;
use Throwable;

class AclException extends RuntimeException
{

    public function __construct(
        string $message = "You don't have permissions to do this action.",
        ?Throwable $previous = null,
    )
    {
        parent::__construct($message, $previous, ResponseStatusCode::FORBIDDEN_403);
    }

}
