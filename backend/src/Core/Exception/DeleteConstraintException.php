<?php

declare(strict_types = 1);

namespace Pm\Core\Exception;

use Pm\Core\Http\ResponseStatusCode;
use Throwable;

class DeleteConstraintException extends RuntimeException
{

    public function __construct(
        string $message = 'Cannot delete facility because it is used in other parts of the system.',
        ?Throwable $previous = null,
        int $code = ResponseStatusCode::CONFLICT_409,
    )
    {
        parent::__construct($message, $previous, $code);
    }

}
