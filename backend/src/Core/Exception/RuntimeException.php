<?php

declare(strict_types = 1);

namespace Pm\Core\Exception;

use Pm\Core\Http\ResponseStatusCode;
use RuntimeException as NativeRuntimeException;
use Throwable;

abstract class RuntimeException extends NativeRuntimeException
{

    /**
     * @param array<string, int|string> $parameters
     */
    protected function __construct(
        string $message = '',
        ?Throwable $previous = null,
        int $code = ResponseStatusCode::BAD_REQUEST_400,
        protected array $parameters = [],
    )
    {
        parent::__construct($message, $code, $previous);
    }

    public function toLogicException(?string $message = null): LogicException
    {
        return new LogicException($message ?? $this->getMessage(), $this);
    }

    /**
     * @return array<string, int|string>
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }

}
