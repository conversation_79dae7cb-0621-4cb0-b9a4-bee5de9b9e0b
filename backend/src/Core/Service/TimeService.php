<?php

declare(strict_types = 1);

namespace Pm\Core\Service;

use DateInterval;
use DateTimeImmutable;
use DateTimeZone;
use function date_default_timezone_get;

class TimeService
{

    public function __construct()
    {
    }

    /**
     * Convert datetime to local timezone to be compared with current datetime
     */
    public function convertToDefaultTimezone(?DateTimeImmutable $dateTime = null): DateTimeImmutable
    {
        $date = $dateTime === null ? new DateTimeImmutable() : clone $dateTime;

        return $date->setTimezone(new DateTimeZone(date_default_timezone_get()));
    }

    /**
     * Add minutes to given datetime
     */
    public function addMinutesToTime(DateTimeImmutable $date, int $interval): DateTimeImmutable
    {
        $next = clone $date;

        return $next->add(new DateInterval('PT' . $interval . 'M'));
    }

    /**
     * Validate given time is bigger than current time
     */
    public function isInFuture(DateTimeImmutable $dateTime): bool
    {
        $now = new DateTimeImmutable();

        return $now < $dateTime;
    }

}
