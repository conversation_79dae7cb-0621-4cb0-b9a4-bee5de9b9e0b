<?php

declare(strict_types = 1);

namespace Pm\Core\Service;

use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use Pm\Core\Entity\AttachmentEntityInterface;
use Pm\Tools\Exception\ImageOptimizationException;
use Pm\Tools\FileSanitizer;
use Pm\Tools\ImageSanitizer;
use Pm\Tools\Mime\InvalidFileException;
use Pm\Tools\Mime\InvalidImageException;
use Pm\Tools\Mime\MimeType;
use Pm\Tools\StoredFile;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Uid\Uuid;
use function str_replace;
use function strlen;

class AttachmentService
{

    public const MAX_DIMENSIONS = '1920x1080';
    private const MAX_DIMENSIONS_THUMBNAIL = '320x320';
    private const MAX_FILESIZE_MB = 16;
    private const FILE_ICON_DIR = '/images/mime/';

    public function __construct(
        private readonly ImageSanitizer $imageSanitizer,
        private readonly FileSanitizer $fileSanitizer,
        private readonly FilesystemOperator $storage,
    )
    {
    }

    /**
     * @throws InvalidFileException|FilesystemException|InvalidImageException|ImageOptimizationException
     */
    public function store(UploadedFile $uploadedFile, ?string $facilityResolution = null): StoredFile
    {
        // will only resolve valid mime types for application, all non-allowed file types will throw exception
        $mimeTypeEnum = $this->fileSanitizer->resolveMimeType($uploadedFile->getContent());
        $size = (int) $uploadedFile->getSize();
        if ($mimeTypeEnum->isImage()) {
            $sanitizedImage = $this->imageSanitizer->sanitizeAndResizeIfNeededImage($uploadedFile->getContent(), $facilityResolution ?? self::MAX_DIMENSIONS);
            $filename = Uuid::v7() . '.' . $sanitizedImage->getMimeType()->getExtension();
            $optimizedImageContent = $this->imageSanitizer->optimize($filename, $sanitizedImage->getContent());
            $size = strlen($optimizedImageContent);
            $this->storage->write($filename, $optimizedImageContent, ['ContentType' => $mimeTypeEnum->value]);

            // create thumbnail
            $sanitizedImageThumbnail = $this->imageSanitizer->sanitizeAndResizeIfNeededImage($uploadedFile->getContent(), self::MAX_DIMENSIONS_THUMBNAIL);
            $thumbnailFilename = $this->imageSanitizer->getImageThumbnailFilename($filename);
            $optimizedThumbnailContent = $this->imageSanitizer->optimize($filename, $sanitizedImageThumbnail->getContent());
            $this->storage->write($thumbnailFilename, $optimizedThumbnailContent, ['ContentType' => $mimeTypeEnum->value]);
        } else {
            $sanitizedFile = $this->fileSanitizer->sanitize($uploadedFile, self::MAX_FILESIZE_MB);
            $extension = $sanitizedFile->guessExtension();
            $filename = Uuid::v7() . '.' . ($extension ?? 'ext');
            $this->storage->write($filename, $sanitizedFile->getContent(), ['ContentType' => $mimeTypeEnum->value]);
        }

        return new StoredFile($filename, $size);
    }

    public function createThumbnailPublicUrl(AttachmentEntityInterface $attachment): string
    {
        $mimeType = MimeType::from($attachment->getMime());

        if ($mimeType->isImage()) {
            // get thumbnail public url
            return $this->imageSanitizer->getImageThumbnailPublicUrl($attachment->getPath());

        }

        // static icon by filetype
        return self::FILE_ICON_DIR . str_replace('/', '-', $mimeType->value) . '.svg';
    }

}
