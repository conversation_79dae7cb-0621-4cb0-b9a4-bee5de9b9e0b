<?php

declare(strict_types = 1);

namespace Pm\Core\Pm2Api;

use Doctrine\ORM\EntityManagerInterface;
use Pm\Account\Entity\Account;
use Pm\Core\Exception\LogicException;
use Pm\Pops\Entity\Pops;
use Pm\Pops\Entity\PopsEvent;
use Pm\Pops\Entity\PopsEventAttachment;
use Pm\Pops\Exception\NoPopsEventFoundException;
use Pm\Pops\Exception\NoPopsFoundException;
use Pm\Pops\Repository\PopsEventRepository;
use Pm\Pops\Repository\PopsRepository;
use function strtolower;

readonly class Pm2ApiResponseProcessor
{

    public function __construct(
        private PopsRepository $popsRepository,
        private PopsEventRepository $popsEventRepository,
        private EntityManagerInterface $entityManager,
    )
    {
    }

    /**
     * @throws NoPopsFoundException|NoPopsEventFoundException
     */
    public function process(string $entityClass, int $entityId, Account $account, int $originalDbId): void
    {
        if ($entityClass === Pops::class) {
            $entity = $this->popsRepository->get($entityId);
        } elseif ($entityClass === PopsEvent::class) {
            $entity = $this->popsEventRepository->get($entityId);
        } elseif ($entityClass === PopsEventAttachment::class) {
            $entity = $this->entityManager->find(PopsEventAttachment::class, $entityId);
        } else {
            throw new LogicException('Invalid entity class: #[' . $entityClass . ']#');
        }
        if ($entity === null) {
            throw new LogicException('Entity not found: #[' . $entityClass . ']# #[' . $entityId . ']#');
        }

        $originalDbIdCode = strtolower($account->getName()) . '_' . $originalDbId;

        if ($entity->getOriginalDbId() !== null && $entity->getOriginalDbId() !== $originalDbIdCode) {
            throw new LogicException('Entity already has originalDbId: #[' . $entity->getOriginalDbId() . ']#');
        }
        $entity->setOriginalDbId($originalDbIdCode);
        $this->entityManager->flush();
    }

}
