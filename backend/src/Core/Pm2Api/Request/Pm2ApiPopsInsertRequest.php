<?php

declare(strict_types = 1);

namespace Pm\Core\Pm2Api\Request;

use Pm\Tools\OriginalDbIdHelper;

readonly class Pm2ApiPopsInsertRequest implements Pm2ApiRequestInterface
{

    public string $startsAt;

    public string $endsAt;

    public bool $sendAttachment;

    public int $object;

    /**
     * @param array<string, mixed> $data
     */
    public function __construct(array $data)
    {
        $this->startsAt = $data['startsAt'];
        $this->endsAt = $data['endsAt'];
        $this->sendAttachment = $data['isSendAttachment'];
        $this->object = OriginalDbIdHelper::parseOriginalDbId($data['facilityOriginalDbId']);
    }

}
