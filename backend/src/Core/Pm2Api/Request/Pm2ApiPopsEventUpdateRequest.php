<?php

declare(strict_types = 1);

namespace Pm\Core\Pm2Api\Request;

use Pm\Tools\OriginalDbIdHelper;

readonly class Pm2ApiPopsEventUpdateRequest implements Pm2ApiRequestInterface
{

    public int $id;

    public string $name;

    public string $description;

    public string $date;

    public int $event;

    /**
     * @param array<string, mixed> $data
     */
    public function __construct(array $data)
    {
        $this->name = '';
        $this->description = $data['description'];
        $this->date = $data['eventDateTime'];
        $this->event = OriginalDbIdHelper::parseOriginalDbId($data['eventCategoryOriginalDbId']);
        $this->id = OriginalDbIdHelper::parseOriginalDbId($data['originalDbId']);
    }

}
