<?php

declare(strict_types = 1);

namespace Pm\Core\Pm2Api;

use League\Flysystem\FilesystemOperator;
use Pm\Core\Exception\LogicException;
use Pm\Core\Pm2Api\Request\Pm2ApiPopsEventAttachmentInsertRequest;
use Pm\Core\Pm2Api\Request\Pm2ApiPopsEventInsertRequest;
use Pm\Core\Pm2Api\Request\Pm2ApiPopsEventUpdateRequest;
use Pm\Core\Pm2Api\Request\Pm2ApiPopsInsertRequest;
use Pm\Core\Pm2Api\Request\Pm2ApiPopsUpdateRequest;
use Pm\Core\Pm2Api\Request\Pm2ApiRequestInterface;
use Pm\Pops\Entity\Pops;
use Pm\Pops\Entity\PopsEvent;
use Pm\Pops\Entity\PopsEventAttachment;
use Pm\Tools\OriginalDbIdHelper;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function array_key_exists;
use function base64_encode;

readonly class Pm2ApiRequestFactory
{

    public function __construct(
        /**
         * @var array<int, string>
         */
        #[Autowire('%pm2_api_accounts_api_path_map%')]
        private array $pathMap,
        private FilesystemOperator $storage,
    )
    {
    }

    /**
     * @param array<string, mixed> $data
     */
    public function create(string $entityClass, array $data, string $event): ?Pm2ApiRequestInterface
    {
        if ($entityClass === PopsEvent::class) {
            if ($event === 'persist') {
                $requestData = new Pm2ApiPopsEventInsertRequest($data);
            } elseif ($event === 'update') {
                $requestData = new Pm2ApiPopsEventUpdateRequest($data);
            } else {
                throw new LogicException('Invalid event: #[' . $event . ']#');
            }
        } elseif ($entityClass === Pops::class) {
            if ($event === 'persist') {
                $requestData = new Pm2ApiPopsInsertRequest($data);
            } elseif ($event === 'update') {
                $requestData = new Pm2ApiPopsUpdateRequest($data);
            } else {
                throw new LogicException('Invalid event: #[' . $event . ']#');
            }
        } elseif ($entityClass === PopsEventAttachment::class) {
            $requestData = null;
        } else {
            throw new LogicException('Invalid entity class: #[' . $entityClass . ']#');
        }

        return $requestData;
    }

    public function createAttachment(PopsEventAttachment $attachment): Pm2ApiPopsEventAttachmentInsertRequest
    {
        return new Pm2ApiPopsEventAttachmentInsertRequest(
            $attachment->getName(),
            'data:' . $attachment->getMime() . ';base64,' . base64_encode($this->storage->read($attachment->getPath())),
        );
    }

    /**
     * @param array<string, mixed> $entityData
     */
    public function getEndpoint(string $entityClass, int $accountId, string $event, array $entityData, ?int $entityId = null): string
    {
        if ($entityClass === PopsEvent::class) {
            $uri = '/pops-event';
            if ($event === 'update') {
                if ($entityId === null) {
                    throw new LogicException('Missing entity ID for update endpoint.');
                }
                $uri .= '/' . $entityId;
            } elseif ($event === 'persist-attachment') {
                if ($entityId === null) {
                    throw new LogicException('Missing entity ID for persist-attachment endpoint.');
                }
                $uri .= '/' . $entityId . '/attachment';
            } elseif ($event === 'reporting') {
                if ($entityId === null) {
                    throw new LogicException('Missing entity ID for reporting endpoint.');
                }
                $uri .= '/' . $entityId . '/reporting';
            }
        } elseif ($entityClass === Pops::class) {
            $uri = '/pops';
            if ($event === 'update') {
                if ($entityId === null) {
                    throw new LogicException('Missing entity ID for update endpoint.');
                }
                $uri .= '/' . $entityId;
            }
        } elseif ($entityClass === PopsEventAttachment::class) {
            $popsEventOriginalDbId = OriginalDbIdHelper::parseOriginalDbId($entityData['popsEventOriginalDbId']);
            $attachmentOriginalDbId = OriginalDbIdHelper::parseOriginalDbId($entityData['attachmentOriginalDbId']);

            $uri = '/pops-event/' . $popsEventOriginalDbId . '/attachment/' . $attachmentOriginalDbId;
        } else {
            throw new LogicException('Invalid entity class: #[' . $entityClass . ']#');
        }

        if (!array_key_exists($accountId, $this->pathMap)) {
            throw new LogicException('No API path configuration found for account #: #[' . $accountId . ']#');
        }

        return $this->pathMap[$accountId] . $uri;
    }

}
