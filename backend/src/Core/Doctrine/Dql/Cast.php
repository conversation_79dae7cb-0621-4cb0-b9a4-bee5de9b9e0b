<?php

declare(strict_types = 1);

namespace Pm\Core\Doctrine\Dql;

use Doctrine\Common\Lexer\Token;
use Doctrine\ORM\Query\AST\Functions\FunctionNode;
use Doctrine\ORM\Query\AST\Node;
use Doctrine\ORM\Query\Lexer;
use Doctrine\ORM\Query\Parser;
use Doctrine\ORM\Query\SqlWalker;
use Doctrine\ORM\Query\TokenType;
use function class_exists;
use function implode;
use function is_string;
use function sprintf;

class Cast extends FunctionNode
{

    public Node $sourceType;

    public string $targetType;

    public function parse(Parser $parser): void
    {
        $shouldUseLexer = !class_exists(TokenType::class);

        $parser->match($shouldUseLexer ? Lexer::T_IDENTIFIER : TokenType::T_IDENTIFIER);
        $parser->match($shouldUseLexer ? Lexer::T_OPEN_PARENTHESIS : TokenType::T_OPEN_PARENTHESIS);
        $this->sourceType = $parser->SimpleArithmeticExpression();
        $parser->match($shouldUseLexer ? Lexer::T_AS : TokenType::T_AS);

        $parser->match($shouldUseLexer ? Lexer::T_IDENTIFIER : TokenType::T_IDENTIFIER);

        $lexer = $parser->getLexer();
        $token = $lexer->token;
        if (!$token instanceof Token) {
            return;
        }
        if (!is_string($token->value)) {
            return;
        }

        $type = $token->value;
        if ($lexer->isNextToken($shouldUseLexer ? Lexer::T_OPEN_PARENTHESIS : TokenType::T_OPEN_PARENTHESIS)) {
            $parser->match($shouldUseLexer ? Lexer::T_OPEN_PARENTHESIS : TokenType::T_OPEN_PARENTHESIS);
            $parameter = $parser->Literal();
            $parameters = [$parameter->value];
            if ($lexer->isNextToken($shouldUseLexer ? Lexer::T_COMMA : TokenType::T_COMMA)) {
                while ($lexer->isNextToken($shouldUseLexer ? Lexer::T_COMMA : TokenType::T_COMMA)) {
                    $parser->match($shouldUseLexer ? Lexer::T_COMMA : TokenType::T_COMMA);
                    $parameter = $parser->Literal();
                    $parameters[] = $parameter->value;
                }
            }
            $parser->match($shouldUseLexer ? Lexer::T_CLOSE_PARENTHESIS : TokenType::T_CLOSE_PARENTHESIS);
            $type .= '(' . implode(', ', $parameters) . ')';
        }

        $this->targetType = $type;

        $parser->match($shouldUseLexer ? Lexer::T_CLOSE_PARENTHESIS : TokenType::T_CLOSE_PARENTHESIS);
    }

    public function getSql(SqlWalker $sqlWalker): string
    {
        return sprintf('cast(%s as %s)', $this->sourceType->dispatch($sqlWalker), $this->targetType);
    }

}
