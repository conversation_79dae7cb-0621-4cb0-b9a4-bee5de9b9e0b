<?php

declare(strict_types = 1);

namespace Pm\Core\Doctrine;

use BackedEnum;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\StringType;
use Pm\Core\Exception\InvalidEnumValueException;
use Pm\Core\Exception\LogicException;
use Pm\Core\Utils\Enum;
use function is_string;

abstract class StringEnumType extends StringType
{

    /**
     * @return class-string<BackedEnum<string>>
     */
    abstract public function getEnumClass(): string;

    public function getName(): string
    {
        throw new LogicException('No override method getName implemented.');
    }

    /**
     * @param BackedEnum<string>|string|null $value
     * @return BackedEnum<string>|null
     *
     * @throws InvalidEnumValueException
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingNativeTypeHint
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): ?BackedEnum
    {
        if ($value instanceof BackedEnum) {
            return $value;
        }

        $value = parent::convertToPHPValue($value, $platform);

        if ($value === null) {
            return null;
        }

        if (!is_string($value)) {
            throw new LogicException('Invalid data type.');
        }

        return Enum::create(static::getEnumClass(), $value);
    }

    /**
     * @param BackedEnum<string>|string|null $value
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingNativeTypeHint
     */
    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if (!$value instanceof BackedEnum) {
            return $value;
        }

        return $value->value;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }

}
