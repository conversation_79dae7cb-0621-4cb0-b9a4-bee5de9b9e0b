<?php

declare(strict_types = 1);

namespace Pm\Core\Utils;

use BackedEnum;
use Pm\Core\Exception\InvalidEnumValueException;
use Pm\Core\Exception\LogicException;
use function enum_exists;

class Enum
{

    /**
     * @param class-string<BackedEnum<string>> $enumClass
     * @return BackedEnum<string>
     *
     * @throws InvalidEnumValueException
     */
    public static function create(string $enumClass, string $value): BackedEnum
    {
        if (!enum_exists($enumClass)) {
            throw new LogicException('No enum #[' . $enumClass . ']# found.');
        }

        $enum = $enumClass::tryFrom($value);
        if ($enum === null) {
            throw new InvalidEnumValueException($value, $enumClass);
        }

        return $enum;
    }

}
