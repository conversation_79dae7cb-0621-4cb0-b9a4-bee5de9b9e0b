<?php

declare(strict_types = 1);

namespace Pm\Core\Notifier\Bridge\Firebase\Notification;

use Symfony\Component\Notifier\Bridge\Firebase\FirebaseOptions;
use function array_merge;

final class FirebaseNotification extends FirebaseOptions
{

    private readonly string $to;

    /**
     * @var array<string, mixed>
     */
    private readonly array $data;

    /**
     * Defaults for Firebase
     *
     * @var array<string, mixed>
     */
    private array $defaults = [
        'priority' => 'high',
        'content_available' => true,
        'notification' => [
            'sound' => 'default',
            'color' => '#cccccc',
            'icon' => 'icon_white',
        ],
    ];

    /**
     * @param array<string, mixed> $options
     * @param array<string, mixed> $data
     */
    public function __construct(string $to, array $options, array $data = [])
    {
        parent::__construct($to, $options, $data);
        $this->to = $to;
        $this->options = $options;
        $this->data = $data;
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return array_merge($this->defaults, [
            'to' => $this->to,
            'notification' => $this->options,
            'data' => $this->data,
        ]);
    }

    /**
     * @return $this
     */
    public function channelId(string $channelId): static
    {
        $this->options['android_channel_id'] = $channelId;

        return $this;
    }

    /**
     * @return $this
     */
    public function icon(string $icon): static
    {
        $this->options['icon'] = $icon;

        return $this;
    }

    /**
     * @return $this
     */
    public function badge(string $badge): static
    {
        $this->options['badge'] = $badge;

        return $this;
    }

    /**
     * @return $this
     */
    public function sound(string $sound): static
    {
        $this->options['sound'] = $sound;

        return $this;
    }

    /**
     * @return $this
     */
    public function tag(string $tag): static
    {
        $this->options['tag'] = $tag;

        return $this;
    }

    /**
     * @return $this
     */
    public function color(string $color): static
    {
        $this->options['color'] = $color;

        return $this;
    }

    /**
     * @return $this
     */
    public function clickAction(string $clickAction): static
    {
        $this->options['click_action'] = $clickAction;

        return $this;
    }

    /**
     * @return $this
     */
    public function bodyLocKey(string $bodyLocKey): static
    {
        $this->options['body_loc_key'] = $bodyLocKey;

        return $this;
    }

    /**
     * @param string[] $bodyLocArgs
     * @return $this
     */
    public function bodyLocArgs(array $bodyLocArgs): static
    {
        $this->options['body_loc_args'] = $bodyLocArgs;

        return $this;
    }

    /**
     * @return $this
     */
    public function titleLocKey(string $titleLocKey): static
    {
        $this->options['title_loc_key'] = $titleLocKey;

        return $this;
    }

    /**
     * @param string[] $titleLocArgs
     * @return $this
     */
    public function titleLocArgs(array $titleLocArgs): static
    {
        $this->options['title_loc_args'] = $titleLocArgs;

        return $this;
    }

}
