<?php

declare(strict_types = 1);

namespace Pm\Core\Input;

interface FilterInputInterface
{

    /**
     * @return string[]
     */
    public function getAllowedFields(): array;

    public function getFilterVarType(FilterInput $filterInput): string;

    /**
     * @return FilterInput[]|null
     */
    public function getFilterArgs(): ?array;

    public function getFilterArg(string $field): ?FilterInput;

}
