<?php

declare(strict_types = 1);

namespace Pm\Core\Input;

use DateTime;
use DateTimeImmutable;
use OpenApi\InputMapper\Input;
use Pm\Core\Enum\FilterTypeEnum;
use Pm\Core\Enum\OperatorTypeEnum;
use function str_replace;

class FilterInput implements Input
{

    public function __construct(private string $field, protected mixed $value, private readonly FilterTypeEnum $type, private readonly ?OperatorTypeEnum $operator)
    {
    }

    public function getField(): string
    {
        return $this->field;
    }

    public function setField(mixed $field): void
    {
        $this->field = $field;
    }

    public function getValue(): string
    {
        return (string) $this->value;
    }

    public function getIntValue(): int
    {
        return (int) $this->value;
    }

    public function setValue(string $value): void
    {
        $this->value = $value;
    }

    public function getDateTimeValue(): string
    {
        return (new DateTimeImmutable(str_replace('+', ' ', $this->value)))->format(DateTime::W3C);
    }

    public function getOperator(): OperatorTypeEnum
    {
        return $this->operator ?? OperatorTypeEnum::AND;
    }

    public function getType(): FilterTypeEnum
    {
        return $this->type;
    }

    public function getPattern(): string
    {
        return match ($this->type) {
            FilterTypeEnum::STARTS_WITH => "'{$this->value}%'",
            FilterTypeEnum::ENDS_WITH => "'%{$this->value}'",
            FilterTypeEnum::CONTAINS => "'%{$this->value}%'",
            FilterTypeEnum::LESS_THAN => '<',
            FilterTypeEnum::GREATER_THAN => '>',
            FilterTypeEnum::MATCH => '=',
            FilterTypeEnum::IN => 'IN',
            FilterTypeEnum::NOT_IN => 'NOT IN',
            FilterTypeEnum::BETWEEN => 'between',
        };
    }

}
