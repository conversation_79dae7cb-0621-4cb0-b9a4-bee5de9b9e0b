<?xml version="1.0" encoding="utf-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" datatype="plaintext" original="file.ext">
        <body>
            <group resname="custom">
                <note>Custom validations</note>    
                <trans-unit id="shift_is_overlaped">
                    <source>service_create.shift_is_overlaped</source>
                    <target>Введенные смены совпадают</target>
                </trans-unit>
                <trans-unit id="service_create.shift_end_before_start">
                    <source>service_create.shift_end_before_start</source>
                    <target>Смена начинается позже, чем оканчивается</target>
                </trans-unit>
                <trans-unit id="service_create.start_after_end_error">
                    <source>service_create.start_after_end_error</source>
                    <target>Период начинается позже, чем оканчивается</target>
                </trans-unit>
                <trans-unit id="service_create.all_times_empty">
                    <source>service_create.all_times_empty</source>
                    <target>Невозможно генерировать – нет смены с введенным временем</target>
                </trans-unit>
                <trans-unit id="service_create.missing_time">
                    <source>service_create.missing_time</source>
                    <target>Пропущены времена начала и окончания смены</target>
                </trans-unit>
                <trans-unit id="service_create.not_datetime">
                    <source>service_create.not_datetime</source>
                    <target>Недействительный ввод даты и времени</target>
                </trans-unit>
                <trans-unit id="service_create.shift_exists">
                    <source>service_create.shift_exists</source>
                    <target>Смена в местоположении уже существует</target>
                </trans-unit>
                <trans-unit id="service.create.not_in_period">
                    <source>service.create.not_in_period</source>
                    <target>Период начала этой службы окончен</target>
                </trans-unit>
                <trans-unit id="negative_value">
					<source>negative_value</source>
                <target>Величина не может быть отрицательной</target>
            </trans-unit>     
            </group> 
			    			
        </body>
    </file>
</xliff>
