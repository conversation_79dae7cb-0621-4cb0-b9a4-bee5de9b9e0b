<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
    <file datatype="plaintext" original="file.ext" source-language="en" target-language="en-gb">
        
    <body>
            <group resname="custom">
                <note>custom validations</note>    
                <trans-unit id="shift_is_overlaped">
                    <source>service_create.shift_is_overlaped</source>
                    <target>wpisane dyżury nakładają się na siebie</target>
                </trans-unit>
                <trans-unit id="service_create.shift_end_before_start">
                    <source>service_create.shift_end_before_start</source>
                    <target>początek rozpoczęcia zmiany przypada później niż jej koniec</target>
                </trans-unit>
                <trans-unit id="service_create.start_after_end_error">
                    <source>service_create.start_after_end_error</source>
                    <target>przedział zaczyna się później niż się kończy</target>
                </trans-unit>
                <trans-unit id="service_create.interval_out_of_period">
                    <source>service_create.interval_out_of_period</source>
                    <target>Terminy serwisowe są nieaktualne</target>
                </trans-unit>
                <trans-unit id="service_create.all_times_empty">
                    <source>service_create.all_times_empty</source>
                    <target>nie można wygenerować, brak informacji o czasie dużuru</target>
                </trans-unit>
                <trans-unit id="service_create.missing_time">
                    <source>service_create.missing_time</source>
                    <target>brak czasu rozpoczęcia i zakończenia dyżuru</target>
                </trans-unit>
                <trans-unit id="service_create.not_datetime">
                    <source>service_create.not_datetime</source>
                    <target>nieprawidłowo wpisany czas i data</target>
                </trans-unit>
                <trans-unit id="service_create.shift_exists">
                    <source>service_create.shift_exists</source>
                    <target>dyżur dla wybranego pracownika już istnieje</target>
                </trans-unit>
                <trans-unit id="service.create.not_in_period">
                    <source>service.create.not_in_period</source>
                    <target>dyżur rozpoczyna się po jego zakończeniu</target>
                </trans-unit>
                <trans-unit id="negative_value">
                    <source>negative_value</source>
                    <target>wartość nie może być negacją</target>
                </trans-unit> 
                <trans-unit id="phone_number_not_valid">
                    <source>phone_number_not_valid</source>
                    <target>Numer telefonu musi być w formacie międzynarodowym (+420123456789) i bez spacji.</target>
                </trans-unit>                
                <trans-unit id="agency_rate.agency_rate_exists">
                    <source>agency_rate.agency_rate_exists</source>
                    <target>Stawka Agencji na to stanowisko już istnieje!</target>
                </trans-unit>
                <trans-unit id="contract.contract_of_type_exists">
                    <source>contract.contract_of_type_exists</source>
                    <target>Kontrakty tego samego typu na tym samym obiekcie nakładają się na siebie!</target>
                </trans-unit>  
                <trans-unit id="object.object_already_exists">
                    <source>object.object_already_exists</source>
                    <target>Obiekt z tym kodem i firma już istnieje</target>
                </trans-unit> 
                <trans-unit id="agency_rate.position_group_required">
                    <source>agency_rate.position_group_required</source>
                    <target>Nie wybrałeś grupy pozycji</target>
                </trans-unit>
                <trans-unit id="agency_rate.agency_not_set">
                    <source>agency_rate.agency_not_set</source>
                    <target>Nie wybrałes agencji</target>
                </trans-unit>
                <trans-unit id="agency_rate.position_not_set">
                    <source>agency_rate.position_not_set</source>
                    <target>Nie wybrałeś pozycji</target>
                </trans-unit>
                <trans-unit id="contract.error_starts_before_ends">
                    <source>contract.error_starts_before_ends</source>
                    <target>Koniec umowy jest wcześniejszy niż jej początek</target>
                </trans-unit>
                <trans-unit id="period.end_before_start">
                    <source>period.end_before_start</source>
                    <target>Koniec umowy jest wcześniejszy niż jej początek</target>
                </trans-unit>
                <trans-unit id="reporting.too_many_combinations">
                    <source>reporting.too_many_combinations</source>
                    <target>Maksymalna liczba kombinacji obiektów i przedmiotów wynosi 1000. Wybrałeś %object% obiektów i %subjects% przedmiotów - razem %combinations% kombinacji.</target>
                </trans-unit>
                <trans-unit id="reporting.item_not_selectted">
                    <source>reporting.item_not_selectted</source>
                    <target>Musisz wybrać pozycję</target>
                </trans-unit>
                <trans-unit id="service_template.shift_is_overlaped">
                    <source>service_template.shift_is_overlaped</source>
                    <target>Nakładanie się zmian</target>
                </trans-unit>
                <trans-unit id="service_template.shift_exists">
                    <source>service_template.shift_exists</source>
                    <target>Ta zmiana już istnieje dla wybranej pozycji</target>
                </trans-unit>
                <trans-unit id="service_template.shift_order">
                    <source>service_template.shift_order</source>
                    <target>Zmiany muszą być kolejne</target>
                </trans-unit>
                <trans-unit id="service_template.last_shift_overlaps_first">
                    <source>service_template.last_shift_overlaps_first</source>
                    <target>Koniec ostatniej zmiany przekracza początek pierwszej. </target>
                </trans-unit>
                <trans-unit id="service_template.shift_wrong_order">
                    <source>service_template.shift_wrong_order</source>
                    <target> Przegapiłeś zmianę. Numery zmian muszą być używane w kolejności rosnącej. </target>
                </trans-unit>
                <trans-unit id="date_range.min_date">
                    <source>date_range.min_date</source>
                    <target>Ta data powinna być większa niż {{ limit }}.</target>
                </trans-unit>
                <trans-unit id="date_range.max_date">
                    <source>date_range.max_date</source>
                    <target>Ta data powinna być większa niż {{ limit }}. </target>
                </trans-unit>
                <trans-unit id="subject_photo_allowed_types">
                    <source>subject_photo_allowed_types</source>
                    <target>Zdjęcie musi być w formacie JPEG lub PNG</target>
                </trans-unit>  
            </group> 
			    			
        </body></file>
</xliff>