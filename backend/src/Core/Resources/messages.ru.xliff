<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
    <file datatype="plaintext" original="file.ext" source-language="en" target-language="EN-GB">
    <body>            
            <group resname="GLOBAL">
                <note>General texts common for the entire application</note>
                <trans-unit id="name">
                    <source>name</source>
                    <target>Название</target>
                </trans-unit>
                <trans-unit id="abbr">
                    <source>abbr</source>
                    <target>Аббревиатура</target>
                </trans-unit>
                <trans-unit id="code">
                    <source>code</source>
                    <target>Код</target>
                </trans-unit>
                <trans-unit id="inserted_by">
                    <source>inserted_by</source>
                    <target>Создал:</target>
                </trans-unit>
                <trans-unit id="updated_by">
                    <source>updated_by</source>
                    <target>Обновил:</target>
                </trans-unit>
                <trans-unit id="inserted_at">
                    <source>inserted_at</source>
                    <target>Создал</target>
                </trans-unit>
                <trans-unit id="updated_at">
                    <source>updated_at</source>
                    <target>Обновил</target>
                </trans-unit>
                <trans-unit id="ico">
                    <source>ico</source>
                    <target>Рабочий ID</target>
                </trans-unit>
                <trans-unit id="firstname">
                    <source>firstname</source>
                    <target>Фамилия</target>
                </trans-unit>
                <trans-unit id="lastname">
                    <source>lastname</source>
                    <target>Имя</target>
                </trans-unit>
                <trans-unit id="full_name">
                    <source>full_name</source>
                    <target>Имя</target>
                </trans-unit>
                <trans-unit id="is_active">
                    <source>is_active</source>
                    <target>Активировать</target>
                </trans-unit>
                <trans-unit id="title">
                    <source>title</source>
                    <target>Название</target>
                </trans-unit>
                <trans-unit id="level">
                    <source>level</source>
                    <target>Уровень</target>
                </trans-unit>
                <trans-unit id="currency">
                    <source>currency</source>
                    <target>Валюта</target>
                </trans-unit>
                <trans-unit id="company">
                    <source>company</source>
                    <target>Company</target>
                </trans-unit>
                <trans-unit id="subject">
                    <source>subject</source>
                    <target>Предмет</target>
                </trans-unit>
                <trans-unit id="starts_at">
                    <source>starts_at</source>
                    <target>Стартует</target>
                </trans-unit>
                <trans-unit id="ends_at">
                    <source>ends_at</source>
                    <target>Оканчивается</target>
                </trans-unit>
                <trans-unit id="object">
                    <source>object</source>
                    <target>Объект</target>
                </trans-unit>
                <trans-unit id="phone">
                    <source>phone</source>
                    <target>Телефон</target>
                </trans-unit>
                <trans-unit id="mobile">
                    <source>mobile</source>
                    <target>Мобильный </target>
                </trans-unit>
                <trans-unit id="email">
                    <source>email</source>
                    <target>электронный адрес</target>
                </trans-unit>                                    
                <trans-unit id="attachments">
                    <source>attachments</source>
                    <target>Вложения</target>
                </trans-unit>
                <trans-unit id="Start">
                    <source>Start</source>
                    <target>Старт</target>
                </trans-unit>
                <trans-unit id="End">
                    <source>End</source>
                    <target>Завершение</target>
                </trans-unit>
                <trans-unit id="is_approved">
                    <source>is_approved</source>
                    <target>Одобрено</target>
                </trans-unit>
                <trans-unit id="not_approved">
                    <source>not_approved</source>
                    <target>Не одобрено</target>
                </trans-unit>
                <trans-unit id="go_approbate">
                    <source>go_approbate</source>
                    <target>Одобрить</target>
                </trans-unit>
                <trans-unit id="go_withdraw_approval">
                    <source>go_withdraw_approval</source>
                    <target>Изъять одобрение</target>
                </trans-unit> 
                <trans-unit id="activate">
                    <source>activate</source>
                    <target>Активировать</target>
                </trans-unit>
                <trans-unit id="deactivate">
                    <source>deactivate</source>
                    <target>Дезактивировать </target>
                </trans-unit>
                <trans-unit id="flash_activate">
                    <source>flash_activate</source>
                    <target>Элемент был активирован</target>
                </trans-unit>
                <trans-unit id="flash_deactivate">
                    <source>flash_deactivate</source>
                    <target>Элемент был дезактивирован</target>
                </trans-unit>     
                <trans-unit id="flash_batch_activate_success">
                    <source>flash_batch_activate_success</source>
                    <target>Выделенные элементы были активированы</target>
                </trans-unit>
                <trans-unit id="flash_batch_activate_error">
                    <source>flash_batch_activate_error</source>
                    <target>Выделенные элементы не могут быть активированы</target>
                </trans-unit>                                               
                <trans-unit id="flash_batch_deactivate_success">
                    <source>flash_batch_deactivate_success</source>
                    <target>Выделенные элементы были деактивированы</target>
                </trans-unit>
                <trans-unit id="flash_batch_deactivate_error">
                    <source>flash_batch_deactivate_error</source>
                    <target> Выделенные элементы не могут быть деактивированы</target>
                </trans-unit>
                <trans-unit id="select_all">
                    <source>select_all</source>
                    <target>Выделить всё</target>
                </trans-unit>
                <trans-unit id="select_none">
                    <source>select_none</source>
                    <target>Отменить выделение</target>
                </trans-unit>
                <trans-unit id="select_row">
                    <source>select_row</source>
                    <target>Выбрать ряд</target>
                </trans-unit>
                <trans-unit id="select_column">
                    <source>select_column</source>
                    <target>Выбрать столбец</target>
                </trans-unit>
                <trans-unit id="minutes">
                    <source>minutes</source>
                    <target>протокол</target>
                </trans-unit>
                <trans-unit id="Yes">
                    <source>Yes</source>
                    <target>Да</target>
                </trans-unit>
                <trans-unit id="No">
                    <source>No</source>
                    <target>Нет</target>
                </trans-unit>  
                <trans-unit id="print">
                    <source>print</source>
                    <target>Печать</target>
                </trans-unit>                        
                <trans-unit id="page">
                    <source>page</source>
                    <target>Страница</target>
                </trans-unit>                                              
                <trans-unit id="ipAddress">
                    <source>ipAddress</source>
                    <target>IP адрес</target>
                </trans-unit>
                <trans-unit id="loginTime">
                    <source>loginTime</source>
                    <target>Дата/Время – войти/выйти</target>
                </trans-unit>
                <trans-unit id="log.login">
                    <source>log.login</source>
                    <target>Вход выполнен</target>
                </trans-unit>  
                <trans-unit id="log.logout">
                    <source>log.logout</source>
                    <target>Выход выполнен</target>
                </trans-unit> 
                <trans-unit id="log.autologout">
                    <source>log.autologout</source>
                    <target>Выход выполнен автоматически</target>
                </trans-unit>  
                <trans-unit id="password_customer">
                    <source>password_customer</source>
                    <target></target>
                </trans-unit>               
                <trans-unit id="password">
                    <source>password</source>
                    <target>Пароль</target>
                </trans-unit>
                <trans-unit id="password_confirm">
                    <source>password_confirm</source>
                    <target>Подтверждение пароля</target>
                </trans-unit> 
                <trans-unit id="log.data">
                    <source>log.data</source>
                    <target>Действие с данными</target>
                </trans-unit> 
                <trans-unit id="log.error">
                    <source>log.error</source>
                    <target>Сообщение об ошибке</target>
                </trans-unit>
                <trans-unit id="log.email">
                    <source>log.email</source>
                    <target>Отправить электронное сообщение</target>
                </trans-unit>
                <trans-unit id="log.sms">
                    <source>log.sms</source>
                    <target>SMS сообщение</target>
                </trans-unit>
                <trans-unit id="log.panic">
                    <source>log.panic</source>
                    <target>Тревожное сообщение</target>
                </trans-unit>
                <trans-unit id="log.import">
                    <source>log.import</source>
                    <target>Импорт</target>
                </trans-unit>
                <trans-unit id="log.export">
                    <source>log.export</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="log.customer_login">
                    <source>log.customer_login</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="log.control_presence">
                    <source>log.control_presence</source>
                    <target>Проверка присутствия</target>
                </trans-unit>                
                <trans-unit id="severity.success">
                    <source>severity.success</source>
                    <target>Успех</target>
                </trans-unit>
                <trans-unit id="severity.info">
                    <source>severity.info</source>
                    <target>Информация</target>
                </trans-unit>
                <trans-unit id="severity.warning">
                    <source>severity.warning</source>
                    <target>Опасно</target>
                </trans-unit>
                <trans-unit id="severity.error">
                    <source>severity.error</source>
                    <target>Ошибка</target>
                </trans-unit>
                <trans-unit id="severity.critical">
                    <source>severity.critical</source>
                    <target>Критическая ситуация</target>
                </trans-unit>
                <trans-unit id="action">
                    <source>action</source>
                    <target>Действие</target>
                </trans-unit> 
                <trans-unit id="order">
                    <source>order</source>
                    <target>Порядок</target>
                </trans-unit>              
                <trans-unit id="version">
                    <source>version</source>
                    <target>Версия</target>
                </trans-unit>  
                <trans-unit id="change_user_profile">
                    <source>change_user_profile</source>
                    <target>Сменить профиль пользователя</target>
                </trans-unit>    
                <trans-unit id="aplication_welcome.title">
                    <source>aplication_welcome.title</source>
                    <target>Добро пожаловать в программу PM 2.0</target>
                </trans-unit>   
                <trans-unit id="aplication_welcome.text">
                    <source>aplication_welcome.text</source>
                    <target>Добро пожаловать на стандартную страницу нашей новой и лучшей программы PM 2.0! Нас ждет много работы – давайте начнём!</target>
                </trans-unit>
                <trans-unit id="select_lang">
                    <source>select_lang</source>
                    <target>Сменить язык</target>
                </trans-unit>  
                <trans-unit id="language">
                    <source>language</source>
                    <target>Язык</target>
                </trans-unit> 
                <trans-unit id="translate">
                    <source>translate</source>
                    <target>Перевести</target>
                </trans-unit>  
                <trans-unit id="control_sms_text">
                    <source>control_sms_text</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="show">
                    <source>show</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="hide">
                    <source>hide</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="description">
                    <source>description</source>
                    <target></target>
                </trans-unit>
            </group>     
            <group resname="validators">
                <note>Validator texts</note>
                <trans-unit id="not_the_same">
                    <source>not_the_same</source>
                    <target>Значения неравны</target>
                </trans-unit>
                <trans-unit id="password_info">
                    <source>password_info</source>
                    <target>Покиньте страницу с формой, если Вы не хотите менять пароль. Пароль должен состоять из 8-ми символов, являющихся прописной буквой, заглавной буквой и цифрой.</target>
                </trans-unit>    
                <trans-unit id="password_invalid">
                    <source>password_invalid</source>
                    <target>Пароли не совпадают</target>
                </trans-unit>
                <trans-unit id="password_minlenght">
                    <source>password_minlenght</source>
                    <target>Пароль должен содержать %limit% символы</target>
                </trans-unit>
                <trans-unit id="password_smallletter">
                    <source>password_smallletter</source>
                    <target>Пароль должен содержать хотя бы одну прописную букву</target>
                </trans-unit>
                <trans-unit id="password_capitalletter">
                    <source>password_capitalletter</source>
                    <target>Пароль должен содержать хотя бы одну заглавную букву</target>
                </trans-unit>  
                <trans-unit id="password_digit">
                    <source>password_digit</source>
                    <target>Пароль должен содержать хотя бы одну цифру</target>
                </trans-unit>  
                <trans-unit id="all_items_filled">
                    <source>all_items_filled</source>
                    <target>Все поля должны быть заполнены</target>
                </trans-unit>                  
            </group>     
            
            <group resname="buttons">  
                <note>Buttons</note>                           
                <trans-unit id="btn_save">
                    <source>btn_save</source>
                    <target>Сохранить</target>
                </trans-unit>   
                <trans-unit id="btn_back">
                    <source>btn_back</source>
                    <target>Назад</target>
                </trans-unit>       
                <trans-unit id="select">
                    <source>select</source>
                    <target>Выбрать</target>
                </trans-unit>             
                <trans-unit id="button.pass_generate">
                    <source>button.pass_generate</source>
                    <target>Создать пароль</target>
                </trans-unit>  
                <trans-unit id="btn_apply">
                    <source>btn_apply</source>
                    <target>Применить</target>
                </trans-unit>
                <trans-unit id="btn_cancel">
                    <source>btn_cancel</source>
                    <target>Отменить</target>
                </trans-unit>              
            </group>
            
            <group resname="login">
                <note>Login page</note>
                <trans-unit id="login.title">
                    <source>login.title</source>
                    <target>Зарегистрироваться на Операционном Модуле приложения 2.0 </target>
                </trans-unit>
                <trans-unit id="login.header">
                    <source>login.header</source>
                    <target> Зарегистрироваться на Операционном Модуле приложения 2.0 </target>
                </trans-unit>
                <trans-unit id="login.pass">
                    <source>login.pass</source>
                    <target>Пароль</target>
                </trans-unit>
                <trans-unit id="login.log_in">
                    <source>login.log_in</source>
                    <target>Войти</target>
                </trans-unit>
                <trans-unit id="login.log_out">
                    <source>login.log_out</source>
                    <target>Logout</target>
                </trans-unit>
                <trans-unit id="login.loged_in">
                    <source>login.loged_in</source>
                    <target>Вход выполнен</target>
                </trans-unit>
                <trans-unit id="error.bad_credentials">
                    <source>Bad credentials</source>
                    <target>Неверные персональные данные</target>
                </trans-unit>
                <trans-unit id="error.empty_password">
                    <source>The presented password cannot be empty.</source>
                    <target> Введенный пароль не может быть свободным.</target>
                </trans-unit>
                <trans-unit id="error.password_invalid">
                    <source>The presented password is invalid.</source>
                    <target>Введенный пароль недействителен.</target>
                </trans-unit>
            </group>
            
            <group resname="permissions">
                <note></note>
                <trans-unit id="permission_group_create">
                    <source>permission_group_create</source>
                    <target>Создать группу разрешений</target>
                </trans-unit>
                <trans-unit id="permission_group_edit">
                    <source>permission_group_edit</source>
                    <target>Редактировать группу разрешений</target>
                </trans-unit>
                <trans-unit id="permission_group_list">
                    <source>permission_group_list</source>
                    <target>Список группы разрешений</target>
                </trans-unit>
                <trans-unit id="permission_group_show">
                    <source>permission_group_show</source>
                    <target>Данные группы разрешений</target>
                </trans-unit>
                <trans-unit id="permission_group_delete">
                    <source>permission_group_delete</source>
                    <target>Удалить группу разрешений</target>
                </trans-unit>
                <trans-unit id="permission_template_create">
                    <source>permission_template_create</source>
                    <target>Создание шаблона разрешения</target>
                </trans-unit>
                <trans-unit id="permission_group.title">
                    <source>permission_group.title</source>
                    <target>Группа разрешения</target>
                </trans-unit>
                <trans-unit id="permission_group_removed_passwords">
                    <source>permission_group_removed_passwords</source>
                    <target>Пароли были удалены у этих лиц: </target>
                </trans-unit>
                <trans-unit id="permission_template_edit">
                    <source>permission_template_edit</source>
                    <target>Редактировать шаблон разрешения</target>
                </trans-unit>
                <trans-unit id="permission_template_list">
                    <source>permission_template_list</source>
                    <target>Список шаблонов разрешения</target>
                </trans-unit>
                <trans-unit id="permission_template_show">
                    <source>permission_template_show</source>
                    <target>Данные шаблона разрешения</target>
                </trans-unit>
                <trans-unit id="permission_template.title">
                    <source>permission_template.title</source>
                    <target>Шаблон</target>
                </trans-unit>
                <trans-unit id="permission_template.use_in_messages">
                    <source>permission_template.use_in_messages</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission_template.use_in_messages_help">
                    <source>permission_template.use_in_messages_help</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.description">
                    <source>permission.description</source>
                    <target>Описание</target>
                </trans-unit>
                <trans-unit id="permission.subject_type_person">
                    <source>permission.subject_type_person</source>
                    <target>Люди</target>
                </trans-unit>
                <trans-unit id="permission.subject_type_agency">
                    <source>permission.subject_type_agency</source>
                    <target>Агентства</target>
                </trans-unit>
                <trans-unit id="permission.subject_type_customer">
                    <source>permission.subject_type_customer</source>
                    <target>Клиенты</target>
                </trans-unit>
                <trans-unit id="permissions">
                    <source>permissions</source>
                    <target>Права</target>
                </trans-unit>
                <trans-unit id="menu_access_objects">
                    <source>menu_access_objects</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="object_permission_list">
                    <source>object_permission_list</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="object_permission.title">
                    <source>object_permission.title</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="person_permission_list">
                    <source>person_permission_list</source>
                    <target>Список людей и групп разрешений</target>
                </trans-unit>
                <trans-unit id="person_permission.title">
                    <source>person_permission.title</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission_group.with_permissions">
                    <source>permission_group.with_permissions</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="with_permissions">
                    <source>with_permissions</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="without_permissions">
                    <source>without_permissions</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="menu_access_persons">
                    <source>menu_access_persons</source>
                    <target>Полный список людей</target>
                </trans-unit>
                <trans-unit id="person_permission_template.title">
                    <source>person_permission_template.title</source>
                    <target>Список лиц и их разрешения для шаблонов</target>
                </trans-unit>
                <trans-unit id="person_permission_template_list">
                    <source>person_permission_template_list</source>
                    <target>Список лиц и их разрешения для шаблонов</target>
                </trans-unit>
                <trans-unit id="person_permission_template_edit">
                    <source>person_permission_template_edit</source>
                    <target>Редактировать разрешения лиц для шаблонов</target>
                </trans-unit>                                
                <trans-unit id="menu_access_persons_template">
                    <source>menu_access_persons_template</source>
                    <target>Разрешения лиц для шаблонов</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_COMMON">
                    <source>permission.PERM_COMMON</source>
                    <target>Стандартный</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECTS">
                    <source>permission.PERM_OBJECTS</source>
                    <target>Действия с объектом</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_SECRET_SHOW">
                    <source>permission.PERM_POSITION_SECRET_SHOW</source>
                    <target>Показать скрытые позиции</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_SELECT">
                    <source>permission.PERM_OBJECT_GROUP_SELECT</source>
                    <target>Выбрать группу объектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PANIC_RECEIVE">
                    <source>permission.PERM_PANIC_RECEIVE</source>
                    <target>Получать тревожное сообщение</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_CREATE">
                    <source>permission.PERM_OBJECT_CREATE</source>
                    <target>Создать объект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_DELETE">
                    <source>permission.PERM_OBJECT_DELETE</source>
                    <target>Удалить объект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_EDIT">
                    <source>permission.PERM_OBJECT_EDIT</source>
                    <target>Редактировать объект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_EDIT_ALL">
                    <source>permission.PERM_OBJECT_EDIT_ALL</source>
                    <target>Редактировать объект - выполнить</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_ACTIVATE">
                    <source>permission.PERM_OBJECT_ACTIVATE</source>
                    <target>Активировать объект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_SHOW">
                    <source>permission.PERM_OBJECT_SHOW</source>
                    <target>Показать объект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_CREATE">
                    <source>permission.PERM_POPS_CREATE</source>
                    <target>Создать ОВУ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EDIT">
                    <source>permission.PERM_POPS_EDIT</source>
                    <target>Редактировать ОВУ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_LOCK">
                    <source>permission.PERM_POPS_LOCK</source>
                    <target>Блокировать ОВУ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_SHOW">
                    <source>permission.PERM_POPS_SHOW</source>
                    <target>Показать ОВУ</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_POPS_EVENT_CREATE">
                    <source>permission.PERM_POPS_EVENT_CREATE</source>
                    <target>Создать событие ОВУ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENT_EDIT">
                    <source>permission.PERM_POPS_EVENT_EDIT</source>
                    <target>Редактировать событие ОВУ</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_POPS_EVENT_SHOW">
                    <source>permission.PERM_POPS_EVENT_SHOW</source>
                    <target>Показать событие ОВУ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENT_TIME">
                    <source>permission.PERM_POPS_EVENT_TIME</source>
                    <target>Редактировать событие ОВУ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_CREATE">
                    <source>permission.PERM_ABSENCE_CREATE</source>
                    <target>Создать отсутствие</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_EDIT">
                    <source>permission.PERM_ABSENCE_EDIT</source>
                    <target>Редактировать отсутствие</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_DELETE">
                    <source>permission.PERM_ABSENCE_DELETE</source>
                    <target>Удалить отсутствие</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_SHOW">
                    <source>permission.PERM_ABSENCE_SHOW</source>
                    <target>Показать отсутствие</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_ABSENCE_TYPE_CREATE">
                    <source>permission.PERM_ABSENCE_TYPE_CREATE</source>
                    <target>Создать тип отсутствия</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_TYPE_EDIT">
                    <source>permission.PERM_ABSENCE_TYPE_EDIT</source>
                    <target> Редактировать тип отсутствия </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_TYPE_DELETE">
                    <source>permission.PERM_ABSENCE_TYPE_DELETE</source>
                    <target>Удалить тип отсутствия </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_TYPE_SHOW">
                    <source>permission.PERM_ABSENCE_TYPE_SHOW</source>
                    <target>Показать тип отсутствия </target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_CREATE">
                    <source>permission.PERM_AGENC_RATE_CREATE</source>
                    <target>Создать Агентский тариф</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_EDIT">
                    <source>permission.PERM_AGENC_RATE_EDIT</source>
                    <target>Редактировать Агентский тариф</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_DELETE">
                    <source>permission.PERM_AGENC_RATE_DELETE</source>
                    <target> Удалить Агентский тариф </target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_SHOW">
                    <source>permission.PERM_AGENC_RATE_SHOW</source>
                    <target>Показать Агентский тариф </target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_CREATE">
                    <source>permission.PERM_AGENCY_CREATE</source>
                    <target>Создать Агентство</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_EDIT">
                    <source>permission.PERM_AGENCY_EDIT</source>
                    <target>Редактировать Агентство</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_DELETE">
                    <source>permission.PERM_AGENCY_DELETE</source>
                    <target>Удалить Агентство</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SHOW">
                    <source>permission.PERM_AGENCY_SHOW</source>
                    <target>Показать Агентство</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SERVICE_SHOW">
                    <source>permission.PERM_AGENCY_SERVICE_SHOW</source>
                    <target>Показать документацию для поставщика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SERVICE_DETAIL_SHOW">
                    <source>permission.PERM_AGENCY_SERVICE_DETAIL_SHOW</source>
                    <target>Показать данные о документации для поставщика</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_CREATE">
                    <source>permission.PERM_AGENCY_SUBJECT_CREATE</source>
                    <target>Создать участника агентства</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_EDIT">
                    <source>permission.PERM_AGENCY_SUBJECT_EDIT</source>
                    <target> Редактировать участника агентства </target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_DELETE">
                    <source>permission.PERM_AGENCY_SUBJECT_DELETE</source>
                    <target>Удалить участника агентства</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_SHOW">
                    <source>permission.PERM_AGENCY_SUBJECT_SHOW</source>
                    <target>Показать участника агентства</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_END_CREATE">
                    <source>permission.PERM_ATTENDANCE_END_CREATE</source>
                    <target>Создать окончание обслуживания</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_END_EDIT">
                    <source>permission.PERM_ATTENDANCE_END_EDIT</source>
                    <target> Редактировать окончание обслуживания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_END_SHOW">
                    <source>permission.PERM_ATTENDANCE_END_SHOW</source>
                    <target>Показать окончание обслуживания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_NO_PLAN_SHOW">
                    <source>permission.PERM_ATTENDANCE_NO_PLAN_SHOW</source>
                    <target>Показать список "несуществующего" плана</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_NOT_APPROVED_EXPORT">
                    <source>permission.PERM_ATTENDANCE_NOT_APPROVED_EXPORT</source>
                    <target>Экспорт неодобренного присутствия и планов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_NOT_APPROVED_SHOW">
                    <source>permission.PERM_ATTENDANCE_NOT_APPROVED_SHOW</source>
                    <target>Показать неодобренные присутствия и планы</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_RUNNING_EDIT">
                    <source>permission.PERM_ATTENDANCE_RUNNING_EDIT</source>
                    <target>Редактировать текущее обслуживание</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_RUNNING_SHOW">
                    <source>permission.PERM_ATTENDANCE_RUNNING_SHOW</source>
                    <target>Показать текущее обслуживание </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_CREATE">
                    <source>permission.PERM_ATTENDANCE_START_CREATE</source>
                    <target>Создать начало обслуживания</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_EDIT">
                    <source>permission.PERM_ATTENDANCE_START_EDIT</source>
                    <target> Редактировать начало обслуживания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_DELETE">
                    <source>permission.PERM_ATTENDANCE_START_DELETE</source>
                    <target>Удалить начало обслуживания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_CREATE_SERVICE">
                    <source>permission.PERM_ATTENDANCE_START_CREATE_SERVICE</source>
                    <target>Установить на обслуживание статус "не началось"</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_SHOW">
                    <source>permission.PERM_ATTENDANCE_START_SHOW</source>
                    <target>Показать начало обслуживания </target>
                </trans-unit>
                <trans-unit id="permission.ATTENDANCE_START_CHECK_SHOW">
                    <source>permission.PERM_ATTENDANCE_START_CHECK_SHOW</source>
                    <target>Показать преждевременное присутствие</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_SHOW">
                    <source>permission.PERM_ATTENDANCE_SHOW</source>
                    <target>Показать полный список присутствия</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_EDIT">
                    <source>permission.PERM_ATTENDANCE_EDIT</source>
                    <target>Редактировать полный список присутствия </target>
                </trans-unit>

                <trans-unit id="permission.PERM_BILLING_DOCUMENT_ATTENDANCE_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_ATTENDANCE_SHOW</source>
                    <target>Показать документацию по биллингу - Присутствие</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_SERVICE_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_SERVICE_SHOW</source>
                    <target> Показать документацию по биллингу - План</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_ANNEX_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_ANNEX_SHOW</source>
                    <target> Показать документацию по биллингу – Создать документацию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_SUBJECT_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_SUBJECT_SHOW</source>
                    <target> Показать документацию по биллингу – Подробности о присутствии человека</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_EXPORT">
                    <source>permission.PERM_BILLING_DOCUMENT_EXPORT</source>
                    <target>Документация по биллингу – экспорт расходов</target>
                </trans-unit>
                                                
                <trans-unit id="permission.PERM_CALENDAR_ADMIN">
                    <source>permission.PERM_CALENDAR_ADMIN</source>
                    <target>Show holidays' callender</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CALENDAR_ADMIN_EDIT">
                    <source>permission.PERM_CALENDAR_ADMIN_EDIT</source>
                    <target>Редактировать календарь отпусков</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_CREATE">
                    <source>permission.PERM_CITY_CREATE</source>
                    <target>Создать город</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_EDIT">
                    <source>permission.PERM_CITY_EDIT</source>
                    <target>Редактировать город</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_DELETE">
                    <source>permission.PERM_CITY_DELETE</source>
                    <target>Удалить город</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_SHOW">
                    <source>permission.PERM_CITY_SHOW</source>
                    <target>Показать город </target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_CREATE">
                    <source>permission.PERM_COMPANY_CREATE</source>
                    <target>Создать компанию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_EDIT">
                    <source>permission.PERM_COMPANY_EDIT</source>
                    <target>Редактировать компанию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_DELETE">
                    <source>permission.PERM_COMPANY_DELETE</source>
                    <target>Удалить компанию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_SHOW">
                    <source>permission.PERM_COMPANY_SHOW</source>
                    <target>Показать компанию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_CREATE">
                    <source>permission.PERM_CONTRACT_CREATE</source>
                    <target>Создать контракт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_EDIT">
                    <source>permission.PERM_CONTRACT_EDIT</source>
                    <target>Редактировать контракт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_DELETE">
                    <source>permission.PERM_CONTRACT_DELETE</source>
                    <target>Удалить контракт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_SHOW">
                    <source>permission.PERM_CONTRACT_SHOW</source>
                    <target>Показать контракт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_CREATE">
                    <source>permission.PERM_COUNTRY_CREATE</source>
                    <target>Создать страну</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_EDIT">
                    <source>permission.PERM_COUNTRY_EDIT</source>
                    <target>Редактировать страну</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_DELETE">
                    <source>permission.PERM_COUNTRY_DELETE</source>
                    <target>Удалить страну</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_SHOW">
                    <source>permission.PERM_COUNTRY_SHOW</source>
                    <target>Показать страну</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_CREATE">
                    <source>permission.PERM_CURRENCY_CREATE</source>
                    <target>Создать валюту</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_EDIT">
                    <source>permission.PERM_CURRENCY_EDIT</source>
                    <target>Редактировать валюту</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_DELETE">
                    <source>permission.PERM_CURRENCY_DELETE</source>
                    <target>Удалить валюту</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_SHOW">
                    <source>permission.PERM_CURRENCY_SHOW</source>
                    <target>Показать валюту</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_CREATE">
                    <source>permission.PERM_CUSTOMERS_CREATE</source>
                    <target>Создать заказчика</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_EDIT">
                    <source>permission.PERM_CUSTOMERS_EDIT</source>
                    <target>Редактировать заказчика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_DELETE">
                    <source>permission.PERM_CUSTOMERS_DELETE</source>
                    <target>Удалить заказчика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_SHOW">
                    <source>permission.PERM_CUSTOMERS_SHOW</source>
                    <target>Показать заказчика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_EDIT_PASSWORD">
                    <source>permission.PERM_CUSTOMERS_EDIT_PASSWORD</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_CREATE">
                    <source>permission.PERM_CUSTOMER_CONTACT_CREATE</source>
                    <target>Добавить контактное лицо заказчика</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_EDIT">
                    <source>permission.PERM_CUSTOMER_CONTACT_EDIT</source>
                    <target>Редактировать контактное лицо заказчика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_DELETE">
                    <source>permission.PERM_CUSTOMER_CONTACT_DELETE</source>
                    <target>Аннулировать контактное лицо заказчика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_SHOW">
                    <source>permission.PERM_CUSTOMER_CONTACT_SHOW</source>
                    <target>Показать контактное лицо заказчика </target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_CREATE">
                    <source>permission.PERM_DIMENSION_CREATE</source>
                    <target>Создать величину</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_EDIT">
                    <source>permission.PERM_DIMENSION_EDIT</source>
                    <target>Редактировать величину</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_DELETE">
                    <source>permission.PERM_DIMENSION_DELETE</source>
                    <target>Удалить величину</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_SHOW">
                    <source>permission.PERM_DIMENSION_SHOW</source>
                    <target>Показать величину</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_CREATE">
                    <source>permission.PERM_DIMENSION_LEVEL_CREATE</source>
                    <target>Создать уровень величины</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_EDIT">
                    <source>permission.PERM_DIMENSION_LEVEL_EDIT</source>
                    <target>Редактировать уровень величины </target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_DELETE">
                    <source>permission.PERM_DIMENSION_LEVEL_DELETE</source>
                    <target>Удалить уровень величины </target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_SHOW">
                    <source>permission.PERM_DIMENSION_LEVEL_SHOW</source>
                    <target>Показать уровень величины </target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_CREATE">
                    <source>permission.PERM_EVENT_CREATE</source>
                    <target>Создать событие из дерева событий</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_EDIT">
                    <source>permission.PERM_EVENT_EDIT</source>
                    <target>Редактировать событие из дерева событий </target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_ACTIVATE">
                    <source>permission.PERM_EVENT_ACTIVATE</source>
                    <target>Активировать событие из дерева событий </target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_HIDE">
                    <source>permission.PERM_EVENT_HIDE</source>
                    <target>Скрыть событие в дереве событий</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_EVENT_DELETE">
                    <source>permission.PERM_EVENT_DELETE</source>
                    <target>Удалить событие из дерева событий </target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_SHOW">
                    <source>permission.PERM_EVENT_SHOW</source>
                    <target>Показать событие из дерева событий </target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_CREATE">
                    <source>permission.PERM_INDIVIDUAL_RATE_CREATE</source>
                    <target>Создать индивидуальный тариф</target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_EDIT">
                    <source>permission.PERM_INDIVIDUAL_RATE_EDIT</source>
                    <target>Редактировать индивидуальный тариф </target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_DELETE">
                    <source>permission.PERM_INDIVIDUAL_RATE_DELETE</source>
                    <target>Удалить индивидуальный тариф </target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_SHOW">
                    <source>permission.PERM_INDIVIDUAL_RATE_SHOW</source>
                    <target>Показать индивидуальный тариф </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOG">
                    <source>permission.PERM_LOG</source>
                    <target>Регистрировать доступ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGIN_LOG_SHOW">
                    <source>permission.PERM_LOGIN_LOG_SHOW</source>
                    <target>Показать журнал входов/выходов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGIN_LOG_EXPORT">
                    <source>permission.PERM_LOGIN_LOG_EXPORT</source>
                    <target>Экспортировать журнал входов/выходов </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_DATA_SHOW">
                    <source>permission.PERM_LOGS_DATA_SHOW</source>
                    <target>Показать журнал действий с данными </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_DATA_EXPORT">
                    <source>permission.PERM_LOGS_DATA_EXPORT</source>
                    <target>Экспортировать журнал действий с данными </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_EMERGENCY_SHOW">
                    <source>permission.PERM_LOGS_EMERGENCY_SHOW</source>
                    <target>Показать журнал тревожных сообщений</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_EMERGENCY_EXPORT">
                    <source>permission.PERM_LOGS_EMERGENCY_EXPORT</source>
                    <target>Экспортировать журнал тревожных сообщений </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_REPORTING_SHOW">
                    <source>permission.PERM_LOGS_REPORTING_SHOW</source>
                    <target>Показать журнал передачи сообщений</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_REPORTING_EXPORT">
                    <source>permission.PERM_LOGS_REPORTING_EXPORT</source>
                    <target>Экспортировать журнал передачи сообщений </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ERRORS_SHOW">
                    <source>permission.PERM_LOGS_ERRORS_SHOW</source>
                    <target>Показать журнал обнаруженных ошибок</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ERRORS_EXPORT">
                    <source>permission.PERM_LOGS_ERRORS_EXPORT</source>
                    <target>Экспортировать журнал обнаруженных ошибок </target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ALL LOG">
                    <source>permission.PERM_LOGS_ALL_SHOW</source>
                    <target>Показать журнал всех журналов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ALL_DELETE">
                    <source>permission.PERM_LOGS_ALL_DELETE</source>
                    <target>Стереть все журналы</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_PM_CREATE">
                    <source>permission.PERM_NEWS_PM_CREATE</source>
                    <target>Создать сообщение Операционного Модуля (ОМ)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_PM_SHOW">
                    <source>permission.PERM_NEWS_PM_SHOW</source>
                    <target>Показать ОМ сообщение (администрация)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_OPM_CREATE">
                    <source>permission.PERM_NEWS_OPM_CREATE</source>
                    <target>Создать сообщение Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_OPM_SHOW">
                    <source>permission.PERM_NEWS_OPM_SHOW</source>
                    <target>Показать сообщение Клиентского доступа (администрация)</target>
                </trans-unit>                                                                
                <trans-unit id="permission.PERM_LOGS_ALL_EXPORT">
                    <source>permission.PERM_LOGS_ALL_EXPORT</source>
                    <target>Экспортировать все журналы </target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_CREATE">
                    <source>permission.PERM_OBJECT_GROUP_CREATE</source>
                    <target>Создать группу объектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_EDIT">
                    <source>permission.PERM_OBJECT_GROUP_EDIT</source>
                    <target>Редактировать группу объектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_DELETE">
                    <source>permission.PERM_OBJECT_GROUP_DELETE</source>
                    <target>Удалить группу объектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_SHOW">
                    <source>permission.PERM_OBJECT_GROUP_SHOW</source>
                    <target>Показать группу объектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PANIC_BUTTON">
                    <source>permission.PERM_PANIC_BUTTON</source>
                    <target>Нажать на тревожную кнопку</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_CREATE">
                    <source>permission.PERM_PERIOD_CREATE</source>
                    <target>Создать период</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_EDIT">
                    <source>permission.PERM_PERIOD_EDIT</source>
                    <target>Редактировать период</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_ACTIVATE">
                    <source>permission.PERM_PERIOD_ACTIVATE</source>
                    <target>Активировать/деактивировать период</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_DELETE">
                    <source>permission.PERM_PERIOD_DELETE</source>
                    <target>Удалить период</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_SHOW">
                    <source>permission.PERM_PERIOD_SHOW</source>
                    <target>Показать период</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_COPY_SHOW">
                    <source>permission.PERM_PERIOD_COPY_SHOW</source>
                    <target>Показать копию месяца</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_COPY_CREATE">
                    <source>permission.PERM_PERIOD_COPY_CREATE</source>
                    <target>Создать копию месяца</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_COPY_EXPORT">
                    <source>permission.PERM_PERIOD_COPY_EXPORT</source>
                    <target>Экспорт копии месяца</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_CREATE">
                    <source>permission.PERM_PERIODIC_PAYMENT_CREATE</source>
                    <target>Создать периодический платёж</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_EDIT">
                    <source>permission.PERM_PERIODIC_PAYMENT_EDIT</source>
                    <target>Редактировать периодический платёж </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_APPROBATE">
                    <source>permission.PERM_PERIODIC_PAYMENT_APPROBATE</source>
                    <target>Одобрить/отвргнуть периодический платёж </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_DELETE">
                    <source>permission.PERM_PERIODIC_PAYMENT_DELETE</source>
                    <target>Удалить периодический платёж </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_SHOW">
                    <source>permission.PERM_PERIODIC_PAYMENT_SHOW</source>
                    <target>Показать периодический платёж </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_UNBLOCK">
                    <source>permission.PERM_PERIODIC_PAYMENT_UNBLOCK</source>
                    <target>Разблокировать периодические платежи (после экспорта)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_CREATE">
                    <source>permission.PERM_PERMISSION_GROUP_CREATE</source>
                    <target>Создать группу разрешения</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_DELETE">
                    <source>permission.PERM_PERMISSION_GROUP_DELETE</source>
                    <target>Удалить группу разрешения </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_EDIT">
                    <source>permission.PERM_PERMISSION_GROUP_EDIT</source>
                    <target>Редактировать группу разрешения </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_SHOW">
                    <source>permission.PERM_PERMISSION_GROUP_SHOW</source>
                    <target>Показать группу разрешения </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_CREATE">
                    <source>permission.PERM_PERMISSION_TEMPLATE_CREATE</source>
                    <target>Создать шаблон разрешения</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_DELETE">
                    <source>permission.PERM_PERMISSION_TEMPLATE_DELETE</source>
                    <target>Удалить шаблон разрешения </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_EDIT">
                    <source>permission.PERM_PERMISSION_TEMPLATE_EDIT</source>
                    <target>Редактировать шаблон разрешения </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_SHOW">
                    <source>permission.PERM_PERMISSION_TEMPLATE_SHOW</source>
                    <target>Показать шаблон разрешения </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_CREATE">
                    <source>permission.PERM_PERSON_CREATE</source>
                    <target>Создать субъекта</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_EDIT">
                    <source>permission.PERM_PERSON_EDIT</source>
                    <target>Редактировать субъект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_DELETE">
                    <source>permission.PERM_PERSON_DELETE</source>
                    <target>Аннулировать субъект</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_ADDRESS_SHOW">
                    <source>permission.PERM_PERSON_ADDRESS_SHOW</source>
                    <target>Работа по адресу персоны</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_SHOW">
                    <source>permission.PERM_PERSON_SHOW</source>
                    <target>Показать субъекта</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_PASSWORD">
                    <source>permission.PERM_SUBJECT_ACCESS_PASSWORD</source>
                    <target>Сделать ОМ доступным/запретить доступ на ОМ</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_PASSWORD_CUSTOMER">
                    <source>permission.PERM_SUBJECT_ACCESS_PASSWORD_CUSTOMER</source>
                    <target>Сделать доступным доступ Клиента / запретить доступ Клиента</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_CREATE">
                    <source>permission.PERM_PERSONS_RATE_CREATE</source>
                    <target>Создать индивидуальный тариф</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_EDIT">
                    <source>permission.PERM_PERSONS_RATE_EDIT</source>
                    <target>Редактировать индивидуальный тариф</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_DELETE">
                    <source>permission.PERM_PERSONS_RATE_DELETE</source>
                    <target>Удалить индивидуальный тариф </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_SHOW">
                    <source>permission.PERM_PERSONS_RATE_SHOW</source>
                    <target>Показать индивидуальный тариф </target>
                </trans-unit>                
                <trans-unit id="permission.PERM_POPSLIST_EDIT">
                    <source>permission.PERM_POPSLIST_EDIT</source>
                    <target>Редактировать ОВУ (архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPSLIST_SHOW">
                    <source>permission.PERM_POPSLIST_SHOW</source>
                    <target>Показать ОВУ (архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPSLIST_EXPORT">
                    <source>permission.PERM_POPSLIST_EXPORT</source>
                    <target>Экспортировать ОВУ (архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_EDIT">
                    <source>permission.PERM_POPS_EVENTS_EDIT</source>
                    <target> Редактировать событие ОВУ(архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_TRANSLATE">
                    <source>permission.PERM_POPS_EVENTS_TRANSLATE</source>
                    <target>Перевести событие ОВУ (архивировать)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_FOR_CUSTOMER">
                    <source>permission.PERM_POPS_EVENTS_FOR_CUSTOMER</source>
                    <target>Показать/скрыть от клиента (архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_DELETE">
                    <source>permission.PERM_POPS_EVENTS_DELETE</source>
                    <target>Удалить событие ОВУ(архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_SHOW">
                    <source>permission.PERM_POPS_EVENTS_SHOW</source>
                    <target>Показать событие ОВУ (архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_EXPORT">
                    <source>permission.PERM_POPS_EVENTS_EXPORT</source>
                    <target>Экспортировать событие ОВУ (архив)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_EDIT">
                    <source>permission.PERM_OBJECT_POPS_EDIT</source>
                    <target>Редактировать ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_DELETE">
                    <source>permission.PERM_OBJECT_POPS_DELETE</source>
                    <target>Удалить ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_EXPORT">
                    <source>permission.PERM_OBJECT_POPS_EXPORT</source>
                    <target>Экспортировать список ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_SHOW">
                    <source>permission.PERM_OBJECT_POPS_SHOW</source>
                    <target>Показать ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_EDIT">
                    <source>permission.PERM_EVENTS_OBJECT_EDIT</source>
                    <target>Редактировать событие ОВУ (на объекте)</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_EVENTS_OBJECT_TRANSLATE">
                    <source>permission.PERM_EVENTS_OBJECT_TRANSLATE</source>
                    <target>Перевести событие ОВУ (для объекта)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_FOR_CUSTOMER">
                    <source>permission.PERM_EVENTS_OBJECT_FOR_CUSTOMER</source>
                    <target>Показать/скрыть от клиента (для объекта)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_DELETE">
                    <source>permission.PERM_EVENTS_OBJECT_DELETE</source>
                    <target>Удалить событие ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_EXPORT">
                    <source>permission.PERM_EVENTS_OBJECT_EXPORT</source>
                    <target>Экспортировать список событий ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_SHOW">
                    <source>permission.PERM_EVENTS_OBJECT_SHOW</source>
                    <target>Показать событие ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_CREATE">
                    <source>permission.PERM_POSITION_RATE_CREATE</source>
                    <target>Создать тариф для позиции</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_EDIT">
                    <source>permission.PERM_POSITION_RATE_EDIT</source>
                    <target>Редактировать тариф для позиции </target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_DELETE">
                    <source>permission.PERM_POSITION_RATE_DELETE</source>
                    <target>Удалить тариф для позиции </target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_SHOW">
                    <source>permission.PERM_POSITION_RATE_SHOW</source>
                    <target>Показать тариф для позиции </target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_CREATE">
                    <source>permission.PERM_PRODUCT_CREATE</source>
                    <target>Создать продукт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_EDIT">
                    <source>permission.PERM_PRODUCT_EDIT</source>
                    <target>Редактировать продукт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_ACTIVATE">
                    <source>permission.PERM_PRODUCT_ACTIVATE</source>
                    <target>Активировать/деактивировать продукт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_DELETE">
                    <source>permission.PERM_PRODUCT_DELETE</source>
                    <target>Удалить продукт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_SHOW">
                    <source>permission.PERM_PRODUCT_SHOW</source>
                    <target>Показать продукт</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_CREATE">
                    <source>permission.PERM_REPORTING_CREATE</source>
                    <target>Создать установки передачи сообщений</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_EDIT">
                    <source>permission.PERM_REPORTING_EDIT</source>
                    <target>Редактировать установки передачи сообщений </target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_DELETE">
                    <source>permission.PERM_REPORTING_DELETE</source>
                    <target>Удалить установки передачи сообщений </target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_SHOW">
                    <source>permission.PERM_REPORTING_SHOW</source>
                    <target>Показать установки передачи сообщений </target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_CREATE">
                    <source>permission.PERM_REPORTING_TEMPLATE_CREATE</source>
                    <target>Создать шаблон для установки составления отчета</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_EDIT">
                    <source>permission.PERM_REPORTING_TEMPLATE_EDIT</source>
                    <target>Редактировать шаблон для установки составления отчета</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_DELETE">
                    <source>permission.PERM_REPORTING_TEMPLATE_DELETE</source>
                    <target>Удалить шаблон для установки составления отчета</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_SHOW">
                    <source>permission.PERM_REPORTING_TEMPLATE_SHOW</source>
                    <target>Показать шаблон для установки составления отчета</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_CREATE">
                    <source>permission.PERM_ROLE_CREATE</source>
                    <target>Создать позицию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_EDIT">
                    <source>permission.PERM_ROLE_EDIT</source>
                    <target>Редактировать позицию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_ACTIVATE">
                    <source>permission.PERM_ROLE_ACTIVATE</source>
                    <target>Активировать/деактивировать позицию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_DELETE">
                    <source>permission.PERM_ROLE_DELETE</source>
                    <target>Удалить позицию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_SHOW">
                    <source>permission.PERM_ROLE_SHOW</source>
                    <target>Показать позицию</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_CREATE">
                    <source>permission.PERM_ROLE_GROUP_CREATE</source>
                    <target>Создать группу позиций</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_EDIT">
                    <source>permission.PERM_ROLE_GROUP_EDIT</source>
                    <target>Редактировать группу позиций</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_DELETE">
                    <source>permission.PERM_ROLE_GROUP_DELETE</source>
                    <target>Удалить группу позиций</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_SHOW">
                    <source>permission.PERM_ROLE_GROUP_SHOW</source>
                    <target>Показать группу позиций</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_CREATE">
                    <source>permission.PERM_SERVICE_ATTENDANCE_CREATE</source>
                    <target>Создать присутствие (Подтверждение присутствия)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_EDIT">
                    <source>permission.PERM_SERVICE_ATTENDANCE_EDIT</source>
                    <target>Редактировать присутствие  (Подтверждение присутствия)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_APPROVE">
                    <source>permission.PERM_SERVICE_ATTENDANCE_APPROVE</source>
                    <target>Одобрить/не одобрить присутствие (Подтверждение присутствия)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_DELETE">
                    <source>permission.PERM_SERVICE_ATTENDANCE_DELETE</source>
                    <target>Удалить присутствие (Подтверждение присутствия)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_SHOW">
                    <source>permission.PERM_SERVICE_ATTENDANCE_SHOW</source>
                    <target>Показать присутствие (Подтверждение присутствия)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_UNBLOCK">
                    <source>permission.PERM_SERVICE_ATTENDANCE_UNBLOCK</source>
                    <target>Разблокировать присутствие (после экспорта)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_CREATE">
                    <source>permission.PERM_SERVICE_BILLING_CREATE</source>
                    <target>Создать обслуживание (Подтверждение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_SHOW_RATE">
                    <source>permission.PERM_SERVICE_BILLING_SHOW_RATE</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_EDIT">
                    <source>permission.PERM_SERVICE_BILLING_EDIT</source>
                    <target>Редактировать обслуживание (Подтверждение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_APPROVE">
                    <source>permission.PERM_SERVICE_BILLING_APPROVE</source>
                    <target>Одобрить/не одобрить обслуживание (Подтверждение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_DELETE">
                    <source>permission.PERM_SERVICE_BILLING_DELETE</source>
                    <target>Удалить обслуживание (Подтверждение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_SHOW">
                    <source>permission.PERM_SERVICE_BILLING_SHOW</source>
                    <target>Показать обслуживание (Подтверждение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_UNBLOCK">
                    <source>permission.PERM_SERVICE_BILLING_UNBLOCK</source>
                    <target>Разблокировать присутствие (после экспорта)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_UPDATE_RATES">
                    <source>permission.PERM_SERVICE_BILLING_UPDATE_RATES</source>
                    <target>Обновить тарифы биллинга (Одобрение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_EDIT_EDIT">
                    <source>permission.PERM_SERVICE_EDIT_EDIT</source>
                    <target>Редактировать обслуживание  (Подтверждение биллинга)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_EDIT_SHOW">
                    <source>permission.PERM_SERVICE_EDIT_SHOW</source>
                    <target>Показать обслуживание (Редактор обслуживания)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_CREATE_CREATE">
                    <source>permission.PERM_SERVICE_CREATE_CREATE</source>
                    <target>Вставить обслуживания - генерировать</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_DELETE">
                    <source>permission.PERM_SUBJECT_ACCESS_DELETE</source>
                    <target>Удалить доступ субъекта</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_SHOW">
                    <source>permission.PERM_SUBJECT_ACCESS_SHOW</source>
                    <target>Показать доступ субъекта </target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_CREATE">
                    <source>permission.PERM_SUBJECT_GROUP_CREATE</source>
                    <target>Создать группу субъектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_EDIT">
                    <source>permission.PERM_SUBJECT_GROUP_EDIT</source>
                    <target>Редактировать группу субъектов</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_DELETE">
                    <source>permission.PERM_SUBJECT_GROUP_DELETE</source>
                    <target>Удалить группу субъектов </target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_SHOW">
                    <source>permission.PERM_SUBJECT_GROUP_SHOW</source>
                    <target>Показать группу субъектов </target>
                </trans-unit>
                <trans-unit id="permission.PERM_OPM_POPS_SHOW">
                    <source>permission.PERM_OPM_POPS_SHOW</source>
                    <target>Доступ Клиента – Показать ОВУ, Редактировать установки передачи сообщений</target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_SHOW">
                    <source>permission.PERM_FLUCTUATION_SHOW</source>
                    <target>Показать колебания</target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_DELETE">
                    <source>permission.PERM_FLUCTUATION_DELETE</source>
                    <target>Стереть колебания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_EXPORT">
                    <source>permission.PERM_FLUCTUATION_EXPORT</source>
                    <target>Экспортировать колебания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_GENERATE">
                    <source>permission.PERM_FLUCTUATION_GENERATE</source>
                    <target>Генерировать колебания </target>
                </trans-unit>
                <trans-unit id="permission.PERM_CD_PROFIT_SHOW">
                    <source>permission.PERM_CD_PROFIT_SHOW</source>
                    <target>Показать коэффициент Специального агентства</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_CD_PROFIT_EXPORT">
                    <source>permission.PERM_OBJECT_CD_PROFIT_EXPORT</source>
                    <target>Экспортировать коэффициент Специального агентства </target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISABLE_AUTOLOGOUT">
                    <source>permission.PERM_DISABLE_AUTOLOGOUT</source>
                    <target>Отключить авто-выход из системы</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISABLE_CONTROL_PRESENCE">
                    <source>permission.PERM_DISABLE_CONTROL_PRESENCE</source>
                    <target>Отключить проверку присутствия</target>
                </trans-unit>                                

                <trans-unit id="permission.PERM_ARCHIVES_SERVICE_SHOW">
                    <source>permission.PERM_ARCHIVES_SERVICE_SHOW</source>
                    <target>Архив - показать услуги</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_SERVICE_DELETE">
                    <source>permission.PERM_ARCHIVES_SERVICE_DELETE</source>
                    <target>Архив - удалить услугу</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_SERVICE_EXPORT">
                    <source>permission.PERM_ARCHIVES_SERVICE_EXPORT</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_ATTENDANCE_SHOW">
                    <source>permission.PERM_ARCHIVES_ATTENDANCE_SHOW</source>
                    <target>Архив - показать присутствия</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_ATTENDANCE_DELETE">
                    <source>permission.PERM_ARCHIVES_ATTENDANCE_DELETE</source>
                    <target>Архив - удалить присутствие</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_ATTENDANCE_EXPORT">
                    <source>permission.PERM_ARCHIVES_ATTENDANCE_EXPORT</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_POPS_SHOW">
                    <source>permission.PERM_ARCHIVES_POPS_SHOW</source>
                    <target>Архив - показать ОВУ и его события</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_POPS_DELETE">
                    <source>permission.PERM_ARCHIVES_POPS_DELETE</source>
                    <target>Архив - удалить ОВУ и его события</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_POPS_EXPORT">
                    <source>permission.PERM_ARCHIVES_POPS_EXPORT</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_LOG_SHOW">
                    <source>permission.PERM_ARCHIVES_LOG_SHOW</source>
                    <target>Архив - показать протокол процесса архивации</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_LOG_DELETE">
                    <source>permission.PERM_ARCHIVES_LOG_DELETE</source>
                    <target>Архив - удалить протокол</target>
                </trans-unit>                                                                                                                 
                <trans-unit id="permission.PERM_ARCHIVES_SETTINGS">
                    <source>permission.PERM_ARCHIVES_SETTINGS</source>
                    <target>Архив - установки</target>
                </trans-unit>      
                <trans-unit id="permission.PERM_MESSAGE_SEN">
                    <source>permission.PERM_MESSAGE_SEN</source>
                    <target>Создать и отправить сообщения</target>
                </trans-unit>       
                <trans-unit id="permission.PERM_MESSAGE_SEND">
                    <source>permission.PERM_MESSAGE_SEND</source>
                    <target>Отправить сообщения</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MESSAGE_SEND_PERM_TEMPLATE">
                    <source>permission.PERM_MESSAGE_SEND_PERM_TEMPLATE</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_PASS_AUTO">
                    <source>permission.PERM_PASS_AUTO</source>
                    <target>Создавать пароли автоматически</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SUBJECT_HIRE">
                    <source>permission.PERM_SUBJECT_HIRE</source>
                    <target>Нанять лиц с других объектов</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SEND_CONTROL_MESSAGE">
                    <source>permission.PERM_SEND_CONTROL_MESSAGE</source>
                    <target></target>
                </trans-unit>          
                <trans-unit id="permission.PERM_NAVISION_XLS_EXPORT">
                    <source>permission.PERM_NAVISION_XLS_EXPORT</source>
                    <target></target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ATTENDANCE_LENGTH_SHOW">
                    <source>permission.PERM_ATTENDANCE_LENGTH_SHOW</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_LENGTH_EXPORT">
                    <source>permission.PERM_ATTENDANCE_LENGTH_EXPORT</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="permission.PERM_CUSTOMER_LOGIN_LOG_SHOW">
                    <source>permission.PERM_CUSTOMER_LOGIN_LOG_SHOW</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_LOGIN_LOG_EXPORT">
                    <source>permission.PERM_CUSTOMER_LOGIN_LOG_EXPORT</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_CREATE">
                    <source>permission.PERM_SERVICE_TEMPLATE_CREATE</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_EDIT">
                    <source>permission.PERM_SERVICE_TEMPLATE_EDIT</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_DELETE">
                    <source>permission.PERM_SERVICE_TEMPLATE_DELETE</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_SHOW">
                    <source>permission.PERM_SERVICE_TEMPLATE_SHOW</source>
                    <target></target>
                </trans-unit>       
            </group>
            
            <!-- Dashboard - Číselníky -->
            <group resname="dashboard">
                <note>Application dashboards</note>
                <trans-unit id="dashboard.title">
                    <source>dashboard.title</source>
                    <target>Метки групп</target>
                </trans-unit>
                <trans-unit id="country_city_group_label">
                    <source>country_city_group_label</source>
                    <target>Метка группы по странам и городам </target>
                </trans-unit>
                <trans-unit id="Companies">
                    <source>Companies</source>
                    <target>Компании</target>
                </trans-unit>
                <trans-unit id="company_list">
                    <source>company_list</source>
                    <target>Список компаний</target>
                </trans-unit> 
                <trans-unit id="company_create">
                    <source>company_create</source>
                    <target>Создать компанию</target>
                </trans-unit>    
                <trans-unit id="company_edit">
                    <source>company_edit</source>
                    <target>Настроить компанию</target>
                </trans-unit>    
                <trans-unit id="company_show">
                    <source>company_show</source>
                    <target>Данные о компании</target>
                </trans-unit>    
                <trans-unit id="company_delete">
                    <source>company_delete</source>
                    <target>Удалить компанию </target>
                </trans-unit>                
                <trans-unit id="company_history">
                    <source>company_history</source>
                    <target>История редактирования компании</target>
                </trans-unit>                
                <trans-unit id="company.title">
                    <source>company.title</source>
                    <target>Компания</target>
                </trans-unit>
                <trans-unit id="company.ico">
                    <source>company.ico</source>
                    <target>Рабочий ID</target>
                </trans-unit>
                <trans-unit id="company.code_help">
                    <source>company.code_help</source>
                    <target>Код компании. Будет применяться как часть кода объекта.</target>
                </trans-unit>
                <trans-unit id="company.abbr_help">
                    <source>company.abbr_help</source>
                    <target>Сокращенное название компании. Не должно превышать 5 символов.</target>
                </trans-unit>
                <trans-unit id="company.note">
                    <source>company.note</source>
                    <target>Примечание</target>
                </trans-unit>
            </group>
            
            
            <group resname="contactInfo">
                <note>Contact detail editing and display</note>           
                <trans-unit id="contact_info.title">
                    <source>contact_info.title</source>
                    <target>Контактные данные</target>
                </trans-unit>            
                <trans-unit id="contact_info.street">
                    <source>contact_info.street</source>
                    <target>Улица</target>
                </trans-unit>
                <trans-unit id="contact_info.no">
                    <source>contact_info.no</source>
                    <target>Номер</target>
                </trans-unit>
                <trans-unit id="contact_info.city">
                    <source>contact_info.city</source>
                    <target>Город</target>
                </trans-unit>
                <trans-unit id="contact_info.zip">
                    <source>contact_info.zip</source>
                    <target>Индекс</target>
                </trans-unit>
                <trans-unit id="contact_info.country">
                    <source>contact_info.country</source>
                    <target>Страна</target>
                </trans-unit>
                <trans-unit id="contact_info.phone1">
                    <source>contact_info.phone1</source>
                    <target>Рабочий телефон</target>
                </trans-unit>
                <trans-unit id="contact_info.mobile1">
                    <source>contact_info.mobile1</source>
                    <target>Рабочий мобильный</target>
                </trans-unit>
                <trans-unit id="contact_info.email1">
                    <source>contact_info.email1</source>
                    <target>Рабочий электронный адрес </target>
                </trans-unit>
                <trans-unit id="contact_info.fax">
                    <source>contact_info.fax</source>
                    <target>Факс</target>
                </trans-unit>
                <trans-unit id="contact_info.phone2">
                    <source>contact_info.phone2</source>
                    <target>Частный номер телефона</target>
                </trans-unit>
                <trans-unit id="contact_info.mobile2">
                    <source>contact_info.mobile2</source>
                    <target> Номер частного мобильного </target>
                </trans-unit>
                <trans-unit id="contact_info.email2">
                    <source>contact_info.email2</source>
                    <target>Частная электронная почта</target>
                </trans-unit>
                <trans-unit id="contact_info.email3">
                    <source>contact_info.email3</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="contact_info.email3_help">
                    <source>contact_info.email3_help</source>
                    <target></target>
                </trans-unit>
            </group>
            <group resname="agencies">
                <note>Agencies editing overview</note>
                <trans-unit id="agency.title">
                    <source>agency.title</source>
                    <target>Агентства</target>
                </trans-unit>
                <trans-unit id="agency_list">
                    <source>agency_list</source>
                    <target>Список агентств</target>
                </trans-unit> 
                <trans-unit id="agency_create">
                    <source>agency_create</source>
                    <target>Создать агентство</target>
                </trans-unit>    
                <trans-unit id="agency_edit">
                    <source>agency_edit</source>
                    <target>Настроить агентство</target>
                </trans-unit>    
                <trans-unit id="agency_show">
                    <source>agency_show</source>
                    <target>Данные об агентстве</target>
                </trans-unit>    
                <trans-unit id="agency_delete">
                    <source>agency_delete</source>
                    <target>Удалить агентство</target>
                </trans-unit>                
                <trans-unit id="agency_history">
                    <source>agency_history</source>
                    <target>История редактирования агентства</target>
                </trans-unit>                
                <trans-unit id="agency.name">
                    <source>agency.name</source>
                    <target>Название агентства</target>
                </trans-unit>
            </group>
            <group resname="persons">
                <note>Persons</note>
                <trans-unit id="person.title">
                    <source>person.title</source>
                    <target>Люди</target>
                </trans-unit>
                <trans-unit id="person_list">
                    <source>person_list</source>
                    <target>Список людей</target>
                </trans-unit>       
                <trans-unit id="person_create">
                    <source>person_create</source>
                    <target>Создать человека </target>
                </trans-unit>    
                <trans-unit id="person_edit">
                    <source>person_edit</source>
                    <target>Настроить человека </target>
                </trans-unit>    
                <trans-unit id="person_show">
                    <source>person_show</source>
                    <target>Данные о человеке</target>
                </trans-unit>    
                <trans-unit id="person_delete">
                    <source>person_delete</source>
                    <target>Удалить человека </target>
                </trans-unit>                
                <trans-unit id="person_history">
                    <source>person_history</source>
                    <target>История редактирования человека</target>
                </trans-unit>  
                <trans-unit id="person.password_generated">
                    <source>person.password_generated</source>
                    <target>Пароль доступа на Операционный Модуль был сгенерирован и отправлен субъекту.</target>
                </trans-unit> 
                <trans-unit id="person.password_no_generated">
                    <source>person.password_no_generated</source>
                    <target> Пароль доступа на Операционный Модуль не был сгенерирован. Субъект не установил адрес электронной почты.</target>
                </trans-unit>                 
                <trans-unit id="person.generate_password">
                    <source>person.generate_password</source>
                    <target>Генерировать пароль</target>
                </trans-unit>   
                <trans-unit id="person.generate_password_PM">
                    <source>person.generate_password_PM</source>
                    <target></target>
                </trans-unit> 
                <trans-unit id="person.generate_password_OPM">
                    <source>person.generate_password_OPM</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="person.generate_password_not_allowed">
                    <source>person.generate_password_not_allowed</source>
                    <target>Пароль не может быть сгенерирован. Рабочий электронный адрес не установлен.</target>
                </trans-unit>                  
                <trans-unit id="person.personalNo">
                    <source>person.personalNo</source>
                    <target>Личный номер</target>
                </trans-unit>
                <trans-unit id="person.rc">
                    <source>person.rc</source>
                    <target>Личный ID</target>
                </trans-unit>
                <trans-unit id="personal_info.title">
                    <source>personal_info.title</source>
                    <target>Личные входы</target>
                </trans-unit> 
                <trans-unit id="personal_info_edit">
                    <source>personal_info_edit</source>
                    <target>Редактирование личных входов</target>
                </trans-unit>
                <trans-unit id="personal_info.password_change">
                    <source>personal_info.password_change</source>
                    <target>Смена пароля</target>
                </trans-unit>
                <trans-unit id="person.permission_role">
                    <source>person.permission_role</source>
                    <target>Список разрешений</target>
                </trans-unit>
                <trans-unit id="personal_info.opm_password_change">
                    <source>personal_info.opm_password_change</source>
                    <target>Смена пароля на портал Клиента</target>
                </trans-unit>
            </group>
            
            <group resname="customers">
                <note>Customers</note>           
                <trans-unit id="customer.title">
                    <source>customer.title</source>
                    <target>Заказчики</target>
                </trans-unit>
                <trans-unit id="customers_list">
                    <source>customers_list</source>
                    <target>Список заказчиков</target>
                </trans-unit>       
                <trans-unit id="customers_create">
                    <source>customers_create</source>
                    <target>Создать заказчика</target>
                </trans-unit>    
                <trans-unit id="customers_edit">
                    <source>customers_edit</source>
                    <target>Настроить заказчика </target>
                </trans-unit>    
                <trans-unit id="customers_show">
                    <source>customers_show</source>
                    <target>Данные о заказчике</target>
                </trans-unit>    
                <trans-unit id="customers_delete">
                    <source>customers_delete</source>
                    <target>Удалить заказчика </target>
                </trans-unit>                
                <trans-unit id="customers_history">
                    <source>customers_history</source>
                    <target>История редактирования заказчика </target>
                </trans-unit>   
                <trans-unit id="customer.objects">
                    <source>customer.objects</source>
                    <target>Подключиться к объектам</target>
                </trans-unit> 
                <trans-unit id="customer.objects_show">
                    <source>customer.objects_show</source>
                    <target>Объекты клиента</target>
                </trans-unit>
                <trans-unit id="customer.password_generated">
                    <source>customer.password_generated</source>
                    <target>Пароль для доступа клиента был сгенерирован и отправлен клиенту.</target>
                </trans-unit> 
                <trans-unit id="customer.password_no_generated">
                    <source>customer.password_no_generated</source>
                    <target> Пароль для доступа клиента не был сгенерирован. Лицо не установило электронный адрес. </target>
                </trans-unit>                 
                
                <trans-unit id="customer.generate_password_for_zp">
                    <source>customer.generate_password_for_zp</source>
                    <target>Генерировать пароль для доступа клиента.</target>
                </trans-unit> 
                <trans-unit id="customer.generate_password_not_allowed">
                    <source>customer.generate_password_not_allowed</source>
                    <target>Пароль для доступа Клиента не может быть сгенерирован. Не установлен рабочий электронный адрес.</target>
                </trans-unit>                 
            </group>                                                  
            <group resname="currencies">
                <note>Currencies</note>
                <trans-unit id="currency.title">
                    <source>currency.title</source>
                    <target>Валюты</target>
                </trans-unit>
                <trans-unit id="currency_list">
                    <source>currency_list</source>
                    <target>Список валют</target>
                </trans-unit>          
                <trans-unit id="currency_create">
                    <source>currency_create</source>
                    <target>Создать валюту </target>
                </trans-unit>    
                <trans-unit id="currency_edit">
                    <source>currency_edit</source>
                    <target>Настроить валюту </target>
                </trans-unit>    
                <trans-unit id="currency_show">
                    <source>currency_show</source>
                    <target>Подробности о валюте</target>
                </trans-unit>    
                <trans-unit id="currency_delete">
                    <source>currency_delete</source>
                    <target>Удалить валюту </target>
                </trans-unit>                
                <trans-unit id="currency_history">
                    <source>currency_history</source>
                    <target>История редактирования валюты </target>
                </trans-unit>                
                <trans-unit id="currency.name">
                    <source>currency.name</source>
                    <target>Наименование валюты </target>
                </trans-unit>
                <trans-unit id="currency.abbr">
                    <source>currency.abbr</source>
                    <target>Сокращенное наименование валюты</target>
                </trans-unit>
            </group>
            <group resname="cities">
                <note>Cities</note>
                <trans-unit id="city.title">
                    <source>city.title</source>
                    <target>Города</target>
                </trans-unit>
                <trans-unit id="city_list">
                    <source>city_list</source>
                    <target>Список городов</target>
                </trans-unit>     
                <trans-unit id="city_create">
                    <source>city_create</source>
                    <target>Создать город </target>
                </trans-unit>    
                <trans-unit id="city_edit">
                    <source>city_edit</source>
                    <target>Настроить город </target>
                </trans-unit>    
                <trans-unit id="city_show">
                    <source>city_show</source>
                    <target>Подробности о городе</target>
                </trans-unit>    
                <trans-unit id="city_delete">
                    <source>city_delete</source>
                    <target>Удалить город </target>
                </trans-unit>                
                <trans-unit id="city_history">
                    <source>city_history</source>
                    <target>История редактирования города </target>
                </trans-unit>                
                <trans-unit id="city.name">
                    <source>city.name</source>
                    <target>Название города</target>
                </trans-unit>
            </group>
            <group resname="countries">
                <note>Cities</note>
                <trans-unit id="country.title">
                    <source>country.title</source>
                    <target>Страна</target>
                </trans-unit>
                <trans-unit id="country_list">
                    <source>country_list</source>
                    <target>Список стран </target>
                </trans-unit> 
                <trans-unit id="country_create">
                    <source>country_create</source>
                    <target>Создать страну </target>
                </trans-unit>    
                <trans-unit id="country_edit">
                    <source>country_edit</source>
                    <target>Настроить страну </target>
                </trans-unit>    
                <trans-unit id="country_show">
                    <source>country_show</source>
                    <target>Подробности о стране </target>
                </trans-unit>    
                <trans-unit id="country_delete">
                    <source>country_delete</source>
                    <target>Удалить страну </target>
                </trans-unit>                
                <trans-unit id="country_history">
                    <source>country_history</source>
                    <target>История редактирования страны  </target>
                </trans-unit>                
                <trans-unit id="country.name">
                    <source>country.name</source>
                    <target>Название страны </target>
                </trans-unit>
            </group>
            <group resname="absence">
                <note>Nepшнtomnostн</note>
                <trans-unit id="absence.title">
                    <source>absence.title</source>
                    <target>Отсутствие</target>
                </trans-unit>
                <trans-unit id="menu_group_absence">
                    <source>menu_group_absence</source>
                    <target>Отсутствие</target>
                </trans-unit>                
                <trans-unit id="absence_list">
                    <source>absence_list</source>
                    <target>Список отсутствия</target>
                </trans-unit>     
                <trans-unit id="absence_create">
                    <source>absence_create</source>
                    <target>Создать отсутствие</target>
                </trans-unit>    
                <trans-unit id="absence_edit">
                    <source>absence_edit</source>
                    <target>Редактировать отсутствие</target>
                </trans-unit>    
                <trans-unit id="absence_show">
                    <source>absence_show</source>
                    <target>Подробности отсутствия</target>
                </trans-unit>    
                <trans-unit id="absence_delete">
                    <source>absence_delete</source>
                    <target>Удалить отсутствие</target>
                </trans-unit>                
                <trans-unit id="absence_history">
                    <source>absence_history</source>
                    <target>История отсутствия</target>
                </trans-unit>                
                <trans-unit id="absence.subject">
                    <source>absence.subject</source>
                    <target>Субъект</target>
                </trans-unit>
                <trans-unit id="absence.absence_type">
                    <source>absence.absence_type</source>
                    <target>Тип отсутствия</target>
                </trans-unit>
                <trans-unit id="absence.starts_at">
                    <source>absence.starts_at</source>
                    <target>Отсутствует с</target>
                </trans-unit>
                <trans-unit id="absence.ends_at">
                    <source>absence.ends_at</source>
                    <target>Отсутствует до</target>
                </trans-unit>
                
            </group>            
            <group resname="absenceTypes">
                <note>Absence types</note>
                <trans-unit id="absence_type.title">
                    <source>absence_type.title</source>
                    <target>Тип отсутствия </target>
                </trans-unit>
                <trans-unit id="absence_type_list">
                    <source>absence_type_list</source>
                    <target>Список типов отсутствия </target>
                </trans-unit>     
                <trans-unit id="absence_type_create">
                    <source>absence_type_create</source>
                    <target>Создать тип отсутствия </target>
                </trans-unit>    
                <trans-unit id="absence_type_edit">
                    <source>absence_type_edit</source>
                    <target>Настроить тип отсутствия </target>
                </trans-unit>    
                <trans-unit id="absence_type_show">
                    <source>absence_type_show</source>
                    <target>Подробности о типе отсутствия </target>
                </trans-unit>    
                <trans-unit id="absence_type_delete">
                    <source>absence_type_delete</source>
                    <target>Удалить тип отсутствия </target>
                </trans-unit>                
                <trans-unit id="absence_type_history">
                    <source>absence_type_history</source>
                    <target>История редактирования типа отсутствия </target>
                </trans-unit>                
                <trans-unit id="absence_type.name">
                    <source>absence_type.name</source>
                    <target>Название</target>
                </trans-unit>
                <trans-unit id="absence_type.code">
                    <source>absence_type.code</source>
                    <target>код отсутствия </target>
                </trans-unit>
            </group>
            
            <group resname="dimensionlevels">
                <note>Уровни велиины</note>
                <trans-unit id="dimension_level.title">
                    <source>dimension_level.title</source>
                    <target>Уровни величины </target>
                </trans-unit>
                <trans-unit id="dimension_level_list">
                    <source>dimension_level_list</source>
                    <target>Список уровней величины </target>
                </trans-unit>   
                <trans-unit id="dimension_level_create">
                    <source>dimension_level_create</source>
                    <target>Создать уровень величины </target>
                </trans-unit>    
                <trans-unit id="dimension_level_edit">
                    <source>dimension_level_edit</source>
                    <target>Настроить уровень величины </target>
                </trans-unit>    
                <trans-unit id="dimension_level_show">
                    <source>dimension_level_show</source>
                    <target>Подробности об уровне величины </target>
                </trans-unit>    
                <trans-unit id="dimension_level_delete">
                    <source>dimension_level_delete</source>
                    <target>Удалить уровень величины </target>
                </trans-unit>                
                <trans-unit id="dimension_level_history">
                    <source>dimension_level_history</source>
                    <target>История редактирования уровня величины </target>
                </trans-unit>                
                <trans-unit id="dimension_level.name">
                    <source>dimension_level.name</source>
                    <target>Название уровня</target>
                </trans-unit>
                <trans-unit id="dimension_level.message_min_level">
                    <source>dimension_level.message_min_level</source>
                    <target>Уровень начинается с 1 и выше </target>
                </trans-unit>
            </group>
            
            <group resname="dimensions">
                <note>Dimension - setting up a logical structure of items</note>
                <trans-unit id="dimension.title">
                    <source>dimension.title</source>
                    <target>Величина </target>
                </trans-unit>
                <trans-unit id="dimension_list">
                    <source>dimension_list</source>
                    <target>Список величин </target>
                </trans-unit>   
                <trans-unit id="dimension_create">
                    <source>dimension_create</source>
                    <target>Создать величину </target>
                </trans-unit>    
                <trans-unit id="dimension_edit">
                    <source>dimension_edit</source>
                    <target>Настроить величину </target>
                </trans-unit>    
                <trans-unit id="dimension_show">
                    <source>dimension_show</source>
                    <target>Подробности о величине </target>
                </trans-unit>    
                <trans-unit id="dimension_delete">
                    <source>dimension_delete</source>
                    <target>Удалить величину </target>
                </trans-unit>                
                <trans-unit id="dimension_history">
                    <source>dimension_history</source>
                    <target>История редактирования величины  </target>
                </trans-unit>                
                <trans-unit id="dimension.name">
                    <source>dimension.name</source>
                    <target>Название величины</target>
                </trans-unit>
                <trans-unit id="dimension.level">
                    <source>dimension.level</source>
                    <target>Уровень</target>
                </trans-unit>
                <trans-unit id="dimension.code">
                    <source>dimension.code</source>
                    <target>Код величины</target>
                </trans-unit>
            </group>
            <group resname="object">
                <note>Objects</note>
                <trans-unit id="object.title">
                    <source>object.title</source>
                    <target>Объекты</target>
                </trans-unit>
                <trans-unit id="object_list">
                    <source>object_list</source>
                    <target>Список объектов</target>
                </trans-unit>  
                <trans-unit id="object_create">
                    <source>object_create</source>
                    <target>Создать объект</target>
                </trans-unit>    
                <trans-unit id="object_edit">
                    <source>object_edit</source>
                    <target>Настроить объект</target>
                </trans-unit>    
                <trans-unit id="object_show">
                    <source>object_show</source>
                    <target>Подробности об объекте </target>
                </trans-unit>    
                <trans-unit id="object_delete">
                    <source>object_delete</source>
                    <target>Удалить объект</target>
                </trans-unit>                
                <trans-unit id="object_history">
                    <source>object_history</source>
                    <target>История редактирования объекта </target>
                </trans-unit>                
                <trans-unit id="object.name">
                    <source>object.name</source>
                    <target>Название объекта</target>
                </trans-unit>
                <trans-unit id="object.company">
                    <source>object.company</source>
                    <target>Название компании</target>
                </trans-unit>
                <trans-unit id="object.code">
                    <source>object.code</source>
                    <target>Код объектаа</target>
                </trans-unit>
                <trans-unit id="object.currency">
                    <source>object.currency</source>
                    <target>Валюта объекта </target>
                </trans-unit>
                <trans-unit id="object.dimensions">
                    <source>object.dimensions</source>
                    <target>Величина объекта </target>
                </trans-unit>
                <trans-unit id="object.successfully_set">
                    <source>object.successfully_set</source>
                    <target>Объект успешно установлен </target>
                </trans-unit>
                <trans-unit id="object.group_successfully_set">
                    <source>object.group_successfully_set</source>
                    <target>Группа объектов успешно установлена </target>
                </trans-unit>
                <trans-unit id="object.select">
                    <source>object.select</source>
                    <target>Выбрать объект</target>
                </trans-unit>
                <trans-unit id="object.all_objects">
                    <source>object.all_objects</source>
                    <target>Все объекты</target>
                </trans-unit>
                <trans-unit id="object.selection">
                    <source>object.selection</source>
                    <target>Выделение объектов </target>
                </trans-unit>
                <trans-unit id="object.selection_message">
                    <source>object.selection_message</source>
                    <target>Пожалуйста, выделите объект </target>
                </trans-unit>
                <trans-unit id="object.selection_label">
                    <source>object.selection_label</source>
                    <target>Объект: %name%</target>
                </trans-unit>
                <trans-unit id="object.group_label">
                    <source>object.group_label</source>
                    <target>Группа: %name%</target>
                </trans-unit>                
            </group>
            <group resname="object_info">
                <note>Karta objektu</note>
                <trans-unit id="object_info.title">
                    <source>object_info.title</source>
                    <target>Меню объекта</target>
                </trans-unit>
                <trans-unit id="object_info.title_plus">
                    <source>object_info.title_plus</source>
                    <target>Меню объекта (для биллинга)</target>
                </trans-unit>                
                <trans-unit id="object_info.object_name">
                    <source>object_info.object_name</source>
                    <target>Имя объекта</target>
                </trans-unit>  
                <trans-unit id="object_info.customer">
                    <source>object_info.customer</source>
                    <target>Имя Клиента</target>
                </trans-unit>    
                <trans-unit id="object_info.ico">
                    <source>object_info.ico</source>
                    <target>Номер ID Клиента</target>
                </trans-unit>    
                <trans-unit id="object_info.division">
                    <source>object_info.division</source>
                    <target>Номер дивизии</target>
                </trans-unit>    
                <trans-unit id="object_info.bm">
                    <source>object_info.bm</source>
                    <target>Менеджер по безопасности</target>
                </trans-unit>                
                <trans-unit id="object_info.object_contact_info">
                    <source>object_info.object_contact_info</source>
                    <target>Адрес объекта</target>
                </trans-unit>                
                <trans-unit id="object_info.customer_contact_info">
                    <source>object_info.customer_contact_info</source>
                    <target>Адрес Клиента</target>
                </trans-unit>
            </group>
            <group resname="pops">
                <note>Service performance reports</note>
                <trans-unit id="pops">
                    <source>pops</source>
                    <target>POPS</target>
                </trans-unit>                
                <trans-unit id="pops.title">
                    <source>pops.title</source>
                    <target>Отчеты о выполненных услугах</target>
                </trans-unit>
                <trans-unit id="pops_event_list">
                    <source>pops_event_list</source>
                    <target> Отчеты о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_event.title">
                    <source>pops_event.title</source>
                    <target>События в  Отчетах о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_create">
                    <source>pops_create</source>
                    <target>Создать Отчет о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_edit">
                    <source>pops_edit</source>
                    <target>Редактирование ОВУ</target>
                </trans-unit>
                <trans-unit id="pops_show">
                    <source>pops_show</source>
                    <target>Подробности Отчета о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_event_create">
                    <source>pops_event_create</source>
                    <target>Создать событие в Отчете о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_event_edit">
                    <source>pops_event_edit</source>
                    <target>Настроить событие в Отчете о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_event_show">
                    <source>pops_event_show</source>
                    <target>Удалить событие из Отчета о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops.starts_at">
                    <source>pops.starts_at</source>
                    <target>Старт</target>
                </trans-unit>
                <trans-unit id="pops.ends_at">
                    <source>pops.ends_at</source>
                    <target>Завершение</target>
                </trans-unit>
                <trans-unit id="pops.serial_number">
                    <source>pops.serial_number</source>
                    <target>Серийный номер </target>
                </trans-unit>
                <trans-unit id="pops.event">
                    <source>pops.event</source>
                    <target>Event</target>
                </trans-unit>
                <trans-unit id="pops.description">
                    <source>pops.description</source>
                    <target>Описание </target>
                </trans-unit>
                <trans-unit id="pops.lock">
                    <source>pops.lock</source>
                    <target>Блокировать Отчет о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops.confirm_lock">
                    <source>pops.confirm_lock</source>
                    <target>После блокирования этого Отчета о выполненных услугах, ни одно событие не может быть добавлено в него. Вы всё ещё хотите его заблокировать?</target>
                </trans-unit>
                <trans-unit id="pops.lock_success">
                    <source>pops.lock_success</source>
                    <target> Отчет о выполненных услугах был заблокирован, и ни одно событие не может быть добавлено в него.</target>
                </trans-unit>
                <trans-unit id="pops_list">
                    <source>pops_list</source>
                    <target> Отчеты о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_event">
                    <source>pops_event</source>
                    <target>Событие в Отчете о выполненных услугах </target>
                </trans-unit>
                <trans-unit id="pops_event.list_title">
                    <source>pops_event.list_title</source>
                    <target>События в Отчетах о выполненных услугах %from% до %till%</target>
                </trans-unit>
                <trans-unit id="pops.event_time">
                    <source>pops.event_time</source>
                    <target>Время события </target>
                </trans-unit>
                <trans-unit id="pops.no_active_pops">
                    <source>pops.no_active_pops</source>
                    <target>На текущее время Отчет о выполненных услугах не был создан. Вы можете создать его ниже.</target>
                </trans-unit>
                <trans-unit id="pops.period_validation_message1">
                    <source>pops.period_validation_message1</source>
                    <target>Дата начала должна предшествовать дате окончания.</target>
                </trans-unit>
                <trans-unit id="pops.period_validation_message2">
                    <source>pops.period_validation_message2</source>
                    <target>Введенный набор дат не должен пересекаться с другим отчетом о выполненных услугах. </target>
                </trans-unit>
                <trans-unit id="pops.period_validation_message3">
                    <source>pops.period_validation_message3</source>
                    <target>Текущая дата не относится к набору дат, введенному в форму.</target>
                </trans-unit>
                <trans-unit id="pops_event.datetime_validation_message">
                    <source>pops_event.datetime_validation_message</source>
                    <target>Время события должно относиться к ряду отчета о выполненных услугах.</target>
                </trans-unit>
                <trans-unit id="pops_event.link_action_create">
                    <source>pops_event.link_action_create</source>
                    <target>Добавить событие</target>
                </trans-unit>
                <trans-unit id="pops_event.new_event_mail_subject">
                    <source>pops_event.new_event_mail_subject</source>
                    <target>Новое событие</target>
                </trans-unit>
                <trans-unit id="pops_event.new_event_mail_message">
                    <source>pops_event.new_event_mail_message</source>
                    <target>Новое событие "%event%" произошло на объекте "%object%".</target>
                </trans-unit>
                <trans-unit id="pops_event.new_event_message">
                    <source>pops_event.new_event_message</source>
                    <target>Новое событие "%event%" произошло на объекте "%object%". Время: "%eventTime% описание: %description%"</target>
                </trans-unit>
                <trans-unit id="object_pops_events">
                    <source>object_pops_events</source>
                    <target>архив ОВУ (на объекте)</target>
                </trans-unit>
                <trans-unit id="object_events_list">
                    <source>object_events_list</source>
                    <target>Список событий на объекте</target>
                </trans-unit>
                <trans-unit id="object_events_show">
                    <source>object_events_show</source>
                    <target>Подробности о событии</target>
                </trans-unit>
                <trans-unit id="object_events_edit">
                    <source>object_events_edit</source>
                    <target>Редактировать событие</target>
                </trans-unit>
                <trans-unit id="object_events_delete">
                    <source>object_events_delete</source>
                    <target>Удалить событие</target>
                </trans-unit>
                <trans-unit id="object_pops">
                    <source>object_pops</source>
                    <target>ОВУ с объекта</target>
                </trans-unit>
                <trans-unit id="object_pops_list">
                    <source>object_pops_list</source>
                    <target>Список ОВУ с объекта</target>
                </trans-unit>
                <trans-unit id="object_pops_show">
                    <source>object_pops_show</source>
                    <target>Подробности ОВУ</target>
                </trans-unit>
                <trans-unit id="object_pops_edit">
                    <source>object_pops_edit</source>
                    <target>Редактировать ОВУ</target>
                </trans-unit>
                <trans-unit id="object_events">
                    <source>object_events</source>
                    <target>События на объекте</target>
                </trans-unit>
                <trans-unit id="pops_event.translate_title">
                    <source>pops_event.translate_title</source>
                    <target>Выбрать язык для перевода</target>
                </trans-unit>
                <trans-unit id="pops_event.translate_action">
                    <source>pops_event.translate_action</source>
                    <target>Перевести вход</target>
                </trans-unit>
                <trans-unit id="pops_events_translate">
                    <source>pops_events_translate</source>
                    <target>Перевод входа</target>
                </trans-unit>
                <trans-unit id="pops.show_to_customer">
                    <source>pops.show_to_customer</source>
                    <target>Показать клиентам</target>
                </trans-unit>
                <trans-unit id="for_customer">
                    <source>for_customer</source>
                    <target>Позволить</target>
                </trans-unit>
                <trans-unit id="not_for_customer">
                    <source>not_for_customer</source>
                    <target>Запретить</target>
                </trans-unit>
                <trans-unit id="pops.events_count">
                    <source>pops.events_count</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="pops.batch_pdf">
                    <source>pops.batch_pdf</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="pops.batch_xls">
                    <source>pops.batch_xls</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="popslist.batch_export_help">
                    <source>popslist.batch_export_help</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="pops.top_events">
                    <source>pops.top_events</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="pops_top_20.title">
                    <source>pops_top_20.title</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="pops_top_20_change">
                    <source>pops_top_20_change</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="pops_top_20_edit">
                    <source>$sourcepops_top_20_edit</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="event_tree.max_items_alert">
                    <source>event_tree.max_items_alert</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_TOP_20">
                    <source>permission.PERM_POPS_TOP_20</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="with_attachments">
                    <source>with_attachments</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="without_attachments">
                    <source>without_attachments</source>
                    <target></target>
                </trans-unit>
            </group>
            <group resname="menu">
                <note>Texts for all menus</note>            
                <trans-unit id="menu_object">
                    <source>menu_object</source>
                    <target>Объект</target>
                </trans-unit>            
                <trans-unit id="menu_edit_services">
                    <source>menu_edit_services</source>
                    <target>Редактор обслуживаний </target>
                </trans-unit>            
                <trans-unit id="menu_edit_services_edit">
                    <source>menu_edit_services_edit</source>
                    <target> Редактор обслуживаний </target>
                </trans-unit>            
                <trans-unit id="menu_edit_services_approval_attendance">
                    <source>menu_edit_services_approval_attendance</source>
                    <target>Одобрение присутствия </target>
                </trans-unit>            
                <trans-unit id="menu_edit_services_approval_billing">
                    <source>menu_edit_services_approval_billing</source>
                    <target>Одобрение биллинга </target>
                </trans-unit>            
                <trans-unit id="menu_add_services">
                    <source>menu_add_services</source>
                    <target>Добавить обслуживания </target>
                </trans-unit>   
                <trans-unit id="menu_persons">
                    <source>menu_persons</source>
                    <target>Люди и тарифы </target>
                </trans-unit>   
                <trans-unit id="menu_persons_subject_rates">
                    <source>menu_persons_subject_rates</source>
                    <target>Тарифы субъектов </target>
                </trans-unit>   
                <trans-unit id="menu_persons_position_rates">
                    <source>menu_persons_position_rates</source>
                    <target>Тарифы позиций </target>
                </trans-unit>   
                <trans-unit id="menu_persons_agency_rates">
                    <source>menu_persons_agency_rates</source>
                    <target>Агентские тарифы </target>
                </trans-unit> 
                <trans-unit id="menu_persons_individual_rates">
                    <source>menu_persons_individual_rates</source>
                    <target>Индивидуальные тарифы </target>
                </trans-unit>             
                <trans-unit id="menu_periodic_payment">
                    <source>menu_periodic_payment</source>
                    <target>Периодические платежи </target>
                </trans-unit>      
                <trans-unit id="menu_role">
                    <source>menu_role</source>
                    <target>Позиция</target>
                </trans-unit>   
                <trans-unit id="menu_role_role">
                    <source>menu_role_role</source>
                    <target>Позиция</target>
                </trans-unit>   
                <trans-unit id="menu_role_group">
                    <source>menu_role_group</source>
                    <target>Группы позиций</target>
                </trans-unit>  
                <trans-unit id="menu_purchase">
                    <source>menu_purchase</source>
                    <target>Заказ дополнительной покупки</target>
                </trans-unit>    
                <trans-unit id="menu_object_select">
                    <source>menu_object_select</source>
                    <target>Изменить объект</target>
                </trans-unit>    
                <trans-unit id="menu_forward_service">
                    <source>menu_forward_service</source>
                    <target>Передать обслуживание </target>
                </trans-unit>              
                <trans-unit id="menu_group">
                    <source>menu_group</source>
                    <target>Группа объектов</target>
                </trans-unit> 
                <trans-unit id="menu_group_attendance">
                    <source>menu_group_attendance</source>
                    <target>Присутствие</target>
                </trans-unit>                             
                <trans-unit id="menu_group_attendance_start">
                    <source>menu_group_attendance_start</source>
                    <target>Начало присутствия</target>
                </trans-unit>            
                <trans-unit id="menu_group_attendance_end">
                    <source>menu_group_attendance_end</source>
                    <target>Окончание присутствия</target>
                </trans-unit>            
                <trans-unit id="menu_group_attendance_running">
                    <source>menu_group_attendance_running</source>
                    <target>Текущие присутствия</target>
                </trans-unit>            
                <trans-unit id="menu_group_attendance_list">
                    <source>menu_group_attendance_list</source>
                    <target>Полный список присутствия</target>
                </trans-unit> 
                <trans-unit id="menu_group_attendance_length_list">
                    <source>menu_group_attendance_length_list</source>
                    <target></target>
                </trans-unit>
                
                <trans-unit id="menu_group_control">
                    <source>menu_group_control</source>
                    <target>Проверить план и присутствие</target>
                </trans-unit> 
                <trans-unit id="menu_group_control_attendance_early">
                    <source>menu_group_control_attendance_early</source>
                    <target>Преждевременное присутствие</target>
                </trans-unit> 
                <trans-unit id="menu_group_control_noplan">
                    <source>menu_group_control_noplan</source>
                    <target>Не существующий план</target>
                </trans-unit> 
                <trans-unit id="menu_group_attendance_not_approved">
                    <source>menu_group_attendance_not_approved</source>
                    <target>Не одобрено</target>
                </trans-unit> 
                <trans-unit id="menu_group_copy_months">
                    <source>menu_group_copy_months</source>
                    <target>Копия месяца</target>
                </trans-unit> 
                <trans-unit id="menu_group_agency_service">
                    <source>menu_group_agency_service</source>
                    <target>Документация поставщика</target>
                </trans-unit>                                 
                <trans-unit id="menu_settings">
                    <source>menu_settings</source>
                    <target>Установки таблиц поиска</target>
                </trans-unit>            
                <trans-unit id="menu_access">
                    <source>menu_access</source>
                    <target>Права доступа </target>
                </trans-unit>            
                <trans-unit id="menu_access_groups">
                    <source>menu_access_groups</source>
                    <target>Группы прав</target>
                </trans-unit>            
                <trans-unit id="menu_access_templates">
                    <source>menu_access_templates</source>
                    <target>Шаблоны доступа</target>
                </trans-unit>            
                <trans-unit id="menu_statistics">
                    <source>menu_statistics</source>
                    <target>Статистика </target>
                </trans-unit>  
                <trans-unit id="menu_statistics_login">
                    <source>menu_statistics_login</source>
                    <target>Войти/выйти</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_all">
                    <source>menu_statistics_logins_all</source>
                    <target>Все журналы</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_errors">
                    <source>menu_statistics_logins_errors</source>
                    <target>Обнаруженные ошибки</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_emergency">
                    <source>menu_statistics_logins_emergency</source>
                    <target>Экстренные сообщения</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_reporting">
                    <source>menu_statistics_logins_reporting</source>
                    <target>Отчеты</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logs">
                    <source>menu_statistics_logs</source>
                    <target>Журналы</target>
                </trans-unit>
                <trans-unit id="menu_statistics_attendance_pops_check">
                    <source>menu_statistics_attendance_pops_check</source>
                    <target></target>
                </trans-unit>                
                <trans-unit id="pops_attendance_check_list">
                    <source>pops_attendance_check_list</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="last_attendance">
                    <source>last_attendance</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="last_pops">
                    <source>last_pops</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_POPS_CHECK_SHOW">
                    <source>permission.PERM_ATTENDANCE_POPS_CHECK_SHOW</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_POPS_CHECK_EXPORT">
                    <source>permission.PERM_ATTENDANCE_POPS_CHECK_EXPORT</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="login_log_list">
                    <source>login_log_list</source>
                    <target>Статистика - войти/выйти</target>
                </trans-unit>  
                <trans-unit id="customer_login_log_list">
                    <source>customer_login_log_list</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="logs_all_list">
                    <source>logs_all_list</source>
                    <target>Статистика – все действия</target>
                </trans-unit>  
                <trans-unit id="logs_errors_list">
                    <source>logs_errors_list</source>
                    <target>Статистика – Отчет об ошибке</target>
                </trans-unit>  
                <trans-unit id="logs_reporting_list">
                    <source>logs_reporting_list</source>
                    <target>Журнал отправленных отчетов</target>
                </trans-unit>
                <trans-unit id="logs_emergency_list">
                    <source>logs_emergency_list</source>
                    <target>Статистика – Экстренные сообщения</target>
                </trans-unit>  
                <trans-unit id="logs_data_list">
                    <source>logs_data_list</source>
                    <target>Статистика - изменения данных</target>
                </trans-unit>
                <trans-unit id="log.loginType​">
                    <source>log.loginType​</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="logsall.loginType">
                    <source>logsall.loginType</source>
                    <target>Тип действия</target>
                </trans-unit>  
                <trans-unit id="logsall.loginTime">
                    <source>logsall.loginTime</source>
                    <target>Дата/время записи</target>
                </trans-unit>  
                <trans-unit id="logsall.message">
                    <source>logsall.message</source>
                    <target>Описание</target>
                </trans-unit>  
                <trans-unit id="logs.severity">
                    <source>logs.severity</source>
                    <target>Статус</target>
                </trans-unit>  
                <trans-unit id="menu_statistics_logins_data_change">
                    <source>menu_statistics_logins_data_change</source>
                    <target>Действия с данными</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import">
                    <source>menu_exchange_import</source>
                    <target>Экспорт/ импорт</target>
                </trans-unit>    
                <trans-unit id="menu_group_objects_group">
                    <source>menu_group_objects_group</source>
                    <target>Группа объектов</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import_upload">
                    <source>menu_exchange_import_upload</source>
                    <target>Ручной импорт</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import_file_list">
                    <source>menu_exchange_import_file_list</source>
                    <target>Импортированные файлы</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import_result_list">
                    <source>menu_exchange_import_result_list</source>
                    <target>Результаты импорта</target>
                </trans-unit>
                <trans-unit id="menu_exchange_export_file_list">
                    <source>menu_exchange_export_file_list</source>
                    <target>Экспортированные файлы</target>
                </trans-unit>
                <trans-unit id="menu_exchange_manual_export">
                    <source>menu_exchange_manual_export</source>
                    <target>Ручной экспорт</target>
                </trans-unit>
                <trans-unit id="menu_statistics_customer_login">
                    <source>menu_statistics_customer_login</source>
                    <target></target>
                </trans-unit>
            </group>
            <group resname="products">
                <note>Products</note>                                      
                <trans-unit id="product.title">
                    <source>product.title</source>
                    <target>Продукты </target>
                </trans-unit>
                <trans-unit id="product">
                    <source>product</source>
                    <target>Продукт</target>
                </trans-unit>                
                <trans-unit id="product_list">
                    <source>product_list</source>
                    <target>Список продуктов </target>
                </trans-unit>
                <trans-unit id="product_create">
                    <source>product_create</source>
                    <target>Создать продукт</target>
                </trans-unit>    
                <trans-unit id="product_edit">
                    <source>product_edit</source>
                    <target>Настроить продукт</target>
                </trans-unit>    
                <trans-unit id="product_show">
                    <source>product_show</source>
                    <target>Подробности о продукте </target>
                </trans-unit>    
                <trans-unit id="product_delete">
                    <source>product_delete</source>
                    <target>Удалить продукт</target>
                </trans-unit>                
                <trans-unit id="product_history">
                    <source>product_history</source>
                    <target>История редактирования  продукта</target>
                </trans-unit>                
                <trans-unit id="product.name">
                    <source>product.name</source>
                    <target>Название продукта</target>
                </trans-unit>
                <trans-unit id="product.isActiv">
                    <source>product.isActiv</source>
                    <target>Активен</target>
                </trans-unit>
            </group>
            <group resname="roles">
                <note>Work positions</note>
                <trans-unit id="role.title">
                    <source>role.title</source>
                    <target>Позиция</target>
                </trans-unit>    
                <trans-unit id="role_list">
                    <source>role_list</source>
                    <target>Список позиций</target>
                </trans-unit> 
                <trans-unit id="role_create">
                    <source>role_create</source>
                    <target>Создать позицию </target>
                </trans-unit>    
                <trans-unit id="role_edit">
                    <source>role_edit</source>
                    <target>Настроить позицию </target>
                </trans-unit>    
                <trans-unit id="role_show">
                    <source>role_show</source>
                    <target>Подробности о позиции </target>
                </trans-unit>    
                <trans-unit id="role_delete">
                    <source>role_delete</source>
                    <target>Удалить позицию </target>
                </trans-unit>                
                <trans-unit id="role_history">
                    <source>role_history</source>
                    <target>История редактирования позиции </target>
                </trans-unit>                
                <trans-unit id="role.name">
                    <source>role.name</source>
                    <target>Название позиции</target>
                </trans-unit>
                <trans-unit id="role.position_rate">
                    <source>role.position_rate</source>
                    <target>Тариф позиции</target>
                </trans-unit>
                <trans-unit id="role.billing_rate">
                    <source>role.billing_rate</source>
                    <target>Тариф по биллингу</target>
                </trans-unit>
                <trans-unit id="role.visibility">
                    <source>role.visibility</source>
                    <target>Видимый</target>
                </trans-unit>
                <trans-unit id="role.is_active">
                    <source>role.is_active</source>
                    <target>Активный</target>
                </trans-unit>
                <trans-unit id="role.inactivate_at">
                    <source>role.inactivate_at</source>
                    <target>Неактивный</target>
                </trans-unit>
                <trans-unit id="role.break_type">
                    <source>role.break_type</source>
                    <target>Тип перерыва</target>
                </trans-unit>
                <trans-unit id="role.billing_type">
                    <source>role.billing_type</source>
                    <target>Тип биллинга</target>
                </trans-unit>
                <trans-unit id="role.visible">
                    <source>role.visible</source>
                    <target>Видимый</target>
                </trans-unit>
                <trans-unit id="role.secret">
                    <source>role.secret</source>
                    <target>только МБ</target>
                </trans-unit>
                <trans-unit id="role.paid_break">
                    <source>role.paid_break</source>
                    <target>Оплачен</target>
                </trans-unit>
                <trans-unit id="role.non_paid_break">
                    <source>role.non_paid_break</source>
                    <target>Не оплачен</target>
                </trans-unit>
                <trans-unit id="role.billing_by_attendance">
                    <source>role.billing_by_attendance</source>
                    <target>Согласно присутствию</target>
                </trans-unit>
                <trans-unit id="role.billing_by_plan">
                    <source>role.billing_by_plan</source>
                    <target>Согласно плану</target>      
                </trans-unit>
                <trans-unit id="role.no_billing">
                    <source>role.no_billing</source>
                    <target>Нет биллинга</target>
                </trans-unit>       
                <trans-unit id="role.role_groups">
                    <source>role.role_groups</source>
                    <target>Группа позиций </target>
                </trans-unit>
                <trans-unit id="reorder_start">
                    <source>reorder_start</source>
                    <target>Сменить заказ</target>
                </trans-unit>
                <trans-unit id="reorder_confirm">
                    <source>reorder_confirm</source>
                    <target>Сохранить</target>
                </trans-unit>
            </group>
            <group resname="roleGroups">
                <note>Groups of work positions</note>
                <trans-unit id="role_groups.title">
                    <source>role_groups.title</source>
                    <target>Группы позиций</target>
                </trans-unit>
                <trans-unit id="role_group_list">
                    <source>role_group_list</source>
                    <target>Список групп позиций </target>
                </trans-unit>   
                <trans-unit id="role_group_create">
                    <source>role_group_create</source>
                    <target>Создать группу позиций </target>
                </trans-unit>    
                <trans-unit id="role_group_edit">
                    <source>role_group_edit</source>
                    <target>Настроить группу позиций </target>
                </trans-unit>    
                <trans-unit id="role_group_show">
                    <source>role_group_show</source>
                    <target>Подробности о группе позиций  </target>
                </trans-unit>    
                <trans-unit id="role_group_delete">
                    <source>role_group_delete</source>
                    <target>Удалить группу позиций </target>
                </trans-unit>                
                <trans-unit id="role_group_history">
                    <source>role_group_history</source>
                    <target>История редактирования группы позиций </target>
                </trans-unit>                
                <trans-unit id="role_group.name ">
                    <source>role_group.name</source>
                    <target>Название группы</target>
                </trans-unit>
                <trans-unit id="role_group.description">
                    <source>role_group.description</source>
                    <target>Описание группы </target>
                </trans-unit>
                <trans-unit id="role_group.roles">
                    <source>role_group.roles</source>
                    <target>Позиция в группе</target>
                </trans-unit>
            </group>
            
            <group resname="breaks">
                <note>Breaks</note>
                <trans-unit id="attendance_break.title">
                    <source>attendance_break.title</source>
                    <target>Перерывы</target>
                </trans-unit>
                <trans-unit id="break.starts_at">
                    <source>break.starts_at</source>
                    <target>Перерыв с:</target>
                </trans-unit>
                <trans-unit id="break.ends_at">
                    <source>break.ends_at</source>
                    <target>Перерыв до:</target>
                </trans-unit>
            </group>
            
            <group resname="contracts">
                <note>Contracts of employees and agencies</note>
                <trans-unit id="contract.title">
                    <source>contract.title</source>
                    <target>Контракты</target>
                </trans-unit>  
                <trans-unit id="contract_list">
                    <source>contract_list</source>
                    <target>Список контрактов </target>
                </trans-unit>     
                <trans-unit id="contract_create">
                    <source>contract_create</source>
                    <target>Создать контракт</target>
                </trans-unit>    
                <trans-unit id="contract_edit">
                    <source>contract_edit</source>
                    <target>Настроить контракт </target>
                </trans-unit>    
                <trans-unit id="contract_show">
                    <source>contract_show</source>
                    <target>Подробности о контракте </target>
                </trans-unit>    
                <trans-unit id="contract_delete">
                    <source>contract_delete</source>
                    <target>Удалить контракт</target>
                </trans-unit>                
                <trans-unit id="contract_history">
                    <source>contract_history</source>
                    <target>История редактирования контракта</target>
                </trans-unit>                
                <trans-unit id="contract.person">
                    <source>contract.person</source>
                    <target>Работник</target>
                </trans-unit>
                <trans-unit id="contract.agency">
                    <source>contract.agency</source>
                    <target>Агентство</target>
                </trans-unit>
                <trans-unit id="contract.company">
                    <source>contract.company</source>
                    <target>Компания</target>
                </trans-unit>
                <trans-unit id="contract.code_name">
                    <source>contract.object_code_name</source>
                    <target>(Код) название объекта</target>
                </trans-unit>
                <trans-unit id="contract.contract_subject_is_immutable">
                    <source>contract.contract_subject_is_immutable</source>
                    <target>Предмет контракта не может быть изменен </target>
                </trans-unit>
                <trans-unit id="contract.message_type_not_selected">
                    <source>contract.message_type_not_selected</source>
                    <target>Вам необходимо выбрать тип контракта.</target>
                </trans-unit>
                <trans-unit id="contract.agency_hasnt_company">
                    <source>contract.agency_hasnt_company</source>
                    <target>Агентствам не назначены компании. </target>
                </trans-unit>
                <trans-unit id="contract.type">
                    <source>contract.type</source>
                    <target>Тип контракта</target>
                </trans-unit>
                <trans-unit id="contract.subject">
                    <source>contract.subject</source>
                    <target>Работник/Агентство</target>
                </trans-unit> 
                <trans-unit id="contract.employment">
                    <source>contract.employment</source>
                    <target>Полная занятость </target>
                </trans-unit>
                <trans-unit id="contract.dpc">
                    <source>contract.dpc</source>
                    <target>Контракт на обслуживание </target>
                </trans-unit>
                <trans-unit id="contract.tradesman">
                    <source>contract.tradesman</source>
                    <target>Само-занятое лицо</target>
                </trans-unit> 
                <trans-unit id="contract.dpp">
                    <source>contract.dpp</source>
                    <target>Трудовое соглашение</target>
                </trans-unit>
                <trans-unit id="contract.external">
                    <source>contract.external</source>
                    <target>Внешнее сотрудничество</target>
                </trans-unit>
                <trans-unit id="contract.other">
                    <source>contract.other</source>
                    <target>Другое</target>
                </trans-unit>
                <trans-unit id="contract.parallel">
                    <source>contract.parallel</source>
                    <target>Трудовой договор по совместительству</target>
                </trans-unit> 
                <trans-unit id="contract.vpp">
                    <source>contract.vpp</source>
                    <target></target>
                </trans-unit> 
                <trans-unit id="contract.short">
                    <source>contract.short</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="contract.alert_title">
                    <source>contract.alert_title</source>
                    <target>Предупреждение! Этот предмет уже содержат эти контракты:</target>
                </trans-unit>
                <trans-unit id="contract.employment_abbr">
                    <source>contract.employment_abbr</source>
                    <target>Полная занятость</target>
                </trans-unit>
                <trans-unit id="contract.dpc_abbr">
                    <source>contract.dpc_abbr</source>
                    <target>Частичная занятость</target>
                </trans-unit>
                <trans-unit id="contract.tradesman_abbr">
                    <source>contract.tradesman_abbr</source>
                    <target>ID номер</target>                    
                </trans-unit>         
                <trans-unit id="contract.agency_abbr">
                    <source>contract.agency_abbr</source>
                    <target>Агентство</target>
                </trans-unit>
                <trans-unit id="contract.dpp_abbr">
                    <source>contract.dpp_abbr</source>
                    <target>Трудовое соглашение</target>
                </trans-unit>
                <trans-unit id="contract.external_abbr">
                    <source>contract.external_abbr</source>
                    <target>Внешнее</target>
                </trans-unit>
                <trans-unit id="contract.other_abbr">
                    <source>contract.other_abbr</source>
                    <target>Другое</target>
                </trans-unit>
                <trans-unit id="contract.parallel_abbr">
                    <source>contract.parallel_abbr</source>
                    <target>Трудовой договор по совместительству</target>
                </trans-unit>  
                <trans-unit id="contract.short_abbr">
                    <source>contract.short_abbr</source>
                    <target></target>
                </trans-unit> 
                <trans-unit id="contract.vpp_abbr">
                    <source>contract.vpp_abbr</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="link_action_create_contract_subjects">
                    <source>link_action_create_contract_subjects</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="link_action_create_contract_objects">
                    <source>link_action_create_contract_objects</source>
                    <target></target>
                </trans-unit>     
                <trans-unit id="contract_batch_create_success">
                    <source>contract_batch_create_success</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="contract_batch_create_error">
                    <source>contract_batch_create_error</source>
                    <target></target>
                </trans-unit>   
                <trans-unit id="contract.subject_not_selected">
                    <source>contract.subject_not_selected</source>
                    <target></target>
                </trans-unit> 
                <trans-unit id="contract.object_not_selected">
                    <source>contract.object_not_selected</source>
                    <target></target>
                </trans-unit>    
            </group>
            
            <group resname="subjectGroups">
                <note>Groups of subjects</note>
                <trans-unit id="subject_group.title">
                    <source>subject_group.title</source>
                    <target>Группы лиц</target>
                </trans-unit>  
                <trans-unit id="subject_group_list">
                    <source>subject_group_list</source>
                    <target>Список групп лиц</target>
                </trans-unit>  
                <trans-unit id="subject_group_create">
                    <source>subject_group_create</source>
                    <target>Создать группу лиц </target>
                </trans-unit>    
                <trans-unit id="subject_group_edit">
                    <source>subject_group_edit</source>
                    <target>Настроить группу лиц </target>
                </trans-unit>    
                <trans-unit id="subject_group_show">
                    <source>subject_group_show</source>
                    <target>Подробности о группе лиц </target>
                </trans-unit>    
                <trans-unit id="subject_group_delete">
                    <source>subject_group_delete</source>
                    <target>Удалить группу лиц </target>
                </trans-unit>                
                <trans-unit id="subject_group_history">
                    <source>subject_group_history</source>
                    <target>История редактирования группы лиц  </target>
                </trans-unit> 
                <trans-unit id="subject_group.subjects">
                    <source>subject_group.subjects</source>
                    <target>Группы </target>
                </trans-unit>              
                <trans-unit id="subject_group.name">
                    <source>subject_group.name</source>
                    <target>Название группы</target>
                </trans-unit>              
                <trans-unit id="subject_group.count">
                    <source>subject_group.count</source>
                    <target>Лица</target>
                </trans-unit>               
            </group>
            
            <group resname="subjects">
                <note>Subjects</note>             
                <trans-unit id="subject.title">
                    <source>subject.title</source>
                    <target>Лица</target>
                </trans-unit> 
                <trans-unit id="subject_list">
                    <source>subject_list</source>
                    <target>Список лиц</target>
                </trans-unit>
                <trans-unit id="subject_create">
                    <source>subject_create</source>
                    <target>Создать лицо </target>
                </trans-unit>    
                <trans-unit id="subject_edit">
                    <source>subject_edit</source>
                    <target>Настроить лицо </target>
                </trans-unit>    
                <trans-unit id="subject_show">
                    <source>subject_show</source>
                    <target>Подробности о лице</target>
                </trans-unit>    
                <trans-unit id="subject_delete">
                    <source>subject_delete</source>
                    <target>Удалить лицо </target>
                </trans-unit>                
                <trans-unit id="subject_history">
                    <source>subject_history</source>
                    <target>История редактирования лица</target>
                </trans-unit>                     
            </group>
            
            
            <group resname="periond">
                <note>Period (accounting etc.)</note>
                <trans-unit id="period.title">
                    <source>period.title</source>
                    <target>Период</target>
                </trans-unit>  
                <trans-unit id="period_list">
                    <source>period_list</source>
                    <target>Список периодов</target>
                </trans-unit> 
                <trans-unit id="period_create">
                    <source>period_create</source>
                    <target>Создать период</target>
                </trans-unit>    
                <trans-unit id="period_edit">
                    <source>period_edit</source>
                    <target>Настроить период</target>
                </trans-unit>    
                <trans-unit id="period_show">
                    <source>period_show</source>
                    <target>Подробности о периоде</target>
                </trans-unit>    
                <trans-unit id="period_delete">
                    <source>period_delete</source>
                    <target>Удалить период </target>
                </trans-unit>                
                <trans-unit id="period_history">
                    <source>period_history</source>
                    <target>История редактирования периода </target>
                </trans-unit>                
                <trans-unit id="period.object">
                    <source>period.object</source>
                    <target>Объект</target>
                </trans-unit>
                <trans-unit id="period.objects">
                    <source>period.objects</source>
                    <target>Объекты</target>
                </trans-unit>
                <trans-unit id="period.type">
                    <source>period.type</source>
                    <target>Тип периода</target>
                </trans-unit>
                <trans-unit id="period.billing_type">
                    <source>period.billing_type</source>
                    <target>Тип биллинга</target>
                </trans-unit>
                <trans-unit id="period.salary_type">
                    <source>period.salary_type</source>
                    <target>Тип заработной платы</target>
                </trans-unit>
                <trans-unit id="period.is_overlaped">
                    <source>period.is_overlaped</source>
                    <target>Введенный период пересекает другой, назначенный на объект.</target>
                </trans-unit>
            </group>
            <group resname="periodicPayments">
                <note>Периодические платежи и их установки</note>
                <trans-unit id="periodic_payment.title">
                    <source>periodic_payment.title</source>
                    <target>Периодические платежи </target>
                </trans-unit>
                <trans-unit id="periodic_payment_list">
                    <source>periodic_payment_list</source>
                    <target>Список периодических платежей </target>
                </trans-unit>
                <trans-unit id="periodic_payment_create">
                    <source>periodic_payment_create</source>
                    <target>Создать периодический платеж</target>
                </trans-unit>    
                <trans-unit id="periodic_payment_edit">
                    <source>periodic_payment_edit</source>
                    <target>Настроить периодический платеж</target>
                </trans-unit>    
                <trans-unit id="periodic_payment_show">
                    <source>periodic_payment_show</source>
                    <target>Подробности о периодическом платеже</target>
                </trans-unit>    
                <trans-unit id="periodic_payment_delete">
                    <source>periodic_payment_delete</source>
                    <target>Удалить периодический платеж </target>
                </trans-unit>                
                <trans-unit id="periodic_payment_history">
                    <source>periodic_payment_history</source>
                    <target>История редактирования периодического платежа</target>
                </trans-unit>                
                <trans-unit id="periodic_payment.product">
                    <source>periodic_payment.product</source>
                    <target>Продукт</target>
                </trans-unit>
                <trans-unit id="periodic_payment.paymentType">
                    <source>periodic_payment.paymentType</source>
                    <target>Тип платежа </target>
                </trans-unit>
                <trans-unit id="periodic_payment.amount">
                    <source>periodic_payment.amount</source>
                    <target>Сумма </target>
                </trans-unit>
                <trans-unit id="periodic_payment.period">
                    <source>periodic_payment.period</source>
                    <target>Период</target>
                </trans-unit>
                <trans-unit id="periodic_payment.approved">
                    <source>periodic_payment.approved</source>
                    <target>Статус</target>
                </trans-unit>   
                <trans-unit id="periodic_payment.approved_filter">
                    <source>periodic_payment.approved_filter</source>
                    <target>Одобрено?</target>
                </trans-unit>              
                <trans-unit id="periodic_payment.note">
                    <source>periodic_payment.note</source>
                    <target>Примечание</target>
                </trans-unit>   
                <trans-unit id="periodic_payment.note_help">
                    <source>periodic_payment.note_help</source>
                    <target></target>
                </trans-unit>                   
                <trans-unit id="periodic_payment.is_approved">
                    <source>periodic_payment.is_approved</source>
                    <target>Одобрено</target>
                </trans-unit>
                <trans-unit id="periodic_payment.not_approved">
                    <source>periodic_payment.not_approved</source>
                    <target>Не одобрено</target>
                </trans-unit>            
                <trans-unit id="periodic_payment.flash_approbate">
                    <source>periodic_payment.flash_approbate</source>
                    <target>Выбранный периодический платеж был одобрен.</target>
                </trans-unit>  
                <trans-unit id="periodic_payment.flash_with_draw_approval">
                    <source>periodic_payment.flash_with_draw_approval</source>
                    <target> Выбранный периодический платеж был отмечен как неодобренный.</target>
                </trans-unit>  
                <trans-unit id="periodic_payment.flash_error_is_exported">
                    <source>periodic_payment.flash_error_is_exported</source>
                    <target>Операция не может быть выполнена,периодический платеж уже был экспортирован</target>
                </trans-unit>
                <trans-unit id="periodic_payment_batch_unblock">
                    <source>periodic_payment_batch_unblock</source>
                    <target>Разблокировать после экспорта</target>
                </trans-unit>
            </group>
            
            <group resname="paymentTypesSelect">
                <note>Selecting the type of payment</note>
                <trans-unit id="periodic_payment.choose_periodic_payment">
                    <source>periodic_payment.choose_periodic_payment</source>
                    <target>-- Выбрать тип платежа --</target>
                </trans-unit>
                <trans-unit id="lump_sum_reward">
                    <source>lump_sum_reward</source>
                    <target>вознаграждение</target>
                </trans-unit>
                <trans-unit id="lump_sum_penalty">
                    <source>lump_sum_penalty</source>
                    <target>штраф</target>
                </trans-unit>
                <trans-unit id="recurring_reward">
                    <source>recurring_reward</source>
                    <target>повторное вознаграждение</target>
                </trans-unit>
                <trans-unit id="recurring_penalty">
                    <source>recurring_penalty</source>
                    <target>повторный штраф</target>
                </trans-unit>
            </group>
            
            
            
            <group resname="positionRates">
                <note>Position rates</note>
                <trans-unit id="position_rate.title">
                    <source>position_rate.title</source>
                    <target>Тарифы по позициям </target>
                </trans-unit>  
                <trans-unit id="position_rate_list">
                    <source>position_rate_list</source>
                    <target>Список тарифов по позициям </target>
                </trans-unit>   
                <trans-unit id="position_rate_create">
                    <source>position_rate_create</source>
                    <target>Создать тариф для позиции </target>
                </trans-unit>    
                <trans-unit id="position_rate_edit">
                    <source>position_rate_edit</source>
                    <target>Настроить тариф для позиции </target>
                </trans-unit>    
                <trans-unit id="position_rate_show">
                    <source>position_rate_show</source>
                    <target>Подробности о тарифе для позиции </target>
                </trans-unit>    
                <trans-unit id="position_rate_delete">
                    <source>position_rate_delete</source>
                    <target>Удалить тариф для позиции </target>
                </trans-unit>                
                <trans-unit id="position_rate_history">
                    <source>position_rate_history</source>
                    <target>История редактирования тарифа для позиции </target>
                </trans-unit>                
            </group>
            <group resname="personRates">
                <note>Person rates</note>
                <trans-unit id="person_rates.title">
                    <source>person_rates.title</source>
                    <target>Личный тариф </target>
                </trans-unit>
                <trans-unit id="person_rate_list">
                    <source>persons_rate_list</source>
                    <target>Список личных тарифов</target>
                </trans-unit>
                <trans-unit id="person_rate_create">
                    <source>persons_rate_create</source>
                    <target>Создать личный тариф</target>
                </trans-unit>    
                <trans-unit id="person_rate_edit">
                    <source>persons_rate_edit</source>
                    <target>Настроить личный тариф </target>
                </trans-unit>    
                <trans-unit id="person_rate_show">
                    <source>persons_rate_show</source>
                    <target>Подробности о личном тарифе </target>
                </trans-unit>    
                <trans-unit id="person_rate_delete">
                    <source>persons_rate_delete</source>
                    <target>Удалить личный тариф </target>
                </trans-unit>                
                <trans-unit id="person_rate_history">
                    <source>persons_rate_history</source>
                    <target>История редактирования  личного тарифа </target>
                </trans-unit>        
                <trans-unit id="person_rate.rate">
                    <source>person_rate.rate</source>
                    <target> Личный тариф </target>
                </trans-unit>
                <trans-unit id="person_rate.starts_at">
                    <source>person_rate.starts_at</source>
                    <target>Действителен с</target>
                </trans-unit>
                <trans-unit id="person_rate.ends_at">
                    <source>person_rate.ends_at</source>
                    <target>Действителен до</target>
                </trans-unit>  
                <trans-unit id="person_rate.person_rate_subject_is_immutable">
                    <source>person_rate.person_rate_subject_is_immutable</source>
                    <target>Назначенное лицо не может быть изменено</target>
                </trans-unit> 
                <trans-unit id="person_rate.error_starts_before_ends">
                    <source>person_rate.error_starts_before_ends</source>
                    <target>Окончание периода начинается прежде начала периода</target>
                </trans-unit>            
            </group>
            <group resname="agencyRates">
                <note>Agency rates</note>
                <trans-unit id="agency_rate.title">
                    <source>agency_rate.title</source>
                    <target>Агентские тарифы </target>
                </trans-unit>  
                <trans-unit id="agency_rate_list">
                    <source>agency_rate_list</source>
                    <target>Список агентских тарифов </target>
                </trans-unit>    
                <trans-unit id="agency_rate_create">
                    <source>agency_rate_create</source>
                    <target>Создать агентский тариф </target>
                </trans-unit>    
                <trans-unit id="agency_rate_edit">
                    <source>agency_rate_edit</source>
                    <target>Настроить агентский тариф </target>
                </trans-unit>    
                <trans-unit id="agency_rate_show">
                    <source>agency_rate_show</source>
                    <target>Подробности об агентском тарифе </target>
                </trans-unit>    
                <trans-unit id="agency_rate_delete">
                    <source>agency_rate_delete</source>
                    <target>Удалить агентский тариф</target>
                </trans-unit>                
                <trans-unit id="agency_rate_history">
                    <source>agency_rate_history</source>
                    <target>История редактирования агентского тарифа </target>
                </trans-unit>             
                <trans-unit id="agency_rate.rate">
                    <source>agency_rate.rate</source>
                    <target>Агентский тариф </target>
                </trans-unit>  
                <trans-unit id="agency_rate.role">
                    <source>agency_rate.role</source>
                    <target>Позиция</target>
                </trans-unit>  
                <trans-unit id="agency_rate.agency_rate_subject_is_immutable">
                    <source>agency_rate.person_rate_subject_is_immutable</source>
                    <target>Назначенное лицо не может быть изменено</target>
                </trans-unit>  
                <trans-unit id="agency_rate.positions_label">
                    <source>agency_rate.positions_label</source>
                    <target>Создать тариф для</target>
                </trans-unit>
                <trans-unit id="agency_rate.all_positions">
                    <source>agency_rate.all_positions</source>
                    <target>Все позиции</target>
                </trans-unit>
                <trans-unit id="agency_rate.position_group">
                    <source>agency_rate.position_group</source>
                    <target>Группа позиций</target>
                </trans-unit>       
            </group>
            <group resname="individualRates">
                <note>Individual rates</note>
                <trans-unit id="individual_rate">
                    <source>individual_rate</source>
                    <target>Индивидуальный тариф </target>
                </trans-unit>
                <trans-unit id="individual_rate_list">
                    <source>individual_rate_list</source>
                    <target>Список индивидуальных тарифов </target>
                </trans-unit>
                <trans-unit id="individual_rate_create">
                    <source>individual_rate_create</source>
                    <target>Создать индивидуальный тариф </target>
                </trans-unit>    
                <trans-unit id="individual_rate_edit">
                    <source>individual_rate_edit</source>
                    <target>Настроить индивидуальный тариф </target>
                </trans-unit>    
                <trans-unit id="individual_rate_show">
                    <source>individual_rate_show</source>
                    <target>Подробности об индивидуальном тарифе </target>
                </trans-unit>    
                <trans-unit id="individual_rate_delete">
                    <source>individual_rate_delete</source>
                    <target>Удалить индивидуальный тариф </target>
                </trans-unit>                
                <trans-unit id="individual_rate_history">
                    <source>individual_rate_history</source>
                    <target>История редактирования индивидуального тарифа </target>
                </trans-unit>           
                <trans-unit id="individual_rate.rate">
                    <source>individual_rate.rate</source>
                    <target> Индивидуальный тариф </target>
                </trans-unit>
                <trans-unit id="individual_rate.position_is_immutable">
                    <source>individual_rate.position_is_immutable</source>
                    <target>Назначенное лицо не может быть изменено </target>
                </trans-unit> 
                <trans-unit id="individual_rate_hired_create">
                    <source>individual_rate_hired_create</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="individual_rate_hired_edit">
                    <source>individual_rate_hired_edit</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="create_for_hired_button">
                    <source>create_for_hired_button</source>
                    <target></target>
                </trans-unit>
            </group>
            <group resname="objectGroups">
                <note>Groups of objects</note>
                <trans-unit id="object_group.title">
                    <source>object_group.title</source>
                    <target>Группы объектов</target>
                </trans-unit>
                <trans-unit id="object_group_list">
                    <source>object_group_list</source>
                    <target>Группа объектов</target>
                </trans-unit>             
                <trans-unit id="object_group_create">
                    <source>object_group_create</source>
                    <target>Создать группу объектов </target>
                </trans-unit>    
                <trans-unit id="object_group_edit">
                    <source>object_group_edit</source>
                    <target>Настроить группу объектов </target>
                </trans-unit>    
                <trans-unit id="object_group_show">
                    <source>object_group_show</source>
                    <target>Подробности о группе объектов </target>
                </trans-unit>    
                <trans-unit id="object_group_delete">
                    <source>object_group_delete</source>
                    <target>Удалить группу объектов </target>
                </trans-unit>                
                <trans-unit id="object_group_history">
                    <source>object_group_history</source>
                    <target>История редактирования группы объектов </target>
                </trans-unit>             
                <trans-unit id="object_group.name">
                    <source>object_group.name</source>
                    <target>Название группы</target>
                </trans-unit>
                <trans-unit id="object_group.count">
                    <source>object_group.count</source>
                    <target>Номер объектов</target>
                </trans-unit>
            </group>
            
            
            <group resname="calendar">
                <note>Calendar for setting up public holidays</note>            
                <trans-unit id="calendar.title">
                    <source>calendar.title</source>
                    <target>Календарь</target>
                </trans-unit>
                <trans-unit id="year">
                    <source>year</source>
                    <target>Год</target>
                </trans-unit> 
                <trans-unit id="calendar">
                    <source>calendar</source>
                    <target>Календарь</target>
                </trans-unit>           
            </group>
            <group resname="service_editor">
                <note></note>
                <trans-unit id="service">
                    <source>service</source>
                    <target>Услуга</target>
                </trans-unit>                
                <!-- Services -->   
                <trans-unit id="service.service">
                    <source>service.service</source>
                    <target>Сервис</target>
                </trans-unit>
                <trans-unit id="service_edit_edit">
                    <source>service_edit_edit</source>
                    <target>Назначение лица на обслуживание </target>
                </trans-unit>
                <trans-unit id="service_billing_create">
                    <source>service_billing_create</source>
                    <target>Создание обслуживания</target>
                </trans-unit>
                <trans-unit id="service_billing_edit">
                    <source>service_billing_edit</source>
                    <target>Установить времена обслуживания </target>
                </trans-unit>
                <trans-unit id="service_billing_delete">
                    <source>service_billing_delete</source>
                    <target>Удалить обслуживание</target>
                </trans-unit>
                <trans-unit id="service.starts_at">
                    <source>service.starts_at</source>
                    <target>Начало обслуживания</target>
                </trans-unit>
                <trans-unit id="service.ends_at">
                    <source>service.ends_at</source>
                    <target>Окончание обслуживания</target>
                </trans-unit>
                <trans-unit id="service_billing_list">
                    <source>service_billing_list</source>
                    <target>Изменение обслуживания</target>
                </trans-unit>           
                <trans-unit id="service.position">
                    <source>service.position</source>
                    <target>Позиция</target>
                </trans-unit>
                <trans-unit id="date/time">
                    <source>date/time</source>
                    <target>Дата/время</target>
                </trans-unit>
                <trans-unit id="service.service_billing">
                    <source>service.service_billing</source>
                    <target>Настроить обслуживание </target>
                </trans-unit> 
                <trans-unit id="service_edit_list">
                    <source>service_edit_list</source>
                    <target>Редактор обслуживаний</target>
                </trans-unit>
                <trans-unit id="service_action_batch_edit">
                    <source>service_action_batch_edit</source>
                    <target>Установить время</target>
                </trans-unit>
                <trans-unit id="service_action_batch_billing_rates_update">
                    <source>service_action_batch_billing_rates_update</source>
                    <target>Обновить тарифы биллинга</target>
                </trans-unit>
                <trans-unit id="service.remove_subject">
                    <source>service.remove_subject</source>
                    <target>Удалить лицо от обслуживания </target>
                </trans-unit>
                <trans-unit id="service.service_edit">
                    <source>service.service_edit</source>
                    <target>Поменять лицо </target>
                </trans-unit>
                <trans-unit id="service.subject">
                    <source>service.subject</source>
                    <target>Лицо / агентство</target>
                </trans-unit>
                <trans-unit id="service.empty_day">
                    <source>service.empty_day</source>
                    <target>Нет обслуживания</target>
                </trans-unit>
                <trans-unit id="service.not_occupied">
                    <source>service.not_occupied</source>
                    <target>Не занято </target>
                </trans-unit>
                <trans-unit id="service.person">
                    <source>service.person</source>
                    <target>Лицо</target>
                </trans-unit>
                <trans-unit id="service.service_date">
                    <source>service.service_date</source>
                    <target>Дата и время начала обслуживания</target>
                </trans-unit>
                <trans-unit id="service.billing_batch_dialog_title">
                    <source>service.billing_batch_dialog_title</source>
                    <target>Установка времени обслуживаний</target>
                </trans-unit>
                <trans-unit id="service.change_person_batch_dialog_title">
                    <source>service.change_person_batch_dialog_title</source>
                    <target>Выбор предмета к избранному обслуживанию</target>
                </trans-unit>
                <trans-unit id="service.change_person">
                    <source>service.change_person</source>
                    <target>Назначить лицо</target>
                </trans-unit>
                <trans-unit id="service.remove_person">
                    <source>service.remove_person</source>
                    <target>Удалить лица из обслуживаний</target>
                </trans-unit>
                <trans-unit id="service.subject_hire_show">
                    <source>service.subject_hire_show</source>
                    <target>Лица с другого объекта</target>
                </trans-unit>                
                <trans-unit id="service.date">
                    <source>service.date</source>
                    <target>Дата обслуживания</target>
                </trans-unit>
                <trans-unit id="service.attendance">
                    <source>service.attendance</source>
                    <target>Присутствие</target>
                </trans-unit>
                <trans-unit id="service.schedule">
                    <source>service.schedule</source>
                    <target>План</target>
                </trans-unit>
                <trans-unit id="service.shift">
                    <source>service.shift</source>
                    <target>Смена</target>
                </trans-unit>
                <trans-unit id="service_attendance_edit">
                    <source>service_attendance_edit</source>
                    <target>Редактирование присутствия</target>
                </trans-unit>
                <trans-unit id="service_attendance_delete">
                    <source>service_attendance_delete</source>
                    <target>Удалить присутствие </target>
                </trans-unit>
                <trans-unit id="service.period">
                    <source>service.period</source>
                    <target>Период</target>
                </trans-unit>
                <trans-unit id="service.create_attendance">
                    <source>service.create_attendance</source>
                    <target>Ввести новое присутствие </target>
                </trans-unit>
                <trans-unit id="service.approved">
                    <source>service.approved</source>
                    <target>ОДОБРЕНО</target>
                </trans-unit>                
                <trans-unit id="service.set_correction_batch_dialog_title">
                    <source>service.set_correction_batch_dialog_title</source>
                    <target>Формировать коррекцию времени присутствия </target>
                </trans-unit>
                <trans-unit id="attendance_batch_time_correction">
                    <source>attendance.batch_time_correction</source>
                    <target>Время коррекции</target>
                </trans-unit>
                <trans-unit id="btn_set_by_service">
                    <source>btn_set_by_service</source>
                    <target>Согласно плану</target>
                </trans-unit>
                <trans-unit id="service_attendance_create">
                    <source>service_attendance_create</source>
                    <target>Ввести присутствие </target>
                </trans-unit>
                <trans-unit id="service_attendance_list">
                    <source>service_attendance_list</source>
                    <target>Редактирование и одобрение присутствия </target>
                </trans-unit>
                <trans-unit id="service_for_attendance_edit">
                    <source>service_for_attendance_edit</source>
                    <target>Ввести новое присутствие к обслуживанию </target>
                </trans-unit>
                <trans-unit id="service_action_batch_approve_services">
                    <source>service_action_batch_approve_services</source>
                    <target>Одобрить обслуживания</target>
                </trans-unit>
                <trans-unit id="service_action_batch_unapprove_services">
                    <source>service_action_batch_unapprove_services</source>
                    <target>Не одобрить обслуживания</target>
                </trans-unit>
                <trans-unit id="service_action_batch_approve_attendances">
                    <source>service_action_batch_approve_attendances</source>
                    <target>Одобрить присутствие </target>
                </trans-unit>
                <trans-unit id="service_action_batch_unapprove_attendances">
                    <source>service_action_batch_unapprove_attendances</source>
                    <target>Не одобрить присутствие </target>
                </trans-unit>
                <trans-unit id="service_attendance_show">
                    <source>service_attendance_show</source>
                    <target>Подробности о присутствии </target>
                </trans-unit>
                <trans-unit id="service_edit_print">
                    <source>service_edit_print</source>
                    <target>Расписание обслуживания на объекте</target>
                </trans-unit>  
                <trans-unit id="service_edit_print.month">
                    <source>service_edit_print.month</source>
                    <target>в месяц</target>
                </trans-unit>  
                <trans-unit id="service_edit_print.period">
                    <source>service_edit_print.period</source>
                    <target>за период</target>
                </trans-unit> 
                <trans-unit id="service.person_in_shift_is_overlaped">
                    <source>service.person_in_shift_is_overlaped</source>
                    <target>Пересекающиеся времена обслуживания </target>
                </trans-unit>  
                <trans-unit id="subject_select.agencies">
                    <source>subject_select.agencies</source>
                    <target>Агентства</target>
                </trans-unit>     
                <trans-unit id="service.flash_no_contract_or_rate">
                    <source>service.flash_no_contract_or_rate</source>
                    <target>К некоторым обслуживаниям не прикреплены предметы, потому что у них нет действующих контрактов или тарифов на данные обслуживания</target>
                </trans-unit>  
                <trans-unit id="service_attendance.empty_subject_error">
                    <source>service_attendance.empty_subject_error</source>
                    <target>Вам нужно выбрать лицо к присутствию</target>
                </trans-unit>      
                <trans-unit id="attendance.create_by_plan">
                    <source>attendance.create_by_plan</source>
                    <target>Создать присутствие согласно плану</target>
                </trans-unit>  
                <trans-unit id="service_attendance.delete">
                    <source>service_attendance.delete</source>
                    <target>Удалить присутствия</target>
                </trans-unit>
                <trans-unit id="service_attendance_remove_attendance">
                    <source>service_attendance_remove_attendance</source>
                    <target>Удаление присутствия</target>
                </trans-unit>
                <trans-unit id="service_attendance.subject_rate_update">
                    <source>service_attendance.subject_rate_update</source>
                    <target>Обновить персональные тарифы на присутствия</target>
                </trans-unit>
                <trans-unit id="service_attendance.unapproved_attendances_in_past">
                    <source>service_attendance.unapproved_attendances_in_past</source>
                    <target>Не может быть одобрено, в последних периодах существует неодобренное присутствие.</target>
                </trans-unit>
                <trans-unit id="service_attendance.unfinished_attendance">
                    <source>service_attendance.unfinished_attendance</source>
                    <target>Присутствие не может быть создано. Услуга содержит неоконченное присутствие!</target>
                </trans-unit> 
                <trans-unit id="service_attendance.unfinished_attendance_approved">
                    <source>service_attendance.unfinished_attendance_approved</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_action_batch_unblock_services">
                    <source>service_action_batch_unblock_services</source>
                    <target>Разблокировать биллинг</target>
                </trans-unit>
                <trans-unit id="service_attendance.unblock_attendances">
                    <source>service_attendance.unblock_attendances</source>
                    <target>Разблокировать присутствие</target>
                </trans-unit>  
                <trans-unit id="batch_error_partial">
                    <source>batch_error_partial</source>
                    <target></target>
                </trans-unit>               
            </group>
            <group resname="events">
                <note>Events during service</note>
                <trans-unit id="event.title">
                    <source>event.title</source>
                    <target>Событие</target>
                </trans-unit>
                <trans-unit id="event.created">
                    <source>event.created</source>
                    <target>Событие создано </target>
                </trans-unit>
                <trans-unit id="event.renamed">
                    <source>event.renamed</source>
                    <target>Событие переименовано </target>
                </trans-unit>
                <trans-unit id="event.create_new">
                    <source>event.create_new</source>
                    <target>Создать новое событие </target>
                </trans-unit>
                <trans-unit id="event.rename">
                    <source>event.rename</source>
                    <target>Переименовать событие </target>
                </trans-unit>
                <trans-unit id="event_list">
                    <source>event_list</source>
                    <target>Список событий</target>
                </trans-unit>
                <trans-unit id="event.is_active_saved">
                    <source>event.is_active_saved</source>
                    <target>Опция сохранена</target>
                </trans-unit>
                <trans-unit id="event.is_hidden_saved">
                    <source>event.is_hidden_saved</source>
                    <target>Выбор был сохранен</target>
                </trans-unit>                
                <trans-unit id="event.activate">
                    <source>event.activate</source>
                    <target>Активировать </target>
                </trans-unit>
                <trans-unit id="event.deactivate">
                    <source>event.deactivate</source>
                    <target>Деактивировать </target>
                </trans-unit>
                <trans-unit id="event.hide">
                    <source>event.hide</source>
                    <target>Скрыть событие</target>
                </trans-unit>
                <trans-unit id="event.show">
                    <source>event.show</source>
                    <target>Сделать событие видимым</target>
                </trans-unit>                
                <trans-unit id="event.search_title">
                    <source>event.search_title</source>
                    <target>Найти из списка событий </target>
                </trans-unit>
            </group>
            <group resname="reporting">
                <note>Reporting setting for the customer</note>
                <trans-unit id="reporting.title">
                    <source>reporting.title</source>
                    <target>Установки передачи сообщений</target>
                </trans-unit>
                <trans-unit id="reporting_edit">
                    <source>reporting_edit</source>
                    <target>Настроить установки передачи сообщений </target>
                </trans-unit>
                <trans-unit id="reporting_list">
                    <source>reporting_list</source>
                    <target>Передачи сообщений</target>
                </trans-unit>
                <trans-unit id="reporting_setting.none">
                    <source>reporting_setting.none</source>
                    <target>Не отправлено</target>
                </trans-unit>           
                <trans-unit id="reporting_setting.sms">
                    <source>reporting_setting.sms</source>
                    <target>SMS</target>
                </trans-unit>
                <trans-unit id="reporting_setting.email">
                    <source>reporting_setting.email</source>
                    <target>E-mail</target>
                </trans-unit>
                <trans-unit id="reporting_setting.all">
                    <source>reporting_setting.all</source>
                    <target>SMS + E-mail</target>
                </trans-unit>  
                <trans-unit id="reporting.reporting_create">
                    <source>reporting_create</source>
                    <target>Создать новые установки передачи сообщений </target>
                </trans-unit>
                <trans-unit id="reporting.object">
                    <source>reporting.object</source>
                    <target>Объект</target>
                </trans-unit>
                <trans-unit id="reporting.subject">
                    <source>reporting.subject</source>
                    <target>Субъект</target>
                </trans-unit>
                <trans-unit id="reporting.object_group">
                    <source>reporting.object_group</source>
                    <target>Группа объектов</target>
                </trans-unit>
                <trans-unit id="reporting.subject_group">
                    <source>reporting.subject_group</source>
                    <target>Группа субъектов </target>
                </trans-unit>
                <trans-unit id="reporting.choose_objects">
                    <source>reporting.choose_objects</source>
                    <target>Выделить объект или группу объектов </target>
                </trans-unit>
                <trans-unit id="reporting.choose_subjects">
                    <source>reporting.choose_subjects</source>
                    <target>Выбрать субъект или группу субъектов </target>
                </trans-unit>
                <trans-unit id="reporting.open_branch">
                    <source>reporting.open_branch</source>
                    <target>Открыть филиал</target>
                </trans-unit>
                <trans-unit id="reporting.close_branch">
                    <source>reporting.close_branch</source>
                    <target>Закрыть филиал</target>
                </trans-unit>
                <trans-unit id="reporting.template">
                    <source>reporting.template</source>
                    <target>Установить согласно шаблону</target>
                </trans-unit>                
            </group>
            <group resname="reporting_template">
                <note>Nastavenн љablony hlбљenн pro zбkaznнka</note>
                <trans-unit id="reporting_template.title">
                    <source>reporting_template.title</source>
                    <target>Установки шаблонов создания отчетов</target>
                </trans-unit>
                <trans-unit id="reporting_template_create">
                    <source>reporting_template_create</source>
                    <target>Создать шаблон установок создания отчетов</target>
                </trans-unit>                
                <trans-unit id="reporting_template_edit">
                    <source>reporting_template_edit</source>
                    <target>Редактировать шаблон установок создания отчетов</target>
                </trans-unit>
                <trans-unit id="reporting_template_delete">
                    <source>reporting_template_delete</source>
                    <target>Удалить шаблон установок создания отчетов</target>
                </trans-unit>                
                <trans-unit id="reporting_template_list">
                    <source>reporting_template_list</source>
                    <target>Список шаблонов создания отчетов</target>
                </trans-unit>
                <trans-unit id="reporting_template.name">
                    <source>reporting_template.name</source>
                    <target>Имя</target>
                </trans-unit>
                <trans-unit id="reporting_template.description">
                    <source>reporting_template.description</source>
                    <target>Описание</target>
                </trans-unit>
            </group>            
            <group resname="customerContacts">
                <note>Contacts for the customer</note>
                <trans-unit id="customer_contact.title">
                    <source>customer_contact.title</source>
                    <target>Подробности о контактах с лицом</target>
                </trans-unit>
                <trans-unit id="customer_contact_list">
                    <source>customer_contact_list</source>
                    <target>Список контактов </target>
                </trans-unit>
                <trans-unit id="contacts_list">
                    <source>contacts_list</source>
                    <target>Список контактов </target>
                </trans-unit>
                <trans-unit id="customer_contact_create">
                    <source>customer_contact_create</source>
                    <target>Создать контакт </target>
                </trans-unit>    
                <trans-unit id="customer_contact_edit">
                    <source>customer_contact_edit</source>
                    <target>Редактировать контакт </target>
                </trans-unit>    
                <trans-unit id="customer_contact_show">
                    <source>customer_contact_show</source>
                    <target>Детали контакта </target>
                </trans-unit>    
                <trans-unit id="customer_contact_delete">
                    <source>customer_contact_delete</source>
                    <target>Удалить контакт</target>
                </trans-unit>                
                <trans-unit id="customer_contact_history">
                    <source>customer_contact_history</source>
                    <target>История редактирования контакта </target>
                </trans-unit>                
                <trans-unit id="customer_contact.subject">
                    <source>customer_contact.subject</source>
                    <target>Заказчик</target>
                </trans-unit>                
                <trans-unit id="customer_contact.position">
                    <source>customer_contact.position</source>
                    <target>Функция лица </target>
                </trans-unit>       
                <trans-unit id="customer_contact.go_contacts">
                    <source>customer_contact.go_contacts</source>
                    <target>Показать список контактов </target>
                </trans-unit>    
                <trans-unit id="customer_contact.list_title">
                    <source>customer_contact.list_title</source>
                    <target>Список контактов с заказчиками </target>
                </trans-unit>       
                <trans-unit id="customer_contact.link_customer_list">
                    <source>customer_contact.link_customer_list</source>
                    <target>Назад к списку заказчиков </target>
                </trans-unit>  
                <trans-unit id="customer_contact.subject_select_title">
                    <source>customer_contact.subject_select_title</source>
                    <target>Выбор существующего контактного лица</target>
                </trans-unit>  
                <trans-unit id="customer_contact.subject_select">
                    <source>customer_contact.subject_select</source>
                    <target>Выбрать существующее контактное лицо </target>
                </trans-unit> 
                <trans-unit id="customer_contact.contact">
                    <source>customer_contact.contact</source>
                    <target>Создать новое контактное лицо</target>
                </trans-unit>      
                <trans-unit id="customer_contact.flash_add_success">
                    <source>customer_contact.flash_add_success</source>
                    <target>Контактное лицо было добавлено к клиенту</target>
                </trans-unit>   
                <trans-unit id="customer_contact.flash_removed">
                    <source>customer_contact.flash_removed</source>
                    <target>Контактное лицо было удалено от клиента </target>
                </trans-unit>   
                <trans-unit id="customer_contact.remove_action">
                    <source>customer_contact.remove_action</source>
                    <target>Удалить контактное лицо</target>
                </trans-unit>  
                <trans-unit id="customer_contact.remove_action_confirm">
                    <source>customer_contact.remove_action_confirm</source>
                    <target>Вы действительно желаете удалить это контактное лицо?</target>
                </trans-unit>                                   
            </group>        
            <group resname="subjectAccess">
                <note>Lists and access setting for subjects</note>   
                <trans-unit id="subject_access_list">
                    <source>subject_access_list</source>
                    <target>Список субъектов, которым позволен доступ к Операционному Модулю и он-лайн Операционному Модулю</target>
                </trans-unit> 
                <trans-unit id="customer_access_list">
                    <source>customer_access_list</source>
                    <target>OPM доступ - список клиентов</target>
                </trans-unit>
                <trans-unit id="menu_group_access">
                    <source>menu_group_access</source>
                    <target>Предоставление доступа</target>
                </trans-unit>                                                                                                                          
                <trans-unit id="menu_group_subject_access">
                    <source>menu_group_subject_access</source>
                    <target>Позволение доступа субъектов</target>
                </trans-unit>  
                <trans-unit id="menu_group_customer_access">
                    <source>menu_group_customer_access</source>
                    <target>Предоставление доступа клиентам</target>
                </trans-unit>                                                                                                                         
                <trans-unit id="subject_access.password">
                    <source>subject_access.password</source>
                    <target>Позволение доступа к Операционному Модулю</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.password_customer">
                    <source>subject_access.password_customer</source>
                    <target>Позволение доступа к он-лайн Операционному Модулю</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.password_not_generated">
                    <source>subject_access.password_not_generated</source>
                    <target>Электронная почта или личный номер субъекта не был установлен! В этом случае операции одобрения и отклонения доступа не могут быть выполнены</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.generate_password">
                    <source>subject_access.generate_password</source>
                    <target>Позволить доступ к Операционному Модулю</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.remove_password">
                    <source>subject_access.remove_password</source>
                    <target>Отклонить  доступ к Операционному Модулю</target>
                </trans-unit>     
                <trans-unit id="subject_access.generate_password_customer">
                    <source>subject_access.generate_password_customer</source>
                    <target>Позволить доступ к он-лайн Операционному Модулю</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.remove_password_customer">
                    <source>subject_access.remove_password_customer</source>
                    <target>Отклонить доступ к он-лайн Операционному Модулю </target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_generated">
                    <source>subject_access.flash_password_generated</source>
                    <target>Субъекту позволен доступ к Операционному Модулю</target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_removed">
                    <source>subject_access.flash_password_removed</source>
                    <target> Субъекту отклонен доступ к Операционному Модулю </target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_customer_generated">
                    <source>subject_access.flash_password_customer_generated</source>
                    <target> Субъекту позволен доступ к он-лайн Операционному Модулю </target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_customer_removed">
                    <source>subject_access.flash_password_customer_removed</source>
                    <target> Субъекту отклонен доступ к он-лайн Операционному Модулю </target>
                </trans-unit>    
                <trans-unit id="subject_access.flash_batch_generate_password_error">
                    <source>subject_access.flash_batch_generate_password_error</source>
                    <target>Ошибка при позволении допуска выбранных субъектов к Операционному Модулю</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_generate_password_success">
                    <source>subject_access.flash_batch_generate_password_success</source>
                    <target>Выбранным субъектам позволен допуск к Операционному Модулю</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_error">
                    <source>subject_access.flash_batch_remove_password_error</source>
                    <target> Ошибка при отклонении допуска выбранных субъектов к Операционному Модулю </target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_success">
                    <source>subject_access.flash_batch_remove_password_success</source>
                    <target> Выбранным субъектам отклонен допуск к Операционному Модулю </target>
                </trans-unit>  
                <trans-unit id="subject_access.flash_batch_generate_password_customer_error">
                    <source>subject_access.flash_batch_generate_password_customer_error</source>
                    <target> Ошибка при позволении допуска выбранных субъектов к он-лайнмОперационному Модулю </target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_generate_password_customer_success">
                    <source>subject_access.flash_batch_generate_password_customer_success</source>
                    <target> Выбранным субъектам позволен допуск к он-лайн Операционному Модулю </target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_customer_error">
                    <source>subject_access.flash_batch_remove_password_customer_error</source>
                    <target> Ошибка при отклонении допуска выбранных субъектов к он-лайн Операционному Модулю </target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_customer_success">
                    <source>subject_access.flash_batch_remove_password_customer_success</source>
                    <target> Выбранным субъектам отклонен допуск к он-лайн Операционному Модулю </target>
                </trans-unit>  
                <trans-unit id="subject_access.new_pass_mail_subject_pm">
                    <source>subject_access.new_pass_mail_subject_pm</source>
                    <target>Новый пароль для доступа к  ОМ</target>
                </trans-unit>
                <trans-unit id="subject_access.new_pass_mail_message_pm">
                    <source>subject_access.new_pass_mail_message_pm</source>
                    <target>Привет, новый пароль для доступа к ОМ был для Вас сгенерирован. Ваше имя доступа: %username% . Ваш новый пароль: %password% . Вы можете сменить пароль в ОМ.</target>
                </trans-unit>
                <trans-unit id="subject_access.new_pass_mail_subject_opm">
                    <source>subject_access.new_pass_mail_subject_opm</source>
                    <target>Новый пароль для Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="subject_access.new_pass_mail_message_opm">
                    <source>subject_access.new_pass_mail_message_opm</source>
                    <target> Привет, новый пароль для Клиентского доступа был для Вас сгенерирован. Ваше имя доступа: %username% . Ваш новый пароль: %password% . Вы можете сменить пароль в доступе Клиента.</target>
                </trans-unit>                
            </group>            
            <group resname="serviceGenerate">
                <note>Insert services</note>
                <trans-unit id="service_create.from_to">
                    <source>service_create.from_to</source>
                    <target>Параметры созданных обслуживаний</target>
                </trans-unit>
                <trans-unit id="service_create_create">
                    <source>service_create_create</source>
                    <target>Генерация обслуживания</target>
                </trans-unit>
                <trans-unit id="service_create.position">
                    <source>service_create.position</source>
                    <target>Позиция</target>
                </trans-unit>
                <trans-unit id="service_create.product">
                    <source>service_create.product</source>
                    <target>Продукт</target>
                </trans-unit>
                <trans-unit id="service_create.period">
                    <source>service_create.period</source>
                    <target>Период</target>
                </trans-unit>
                <trans-unit id="service_create.startsAt">
                    <source>service_create.startsAt</source>
                    <target>Создать с:</target>
                </trans-unit>
                <trans-unit id="service_create.endsAt">
                    <source>service_create.endsAt</source>
                    <target>Создать до:</target>
                </trans-unit>
                <trans-unit id="servise_create.shift_1">
                    <source>servise_create.shift_1</source>
                    <target>1-ая смена</target>
                </trans-unit>
                <trans-unit id="servise_create.shift_2">
                    <source>servise_create.shift_2</source>
                    <target>2-ая смена</target>
                </trans-unit>
                <trans-unit id="servise_create.shift_3">
                    <source>servise_create.shift_3</source>
                    <target>3-я смена</target>
                </trans-unit>
                <trans-unit id="service_create.from_1">
                    <source>service_create.from_1</source>
                    <target>Начало 1-ой смены</target>
                </trans-unit>
                <trans-unit id="service_create.from_2">
                    <source>service_create.from_2</source>
                    <target>Начало 2-ой смены</target>
                </trans-unit>
                <trans-unit id="service_create.from_3">
                    <source>service_create.from_3</source>
                    <target>Начало 3-й смены</target>
                </trans-unit>
                <trans-unit id="service_create.to_1">
                    <source>service_create.to_1</source>
                    <target>Отмена 1-й смены</target>
                </trans-unit>
                <trans-unit id="service_create.to_2">
                    <source>service_create.to_2</source>
                    <target>Отмена 2-й смены</target>
                </trans-unit>
                <trans-unit id="service_create.to_3">
                    <source>service_create.to_3</source>
                    <target>Отмена 3-й смены</target>
                </trans-unit>
                <trans-unit id="servise_create.help_empty_time_not_generate">
                    <source>servise_create.help_empty_time_not_generate</source>
                    <target>Если Вы не введете времена начала и окончания смены, смена не создастся ПРЕДУПРЕЖДЕНИЕ: Если смена оканчивается раньше начала, система отменяет смену на следующий день!</target>
                </trans-unit>
                <trans-unit id="service_create.no_generate">
                    <source>service_create.no_generate</source>
                    <target>Невозможно создать</target>
                </trans-unit>
            </group>                                        
                 
            <group resname="attendance">
                <note>Присутствие</note>
                <trans-unit id="attendance">
                    <source>attendance</source>
                    <target>Присутствие</target>
                </trans-unit>                
                <trans-unit id="attendance_list">
                    <source>attendance_list</source>
                    <target>Полный список присутствий</target>
                </trans-unit>
                <trans-unit id="attendance.title">
                    <source>attendance.title</source>
                    <target> Полный список присутствий </target>
                </trans-unit>
                <trans-unit id="attendance_edit">
                    <source>attendance_edit</source>
                    <target>Редактировать присутствие</target>
                </trans-unit>
                <trans-unit id="attendance_view">
                    <source>attendance_show</source>
                    <target>Подробности о присутствии</target>
                </trans-unit>                 
                
                <trans-unit id="attendance_start_list">
                    <source>attendance_start_list</source>
                    <target>Начало обслуживания</target>
                </trans-unit>
                <trans-unit id="attendance_start.title">
                    <source>attendance_start.title</source>
                    <target>Начало обслуживания</target>
                </trans-unit>
                <trans-unit id="attendance_start_edit">
                    <source>attendance_start_edit</source>
                    <target>Добавление лица к обслуживанию</target>
                </trans-unit>
                <trans-unit id="attendance_start_time">
                    <source>attendance_start_time</source>
                    <target>Установить время начала обслуживания</target>
                </trans-unit>                 
                <trans-unit id="attendance_start.now_action">
                    <source>attendance_start.now_action</source>
                    <target>Сейчас</target>
                </trans-unit>
                <trans-unit id="attendance_start_notenter">
                    <source>attendance_start_notenter</source>
                    <target>Нет начавшейся смены</target>
                </trans-unit>                
                <trans-unit id="attendance_start.plan_action">
                    <source>attendance_start.plan_action</source>
                    <target>Согласно плану</target>
                </trans-unit>
                <trans-unit id="attendance_start.time_action">
                    <source>attendance_start.time_action</source>
                    <target>Ввод времени</target>
                </trans-unit>
                <trans-unit id="attendance_start.notenter_action">
                    <source>attendance_start.notenter_action</source>
                    <target>Смена не началась</target>
                </trans-unit>
                <trans-unit id="attendance_start.unoccupied_action">
                    <source>attendance_start.unoccupied_action</source>
                    <target>Не занято</target>
                </trans-unit>
                <trans-unit id="attendance_start.unoccupied_partly_action">
                    <source>attendance_start.unoccupied_partly_action</source>
                    <target>Частично не занято</target>
                </trans-unit>                
                <trans-unit id="attendance_start.unfinished_attendance">
                    <source>attendance_start.unfinished_attendance</source>
                    <target>Не закончено Присутствие существует!</target>
                </trans-unit>                
                
                <trans-unit id="attendance.starts_at">
                    <source>attendance.starts_at</source>
                    <target>Запланированное начало обслуживания</target>
                </trans-unit>
                <trans-unit id="attendance.ends_at">
                    <source>attendance.ends_at</source>
                    <target>Запланированное окончание обслуживания</target>
                </trans-unit>  
                <trans-unit id="attendance.starts_at_real">
                    <source>attendance.starts_at_real</source>
                    <target>Действительный старт обслуживания</target>
                </trans-unit>
                <trans-unit id="attendance.ends_at_real">
                    <source>attendance.ends_at_real</source>
                    <target>Действительное окончание обслуживания</target>
                </trans-unit>                               
                <trans-unit id="attendance.subject">
                    <source>attendance.subject</source>
                    <target>Название</target>
                </trans-unit>    
                <trans-unit id="attendance.approved">
                    <source>attendance.approved</source>
                    <target>Одобрено</target>
                </trans-unit> 
                <trans-unit id="attendance.exported">
                    <source>attendance.exported</source>
                    <target>Экспортировать</target>
                </trans-unit>                 
                <trans-unit id="attendance_start.flash_success">
                    <source>attendance_start.flash_success</source>
                    <target>Начало обслуживания выбранного пункта успешно реализовано.</target>
                </trans-unit>     
                <trans-unit id="attendance_start.flash_error">
                    <source>attendance_start_flash_error</source>
                    <target> Начало обслуживания выбранного пункта имеет ошибку.</target>
                </trans-unit>   
                <trans-unit id="attendance_start.flash_notenter_success">
                    <source>attendance_start.flash_notenter_success</source>
                    <target> Начало обслуживания выбранного пункта типа "обслуживание не началось" успешно реализовано.</target>
                </trans-unit>     
                <trans-unit id="attendance_start.flash_notenter_error">
                    <source>attendance_start_flash_notenter_error</source>
                    <target> Начало обслуживания выбранного пункта типа "обслуживание не началось" имеет ошибку.</target>
                </trans-unit>
                <trans-unit id="attendance_start.flash_startsAt_novalid_error">
                    <source>attendance_start.flash_startsAt_novalid_error</source>
                    <target>Введенное время начала обслуживания не может быть применено. Оно выходит за запланированное время начала и окончания обслуживания.</target>
                </trans-unit>
                <trans-unit id="attendance_end.flash_endsAt_novalid_error">
                    <source>attendance_end.flash_endsAt_novalid_error</source>
                    <target> Введенное время окончания обслуживания не может быть применено. Оно выходит за запланированное время начала и окончания обслуживания.</target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_startsAt_in_attendance_novalid_error">
                    <source>attendance_end.flash_startsAt_in_attendance_novalid_error</source>
                    <target> Введенное время начала обслуживания не может быть применено. Существует другое присутствие в заданном времени.</target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_endsAt_in_attendance_novalid_error">
                    <source>attendance_end.flash_endsAt_in_attendance_novalid_error</source>
                    <target> Введенное время окончания обслуживания не может быть применено. Существует другое присутствие в заданном времени </target>
                </trans-unit>                                                
                
                
                
                <trans-unit id="attendance_running_list">
                    <source>attendance_running_list</source>
                    <target>Текущие обслуживания</target>
                </trans-unit> 
                <trans-unit id="attendance_running_edit">
                    <source>attendance_running_edit</source>
                    <target>Редактировать текущее обслуживание/присутствие</target>
                </trans-unit> 
                <trans-unit id="attendance_running_show">
                    <source>attendance_running_show</source>
                    <target>Подробности о текущем обслуживании/присутствии</target>
                </trans-unit>    
                <trans-unit id="attendance_running.title">
                    <source>attendance_running.title</source>
                    <target>Текущие обслуживания</target>
                </trans-unit> 
                <trans-unit id="attendance.note">
                    <source>attendance.note</source>
                    <target>Примечание</target>
                </trans-unit>    
                <trans-unit id="attendance.reward_penalty_type">
                    <source>attendance.reward_penalty_type</source>
                    <target>Вознаграждение/штраф</target>
                </trans-unit>    
                <trans-unit id="attendance.reward_penalty">
                    <source>attendance.reward_penalty</source>
                    <target>Сумма</target>
                </trans-unit> 
                <trans-unit id="attendance.type_reward">
                    <source>attendance.type_reward</source>
                    <target>Вознаграждение</target>
                </trans-unit>  
                <trans-unit id="attendance.type_penalty">
                    <source>attendance.type_penalty</source>
                    <target>Штраф</target>
                </trans-unit>   
                <trans-unit id="attendance.state">
                    <source>attendance.state</source>
                    <target>Статус присутствия</target>
                </trans-unit>                 
                <trans-unit id="attendance.state_noplanned">
                    <source>attendance.state_noplanned</source>
                    <target>Не происходит</target>
                </trans-unit>  
                <trans-unit id="attendance.state_running">
                    <source>attendance.state_running</source>
                    <target>Происходит</target>
                </trans-unit>  
                <trans-unit id="attendance.state_end">
                    <source>attendance.state_end</source>
                    <target>Отменено</target>
                </trans-unit>  
                <trans-unit id="attendance.state_noenter">
                    <source>attendance.state_noenter</source>
                    <target>Не началось</target>
                </trans-unit>  
                <trans-unit id="attendance.state_unocuupied">
                    <source>attendance.state_unocuupied</source>
                    <target>Не занято</target>
                </trans-unit>  
                <trans-unit id="attendance_running.flash_edit_success">
                    <source>attendance_running.flash_edit_success</source>
                    <target>Предмет присутствия был успешно редактирован</target>
                </trans-unit>  
                <trans-unit id="attendance_running.flash_edit_error">
                    <source>attendance_running.flash_edit_error</source>
                    <target>Редактирование предмета присутствия не удалось</target>
                </trans-unit> 
                
                <trans-unit id="attendance_end_list">
                    <source>attendance_end_list</source>
                    <target>Прекращение обслуживаний</target>
                </trans-unit>
                <trans-unit id="attendance_end.title">
                    <source>attendance_end.title</source>
                    <target>Прекращение обслуживания</target>
                </trans-unit>
                <trans-unit id="attendance_end_edit">
                    <source>attendance_end_edit</source>
                    <target>Детали присутствия</target>
                </trans-unit>
                <trans-unit id="attendance_end.now_action">
                    <source>attendance_end.now_action</source>
                    <target>Сейчас</target>
                </trans-unit>
                <trans-unit id="attendance_end.plan_action">
                    <source>attendance_end.plan_action</source>
                    <target>Согласно плану</target>
                </trans-unit>
                <trans-unit id="attendance_end.flash_success">
                    <source>attendance_end.flash_success</source>
                    <target>Прекращение обслуживания выбранного пункта успешно реализовано </target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_error">
                    <source>attendance_end.flash_error</source>
                    <target> Прекращение обслуживания выбранного пункта не удалось</target>
                </trans-unit>  
                <trans-unit id="attendance.rate">
                    <source>attendance.rate</source>
                    <target>Тариф</target>
                </trans-unit>
                <trans-unit id="attendance.subject_rate">
                    <source>attendance.subject_rate</source>
                    <target>Тариф субъекта</target>
                </trans-unit>                
                <trans-unit id="attendance.null_attendance">
                    <source>attendance.null_attendance</source>
                    <target>Обслуживание не началось</target>
                </trans-unit>
                <trans-unit id="attendance.time_correction">
                    <source>attendance.time_correction</source>
                    <target>Коррекция времени</target>
                </trans-unit>
                <trans-unit id="attendance.time_correction_help">
                    <source>attendance.time_correction_help</source>
                    <target>Ввести коррекцию времени присутствия в минутах (+/-)</target>
                </trans-unit>
                <trans-unit id="btn_update_and_approve_and_return_to_list">
                    <source>btn_update_and_approve_and_return_to_list</source>
                    <target>Сохранить и одобрить</target>
                </trans-unit> 
                <trans-unit id="attendance_length_list">
                    <source>attendance_length_list</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="set_attendance_length_filter_warning">
                    <source>set_attendance_length_filter_warning</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="attendance.length">
                    <source>attendance.length</source>
                    <target></target>
                </trans-unit>                                
            </group>    

            <group resname="attendanceNotApproved">
                <note></note>
                <trans-unit id="attendance_not_approved_list">
                    <source>attendance_not_approved_list</source>
                    <target>Объекты с существующими неодобренными присутствиями или планом</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_attendance_is_approved">
                    <source>attendance_not_approved.field_attendance_is_approved</source>
                    <target>Одобренное присутствие</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_attendance_not_approved">
                    <source>attendance_not_approved.field_attendance_not_approved</source>
                    <target>Неодобренное присутствие</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_service_is_approved">
                    <source>attendance_not_approved.field_service_is_approved</source>
                    <target>Одобренное обслуживание</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_service_not_approved">
                    <source>attendance_not_approved.field_service_not_approved</source>
                    <target>Неодобренное обслуживание</target>
                </trans-unit>                                                                
            </group>  

            <group resname="attendanceStartCheck">
                <note>Premature attendance</note>
                <trans-unit id="attendance_start_check_list">
                    <source>attendance_start_check_list</source>
                    <target>Преждевременное присутствие</target>
                </trans-unit>
                <trans-unit id="days">
                    <source>days</source>
                    <target>дни</target>
                </trans-unit>
                <trans-unit id="hours">
                    <source>hours</source>
                    <target>часы</target>
                </trans-unit>
                <trans-unit id="attendance.more_than_month">
                    <source>attendance.more_than_month</source>
                    <target>больше, чем месяц</target>
                </trans-unit>
                <trans-unit id="attendance.difference">
                    <source>attendance.difference</source>
                    <target>Различие</target>
                </trans-unit>
            </group>    
            <group resname="attendanceNoPlan">
                <note>Plan doesn't exist</note>
                <trans-unit id="attendance_no_plan_list">
                    <source>attendance_no_plan_list</source>
                    <target>План не существует</target>
                </trans-unit>
            </group>    



            <group resname="panic_button">
                <note>Panic button</note>
                <trans-unit id="panic_button">
                    <source>panic_button</source>
                    <target>Тревожная кнопка</target>
                </trans-unit>
                <trans-unit id="panic_button.form_title">
                    <source>panic_button.form_title</source>
                    <target>Отправить SMS (текстовое сообщение) в экстремальной ситуации</target>
                </trans-unit>
                <trans-unit id="panic_button.object_select">
                    <source>panic_button.object_select</source>
                    <target>Выделить объект</target>
                </trans-unit>
                <trans-unit id="panic_button.message">
                    <source>panic_button.message</source>
                    <target>SMS сообщение</target>
                </trans-unit>
                <trans-unit id="panic_button.submit">
                    <source>panic_button.submit</source>
                    <target>Отправить SMS</target>
                </trans-unit>
                <trans-unit id="panic_button.cancel">
                    <source>panic_button.cancel</source>
                    <target>Отменить</target>
                </trans-unit>
                <trans-unit id="panic_button.success_message">
                    <source>panic_button.success_message</source>
                    <target>Тревожное сообщение было успешно отправлено</target>
                </trans-unit>
                <trans-unit id="panic_button.error_message">
                    <source>panic_button.error_message</source>
                    <target>Отправка не удалась</target>
                </trans-unit>
                <trans-unit id="panic_button.sms_message">
                    <source>panic_button.sms_message</source>
                    <target>Это тревожное сообщение с ОМ 2.0 System. Объект: %object% Время: %eventTime% Сообщение: %text%</target>
                </trans-unit>
            </group>     
            <group resname="copy_period">
                <note></note>
                <trans-unit id="period_copy_list">
                    <source>period_copy_list</source>
                    <target>Создать копию периода</target>
                </trans-unit>
                <trans-unit id="copy_period.not_possible">
                    <source>copy_period.not_possible</source>
                    <target>Генерация невозможна</target>
                </trans-unit>
                <trans-unit id="copy_period.from_period">
                    <source>copy_period.from_period</source>
                    <target>Копировать из периода</target>
                </trans-unit>
                <trans-unit id="copy_period.to_period">
                    <source>copy_period.to_period</source>
                    <target>Копировать в период</target>
                </trans-unit>
                <trans-unit id="copy_period.batch_button">
                    <source>copy_period.batch_button</source>
                    <target>Генерировать копии</target>
                </trans-unit>
                <trans-unit id="period_copy.progress_title">
                    <source>period_copy.progress_title</source>
                    <target>Generation of the services is being processed</target>
                </trans-unit>
                <trans-unit id="copy_period.possible_copy_only">
                    <source>copy_period.possible_copy_only</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="period_copy_error_description">
                    <source>period_copy_error_description</source>
                    <target></target>
                </trans-unit>
            </group>                                                  

            <group resname="agencySubjects">
                <note></note>
                <trans-unit id="agency_subject.title">
                    <source>agency_subject.title</source>
                    <target>Список лиц агентства</target>
                </trans-unit>
                <trans-unit id="agency_subject_list">
                    <source>agency_subject_list</source>
                    <target> Список лиц агентства </target>
                </trans-unit>
                <trans-unit id="agency_subject_create">
                    <source>agency_subject_create</source>
                    <target>Выбрать или создать лицо</target>
                </trans-unit>    
                <trans-unit id="agency_subject_edit">
                    <source>agency_subject_edit</source>
                    <target>Редактировать лицо</target>
                </trans-unit>    
                <trans-unit id="agency_subject_show">
                    <source>agency_subject_show</source>
                    <target>Подробности о лице</target>
                </trans-unit>    
                <trans-unit id="agency_subject_delete">
                    <source>agency_subject_delete</source>
                    <target>Удалить лицо из агентства</target>
                </trans-unit>                
                <trans-unit id="agency_subject_history">
                    <source>agency_subject_history</source>
                    <target>История редактирования лиц</target>
                </trans-unit>                
                <trans-unit id="agency_subject.subject">
                    <source>agency_subject.subject</source>
                    <target>Лицо</target>
                </trans-unit>                
                <trans-unit id="agency_subject.position">
                    <source>agency_subject.position</source>
                    <target>Функция лица</target>
                </trans-unit>       
                <trans-unit id="agency_subject.go_subjects">
                    <source>agency_subject.go_subjects</source>
                    <target>Показать список лиц агентства</target>
                </trans-unit>    
                <trans-unit id="agency_subject.list_title">
                    <source>agency_subject.list_title</source>
                    <target>Список лиц агентства</target>
                </trans-unit>       
                <trans-unit id="agency_subject.link_agency_list">
                    <source>agency_subject.link_agency_list</source>
                    <target>Назад к списку агентств</target>
                </trans-unit>  
                <trans-unit id="agency_subject.subject_select_title">
                    <source>agency_subject.subject_select_title</source>
                    <target>Выбрать существующее лицо</target>
                </trans-unit>  
                <trans-unit id="agency_subject.subject_select">
                    <source>agency_subject.subject_select</source>
                    <target>Выбрать лицо</target>
                </trans-unit> 
                <trans-unit id="agency_subject.contact">
                    <source>agency_subject.contact</source>
                    <target>Лицо</target>
                </trans-unit>      
                <trans-unit id="agency_subject.flash_add_success">
                    <source>agency_subject.flash_add_success</source>
                    <target>Лицо было добавлено к агентству</target>
                </trans-unit>   
                <trans-unit id="agency_subject.flash_removed">
                    <source>agency_subject.flash_removed</source>
                    <target>Лицо было удалено из агентства</target>
                </trans-unit>   
                <trans-unit id="agency_subject.remove_action">
                    <source>agency_subject.remove_action</source>
                    <target>Удалить лицо из агентства</target>
                </trans-unit>  
                <trans-unit id="agency_subject.remove_action_confirm">
                    <source>agency_subject.remove_action_confirm</source>
                    <target>Вы действительно хотите удалить это лицо из агентства?</target>
                </trans-unit>                                   
            </group>        

            <group resname="agencyService">
                <note></note>
                <trans-unit id="agency_service.title">
                    <source>agency_service.title</source>
                    <target>Документация для поставщика</target>
                </trans-unit>
                <trans-unit id="agency_service_list">
                    <source>agency_service_list</source>
                    <target> Документация для поставщика </target>
                </trans-unit>
                <trans-unit id="agency_service.agency">
                    <source>agency_service.agency</source>
                    <target>Агентство</target>
                </trans-unit>                 
                <trans-unit id="agency_service.object">
                    <source>agency_service.object</source>
                    <target>Объект</target>
                </trans-unit>                
                <trans-unit id="agency_service.period">
                    <source>agency_service.period</source>
                    <target>Период</target>
                </trans-unit>       
                <trans-unit id="agency_service.field_service_count">
                    <source>agency_service.field_service_count</source>
                    <target>Номер обслуживания</target>
                </trans-unit>    
                <trans-unit id="agency_service.field_service_hours">
                    <source>agency_service.field_service_hours</source>
                    <target>Счет часов</target>
                </trans-unit>       
            </group>        
            <group resname="agencyServiceDetail">
                <note></note>
                <trans-unit id="agency_service_detail.title">
                    <source>agency_service_detail.title</source>
                    <target> Документация для поставщика - подробности</target>
                </trans-unit>
                <trans-unit id="agency_service_detail_list">
                    <source>agency_service_detail_list</source>
                    <target> Документация для поставщика </target>
                </trans-unit>
                <trans-unit id="agency_service_detail.subject">
                    <source>agency_service_detail.subject</source>
                    <target>Имя работника</target>
                </trans-unit>                 
                <trans-unit id="agency_service_detail.object">
                    <source>agency_service_detail.object</source>
                    <target>Объект</target>
                </trans-unit>  
                <trans-unit id="agency_service_detail.role">
                    <source>agency_service_detail.role</source>
                    <target>Позиция</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.product">
                    <source>agency_service_detail.product</source>
                    <target>Продукт</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.agency_rate">
                    <source>agency_service_detail.agency_rate</source>
                    <target>Тариф</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.startsAt">
                    <source>agency_service_detail.startsAt</source>
                    <target>Начало присутствия</target>
                </trans-unit>      
                <trans-unit id="agency_service_detail.endsAt">
                    <source>agency_service_detail.endsAt</source>
                    <target>Окончание присутствия</target>
                </trans-unit>  
                <trans-unit id="agency_service_detail.break_type">
                    <source>agency_service_detail.break_type</source>
                    <target>Оплаченный перерыв</target>
                </trans-unit>
                <trans-unit id="agency_service_detail.totaltime">
                    <source>agency_service_detail.totaltime</source>
                    <target>Полное время присутствия (исключая отпуски)</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.totaltime_holiday">
                    <source>agency_service_detail.totaltime_holiday</source>
                    <target> Полное время присутствия (отпуски)</target>
                </trans-unit>                                   
                <trans-unit id="agency_service_detail.reward_penalty">
                    <source>agency_service_detail.reward_penalty</source>
                    <target>Сумма вознаграждения/штрафа</target>
                </trans-unit>  
            </group>        
            <group resname="statistics">
                <note>Statistics</note>
                <trans-unit id="menu_statistics_pops">
                    <source>menu_statistics_pops</source>
                    <target>архив ОВУ</target>
                </trans-unit>
                <trans-unit id="menu_statistics_pops_list">
                    <source>menu_statistics_pops_list</source>
                    <target>Полный список ОВУ</target>
                </trans-unit>
                <trans-unit id="popslist_list">
                    <source>popslist_list</source>
                    <target>Статистика - POPS архив</target>
                </trans-unit>
                <trans-unit id="popslist_show">
                    <source>popslist_show</source>
                    <target>ОВУ</target>
                </trans-unit>
                <trans-unit id="pops.events">
                    <source>pops.events</source>
                    <target>ОВУ - подробности</target>
                </trans-unit>
                <trans-unit id="popslist_edit">
                    <source>popslist_edit</source>
                    <target>ОВУ - редактирование</target>
                </trans-unit>
                <trans-unit id="menu_statistics_pops_events">
                    <source>menu_statistics_pops_events</source>
                    <target>Все подробности событий ОВУ</target>
                </trans-unit>
                <trans-unit id="pops_events_list">
                    <source>pops_events_list</source>
                    <target>Статистика – события ОВУ</target>
                </trans-unit>
                <trans-unit id="pops.eventDateTime">
                    <source>pops.eventDateTime</source>
                    <target>Дата</target>
                </trans-unit>
                <trans-unit id="pops.eventName">
                    <source>pops.eventName</source>
                    <target>Название</target>
                </trans-unit>
                <trans-unit id="pops_events.title">
                    <source>pops_events.title</source>
                    <target>подробности ОВУ</target>
                </trans-unit>
                <trans-unit id="pops_events_show">
                    <source>pops_events_show</source>
                    <target>подробности ОВУ</target>
                </trans-unit>
                <trans-unit id="pops_events_edit">
                    <source>pops_events_edit</source>
                    <target>редактировать подробности ОВУ </target>
                </trans-unit>
            </group>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
            <group resname="billingDocument">
                <note>Billing materials</note>
                <trans-unit id="menu_group_billing_document">
                    <source>menu_group_billing_document</source>
                    <target>Документация для биллинга</target>
                </trans-unit>
                <trans-unit id="billing_document.title">
                    <source>billing_document.title</source>
                    <target> Документация для биллинга </target>
                </trans-unit>
                <trans-unit id="billing_document_list">
                    <source>billing_document_list</source>
                    <target> Документация для биллинга </target>
                </trans-unit>
                <trans-unit id="billing_document_subject.title">
                    <source>billing_document_subject.title</source>
                    <target> Документация для биллинга – детали присутствия</target>
                </trans-unit>                  
                <trans-unit id="billing_document_subject_list">
                    <source>billing_document_subject_list</source>
                    <target> Документация для биллинга – детали присутствия </target>
                </trans-unit>                
                <trans-unit id="billing_document_service_period">
                    <source>billing_document_service_period</source>
                    <target>План – стоимость согласно периоду</target>
                </trans-unit>
                <trans-unit id="billing_document_service_month">
                    <source>billing_document_service_month</source>
                    <target> План – стоимость согласно месяцу </target>
                </trans-unit>
                <trans-unit id="billing_document_attendance_period">
                    <source>billing_document_attendance_period</source>
                    <target>Присутствие – стоимость за период</target>
                </trans-unit>
                <trans-unit id="billing_document_attendnace_month">
                    <source>billing_document_attendance_month</source>
                    <target>Присутствие – стоимость за месяц</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_service_period">
                    <source>menu_group_billing_document_service_period</source>
                    <target>План – стоимость за период</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_service_month">
                    <source>menu_group_billing_document_service_month</source>
                    <target> План – стоимость за месяц</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_attendance_period">
                    <source>menu_group_billing_document_attendance_period</source>
                    <target> Присутствие – стоимость за период </target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_attendnace_month">
                    <source>menu_group_billing_document_attendance_month</source>
                    <target> Присутствие – стоимость за месяц </target>
                </trans-unit>                
                
                <trans-unit id="billing_document.subject">
                    <source>billing_document.subject</source>
                    <target>Название</target>
                </trans-unit>
                <trans-unit id="billing_document.reward_penalty">
                    <source>billing_document.reward_penalty</source>
                    <target>Вознаграждение/штраф</target>
                </trans-unit>  
                <trans-unit id="billing_document.holiday_hours">
                    <source>billing_document.holiday_hours</source>
                    <target></target>
                </trans-unit>                                                              
                <trans-unit id="billing_document.total">
                    <source>billing_document.total</source>
                    <target>Итого</target>
                </trans-unit>
                <trans-unit id="billing_document_annex.title">
                    <source>billing_document_annex.title</source>
                    <target>Приложение с выставленными счетами</target>
                </trans-unit> 
                <trans-unit id="billing_document_annex_list">
                    <source>billing_document_annex_list</source>
                    <target> Приложение с выставленными счетами </target>
                </trans-unit>                                  
                <trans-unit id="billing_document_annex.flash_not_open">
                    <source>billing_document_annex.flash_not_open</source>
                    <target>Документ не может быть создан. Нет данных для показа.</target>
                </trans-unit>   
                <trans-unit id="billing_document_annex.go_annex">
                    <source>billing_document_annex.go_annex</source>
                    <target>Создать документацию</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.no_generate">
                    <source>billing_document_annex.no_generate</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="billing_document_annex.product">
                    <source>billing_document_annex.product</source>
                    <target>Название продукта</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.position">
                    <source>billing_document_annex.position</source>
                    <target>Позиция</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.hours">
                    <source>billing_document_annex.hours</source>
                    <target>Часы</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.rate">
                    <source>billing_document_annex.rate</source>
                    <target>Тариф</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.amnout">
                    <source>billing_document_annex.amnout</source>
                    <target>Итого</target>
                </trans-unit>                                                                                    
            </group>
            <group resname="exceptions">
                <note>Exceptions</note>
                <trans-unit id="exception.error_occurred">
                    <source>exception.error_occurred</source>
                    <target>Произошла ошибка</target>
                </trans-unit>
                <trans-unit id="exception.error_occurred_1">
                    <source>exception.error_occurred_1</source>
                    <target>Упс! Произошла Ошибка!</target>
                </trans-unit>
                <trans-unit id="exception.restricted_access">
                    <source>exception.restricted_access</source>
                    <target>Запрещенный доступ</target>
                </trans-unit>
                <trans-unit id="exception.not_found">
                    <source>exception.not_found</source>
                    <target>Не обнаружен</target>
                </trans-unit>
                <trans-unit id="exception.server_returned">
                    <source>exception.server_returned</source>
                    <target>Сервер вернул</target>
                </trans-unit>
                <trans-unit id="exception.msg_error_1">
                    <source>exception.msg_error_1</source>
                    <target>Что-то сломалось.</target>
                </trans-unit>
                <trans-unit id="exception.msg_error_2">
                    <source>exception.msg_error_2</source>
                    <target>Пожалуйста, отправьте нам электронное письмо с информацией, что Вы делали, когда произошла эта ошибка.</target>
                </trans-unit>
                <trans-unit id="exception.msg_error_3">
                    <source>exception.msg_error_3</source>
                    <target>Мы решим это как можно скорее. Приносим извинения за неудобство.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_1">
                    <source>exception.msg_access_1</source>
                    <target>У Вас нет требуемых разрешений для доступа на эту страницу.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_2">
                    <source>exception.msg_access_2</source>
                    <target>Даже если представить, что Вы войдете, эта страница потребует специальных разрешений, которых прямо сейчас у Вас нет.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_3">
                    <source>exception.msg_access_3</source>
                    <target>Пожалуйста, попросите администратора дать Вам разрешения.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_4">
                    <source>exception.msg_access_4</source>
                    <target>Для этого действия у Вас нет требуемых разрешений.</target>
                </trans-unit>
                <trans-unit id="exception.link_back">
                    <source>exception.link_back</source>
                    <target>Назад на предыдущую страницу</target>
                </trans-unit>
                <trans-unit id="exception.link_home">
                    <source>exception.link_home</source>
                    <target>Назад на домашнюю страницу</target>
                </trans-unit>
                
            </group>
            <group resname="auto_logout">
                <note></note>
                <trans-unit id="auto_logout.title">
                    <source>auto_logout.title</source>
                    <target>15 минут отсутствия активности</target>
                </trans-unit>
                <trans-unit id="auto_logout.message">
                    <source>auto_logout.message</source>
                    <target>Ваш выход произойдет через 30 секунд!</target>
                </trans-unit>
                <trans-unit id="auto_logout.button">
                    <source>auto_logout.button</source>
                    <target>Выход не произошел</target>
                </trans-unit>
                <trans-unit id="detect_logout.title">
                    <source>detect_logout.title</source>
                    <target>Обнаруженный выход из системы</target>
                </trans-unit>
                <trans-unit id="detect_logout.message">
                    <source>detect_logout.message</source>
                    <target>Вы выходите из приложения. Сохраните или пометьте текущий сеанс и войдите в систему снова.</target>
                </trans-unit>                
            </group>   
            <group resname="control_presence">
                <note>Automatickй odhlaљovбnн pшi neиinnosti</note>
                <trans-unit id="control_presence.title">
                    <source>control_presence.title</source>
                    <target>Проверка присутствия</target>
                </trans-unit>
                <trans-unit id="control_presence.message">
                    <source>control_presence.message</source>
                    <target>Добавить два этих номера. У Вас есть 30 секунд, чтобы сделать это.</target>
                </trans-unit>
                <trans-unit id="control_presence.button">
                    <source>control_presence.button</source>
                    <target>Подтвердить</target>
                </trans-unit>
                <trans-unit id="control_presence.alert">
                    <source>control_presence.alert</source>
                    <target>Нет, Вы ошиблись. Пожалуйста, попробуйте еще раз.</target>
                </trans-unit>                
            </group>                         
            <group resname="shortcuts">
                <note>Zkratky</note>
                <trans-unit id="shortcuts">
                    <source>shortcuts</source>
                    <target>Быстрый выбор</target>
                </trans-unit>
                <trans-unit id="shortcuts.title">
                    <source>shortcuts.title</source>
                    <target>Редактировать быстрый выбор</target>
                </trans-unit>
                <trans-unit id="shortcuts.edit">
                    <source>shortcuts.edit</source>
                    <target>Редактировать установки</target>
                </trans-unit>
                <trans-unit id="shortcuts.configure">
                    <source>shortcuts.configure</source>
                    <target>Установить</target>
                </trans-unit>
                <trans-unit id="shortcuts.max_items_alert">
                    <source>shortcuts.max_items_alert</source>
                    <target>Вы уже выбрали максимальное число из 5 предметов!</target>
                </trans-unit>
            </group>
            
            <group resname="offline_pops">
                <note>Offline POPS</note>
                <trans-unit id="offline_pops.offline_dialog_title">
                    <source>offline_pops.offline_dialog_title</source>
                    <target>Режим Offline</target>
                </trans-unit>
                <trans-unit id="offline_pops.offline_dialog_message">
                    <source>offline_pops.offline_dialog_message</source>
                    <target>Извините, приложение обнаружило ошибку соединения.</target>
                </trans-unit>
                <trans-unit id="offline_pops.online_dialog_title">
                    <source>offline_pops.online_dialog_title</source>
                    <target>Режим Online</target>
                </trans-unit>
                <trans-unit id="offline_pops.online_dialog_message">
                    <source>offline_pops.online_dialog_message</source>
                    <target>Приложение обнаружило отключение от сети.</target>
                </trans-unit>
                <trans-unit id="offline_pops.new_event_created">
                    <source>offline_pops.new_event_created</source>
                    <target>Событие ОВУ было сохранено</target>
                </trans-unit>
                <trans-unit id="offline_pops.new_event_error">
                    <source>offline_pops.new_event_error</source>
                    <target>Сохранение события ОВУ было завершено с ошибкой. Пожалуйста,попробуйте снова.</target>
                </trans-unit>                                
                <trans-unit id="offline_pops.online_dialog_pops">
                    <source>offline_pops.online_dialog_pops</source>
                    <target>Сохраненные события ОВУ были обнаружены. Вам их показать?</target>
                </trans-unit>
                <trans-unit id="offline_pops.show_offline_popsevent_title">
                    <source>offline_pops.show_offline_popsevent_title</source>
                    <target>Список offline событий ОВУ</target>
                </trans-unit>  
                <trans-unit id="offline_pops.show_offline_popsevent_message">
                    <source>offline_pops.show_offline_popsevent_message</source>
                    <target>Были обнаружены следующие события ОВУ. Подтверждение "сохранить" приведет к отправке этих событий на сервер и сохранению в системе.</target>
                </trans-unit> 
                <trans-unit id="offline_pops.show_offline_popsevent_sending">
                    <source>offline_pops.show_offline_popsevent_sending</source>
                    <target>Отправка данных для обработки ...</target>
                </trans-unit>                 
                <trans-unit id="offline_pops.show_offline_popsevent_saved">
                    <source>offline_pops.show_offline_popsevent_saved</source>
                    <target>События ОВУ были сохранены в системе.</target>
                </trans-unit>     
                <trans-unit id="offline_pops.show_offline_popsevent_nodata">
                    <source>offline_pops.show_offline_popsevent_nodata</source>
                    <target>Были обнаружены некоторые offline события ОВУ.</target>
                </trans-unit>                                              
            </group>  
            
            <group resname="statistics">
                <note>Statistics</note>
                <trans-unit id="menu_statistics_statistics">
                    <source>menu_statistics_statistics</source>
                    <target>Статистика</target>
                </trans-unit>
                <trans-unit id="menu_statistics_statistics_fluctuation">
                    <source>menu_statistics_statistics_fluctuation</source>
                    <target>Колебания</target>
                </trans-unit>
                <trans-unit id="fluctuation_list">
                    <source>fluctuation_list</source>
                    <target> Колебания на объектах</target>
                </trans-unit>
                <trans-unit id="fluctuation.month">
                    <source>fluctuation.month</source>
                    <target>Месяц</target>
                </trans-unit>
                <trans-unit id="fluctuation.year">
                    <source>fluctuation.year</source>
                    <target>Год</target>
                </trans-unit>
                <trans-unit id="fluctuation.personsCount">
                    <source>fluctuation.personsCount</source>
                    <target>Лица за месяц</target>
                </trans-unit>
                <trans-unit id="fluctuation.newPersons">
                    <source>fluctuation.newPersons</source>
                    <target>Новые лица</target>
                </trans-unit>
                <trans-unit id="fluctuation.leavingPersons">
                    <source>fluctuation.leavingPersons</source>
                    <target>Уволенные лица</target>
                </trans-unit>
                <trans-unit id="fluctuation.percent">
                    <source>fluctuation.percent</source>
                    <target>Процент колебаний</target>
                </trans-unit>
                <trans-unit id="fluctuation.generate">
                    <source>fluctuation.generate</source>
                    <target>Генерировать последний месяц</target>
                </trans-unit>
                <trans-unit id="fluctuation_generate_success">
                    <source>fluctuation_generate_success</source>
                    <target>Статистика колебаний за {месяц}/{год} была успешно создана</target>
                </trans-unit>
                <trans-unit id="fluctuation_generate_error">
                    <source>fluctuation_generate_error</source>
                    <target>Произошла ошибка при создании статистики колебаний</target>
                </trans-unit>
            </group> 
            <group resname="app_settings">
                <note>Application settings</note>
                <trans-unit id="setting.title">
                    <source>setting.title</source>
                    <target>Программные установки</target>
                </trans-unit>
                <trans-unit id="setting.parameters_title">
                    <source>setting.parameters_title</source>
                    <target>Программные параметры</target>
                </trans-unit>
                <trans-unit id="setting.mail_description">
                    <source>setting.mail_description</source>
                    <target>Отправка по электронной почте установок параметров</target>
                </trans-unit>
                <trans-unit id="setting.style_description">
                    <source>setting.style_description</source>
                    <target>Выбор стиля программы. Название стиля должно совпадать с названием файла стиля.</target>
                </trans-unit>
                <trans-unit id="setting.cd_agency_description">
                    <source>setting.cd_agency_description</source>
                    <target>Выбор специального агентства. В статистике коэффициент специального агентства будет рассчитан в соответствии  с выбранным агентством.</target>
                </trans-unit>
                <trans-unit id="setting.mail_address">
                    <source>setting.mail_address</source>
                    <target>Адрес электронной почты</target>
                </trans-unit>
                <trans-unit id="setting.mail_sender">
                    <source>setting.mail_sender</source>
                    <target>Имя отправителя</target>
                </trans-unit>
                <trans-unit id="setting.mail_user_name">
                    <source>setting.mail_user_name</source>
                    <target>Имя доступа</target>
                </trans-unit>
                <trans-unit id="setting.mail_password">
                    <source>setting.mail_password</source>
                    <target>Пароль</target>
                </trans-unit>
                <trans-unit id="setting.mail_transport">
                    <source>setting.mail_transport</source>
                    <target>Тип отправления</target>
                </trans-unit>
                <trans-unit id="setting.app_company_style">
                    <source>setting.app_company_style</source>
                    <target>Стиль</target>
                </trans-unit>
                <trans-unit id="setting.cd_agency">
                    <source>setting.cd_agency</source>
                    <target>Специальное агентство</target>
                </trans-unit>
                <trans-unit id="btn_save_and_clear_cache">
                    <source>btn_save_and_clear_cache</source>
                    <target>Сохранить и обновить кэш</target>
                </trans-unit>
                <trans-unit id="setting.news_rss_title">
                    <source>setting.news_rss_title</source>
                    <target>Источник статей введения для Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="setting.news_rss_description">
                    <source>setting.news_rss_description</source>
                    <target>Статьи показываются в приложениях RSS или базы данных</target>
                </trans-unit>
                <trans-unit id="setting.rss_enable">
                    <source>setting.rss_enable</source>
                    <target>Источник статей</target>
                </trans-unit>
                <trans-unit id="setting.disable">
                    <source>setting.disable</source>
                    <target>из базы данных</target>
                </trans-unit>
                <trans-unit id="setting.enable">
                    <source>setting.enable</source>
                    <target>из RSS</target>
                </trans-unit>
                <trans-unit id="setting.news_rss_url">
                    <source>setting.news_rss_url</source>
                    <target>URL источник  RSS новостей</target>
                </trans-unit>                
                <trans-unit id="setting.module_description">
                    <source>setting.module_description</source>
                    <target>Установки модуля (сверхструктура)</target>
                </trans-unit>                  
                <trans-unit id="setting.module_visibility_menu">
                    <source>setting.module_visibility_menu</source>
                    <target>Видимость в меню</target>
                </trans-unit>   
                <trans-unit id="setting.module_visibility_menu_hide">
                    <source>setting.module_visibility_menu_hide</source>
                    <target>Скрыть неактивные модули</target>
                </trans-unit>   
                <trans-unit id="setting.module_visibility_menu_inactive">
                    <source>setting.module_visibility_menu_inactive</source>
                    <target>Показать неактивные модули</target>
                </trans-unit>         
                <trans-unit id="setting.interval_title">
                    <source>setting.interval_title</source>
                    <target>Установка интервала</target>
                </trans-unit>   
                <trans-unit id="setting.interval_description">
                    <source>setting.interval_description</source>
                    <target>Установить интервал для автоматического выхода из системы и проверки присутствия (минуты).</target>
                </trans-unit>   
                <trans-unit id="setting.interval_autologout">
                    <source>setting.interval_autologout</source>
                    <target>Автоматический выход из системы</target>
                </trans-unit>   
                <trans-unit id="setting.interval_presence">
                    <source>setting.interval_presence</source>
                    <target>Проверка присутствия</target>
                </trans-unit>  
                <trans-unit id="setting.show_pops_attachment_to_customer">
                    <source>setting.show_pops_attachment_to_customer</source>
                    <target></target>
                </trans-unit> 
                <trans-unit id="setting.show_attachment_customer_description">
                    <source>setting.show_attachment_customer_description</source>
                    <target></target>
                </trans-unit>                  
                <trans-unit id="setting.customer_pops_event_delay">
                    <source>setting.customer_pops_event_delay</source>
                    <target></target>
                </trans-unit>   
                <trans-unit id="setting.customer_pops_event_delay_description">
                    <source>setting.customer_pops_event_delay_description</source>
                    <target></target>
                </trans-unit>                                                                                            
            </group>
            <group resname="cd_profit">
                <note>Vynos CD</note>
                <trans-unit id="menu_statistics_statistics_cd">
                    <source>menu_statistics_statistics_cd</source>
                    <target>коэффициент специального агентства</target>
                </trans-unit>
                <trans-unit id="btn_calculate">
                    <source>btn_calculate</source>
                    <target>Считать</target>
                </trans-unit>
                <trans-unit id="cd_profit.title">
                    <source>cd_profit.title</source>
                    <target> коэффициент специального агентства </target>
                </trans-unit>
                <trans-unit id="cd_profit.month">
                    <source>cd_profit.month</source>
                    <target>Месяц и год</target>
                </trans-unit>
                <trans-unit id="cd_profit.objects">
                    <source>cd_profit.objects</source>
                    <target>Объекты</target>
                </trans-unit>
                <trans-unit id="cd_profit.hours">
                    <source>cd_profit.hours</source>
                    <target>Часы</target>
                </trans-unit>
                <trans-unit id="cd_profit.no_cd_id">
                    <source>cd_profit.no_cd_id</source>
                    <target>Невозможно посчитать. Специальное агентство не установлено</target>
                </trans-unit>
                <trans-unit id="billingRate">
                    <source>billingRate</source>
                    <target>Тариф</target>
                </trans-unit>
                <trans-unit id="service_time">
                    <source>service_time</source>
                    <target>Сервис</target>
                </trans-unit>
                <trans-unit id="attendance_time">
                    <source>attendance_time</source>
                    <target>Присутствие</target>
                </trans-unit>
                <trans-unit id="diference">
                    <source>diference</source>
                    <target>Разница</target>
                </trans-unit>
                <trans-unit id="amount">
                    <source>amount</source>
                    <target>Сумма</target>
                </trans-unit>
            </group>
            <group resname="news">
                <note>Zprбvy</note>
                <trans-unit id="News">
                    <source>News</source>
                    <target>Новости</target>
                </trans-unit>                
                <trans-unit id="news.title">
                    <source>news.title</source>
                    <target>Новости</target>
                </trans-unit>
                <trans-unit id="news.text">
                    <source>news.text</source>
                    <target>Текст</target>
                </trans-unit>                
                <trans-unit id="news_list">
                    <source>news_list</source>
                    <target>Новости</target>
                </trans-unit> 
                <trans-unit id="news.view_old_news">
                    <source>news.view_old_news</source>
                    <target>Показать прошлые новости</target>
                </trans-unit>                               
                <trans-unit id="news_pm.title">
                    <source>news_pm.title</source>
                    <target>новости ОМ</target>
                </trans-unit>
                <trans-unit id="news_pm_list">
                    <source>news_pm_list</source>
                    <target>новости ОМ</target>
                </trans-unit>
                <trans-unit id="news_pm_create">
                    <source>news_pm_create</source>
                    <target>Создать новости ОМ</target>
                </trans-unit>
                <trans-unit id="news_pm_edit">
                    <source>news_pm_edit</source>
                    <target>Редактировать новости ОМ</target>
                </trans-unit>
                <trans-unit id="news_pm_show">
                    <source>news_pm_show</source>
                    <target>Показать новости ОМ</target>
                </trans-unit>                
                <trans-unit id="news_opm.title">
                    <source>news_opm.title</source>
                    <target>Новости Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="news_opm_list">
                    <source>news_opm_list</source>
                    <target>Новости Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="news_opm_create">
                    <source>news_opm_create</source>
                    <target>Создать новости Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="news_opm_edit">
                    <source>news_opm_edit</source>
                    <target>Редактирвать новости Клиентского доступа</target>
                </trans-unit>
                <trans-unit id="news_opm_show">
                    <source>news_opm_show</source>
                    <target>Показать новости Клиентского доступа</target>
                </trans-unit>                 
            </group>             
            <group resname="ckeditor">
                <note>CKEditor</note>
                <trans-unit id="ckeditor.filename">
                    <source>ckeditor.filename</source>
                    <target>формировать файл</target>
                </trans-unit>
                <trans-unit id="ckeditor.mimetype">
                    <source>ckeditor.mimetype</source>
                    <target>Тип</target>
                </trans-unit> 
                <trans-unit id="ckeditor.size">
                    <source>ckeditor.size</source>
                    <target>Размер</target>
                </trans-unit>                               
                <trans-unit id="ckeditor.created">
                    <source>ckeditor.created</source>
                    <target>Вложен</target>
                </trans-unit>
                <trans-unit id="ckeditor.select_file">
                    <source>ckeditor.select_file</source>
                    <target>Выбрать файл</target>
                </trans-unit>                
                <trans-unit id="ckeditor.upload_error">
                    <source>ckeditor.upload_error</source>
                    <target>Ошибка загрузки файла</target>
                </trans-unit>
                <trans-unit id="ckeditor.file_not_image">
                    <source>ckeditor.file_not_image</source>
                    <target>Загруженный файл не является рисунком.</target>
                </trans-unit>
            </group>  
            <group resname="module">
                <note>Nadstavby</note>
                <trans-unit id="module.not_installed">
                    <source>module.not_installed</source>
                    <target>Модуль не установлен</target>
                </trans-unit>
            </group>                             
            
            <group resname="archives">
                <note>Archiv</note>
                <trans-unit id="menu_archives">
                    <source>menu_archives</source>
                    <target>Архив</target>
                </trans-unit>
                <trans-unit id="menu_archives_service">
                    <source>menu_archives_service</source>
                    <target>Архив услуг</target>
                </trans-unit>  
                <trans-unit id="menu_archives_attendance">
                    <source>menu_archives_attendance</source>
                    <target>Архив присутствий</target>
                </trans-unit>  
                <trans-unit id="menu_archives_pops">
                    <source>menu_archives_pops</source>
                    <target>Архив ОВУ</target>
                </trans-unit>    
                <trans-unit id="menu_archives_pops_parent">
                    <source>menu_archives_pops_parent</source>
                    <target>Архив ОВУ</target>
                </trans-unit>                   
                <trans-unit id="menu_archives_pops_event">
                    <source>menu_archives_pops_event</source>
                    <target>Архив событий ОВУ</target>
                </trans-unit>
                <trans-unit id="menu_archives_log">
                    <source>menu_archives_log</source>
                    <target>Протоколы</target>
                </trans-unit>                                                                                   
                <trans-unit id="menu_archives_settings">
                    <source>menu_archives_settings</source>
                    <target>Установки архивирования</target>
                </trans-unit>                                
                
                <trans-unit id="archives_attendance.title">
                    <source>archives_attendance.title</source>
                    <target>Архив присутствий</target>
                </trans-unit> 
                <trans-unit id="archives_attendance_list">
                    <source>archives_attendance_list</source>
                    <target>Архив присутствий</target>
                </trans-unit> 
                <trans-unit id="archives_attendance_show">
                    <source>archives_attendance_show</source>
                    <target>Подробности архива присутствий</target>
                </trans-unit> 
                <trans-unit id="archives_attendance_delete">
                    <source>archives_attendance_delete</source>
                    <target>Удалить присутствие из архива</target>
                </trans-unit>    
                <trans-unit id="archives_service.title">
                    <source>archives_service.title</source>
                    <target>Архив услуг</target>
                </trans-unit> 
                <trans-unit id="archives_service_list">
                    <source>archives_service_list</source>
                    <target>Архив услуг</target>
                </trans-unit> 
                <trans-unit id="archives_service_show">
                    <source>archives_service_show</source>
                    <target>Подробности архива услуг</target>
                </trans-unit> 
                <trans-unit id="archives_service_delete">
                    <source>archives_service_delete</source>
                    <target>Удалить услугу из архива</target>
                </trans-unit>     
                <trans-unit id="archives_pops.title">
                    <source>archives_pops.title</source>
                    <target>Архив ОВУ</target>
                </trans-unit> 
                <trans-unit id="archives_pops_list">
                    <source>archives_pops_list</source>
                    <target>Архив ОВУ</target>
                </trans-unit> 
                <trans-unit id="archives_pops_show">
                    <source>archives_pops_show</source>
                    <target>Подробности архива ОВУ</target>
                </trans-unit> 
                <trans-unit id="archives_pops_delete">
                    <source>archives_pops_delete</source>
                    <target>Удалить ОВУ из архива, включая его события?</target>
                </trans-unit>   
                <trans-unit id="archives_pops_event.title">
                    <source>archives_pops_event.title</source>
                    <target>Архив событий ОВУ</target>
                </trans-unit> 
                <trans-unit id="archives_pops_event_list">
                    <source>archives_pops_event_list</source>
                    <target>Архив событий ОВУ</target>
                </trans-unit> 
                <trans-unit id="archives_pops_event_show">
                    <source>archives_pops_event_show</source>
                    <target>Подробности архива событий ОВУ</target>
                </trans-unit> 
                <trans-unit id="archives_pops_event_delete">
                    <source>archives_pops_event_delete</source>
                    <target>Удалить событие ОВУ из архива</target>
                </trans-unit> 
                <trans-unit id="archives.link_action_pops">
                    <source>archives.link_action_pops</source>
                    <target>Обратно к ОВУ</target>
                </trans-unit>                  
            </group>             
            
            <group resname="archives.setting">
                <note>Nastavenн archivu</note>
                <trans-unit id="archives.setting.title">
                    <source>archives.setting.title</source>
                    <target>Установки архива</target>
                </trans-unit>
                <trans-unit id="archives.setting.description">
                    <source>archives.setting.description</source>
                    <target>Установить временной интервал для (0 = безлимитный период)</target>
                </trans-unit>                                
                <trans-unit id="archives.setting.keeping_data_in_application">
                    <source>archives.setting.keeping_data_in_application</source>
                    <target>Сохранение данных в приложении</target>
                </trans-unit>
                <trans-unit id="archives.setting.keeping_data_in_archives">
                    <source>archives.setting.keeping_data_in_archives</source>
                    <target>Сохранение данных в архиве</target>
                </trans-unit>    
                <trans-unit id="archives.days">
                    <source>archives.days</source>
                    <target>дни</target>
                </trans-unit>      
                <trans-unit id="archives.weeks">
                    <source>archives.weeks</source>
                    <target>недели</target>
                </trans-unit>   
                <trans-unit id="archives.months">
                    <source>archives.months</source>
                    <target>месяцы</target>
                </trans-unit>   
                <trans-unit id="archives.years">
                    <source>archives.years</source>
                    <target>годы</target>
                </trans-unit> 
            </group> 
            
            <group resname="archives_log">
                <note>Log archivu</note>
                <trans-unit id="archives_log_list">
                    <source>archives_log_list</source>
                    <target>Протокол</target>
                </trans-unit>
                <trans-unit id="archives_log_delete">
                    <source>archives_log_delete</source>
                    <target>Удалить запись в журнале</target>
                </trans-unit>
                <trans-unit id="archives_log.executed_at">
                    <source>archives_log.executed_at</source>
                    <target>Выполнено в</target>
                </trans-unit>
                <trans-unit id="archives_log.executed_by">
                    <source>archives_log.executed_by</source>
                    <target>Выполнил:</target>
                </trans-unit>
                <trans-unit id="archives_log.type">
                    <source>archives_log.type</source>
                    <target>Действие</target>
                </trans-unit>
                <trans-unit id="archives_log.severity">
                    <source>archives_log.severity</source>
                    <target>Статус</target>
                </trans-unit>
                <trans-unit id="archives_log.message">
                    <source>archives_log.message</source>
                    <target>Сообщение</target>
                </trans-unit>
            </group>         
            <group resname="billing_annex">
                <note></note>
                <trans-unit id="billing_annex.annex">
                    <source>billing_annex.annex</source>
                    <target>Приложение к биллингу</target>
                </trans-unit>
                <trans-unit id="billing_annex.period">
                    <source>billing_annex.period</source>
                    <target>ПЕРИОД БИЛЛИНГА</target>
                </trans-unit>
                <trans-unit id="billing_annex.annex_title">
                    <source>billing_annex.annex_title</source>
                    <target>ПРИЛОЖЕНИЕ К ВЫСТАВЛЕННОМУ СЧЕТУ</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_name">
                    <source>billing_annex.client_name</source>
                    <target>Имя клиента</target>
                </trans-unit>
                <trans-unit id="billing_annex.object_name">
                    <source>billing_annex.object_name</source>
                    <target>Название объекта</target>
                </trans-unit>
                <trans-unit id="billing_annex.annex_no">
                    <source>billing_annex.annex_no</source>
                    <target>Приложение к счету №</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_ico">
                    <source>billing_annex.client_ico</source>
                    <target>Номер ID клиента </target>
                </trans-unit>
                <trans-unit id="billing_annex.division_no">
                    <source>billing_annex.division_no</source>
                    <target>Тип ID</target>
                </trans-unit>
                <trans-unit id="billing_annex.security_manager">
                    <source>billing_annex.security_manager</source>
                    <target>Менеджер по безопасности</target>
                </trans-unit>
                <trans-unit id="billing_annex.contract_no">
                    <source>billing_annex.contract_no</source>
                    <target>ID договора</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_person">
                    <source>billing_annex.client_person</source>
                    <target>Ответственное лицо клиента</target>
                </trans-unit>
                <trans-unit id="billing_annex.contract">
                    <source>billing_annex.contract</source>
                    <target>Договор / Ордер №</target>
                </trans-unit>
                <trans-unit id="billing_annex.product_name">
                    <source>billing_annex.product_name</source>
                    <target>Название продукта</target>
                </trans-unit>
                <trans-unit id="billing_annex.billing_of_services">
                    <source>billing_annex.billing_of_services</source>
                    <target>Обслуживание согласно соглашению/ордеру</target>
                </trans-unit>
                <trans-unit id="billing_annex.responsive_person">
                    <source>billing_annex.responsive_person</source>
                    <target>Лицо, ответственное за правильность приложения (by %компания%) + подпись</target>
                </trans-unit>
                <trans-unit id="billing_annex.hours">
                    <source>billing_annex.hours</source>
                    <target>Часы</target>
                </trans-unit>
                <trans-unit id="billing_annex.rate">
                    <source>billing_annex.rate</source>
                    <target>Тариф</target>
                </trans-unit>
                <trans-unit id="billing_annex.sum">
                    <source>billing_annex.sum</source>
                    <target>Итого</target>
                </trans-unit>
                <trans-unit id="billing_annex.date">
                    <source>billing_annex.date</source>
                    <target>День</target>
                </trans-unit>
                <trans-unit id="billing_annex.telefon_abbr">
                    <source>billing_annex.telefon_abbr</source>
                    <target>Телефон</target>
                </trans-unit>
                <trans-unit id="billing_annex.ico">
                    <source>billing_annex.ico</source>
                    <target>Номер ID компании</target>
                </trans-unit>
            </group>
            <group resname="modal">
                <note></note>
                <trans-unit id="modal_admin_text.success">
                    <source>modal_admin_text.success</source>
                    <target></target>
                </trans-unit>    
                <trans-unit id="modal_admin_text.error">
                    <source>modal_admin_text.error</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="modal_admin_text.confirm_delete">
                    <source>modal_admin_text.confirm_delete</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="modal_admin_text.sending">
                    <source>modal_admin_text.sending</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="loading">
                    <source>loading</source>
                    <target></target>
                </trans-unit>    
            </group>
            <group resname="service_template">
                <note></note>
                <trans-unit id="service_template.batch_generate">
                    <source>service_template.batch_generate</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template.cant_batch_generate">
                    <source>service_template.cant_batch_generate</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template.title">
                    <source>service_template.title</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template_list">
                    <source>service_template_list</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template_edit">
                    <source>service_template_edit</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template_create">
                    <source>service_template_create</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template_delete">
                    <source>service_template_delete</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="menu_service_templates">
                    <source>menu_service_templates</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template_generated">
                    <source>service_template_generated</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template_can_not_generate_error">
                    <source>service_template_can_not_generate_error</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="service_template.cant_remove">
                    <source>service_template.cant_remove</source>
                    <target></target>
                </trans-unit>
            </group>
            <group resname="disciplinary measure">
                <note></note>
                <trans-unit id="menu_disciplinary_measure_create">
                    <source>menu_disciplinary_measure_create</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="menu_disciplinary_measure">
                    <source>menu_disciplinary_measure</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="disciplinary_measure_list">
                    <source>disciplinary_measure_list</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_CREATE">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_CREATE</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_SHOW">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_SHOW</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_DELETE">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_DELETE</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="setting.disciplinary_mail_title">
                    <source>setting.disciplinary_mail_title</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="setting.disciplinary_mail_description">
                    <source>setting.disciplinary_mail_description</source>
                    <target></target>
                </trans-unit>
                <trans-unit id="setting.disciplinary_mail_help">
                    <source>setting.disciplinary_mail_help</source>
                    <target></target>
                </trans-unit>       
                <trans-unit id="setting.disciplinary_measure_mail_recipients">
                    <source>setting.disciplinary_measure_mail_recipients</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="disciplinary_measure.title">
                    <source>disciplinary_measure.title</source>
                    <target></target>
                </trans-unit>  
                <trans-unit id="disciplinary_measure_create">
                    <source>disciplinary_measure_create</source>
                    <target></target>
                </trans-unit>                
            </group>
        </body></file>
</xliff>
