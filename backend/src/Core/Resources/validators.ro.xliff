<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" datatype="plaintext" original="file.ext">
        <body>
            <group resname="custom">
                <note>Custom validations</note>    
                <trans-unit id="shift_is_overlaped">
                    <source>service_create.shift_is_overlaped</source>
                    <target>Suprapunerea schimburilor introduse</target>
                </trans-unit>
                <trans-unit id="service_create.shift_end_before_start">
                    <source>service_create.shift_end_before_start</source>
                    <target>Schimbul incepe mai tarziu decat se termina</target>
                </trans-unit>
                <trans-unit id="service_create.start_after_end_error">
                    <source>service_create.start_after_end_error</source>
                    <target>Perioada incepe mai tarziu decat se termina</target>
                </trans-unit>
                <trans-unit id="service_create.all_times_empty">
                    <source>service_create.all_times_empty</source>
                    <target>Imposibil de generat + nu exista schimb cu timpuri completate</target>
                </trans-unit>
                <trans-unit id="service_create.missing_time">
                    <source>service_create.missing_time</source>
                    <target>Lipsesc timpurile de inceput si de sfarsit al schimbului</target>
                </trans-unit>
                <trans-unit id="service_create.not_datetime">
                    <source>service_create.not_datetime</source>
                    <target>Introducere nevalabila de data si timp</target>
                </trans-unit>
                <trans-unit id="service_create.shift_exists">
                    <source>service_create.shift_exists</source>
                    <target>Deja exista schimb pentru pozitie</target>
                </trans-unit>
                <trans-unit id="service.create.not_in_period">
                    <source>service.create.not_in_period</source>
                    <target>Inceputul datoriei este inafara perioadei</target>
                </trans-unit>
                <trans-unit id="negative_value">
                    <source>negative_value</source>
                    <target>Valoarea nu poate fi negativa</target>
                </trans-unit>  
                <trans-unit id="object.object_already_exists">
                    <source>object.object_already_exists</source>
                    <target>Obiectul cu acest cod si companie deja exista</target>
                </trans-unit> 
                <trans-unit id="date_range.min_date">
                    <source>date_range.min_date</source>
                    <target>Aceasta data trebuie sa fie mai mare decat {{ limit }}.</target>
                </trans-unit>
                <trans-unit id="date_range.max_date">
                    <source>date_range.max_date</source>
                    <target>Aceasta data trebuie sa fie mai mica decat {{ limit }}.</target>
                </trans-unit>
            </group> 
			    			
        </body>
    </file>
</xliff>
