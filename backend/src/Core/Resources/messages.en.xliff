<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
    <file datatype="plaintext" original="messages.en.xliff" source-language="en">
        <body>            
            <group resname="GLOBAL">
                <note>Obecné texty společné pro celou aplikaci</note>
                <trans-unit id="name">
                    <source>name</source>
                    <target>Name</target>
                </trans-unit>
                <trans-unit id="abbr">
                    <source>abbr</source>
                    <target>Abbreviation</target>
                </trans-unit>
                <trans-unit id="code">
                    <source>code</source>
                    <target>Code</target>
                </trans-unit>
                <trans-unit id="inserted_by">
                    <source>inserted_by</source>
                    <target>Created by</target>
                </trans-unit>
                <trans-unit id="updated_by">
                    <source>updated_by</source>
                    <target>Updated by</target>
                </trans-unit>
                <trans-unit id="inserted_at">
                    <source>inserted_at</source>
                    <target>Created</target>
                </trans-unit>
                <trans-unit id="updated_at">
                    <source>updated_at</source>
                    <target>Updated</target>
                </trans-unit>
                <trans-unit id="ico">
                    <source>ico</source>
                    <target>Business ID</target>
                </trans-unit>
                <trans-unit id="firstname">
                    <source>firstname</source>
                    <target>First name</target>
                </trans-unit>
                <trans-unit id="lastname">
                    <source>lastname</source>
                    <target>Last name</target>
                </trans-unit>
                <trans-unit id="full_name">
                    <source>full_name</source>
                    <target>Name</target>
                </trans-unit>
                <trans-unit id="is_active">
                    <source>is_active</source>
                    <target>Active</target>
                </trans-unit>
                <trans-unit id="title">
                    <source>title</source>
                    <target>Title</target>
                </trans-unit>
                <trans-unit id="level">
                    <source>level</source>
                    <target>Level</target>
                </trans-unit>
                <trans-unit id="currency">
                    <source>currency</source>
                    <target>Currency</target>
                </trans-unit>
                <trans-unit id="company">
                    <source>company</source>
                    <target>Company</target>
                </trans-unit>
                <trans-unit id="subject">
                    <source>subject</source>
                    <target>Subject</target>
                </trans-unit>
                <trans-unit id="starts_at">
                    <source>starts_at</source>
                    <target>Starts</target>
                </trans-unit>
                <trans-unit id="ends_at">
                    <source>ends_at</source>
                    <target>Ends</target>
                </trans-unit>
                <trans-unit id="object">
                    <source>object</source>
                    <target>Object</target>
                </trans-unit>
                <trans-unit id="phone">
                    <source>phone</source>
                    <target>Telephone</target>
                </trans-unit>
                <trans-unit id="mobile">
                    <source>mobile</source>
                    <target>Mobile </target>
                </trans-unit>
                <trans-unit id="email">
                    <source>email</source>
                    <target>E-mail</target>
                </trans-unit>                                    
                <trans-unit id="attachments">
                    <source>attachments</source>
                    <target>Attachments</target>
                </trans-unit>
                <trans-unit id="Start">
                    <source>Start</source>
                    <target>Start</target>
                </trans-unit>
                <trans-unit id="End">
                    <source>End</source>
                    <target>End</target>
                </trans-unit>
                <trans-unit id="is_approved">
                    <source>is_approved</source>
                    <target>Approved</target>
                </trans-unit>
                <trans-unit id="not_approved">
                    <source>not_approved</source>
                    <target>Not approved</target>
                </trans-unit>
                <trans-unit id="go_approbate">
                    <source>go_approbate</source>
                    <target>Approve</target>
                </trans-unit>
                <trans-unit id="go_withdraw_approval">
                    <source>go_withdraw_approval</source>
                    <target>Withdraw approval</target>
                </trans-unit> 
                <trans-unit id="activate">
                    <source>activate</source>
                    <target>Activate </target>
                </trans-unit>
                <trans-unit id="deactivate">
                    <source>deactivate</source>
                    <target>Deactivate </target>
                </trans-unit>
                <trans-unit id="flash_activate">
                    <source>flash_activate</source>
                    <target>Item has been activated</target>
                </trans-unit>
                <trans-unit id="flash_deactivate">
                    <source>flash_deactivate</source>
                    <target>Item has been deactivated</target>
                </trans-unit>     
                <trans-unit id="flash_batch_activate_success">
                    <source>flash_batch_activate_success</source>
                    <target>Selected items have been activated</target>
                </trans-unit>
                <trans-unit id="flash_batch_activate_error">
                    <source>flash_batch_activate_error</source>
                    <target>Selected items could not be activated</target>
                </trans-unit>                                               
                <trans-unit id="flash_batch_deactivate_success">
                    <source>flash_batch_deactivate_success</source>
                    <target>Selected items have been deactivated</target>
                </trans-unit>
                <trans-unit id="flash_batch_deactivate_error">
                    <source>flash_batch_deactivate_error</source>
                    <target>Selected items could not be deactivated</target>
                </trans-unit>
                <trans-unit id="flash_batch_deactivate_warning">
                    <source>flash_batch_deactivate_warning</source>
                    <target>The selected items have been deactivated, but some could not be deactivated because they are in use</target>
                </trans-unit>                
                <trans-unit id="select_all">
                    <source>select_all</source>
                    <target>Select all</target>
                </trans-unit>
                <trans-unit id="select_none">
                    <source>select_none</source>
                    <target>Cancel selection</target>
                </trans-unit>
                <trans-unit id="select_row">
                    <source>select_row</source>
                    <target>Select row</target>
                </trans-unit>
                <trans-unit id="select_column">
                    <source>select_column</source>
                    <target>Select column</target>
                </trans-unit>
                <trans-unit id="minutes">
                    <source>minutes</source>
                    <target>minutes</target>
                </trans-unit>
                <trans-unit id="Yes">
                    <source>Yes</source>
                    <target>Yes</target>
                </trans-unit>
                <trans-unit id="No">
                    <source>No</source>
                    <target>No</target>
                </trans-unit>  
                <trans-unit id="print">
                    <source>print</source>
                    <target>Print</target>
                </trans-unit>                        
                <trans-unit id="page">
                    <source>page</source>
                    <target>Page</target>
                </trans-unit>                                              
                <trans-unit id="ipAddress">
                    <source>ipAddress</source>
                    <target>IP Address</target>
                </trans-unit>
                <trans-unit id="loginTime">
                    <source>loginTime</source>
                    <target>Date/Time - login/logout</target>
                </trans-unit>
                <trans-unit id="log.login">
                    <source>log.login</source>
                    <target>Logged in</target>
                </trans-unit>  
                <trans-unit id="log.logout">
                    <source>log.logout</source>
                    <target>Logged out</target>
                </trans-unit> 
                <trans-unit id="log.autologout">
                    <source>log.autologout</source>
                    <target>Automatically logged out</target>
                </trans-unit>  
                <trans-unit id="password_customer">
                    <source>password_customer</source>
                    <target>OPM Password</target>
                </trans-unit>               
                <trans-unit id="password">
                    <source>password</source>
                    <target>Password</target>
                </trans-unit>
                <trans-unit id="password_confirm">
                    <source>password_confirm</source>
                    <target>Password confirmation</target>
                </trans-unit> 
                <trans-unit id="log.data">
                    <source>log.data</source>
                    <target>Data operation</target>
                </trans-unit> 
                <trans-unit id="log.error">
                    <source>log.error</source>
                    <target>Error report</target>
                </trans-unit>
                <trans-unit id="log.email">
                    <source>log.email</source>
                    <target>Email report</target>
                </trans-unit>
                <trans-unit id="log.sms">
                    <source>log.sms</source>
                    <target>SMS report</target>
                </trans-unit>
                <trans-unit id="log.panic">
                    <source>log.panic</source>
                    <target>Panic message</target>
                </trans-unit>
                <trans-unit id="log.import">
                    <source>log.import</source>
                    <target>Import</target>
                </trans-unit>
                <trans-unit id="log.export">
                    <source>log.export</source>
                    <target>Export</target>
                </trans-unit>
                <trans-unit id="log.customer_login">
                    <source>log.customer_login</source>
                    <target>Customer portal logins</target>
                </trans-unit>
                <trans-unit id="log.control_presence">
                    <source>log.control_presence</source>
                    <target>Presence check</target>
                </trans-unit>                
                <trans-unit id="severity.success">
                    <source>severity.success</source>
                    <target>Success</target>
                </trans-unit>
                <trans-unit id="severity.info">
                    <source>severity.info</source>
                    <target>Info</target>
                </trans-unit>
                <trans-unit id="severity.warning">
                    <source>severity.warning</source>
                    <target>Warning</target>
                </trans-unit>
                <trans-unit id="severity.error">
                    <source>severity.error</source>
                    <target>Error</target>
                </trans-unit>
                <trans-unit id="severity.critical">
                    <source>severity.critical</source>
                    <target>Critical situation</target>
                </trans-unit>
                <trans-unit id="action">
                    <source>action</source>
                    <target>Action</target>
                </trans-unit> 
                <trans-unit id="order">
                    <source>order</source>
                    <target>Order</target>
                </trans-unit>              
                <trans-unit id="version">
                    <source>version</source>
                    <target>Version</target>
                </trans-unit>  
                <trans-unit id="change_user_profile">
                    <source>change_user_profile</source>
                    <target>Change user profile</target>
                </trans-unit>    
                <trans-unit id="aplication_welcome.title">
                    <source>aplication_welcome.title</source>
                    <target>Welcome to application PM 2.0</target>
                </trans-unit>   
                <trans-unit id="aplication_welcome.text">
                    <source>aplication_welcome.text</source>
                    <target>Welcome to the default page of our new and better PM 2.0! Lots of work is awaiting for us so let's get started!</target>
                </trans-unit>
                <trans-unit id="select_lang">
                    <source>select_lang</source>
                    <target>Change a language</target>
                </trans-unit>  
                <trans-unit id="language">
                    <source>language</source>
                    <target>Language</target>
                </trans-unit> 
                <trans-unit id="translate">
                    <source>translate</source>
                    <target>Translate</target>
                </trans-unit>  
                <trans-unit id="control_sms_text">
                    <source>control_sms_text</source>
                    <target>Control SMS - application Operations Module - "%locale%" is runnig</target>
                </trans-unit>
                <trans-unit id="show">
                    <source>show</source>
                    <target>Show</target>
                </trans-unit>
                <trans-unit id="hide">
                    <source>hide</source>
                    <target>Hide</target>
                </trans-unit>
                <trans-unit id="description">
                    <source>description</source>
                    <target>Description</target>
                </trans-unit>
                <trans-unit id="menu_numbers">
                    <source>menu_numbers</source>
                    <target>Menu numbers</target>
                </trans-unit>
            </group>     
            <group resname="validators">
                <note>Validační texty mimo validatory</note>
                <trans-unit id="not_the_same">
                    <source>not_the_same</source>
                    <target>Values not equal</target>
                </trans-unit>
                <trans-unit id="password_info">
                    <source>password_info</source>
                    <target>Leave blank in case You do not want to change the password. Password should contain at least 8 characters, containing at least one small letter, one capital letter and one digit.</target>
                </trans-unit>    
                <trans-unit id="password_invalid">
                    <source>password_invalid</source>
                    <target>The passwords don't match</target>
                </trans-unit>
                <trans-unit id="password_minlenght">
                    <source>password_minlenght</source>
                    <target>Password should contain at least %limit% characters</target>
                </trans-unit>
                <trans-unit id="password_smallletter">
                    <source>password_smallletter</source>
                    <target>Password should contain at least one small letter</target>
                </trans-unit>
                <trans-unit id="password_capitalletter">
                    <source>password_capitalletter</source>
                    <target>Password should contain at least one capital letter</target>
                </trans-unit>  
                <trans-unit id="password_digit">
                    <source>password_digit</source>
                    <target>Password should contain at least one digit</target>
                </trans-unit>  
                <trans-unit id="all_items_filled">
                    <source>all_items_filled</source>
                    <target>All items have to be filled</target>
                </trans-unit>                  
            </group>     
            
            <group resname="buttons">  
                <note>Tlačítka</note>                           
                <trans-unit id="btn_save">
                    <source>btn_save</source>
                    <target>Save</target>
                </trans-unit>   
                <trans-unit id="btn_back">
                    <source>btn_back</source>
                    <target>Back</target>
                </trans-unit>       
                <trans-unit id="select">
                    <source>select</source>
                    <target>Select</target>
                </trans-unit>             
                <trans-unit id="button.pass_generate">
                    <source>button.pass_generate</source>
                    <target>Generate password</target>
                </trans-unit>  
                <trans-unit id="btn_apply">
                    <source>btn_apply</source>
                    <target>Use</target>
                </trans-unit>
                <trans-unit id="btn_cancel">
                    <source>btn_cancel</source>
                    <target>Cancel</target>
                </trans-unit>  
                <trans-unit id="btn_solve">
                    <source>btn_solve</source>
                    <target>Vyřešit</target>
                </trans-unit>                             
            </group>
            
            <group resname="login">
                <note>Přihlašovací stránka</note>
                <trans-unit id="login.title">
                    <source>login.title</source>
                    <target>Sign in to Operations Module 2.0 application </target>
                </trans-unit>
                <trans-unit id="login.header">
                    <source>login.header</source>
                    <target>Sign in to Operations Module 2.0 application</target>
                </trans-unit>
                <trans-unit id="login.pass">
                    <source>login.pass</source>
                    <target>Password</target>
                </trans-unit>
                <trans-unit id="login.log_in">
                    <source>login.log_in</source>
                    <target>Login</target>
                </trans-unit>
                <trans-unit id="login.log_out">
                    <source>login.log_out</source>
                    <target>Logout</target>
                </trans-unit>
                <trans-unit id="login.loged_in">
                    <source>login.loged_in</source>
                    <target>Logged in</target>
                </trans-unit>
                <trans-unit id="error.bad_credentials">
                    <source>Bad credentials</source>
                    <target>Bad credentials</target>
                </trans-unit>
                <trans-unit id="error.empty_password">
                    <source>The presented password cannot be empty.</source>
                    <target>The presented password cannot be empty.</target>
                </trans-unit>
                <trans-unit id="error.password_invalid">
                    <source>The presented password is invalid.</source>
                    <target>The presented password is invalid.</target>
                </trans-unit>
            </group>
            
            <group resname="permissions">
                <note>Uživatelská oprávnění</note>
                <trans-unit id="permission_group_create">
                    <source>permission_group_create</source>
                    <target>Create a permission group</target>
                </trans-unit>
                <trans-unit id="permission_group_edit">
                    <source>permission_group_edit</source>
                    <target>Edit a permission group</target>
                </trans-unit>
                <trans-unit id="permission_group_list">
                    <source>permission_group_list</source>
                    <target>Permission group list</target>
                </trans-unit>
                <trans-unit id="permission_group_show">
                    <source>permission_group_show</source>
                    <target>Permission group detail</target>
                </trans-unit>
                <trans-unit id="permission_group_delete">
                    <source>permission_group_delete</source>
                    <target>Permission group delete</target>
                </trans-unit>
                <trans-unit id="permission_template_create">
                    <source>permission_template_create</source>
                    <target>Permission template creation</target>
                </trans-unit>
                <trans-unit id="permission_group.title">
                    <source>permission_group.title</source>
                    <target>Permission group</target>
                </trans-unit>
                <trans-unit id="permission_group_removed_passwords">
                    <source>permission_group_removed_passwords</source>
                    <target>Passwords have been removed from these persons: </target>
                </trans-unit>
                <trans-unit id="permission_template_edit">
                    <source>permission_template_edit</source>
                    <target>Edit a permission template</target>
                </trans-unit>
                <trans-unit id="permission_template_list">
                    <source>permission_template_list</source>
                    <target>Permission template list</target>
                </trans-unit>
                <trans-unit id="permission_template_show">
                    <source>permission_template_show</source>
                    <target>Permission template detail</target>
                </trans-unit>
                <trans-unit id="permission_template.title">
                    <source>permission_template.title</source>
                    <target>Template</target>
                </trans-unit>
                <trans-unit id="permission_template.use_in_messages">
                    <source>permission_template.use_in_messages</source>
                    <target>Offer in messages</target>
                </trans-unit>
                <trans-unit id="permission_template.use_in_messages_help">
                    <source>permission_template.use_in_messages_help</source>
                    <target>Choose in case You do want to use this template for sending messages</target>
                </trans-unit>
                <trans-unit id="permission_template.copy_from">
                    <source>permission_template.copy_from</source>
                    <target>Select if you want to use the template for sending messages</target>
                </trans-unit>                
                <trans-unit id="permission.description">
                    <source>permission.description</source>
                    <target>Description</target>
                </trans-unit>
                <trans-unit id="permission.subject_type_person">
                    <source>permission.subject_type_person</source>
                    <target>Persons</target>
                </trans-unit>
                <trans-unit id="permission.subject_type_agency">
                    <source>permission.subject_type_agency</source>
                    <target>Agencies</target>
                </trans-unit>
                <trans-unit id="permission.subject_type_customer">
                    <source>permission.subject_type_customer</source>
                    <target>Customers</target>
                </trans-unit>
                <trans-unit id="permissions">
                    <source>permissions</source>
                    <target>Rights</target>
                </trans-unit>
                <trans-unit id="menu_access_objects">
                    <source>menu_access_objects</source>
                    <target>List of objects</target>
                </trans-unit>
                <trans-unit id="object_permission_list">
                    <source>object_permission_list</source>
                    <target>List of objects and permission groups</target>
                </trans-unit>
                <trans-unit id="object_permission.title">
                    <source>object_permission.title</source>
                    <target>Permission groups on an object</target>
                </trans-unit>
                <trans-unit id="person_permission_list">
                    <source>person_permission_list</source>
                    <target>List of persons and groups of permissions</target>
                </trans-unit>
                <trans-unit id="person_permission.title">
                    <source>person_permission.title</source>
                    <target>Permission groups of a person</target>
                </trans-unit>
                <trans-unit id="permission_group.with_permissions">
                    <source>permission_group.with_permissions</source>
                    <target>Permission</target>
                </trans-unit>
                <trans-unit id="permission_group.from_object">
                    <source>permission_group.from_object</source>
                    <target>Preset from object</target>
                </trans-unit>
                <trans-unit id="with_permissions">
                    <source>with_permissions</source>
                    <target>with permissions</target>
                </trans-unit>
                <trans-unit id="without_permissions">
                    <source>without_permissions</source>
                    <target>without permissions</target>
                </trans-unit>
                <trans-unit id="menu_access_persons">
                    <source>menu_access_persons</source>
                    <target>Summary of persons</target>
                </trans-unit>
                <trans-unit id="person_permission_template.title">
                    <source>person_permission_template.title</source>
                    <target>Persons list and their permissions to templates</target>
                </trans-unit>
                <trans-unit id="person_permission_template_list">
                    <source>person_permission_template_list</source>
                    <target>Persons list and their permissions to templates</target>
                </trans-unit>
                <trans-unit id="person_permission_template_edit">
                    <source>person_permission_template_edit</source>
                    <target>Edit person's permissions to templates</target>
                </trans-unit>                                
                <trans-unit id="menu_access_persons_template">
                    <source>menu_access_persons_template</source>
                    <target>Persons' permission to templates</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_COMMON">
                    <source>permission.PERM_COMMON</source>
                    <target>Common</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECTS">
                    <source>permission.PERM_OBJECTS</source>
                    <target>Object operstions</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_SECRET_SHOW">
                    <source>permission.PERM_POSITION_SECRET_SHOW</source>
                    <target>Show secret positions</target>
                </trans-unit>
                <trans-unit id="permission.PERM_IS_CONTACT">
                    <source>permission.PERM_IS_CONTACT</source>
                    <target>Show as a contact person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_SELECT">
                    <source>permission.PERM_OBJECT_GROUP_SELECT</source>
                    <target>Choose a group of objects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PANIC_RECEIVE">
                    <source>permission.PERM_PANIC_RECEIVE</source>
                    <target>Receive emergency message</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_CREATE">
                    <source>permission.PERM_OBJECT_CREATE</source>
                    <target>Create an object</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_DELETE">
                    <source>permission.PERM_OBJECT_DELETE</source>
                    <target>Remove an object</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_EDIT">
                    <source>permission.PERM_OBJECT_EDIT</source>
                    <target>Edit an object</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_EDIT_ALL">
                    <source>permission.PERM_OBJECT_EDIT_ALL</source>
                    <target>Edit an object - complete</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_ACTIVATE">
                    <source>permission.PERM_OBJECT_ACTIVATE</source>
                    <target>Activate an object</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_SHOW">
                    <source>permission.PERM_OBJECT_SHOW</source>
                    <target>Show object</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_EDIT_POPS_ACTIVITY">
                    <source>permission.PERM_OBJECT_EDIT_POPS_ACTIVITY</source>
                    <target>Edit object - SPR activity settings</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_OBJECT_EDIT_DOCUMENTATION">
                    <source>permission.PERM_OBJECT_EDIT_DOCUMENTATION</source>
                    <target>Edit object - documentation</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_OBJECT_EDIT_MODE">
                    <source>permission.PERM_OBJECT_EDIT_MODE</source>
                    <target>Edit object - mode (nuclear)</target>
                </trans-unit>                                    
                <trans-unit id="permission.PERM_POPS_OBJECT_ALERT">
                    <source>permission.PERM_POPS_OBJECT_ALERT</source>
                    <target>Check SPR activity</target>
                </trans-unit>                            
                <trans-unit id="permission.PERM_POPS_CREATE">
                    <source>permission.PERM_POPS_CREATE</source>
                    <target>Create SPR</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EDIT">
                    <source>permission.PERM_POPS_EDIT</source>
                    <target>Edit SPR</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_LOCK">
                    <source>permission.PERM_POPS_LOCK</source>
                    <target>Lock SPR</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_SHOW">
                    <source>permission.PERM_POPS_SHOW</source>
                    <target>Show SPR</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_POPS_EVENT_CREATE">
                    <source>permission.PERM_POPS_EVENT_CREATE</source>
                    <target>Create a SPR event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENT_EDIT">
                    <source>permission.PERM_POPS_EVENT_EDIT</source>
                    <target>Edit a SPR event</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_POPS_EVENT_SHOW">
                    <source>permission.PERM_POPS_EVENT_SHOW</source>
                    <target>Show SPR event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENT_TIME">
                    <source>permission.PERM_POPS_EVENT_TIME</source>
                    <target>Edit the SPR event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_CREATE">
                    <source>permission.PERM_ABSENCE_CREATE</source>
                    <target>Create an absence</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_EDIT">
                    <source>permission.PERM_ABSENCE_EDIT</source>
                    <target>Edit an absence</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_DELETE">
                    <source>permission.PERM_ABSENCE_DELETE</source>
                    <target>Delete an absence</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_SHOW">
                    <source>permission.PERM_ABSENCE_SHOW</source>
                    <target>Show an absence</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_ABSENCE_TYPE_CREATE">
                    <source>permission.PERM_ABSENCE_TYPE_CREATE</source>
                    <target>Create an absence type</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_TYPE_EDIT">
                    <source>permission.PERM_ABSENCE_TYPE_EDIT</source>
                    <target>Edit an absence type</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_TYPE_DELETE">
                    <source>permission.PERM_ABSENCE_TYPE_DELETE</source>
                    <target>Delete an absence type</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ABSENCE_TYPE_SHOW">
                    <source>permission.PERM_ABSENCE_TYPE_SHOW</source>
                    <target>Show an absence type</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_CREATE">
                    <source>permission.PERM_AGENC_RATE_CREATE</source>
                    <target>Create an agency rate</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_EDIT">
                    <source>permission.PERM_AGENC_RATE_EDIT</source>
                    <target>Edit an agency rate</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_DELETE">
                    <source>permission.PERM_AGENC_RATE_DELETE</source>
                    <target>Delete an agency rate</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENC_RATE_SHOW">
                    <source>permission.PERM_AGENC_RATE_SHOW</source>
                    <target>Show an agency rate</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_CREATE">
                    <source>permission.PERM_AGENCY_CREATE</source>
                    <target>Create an agency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_EDIT">
                    <source>permission.PERM_AGENCY_EDIT</source>
                    <target>Edit an agency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_DELETE">
                    <source>permission.PERM_AGENCY_DELETE</source>
                    <target>Delete an agency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SHOW">
                    <source>permission.PERM_AGENCY_SHOW</source>
                    <target>Show an agency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SERVICE_SHOW">
                    <source>permission.PERM_AGENCY_SERVICE_SHOW</source>
                    <target>Show materials for a supplier </target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SERVICE_DETAIL_SHOW">
                    <source>permission.PERM_AGENCY_SERVICE_DETAIL_SHOW</source>
                    <target>Show a material detail for a supplier</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_CREATE">
                    <source>permission.PERM_AGENCY_SUBJECT_CREATE</source>
                    <target>Create an agency person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_EDIT">
                    <source>permission.PERM_AGENCY_SUBJECT_EDIT</source>
                    <target>Edit an agency person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_DELETE">
                    <source>permission.PERM_AGENCY_SUBJECT_DELETE</source>
                    <target>Delete an agency person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_AGENCY_SUBJECT_SHOW">
                    <source>permission.PERM_AGENCY_SUBJECT_SHOW</source>
                    <target>Show an agency person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_END_CREATE">
                    <source>permission.PERM_ATTENDANCE_END_CREATE</source>
                    <target>Create a service termination</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_END_EDIT">
                    <source>permission.PERM_ATTENDANCE_END_EDIT</source>
                    <target>Edit a service termination</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_END_SHOW">
                    <source>permission.PERM_ATTENDANCE_END_SHOW</source>
                    <target>Show a service termination</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_NO_PLAN_SHOW">
                    <source>permission.PERM_ATTENDANCE_NO_PLAN_SHOW</source>
                    <target>Show a list of "Non/existing plan"</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_NOT_APPROVED_EXPORT">
                    <source>permission.PERM_ATTENDANCE_NOT_APPROVED_EXPORT</source>
                    <target>Export non-approved attendance and plans</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_NOT_APPROVED_SHOW">
                    <source>permission.PERM_ATTENDANCE_NOT_APPROVED_SHOW</source>
                    <target>Show non-approved attendancies and plans</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_RUNNING_EDIT">
                    <source>permission.PERM_ATTENDANCE_RUNNING_EDIT</source>
                    <target>Edit current service</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_RUNNING_SHOW">
                    <source>permission.PERM_ATTENDANCE_RUNNING_SHOW</source>
                    <target>Show current service</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_CREATE">
                    <source>permission.PERM_ATTENDANCE_START_CREATE</source>
                    <target>Create a service beginning</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_EDIT">
                    <source>permission.PERM_ATTENDANCE_START_EDIT</source>
                    <target>Edit a service beginning</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_DELETE">
                    <source>permission.PERM_ATTENDANCE_START_DELETE</source>
                    <target>Delete a service beginning</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_CREATE_SERVICE">
                    <source>permission.PERM_ATTENDANCE_START_CREATE_SERVICE</source>
                    <target>Set "haven't started" to a service</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_START_SHOW">
                    <source>permission.PERM_ATTENDANCE_START_SHOW</source>
                    <target>Show a service beginning</target>
                </trans-unit>
                <trans-unit id="permission.ATTENDANCE_START_CHECK_SHOW">
                    <source>permission.PERM_ATTENDANCE_START_CHECK_SHOW</source>
                    <target>Show a premature attendance</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_SHOW">
                    <source>permission.PERM_ATTENDANCE_SHOW</source>
                    <target>Show an attendance summary</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_EDIT">
                    <source>permission.PERM_ATTENDANCE_EDIT</source>
                    <target>Edit an attendance summary</target>
                </trans-unit>

                <trans-unit id="permission.PERM_BILLING_DOCUMENT_ATTENDANCE_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_ATTENDANCE_SHOW</source>
                    <target>Show billing materials - Attendance</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_SERVICE_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_SERVICE_SHOW</source>
                    <target>Show billing materials - Plan</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_ANNEX_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_ANNEX_SHOW</source>
                    <target>Show billing materials - Generate materials</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_ATTENDANCE_SUBJECT_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_ATTENDANCE_SUBJECT_SHOW</source>
                    <target>Show billing materials - Attendance according to person</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_SUBJECT_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_SUBJECT_SHOW</source>
                    <target>Show billing materials - Person's attendance detail</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_EXPORT">
                    <source>permission.PERM_BILLING_DOCUMENT_EXPORT</source>
                    <target>Billing materials - export of costs</target>
                </trans-unit>
                
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_GROUP_ATTENDANCE_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_GROUP_ATTENDANCE_SHOW</source>
                    <target>Show group billing materials - Attendance</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_GROUP_SERVICE_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_GROUP_SERVICE_SHOW</source>
                    <target>Show group billing materials - Plan</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_ANNEX_GROUP_SHOW">
                    <source>permission.PERM_BILLING_DOCUMENT_ANNEX_GROUP_SHOW</source>
                    <target>Show group billing materials - Generate materials</target>
                </trans-unit>
                <trans-unit id="permission.PERM_BILLING_DOCUMENT_GROUP_EXPORT">
                    <source>permission.PERM_BILLING_DOCUMENT_GROUP_EXPORT</source>
                    <target>Group billing materials - Export costs</target>
                </trans-unit>                
                                                
                <trans-unit id="permission.PERM_CALENDAR_ADMIN">
                    <source>permission.PERM_CALENDAR_ADMIN</source>
                    <target>Show holidays' callender</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CALENDAR_ADMIN_EDIT">
                    <source>permission.PERM_CALENDAR_ADMIN_EDIT</source>
                    <target>Edit holidays' callender</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_CREATE">
                    <source>permission.PERM_CITY_CREATE</source>
                    <target>Create a city</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_EDIT">
                    <source>permission.PERM_CITY_EDIT</source>
                    <target>Edit a city</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_DELETE">
                    <source>permission.PERM_CITY_DELETE</source>
                    <target>Delete a city</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CITY_SHOW">
                    <source>permission.PERM_CITY_SHOW</source>
                    <target>Show a city</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_CREATE">
                    <source>permission.PERM_COMPANY_CREATE</source>
                    <target>Create a company</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_EDIT">
                    <source>permission.PERM_COMPANY_EDIT</source>
                    <target>Edit a company</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_DELETE">
                    <source>permission.PERM_COMPANY_DELETE</source>
                    <target>Delete a company</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COMPANY_SHOW">
                    <source>permission.PERM_COMPANY_SHOW</source>
                    <target>Show a company</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_CREATE">
                    <source>permission.PERM_CONTRACT_CREATE</source>
                    <target>Create a contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_EDIT">
                    <source>permission.PERM_CONTRACT_EDIT</source>
                    <target>Edit a contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_DELETE">
                    <source>permission.PERM_CONTRACT_DELETE</source>
                    <target>Delete a contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_SHOW">
                    <source>permission.PERM_CONTRACT_SHOW</source>
                    <target>Show a contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_CREATE">
                    <source>permission.PERM_COUNTRY_CREATE</source>
                    <target>Create a country</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_EDIT">
                    <source>permission.PERM_COUNTRY_EDIT</source>
                    <target>Edit a country</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_DELETE">
                    <source>permission.PERM_COUNTRY_DELETE</source>
                    <target>Delete a country</target>
                </trans-unit>
                <trans-unit id="permission.PERM_COUNTRY_SHOW">
                    <source>permission.PERM_COUNTRY_SHOW</source>
                    <target>Show a country</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_CREATE">
                    <source>permission.PERM_CURRENCY_CREATE</source>
                    <target>Create a currency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_EDIT">
                    <source>permission.PERM_CURRENCY_EDIT</source>
                    <target>Edit a currency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_DELETE">
                    <source>permission.PERM_CURRENCY_DELETE</source>
                    <target>Delete a currency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CURRENCY_SHOW">
                    <source>permission.PERM_CURRENCY_SHOW</source>
                    <target>Show a currency</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_CREATE">
                    <source>permission.PERM_CUSTOMERS_CREATE</source>
                    <target>Create a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_EDIT">
                    <source>permission.PERM_CUSTOMERS_EDIT</source>
                    <target>Edit a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_DELETE">
                    <source>permission.PERM_CUSTOMERS_DELETE</source>
                    <target>Delete a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_SHOW">
                    <source>permission.PERM_CUSTOMERS_SHOW</source>
                    <target>Show a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMERS_EDIT_PASSWORD">
                    <source>permission.PERM_CUSTOMERS_EDIT_PASSWORD</source>
                    <target>Edit customer password</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_CREATE">
                    <source>permission.PERM_CUSTOMER_CONTACT_CREATE</source>
                    <target>Add a contact person of a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_EDIT">
                    <source>permission.PERM_CUSTOMER_CONTACT_EDIT</source>
                    <target>Edit a contact person of a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_DELETE">
                    <source>permission.PERM_CUSTOMER_CONTACT_DELETE</source>
                    <target>Kill a contact person of a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_CONTACT_SHOW">
                    <source>permission.PERM_CUSTOMER_CONTACT_SHOW</source>
                    <target>Show a contact person of a customer</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_CREATE">
                    <source>permission.PERM_DIMENSION_CREATE</source>
                    <target>Create a dimension</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_EDIT">
                    <source>permission.PERM_DIMENSION_EDIT</source>
                    <target>Edit a dimension</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_DELETE">
                    <source>permission.PERM_DIMENSION_DELETE</source>
                    <target>Delete a dimension</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_SHOW">
                    <source>permission.PERM_DIMENSION_SHOW</source>
                    <target>Show a dimension</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_CREATE">
                    <source>permission.PERM_DIMENSION_LEVEL_CREATE</source>
                    <target>Create a dimension level</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_EDIT">
                    <source>permission.PERM_DIMENSION_LEVEL_EDIT</source>
                    <target>Edit a dimension level</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_DELETE">
                    <source>permission.PERM_DIMENSION_LEVEL_DELETE</source>
                    <target>Delete a dimension level</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DIMENSION_LEVEL_SHOW">
                    <source>permission.PERM_DIMENSION_LEVEL_SHOW</source>
                    <target>Show a dimension level</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_CREATE">
                    <source>permission.PERM_EVENT_CREATE</source>
                    <target>Create an event of an event tree</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_EDIT">
                    <source>permission.PERM_EVENT_EDIT</source>
                    <target>Edit an event of an event tree</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_ACTIVATE">
                    <source>permission.PERM_EVENT_ACTIVATE</source>
                    <target>Activate an event of an event tree</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_HIDE">
                    <source>permission.PERM_EVENT_HIDE</source>
                    <target>Hide an event in the tree of events</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_EVENT_DELETE">
                    <source>permission.PERM_EVENT_DELETE</source>
                    <target>Delete an event of an event tree</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENT_SHOW">
                    <source>permission.PERM_EVENT_SHOW</source>
                    <target>Show an event of an event tree</target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_CREATE">
                    <source>permission.PERM_INDIVIDUAL_RATE_CREATE</source>
                    <target>Create an individual tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_EDIT">
                    <source>permission.PERM_INDIVIDUAL_RATE_EDIT</source>
                    <target>Edit an individual tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_DELETE">
                    <source>permission.PERM_INDIVIDUAL_RATE_DELETE</source>
                    <target>Delete an individual tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_INDIVIDUAL_RATE_SHOW">
                    <source>permission.PERM_INDIVIDUAL_RATE_SHOW</source>
                    <target>Show an individual tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOG">
                    <source>permission.PERM_LOG</source>
                    <target>Log access</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGIN_LOG_SHOW">
                    <source>permission.PERM_LOGIN_LOG_SHOW</source>
                    <target>Show login/logout log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGIN_LOG_EXPORT">
                    <source>permission.PERM_LOGIN_LOG_EXPORT</source>
                    <target>Export login/logout log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_DATA_SHOW">
                    <source>permission.PERM_LOGS_DATA_SHOW</source>
                    <target>Show Data operations' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_DATA_EXPORT">
                    <source>permission.PERM_LOGS_DATA_EXPORT</source>
                    <target>Export Data operations' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_EMERGENCY_SHOW">
                    <source>permission.PERM_LOGS_EMERGENCY_SHOW</source>
                    <target>Show Emergency messages' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_EMERGENCY_EXPORT">
                    <source>permission.PERM_LOGS_EMERGENCY_EXPORT</source>
                    <target>Export Emergency messages' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_REPORTING_SHOW">
                    <source>permission.PERM_LOGS_REPORTING_SHOW</source>
                    <target>Show Reportings' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_REPORTING_EXPORT">
                    <source>permission.PERM_LOGS_REPORTING_EXPORT</source>
                    <target>Export Reportings' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ERRORS_SHOW">
                    <source>permission.PERM_LOGS_ERRORS_SHOW</source>
                    <target>Show Detected errors' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ERRORS_EXPORT">
                    <source>permission.PERM_LOGS_ERRORS_EXPORT</source>
                    <target>Export Detected errors' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ALL LOG">
                    <source>permission.PERM_LOGS_ALL_SHOW</source>
                    <target>Show All logs' log</target>
                </trans-unit>
                <trans-unit id="permission.PERM_LOGS_ALL_DELETE">
                    <source>permission.PERM_LOGS_ALL_DELETE</source>
                    <target>Erase All logs</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_PM_CREATE">
                    <source>permission.PERM_NEWS_PM_CREATE</source>
                    <target>Create a PM message</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_PM_SHOW">
                    <source>permission.PERM_NEWS_PM_SHOW</source>
                    <target>Show a PM message (administration)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_OPM_CREATE">
                    <source>permission.PERM_NEWS_OPM_CREATE</source>
                    <target>Create an OPM message</target>
                </trans-unit>
                <trans-unit id="permission.PERM_NEWS_OPM_SHOW">
                    <source>permission.PERM_NEWS_OPM_SHOW</source>
                    <target>Show an OPM message (administration)</target>
                </trans-unit>                                                                
                <trans-unit id="permission.PERM_LOGS_ALL_EXPORT">
                    <source>permission.PERM_LOGS_ALL_EXPORT</source>
                    <target>Export All logs</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_CREATE">
                    <source>permission.PERM_OBJECT_GROUP_CREATE</source>
                    <target>Create group of objects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_EDIT">
                    <source>permission.PERM_OBJECT_GROUP_EDIT</source>
                    <target>Edit group of objects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_DELETE">
                    <source>permission.PERM_OBJECT_GROUP_DELETE</source>
                    <target>Delete group of objects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_GROUP_SHOW">
                    <source>permission.PERM_OBJECT_GROUP_SHOW</source>
                    <target>Show group of objects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PANIC_BUTTON">
                    <source>permission.PERM_PANIC_BUTTON</source>
                    <target>Push Panic button</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_CREATE">
                    <source>permission.PERM_PERIOD_CREATE</source>
                    <target>Create a period</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_EDIT">
                    <source>permission.PERM_PERIOD_EDIT</source>
                    <target>Edit a period</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_ACTIVATE">
                    <source>permission.PERM_PERIOD_ACTIVATE</source>
                    <target>Activate/deactivate a period</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_DELETE">
                    <source>permission.PERM_PERIOD_DELETE</source>
                    <target>Delete a period</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_SHOW">
                    <source>permission.PERM_PERIOD_SHOW</source>
                    <target>Show a period</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_COPY_SHOW">
                    <source>permission.PERM_PERIOD_COPY_SHOW</source>
                    <target>Show Copy of the month</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_COPY_CREATE">
                    <source>permission.PERM_PERIOD_COPY_CREATE</source>
                    <target>Create a copy of the month</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIOD_COPY_EXPORT">
                    <source>permission.PERM_PERIOD_COPY_EXPORT</source>
                    <target>Export the copy of a month</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_CREATE">
                    <source>permission.PERM_PERIODIC_PAYMENT_CREATE</source>
                    <target>Create periodical payment</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_EDIT">
                    <source>permission.PERM_PERIODIC_PAYMENT_EDIT</source>
                    <target>Edit periodical payment</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_APPROBATE">
                    <source>permission.PERM_PERIODIC_PAYMENT_APPROBATE</source>
                    <target>Aapprove/disapprove periodical payment</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_DELETE">
                    <source>permission.PERM_PERIODIC_PAYMENT_DELETE</source>
                    <target>Delete periodical payment</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_SHOW">
                    <source>permission.PERM_PERIODIC_PAYMENT_SHOW</source>
                    <target>Show periodical payment</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERIODIC_PAYMENT_UNBLOCK">
                    <source>permission.PERM_PERIODIC_PAYMENT_UNBLOCK</source>
                    <target>Unblock periodic payments (after the export)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_CREATE">
                    <source>permission.PERM_PERMISSION_GROUP_CREATE</source>
                    <target>Create a permission group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_DELETE">
                    <source>permission.PERM_PERMISSION_GROUP_DELETE</source>
                    <target>Delete a permission group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_EDIT">
                    <source>permission.PERM_PERMISSION_GROUP_EDIT</source>
                    <target>Edit a permission group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_SHOW">
                    <source>permission.PERM_PERMISSION_GROUP_SHOW</source>
                    <target>Show a permission group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_GROUP_ALLSUBJECT">
                    <source>permission.PERM_PERMISSION_GROUP_ALLSUBJECT</source>
                    <target>Permission group - Allow choice of all subjects</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_CREATE">
                    <source>permission.PERM_PERMISSION_TEMPLATE_CREATE</source>
                    <target>Create a permission template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_DELETE">
                    <source>permission.PERM_PERMISSION_TEMPLATE_DELETE</source>
                    <target>Delete a permission template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_EDIT">
                    <source>permission.PERM_PERMISSION_TEMPLATE_EDIT</source>
                    <target>Edit a permission template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_TEMPLATE_SHOW">
                    <source>permission.PERM_PERMISSION_TEMPLATE_SHOW</source>
                    <target>Show a permission template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_SUBJECT">
                    <source>permission.PERM_PERMISSION_SUBJECT</source>
                    <target>Authorization group - Overview of persons</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERMISSION_OBJECT">
                    <source>permission.PERM_PERMISSION_OBJECT</source>
                    <target>Authorization group - Overview of objects</target>
                </trans-unit>                                
                <trans-unit id="permission.PERM_PERSON_CREATE">
                    <source>permission.PERM_PERSON_CREATE</source>
                    <target>Create a person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_EDIT">
                    <source>permission.PERM_PERSON_EDIT</source>
                    <target>Edit a person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_DELETE">
                    <source>permission.PERM_PERSON_DELETE</source>
                    <target>Kill a person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_ADDRESS_SHOW">
                    <source>permission.PERM_PERSON_ADDRESS_SHOW</source>
                    <target>Work on the address of a person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSON_PRIVATE_SHOW">
                    <source>permission.PERM_PERSON_PRIVATE_SHOW</source>
                    <target>Work with expanded data (personal number, personal data, fax)</target>
                </trans-unit>                 
                <trans-unit id="permission.PERM_PERSON_CUSTOMER_SHOW">
                    <source>permission.PERM_PERSON_CUSTOMER_SHOW</source>
                    <target>Work with contact data (photo, function, contact e-mail, CP index)</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_PERSON_BONUS_SHOW">
                    <source>permission.PERM_PERSON_BONUS_SHOW</source>
                    <target>Work with employee's bonus</target>
                </trans-unit>                                
                <trans-unit id="permission.PERM_PERSON_SHOW">
                    <source>permission.PERM_PERSON_SHOW</source>
                    <target>Show a person</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_PASSWORD">
                    <source>permission.PERM_SUBJECT_ACCESS_PASSWORD</source>
                    <target>Make PM accesible/ban PM access</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_PASSWORD_CUSTOMER">
                    <source>permission.PERM_SUBJECT_ACCESS_PASSWORD_CUSTOMER</source>
                    <target>Make Client access available / Ban Client access</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_CREATE">
                    <source>permission.PERM_PERSONS_RATE_CREATE</source>
                    <target>Create personal tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_EDIT">
                    <source>permission.PERM_PERSONS_RATE_EDIT</source>
                    <target>Edit personal tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_DELETE">
                    <source>permission.PERM_PERSONS_RATE_DELETE</source>
                    <target>Delete personal tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PERSONS_RATE_SHOW">
                    <source>permission.PERM_PERSONS_RATE_SHOW</source>
                    <target>Show personal tariff</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_CONTRACT_RATE_CREATE">
                    <source>permission.PERM_CONTRACT_RATE_CREATE</source>
                    <target>Create a rate according to the contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_RATE_EDIT">
                    <source>permission.PERM_CONTRACT_RATE_EDIT</source>
                    <target>Edit the rate according to the contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_RATE_DELETE">
                    <source>permission.PERM_CONTRACT_RATE_DELETE</source>
                    <target>Remove the rate according to the contract</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CONTRACT_RATE_SHOW">
                    <source>permission.PERM_CONTRACT_RATE_SHOW</source>
                    <target>Show the rate according to the contract</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_POPSLIST_EDIT">
                    <source>permission.PERM_POPSLIST_EDIT</source>
                    <target>Edit SPR (archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPSLIST_SHOW">
                    <source>permission.PERM_POPSLIST_SHOW</source>
                    <target>Show SPR (archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPSLIST_EXPORT">
                    <source>permission.PERM_POPSLIST_EXPORT</source>
                    <target>Export SPR (archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_EDIT">
                    <source>permission.PERM_POPS_EVENTS_EDIT</source>
                    <target> Edit SPR event(archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_TRANSLATE">
                    <source>permission.PERM_POPS_EVENTS_TRANSLATE</source>
                    <target>Translate a SPR event (archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_FOR_CUSTOMER">
                    <source>permission.PERM_POPS_EVENTS_FOR_CUSTOMER</source>
                    <target>Show/hide for a customer (archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_DELETE">
                    <source>permission.PERM_POPS_EVENTS_DELETE</source>
                    <target>Delete SPR event(archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_SHOW">
                    <source>permission.PERM_POPS_EVENTS_SHOW</source>
                    <target>Show SPR event(archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_EVENTS_EXPORT">
                    <source>permission.PERM_POPS_EVENTS_EXPORT</source>
                    <target>Export SPR events (archive)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_TOP_20">
                    <source>permission.PERM_POPS_TOP_20</source>
                    <target>Edit Top 20 SPR events</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POPS_SET_SEND_ATTACHMENT">
                    <source>permission.PERM_POPS_SET_SEND_ATTACHMENT</source>
                    <target>Can set send attachment</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_STATS_EDIT">
                    <source>permission.PERM_MULTIPOPSLIST_STATS_EDIT</source>
                    <target>Edit MultiPOPS (statistics)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_STATS_EXPORT">
                    <source>permission.PERM_MULTIPOPSLIST_STATS_EXPORT</source>
                    <target>Export MultiPOPS (statistiky)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_STATS_SHOW">
                    <source>permission.PERM_MULTIPOPSLIST_STATS_SHOW</source>
                    <target>Show MultiPOPS (statistiky)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_EDIT">
                    <source>permission.PERM_OBJECT_POPS_EDIT</source>
                    <target>Edit SPR (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_DELETE">
                    <source>permission.PERM_OBJECT_POPS_DELETE</source>
                    <target>Delete SPR (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_EXPORT">
                    <source>permission.PERM_OBJECT_POPS_EXPORT</source>
                    <target>Export SPR list (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_POPS_SHOW">
                    <source>permission.PERM_OBJECT_POPS_SHOW</source>
                    <target>Show SPR (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_EDIT">
                    <source>permission.PERM_EVENTS_OBJECT_EDIT</source>
                    <target>Edit SPR event (on an object)</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_EVENTS_OBJECT_TRANSLATE">
                    <source>permission.PERM_EVENTS_OBJECT_TRANSLATE</source>
                    <target>Translate a SPR event (for an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_FOR_CUSTOMER">
                    <source>permission.PERM_EVENTS_OBJECT_FOR_CUSTOMER</source>
                    <target>Show/hide for a customer (for an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_DELETE">
                    <source>permission.PERM_EVENTS_OBJECT_DELETE</source>
                    <target>Delete SPR event (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_EXPORT">
                    <source>permission.PERM_EVENTS_OBJECT_EXPORT</source>
                    <target>Export SPR event list (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_EVENTS_OBJECT_SHOW">
                    <source>permission.PERM_EVENTS_OBJECT_SHOW</source>
                    <target>Show SPR event (on an object)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_EDIT">
                    <source>permission.PERM_MULTIPOPSLIST_EDIT</source>
                    <target>Edit MultiPOPS (group of objects)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_EXPORT">
                    <source>permission.PERM_MULTIPOPSLIST_EXPORT</source>
                    <target>Export MultiPOPS (group of objects)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_SHOW">
                    <source>permission.PERM_MULTIPOPSLIST_SHOW</source>
                    <target>Show MultiPOPS (group of objects)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_CREATE">
                    <source>permission.PERM_POSITION_RATE_CREATE</source>
                    <target>Create a position tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_EDIT">
                    <source>permission.PERM_POSITION_RATE_EDIT</source>
                    <target>Edit a position tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_DELETE">
                    <source>permission.PERM_POSITION_RATE_DELETE</source>
                    <target>Delete a position tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_POSITION_RATE_SHOW">
                    <source>permission.PERM_POSITION_RATE_SHOW</source>
                    <target>Show a position tariff</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_CREATE">
                    <source>permission.PERM_PRODUCT_CREATE</source>
                    <target>Create a product</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_EDIT">
                    <source>permission.PERM_PRODUCT_EDIT</source>
                    <target>Edit a product</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_ACTIVATE">
                    <source>permission.PERM_PRODUCT_ACTIVATE</source>
                    <target>Activate/deactivate a product</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_DELETE">
                    <source>permission.PERM_PRODUCT_DELETE</source>
                    <target>Delete a product</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PRODUCT_SHOW">
                    <source>permission.PERM_PRODUCT_SHOW</source>
                    <target>Show a product</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_CREATE">
                    <source>permission.PERM_REPORTING_CREATE</source>
                    <target>Create a reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_EDIT">
                    <source>permission.PERM_REPORTING_EDIT</source>
                    <target>Edit a reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_DELETE">
                    <source>permission.PERM_REPORTING_DELETE</source>
                    <target>Delete a reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_SHOW">
                    <source>permission.PERM_REPORTING_SHOW</source>
                    <target>Show a reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_CREATE">
                    <source>permission.PERM_REPORTING_TEMPLATE_CREATE</source>
                    <target>Create a template for reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_EDIT">
                    <source>permission.PERM_REPORTING_TEMPLATE_EDIT</source>
                    <target>Edit a template for reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_DELETE">
                    <source>permission.PERM_REPORTING_TEMPLATE_DELETE</source>
                    <target>Delete a template for reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_REPORTING_TEMPLATE_SHOW">
                    <source>permission.PERM_REPORTING_TEMPLATE_SHOW</source>
                    <target>Show a template for reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_CREATE">
                    <source>permission.PERM_ROLE_CREATE</source>
                    <target>Create a position</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_EDIT">
                    <source>permission.PERM_ROLE_EDIT</source>
                    <target>Edit a position</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_EDIT_DEPARTMENT">
                    <source>permission.PERM_ROLE_EDIT_DEPARTMENT</source>
                    <target>Possibility to change the unit at the position</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_ROLE_SHOW_DEPARTMENT">
                    <source>permission.PERM_ROLE_SHOW_DEPARTMENT</source>
                    <target>Possibility to see the unit at the position</target>
                </trans-unit>                               
                <trans-unit id="permission.PERM_ROLE_EDIT_MANAGERIAL_ALLOWANCE">
                    <source>permission.PERM_ROLE_EDIT_MANAGERIAL_ALLOWANCE</source>
                    <target>Possibility to change the surcharge for management</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_ROLE_SHOW_MANAGERIAL_ALLOWANCE">
                    <source>permission.PERM_ROLE_SHOW_MANAGERIAL_ALLOWANCE</source>
                    <target>Possibility to see surcharge for management</target>
                </trans-unit>                   
                <trans-unit id="permission.PERM_ROLE_ACTIVATE">
                    <source>permission.PERM_ROLE_ACTIVATE</source>
                    <target>Activate/deactivate a position</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_DELETE">
                    <source>permission.PERM_ROLE_DELETE</source>
                    <target>Delete a position</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_SHOW">
                    <source>permission.PERM_ROLE_SHOW</source>
                    <target>Show a position</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_CREATE">
                    <source>permission.PERM_ROLE_GROUP_CREATE</source>
                    <target>Create position group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_EDIT">
                    <source>permission.PERM_ROLE_GROUP_EDIT</source>
                    <target>Edit position group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_DELETE">
                    <source>permission.PERM_ROLE_GROUP_DELETE</source>
                    <target>Delete position group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ROLE_GROUP_SHOW">
                    <source>permission.PERM_ROLE_GROUP_SHOW</source>
                    <target>Show position group</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_CREATE">
                    <source>permission.PERM_SERVICE_ATTENDANCE_CREATE</source>
                    <target>Create attendance (Attendance acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_EDIT">
                    <source>permission.PERM_SERVICE_ATTENDANCE_EDIT</source>
                    <target>Edit attendance (Attendance acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_APPROVE">
                    <source>permission.PERM_SERVICE_ATTENDANCE_APPROVE</source>
                    <target>Approve/disapprove attendance (Attendance acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_DELETE">
                    <source>permission.PERM_SERVICE_ATTENDANCE_DELETE</source>
                    <target>Delete attendance (Attendance acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_SHOW">
                    <source>permission.PERM_SERVICE_ATTENDANCE_SHOW</source>
                    <target>Show attendance (Attendance acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_ATTENDANCE_UNBLOCK">
                    <source>permission.PERM_SERVICE_ATTENDANCE_UNBLOCK</source>
                    <target>Unblock attendance (after the export)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_CREATE">
                    <source>permission.PERM_SERVICE_BILLING_CREATE</source>
                    <target>Create a service (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_SHOW_RATE">
                    <source>permission.PERM_SERVICE_BILLING_SHOW_RATE</source>
                    <target>Show billing rate (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_EDIT">
                    <source>permission.PERM_SERVICE_BILLING_EDIT</source>
                    <target>Edit a service (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_APPROVE">
                    <source>permission.PERM_SERVICE_BILLING_APPROVE</source>
                    <target>Approve/disapprove a service (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_DELETE">
                    <source>permission.PERM_SERVICE_BILLING_DELETE</source>
                    <target>Delete a service (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_SHOW">
                    <source>permission.PERM_SERVICE_BILLING_SHOW</source>
                    <target>Show a service (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_UNBLOCK">
                    <source>permission.PERM_SERVICE_BILLING_UNBLOCK</source>
                    <target>Unblock attendance (after the export)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_UPDATE_RATES">
                    <source>permission.PERM_SERVICE_BILLING_UPDATE_RATES</source>
                    <target>Update billing rates (Billing approval)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_BILLING_UPDATE_PRODUCTS">
                    <source>permission.PERM_SERVICE_BILLING_UPDATE_PRODUCTS</source>
                    <target>Update service products (Billing approval)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_EDIT_EDIT">
                    <source>permission.PERM_SERVICE_EDIT_EDIT</source>
                    <target>Edit a service (Billing acknowledge)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_EDIT_SHOW">
                    <source>permission.PERM_SERVICE_EDIT_SHOW</source>
                    <target>Show a service (Service editor)</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_CREATE_CREATE">
                    <source>permission.PERM_SERVICE_CREATE_CREATE</source>
                    <target>Insert services - generate</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_DELETE">
                    <source>permission.PERM_SUBJECT_ACCESS_DELETE</source>
                    <target>Delete a subjects' access</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_ACCESS_SHOW">
                    <source>permission.PERM_SUBJECT_ACCESS_SHOW</source>
                    <target>Show a subjects' access</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_CREATE">
                    <source>permission.PERM_SUBJECT_GROUP_CREATE</source>
                    <target>Create a group of subjects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_EDIT">
                    <source>permission.PERM_SUBJECT_GROUP_EDIT</source>
                    <target>Edit a group of subjects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_DELETE">
                    <source>permission.PERM_SUBJECT_GROUP_DELETE</source>
                    <target>Delete a group of subjects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SUBJECT_GROUP_SHOW">
                    <source>permission.PERM_SUBJECT_GROUP_SHOW</source>
                    <target>Show a group of subjects</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OPM_POPS_SHOW">
                    <source>permission.PERM_OPM_POPS_SHOW</source>
                    <target>Client access - Show SPR, Edit a reporting setting</target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_SHOW">
                    <source>permission.PERM_FLUCTUATION_SHOW</source>
                    <target>Show fluctuations</target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_DELETE">
                    <source>permission.PERM_FLUCTUATION_DELETE</source>
                    <target>Erase fluctuations</target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_EXPORT">
                    <source>permission.PERM_FLUCTUATION_EXPORT</source>
                    <target>Expor fluctuations</target>
                </trans-unit>
                <trans-unit id="permission.PERM_FLUCTUATION_GENERATE">
                    <source>permission.PERM_FLUCTUATION_GENERATE</source>
                    <target>Generate fluctuations</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CD_PROFIT_SHOW">
                    <source>permission.PERM_CD_PROFIT_SHOW</source>
                    <target>Show BA gain</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OBJECT_CD_PROFIT_EXPORT">
                    <source>permission.PERM_OBJECT_CD_PROFIT_EXPORT</source>
                    <target>Export BA gain</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISABLE_AUTOLOGOUT">
                    <source>permission.PERM_DISABLE_AUTOLOGOUT</source>
                    <target>Disable autologout</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISABLE_CONTROL_PRESENCE">
                    <source>permission.PERM_DISABLE_CONTROL_PRESENCE</source>
                    <target>Disable presence check</target>
                </trans-unit>                                

                <trans-unit id="permission.PERM_ARCHIVES_SERVICE_SHOW">
                    <source>permission.PERM_ARCHIVES_SERVICE_SHOW</source>
                    <target>Archive - show services</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_SERVICE_DELETE">
                    <source>permission.PERM_ARCHIVES_SERVICE_DELETE</source>
                    <target>Archive - delete a service</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_SERVICE_EXPORT">
                    <source>permission.PERM_ARCHIVES_SERVICE_EXPORT</source>
                    <target>Archive - export services</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_ATTENDANCE_SHOW">
                    <source>permission.PERM_ARCHIVES_ATTENDANCE_SHOW</source>
                    <target>Archive - show attendancies</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_ATTENDANCE_DELETE">
                    <source>permission.PERM_ARCHIVES_ATTENDANCE_DELETE</source>
                    <target>Archive - delete an attendance</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_ATTENDANCE_EXPORT">
                    <source>permission.PERM_ARCHIVES_ATTENDANCE_EXPORT</source>
                    <target>Archive - export attendances</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_POPS_SHOW">
                    <source>permission.PERM_ARCHIVES_POPS_SHOW</source>
                    <target>Archive - show SPR and it's events</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_POPS_DELETE">
                    <source>permission.PERM_ARCHIVES_POPS_DELETE</source>
                    <target>Archive - delete SPR and it's events</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_POPS_EXPORT">
                    <source>permission.PERM_ARCHIVES_POPS_EXPORT</source>
                    <target>Archive - SPR events' export</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_LOG_SHOW">
                    <source>permission.PERM_ARCHIVES_LOG_SHOW</source>
                    <target>Archive - show archivation process log</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_ARCHIVES_LOG_DELETE">
                    <source>permission.PERM_ARCHIVES_LOG_DELETE</source>
                    <target>Archive - delete the log</target>
                </trans-unit>                                                                                                                 
                <trans-unit id="permission.PERM_ARCHIVES_SETTINGS">
                    <source>permission.PERM_ARCHIVES_SETTINGS</source>
                    <target>Archive - settings</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_ARCHIVES_MULTIPOPS_SHOW">
                    <source>permission.PERM_ARCHIVES_MULTIPOPS_SHOW</source>
                    <target>Archive - Show MultiPOPS</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_MULTIPOPS_DELETE">
                    <source>permission.PERM_ARCHIVES_MULTIPOPS_DELETE</source>
                    <target>Archive - Delete MultiPOPS</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ARCHIVES_MULTIPOPS_EXPORT">
                    <source>permission.PERM_ARCHIVES_MULTIPOPS_EXPORT</source>
                    <target>Archive - Export MultiPOPS</target>
                </trans-unit>    
                <trans-unit id="permission.PERM_MESSAGE_SEN">
                    <source>permission.PERM_MESSAGE_SEN</source>
                    <target>Create and send messages</target>
                </trans-unit>       
                <trans-unit id="permission.PERM_MESSAGE_SEND">
                    <source>permission.PERM_MESSAGE_SEND</source>
                    <target>Send messages</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MESSAGE_SEND_PERM_TEMPLATE">
                    <source>permission.PERM_MESSAGE_SEND_PERM_TEMPLATE</source>
                    <target>Send message to persons by permission template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_PASS_AUTO">
                    <source>permission.PERM_PASS_AUTO</source>
                    <target>Generate passwords automatically</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SUBJECT_HIRE">
                    <source>permission.PERM_SUBJECT_HIRE</source>
                    <target>Hire persons from other objects</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SEND_CONTROL_MESSAGE">
                    <source>permission.PERM_SEND_CONTROL_MESSAGE</source>
                    <target>Send a monitoring SMS (system is running)</target>
                </trans-unit>          
                <trans-unit id="permission.PERM_NAVISION_XLS_EXPORT">
                    <source>permission.PERM_NAVISION_XLS_EXPORT</source>
                    <target>Wages' data export to XLS</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_NAVISION_XLS_OBJECT_EXPORT">
                    <source>permission.PERM_NAVISION_XLS_OBJECT_EXPORT</source>
                    <target>Export objects to XLS</target>
                </trans-unit>                 
                <trans-unit id="permission.PERM_ATTENDANCE_LENGTH_SHOW">
                    <source>permission.PERM_ATTENDANCE_LENGTH_SHOW</source>
                    <target>Show attendance lenghts</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_LENGTH_EXPORT">
                    <source>permission.PERM_ATTENDANCE_LENGTH_EXPORT</source>
                    <target>Export attendance lenghts</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_CUSTOMER_LOGIN_LOG_SHOW">
                    <source>permission.PERM_CUSTOMER_LOGIN_LOG_SHOW</source>
                    <target>Show login to Customer portal statistics</target>
                </trans-unit>
                <trans-unit id="permission.PERM_CUSTOMER_LOGIN_LOG_EXPORT">
                    <source>permission.PERM_CUSTOMER_LOGIN_LOG_EXPORT</source>
                    <target>Export login to Customer portal statistics</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_CREATE">
                    <source>permission.PERM_SERVICE_TEMPLATE_CREATE</source>
                    <target>Service template creation</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_EDIT">
                    <source>permission.PERM_SERVICE_TEMPLATE_EDIT</source>
                    <target>Edit a service template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_DELETE">
                    <source>permission.PERM_SERVICE_TEMPLATE_DELETE</source>
                    <target>Edit a service template</target>
                </trans-unit>
                <trans-unit id="permission.PERM_SERVICE_TEMPLATE_SHOW">
                    <source>permission.PERM_SERVICE_TEMPLATE_SHOW</source>
                    <target>Delete a service template</target>
                </trans-unit>       
                <trans-unit id="permission.PERM_PERIOD_RESTRICTED">
                    <source>permission.PERM_PERIOD_RESTRICTED</source>
                    <target>Restrict display of attendance</target>
                </trans-unit>                  
                <trans-unit id="permission.PERM_PANIC_BUTTON_TENANT">
                    <source>permission.PERM_PANIC_BUTTON_TENANT</source>
                    <target>Receiving an emergency message Tenants</target>
                </trans-unit>  
                
                <trans-unit id="permission.PERM_SUGGESTION_SHOW">
                    <source>permission.PERM_SUGGESTION_SHOW</source>
                    <target>Suggestions - show (Object)</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SUGGESTION_EDIT">
                    <source>permission.PERM_SUGGESTION_EDIT</source>
                    <target>Suggestions - acknowledge (Object)</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SUGGESTION_GROUP_SHOW">
                    <source>permission.PERM_SUGGESTION_GROUP_SHOW</source>
                    <target>Suggestions - show (Group of objects)</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SUGGESTION_GROUP_EDIT">
                    <source>permission.PERM_SUGGESTION_GROUP_EDIT</source>
                    <target>Suggestions - acknowledge (Group of objects)</target>
                </trans-unit>  
                
                <trans-unit id="permission.PERM_SUGGESTION_NOTIFICATION">
                    <source>permission.PERM_SUGGESTION_NOTIFICATION</source>
                    <target>Suggestions - notice via PM</target>
                </trans-unit>     
                <trans-unit id="permission.PERM_SUGGESTION_NOTIFICATION_EMAIL">
                    <source>permission.PERM_SUGGESTION_NOTIFICATION_EMAIL</source>
                    <target>Suggestions - email notice</target>
                </trans-unit>                                                              
                <trans-unit id="permission.PERM_M2CDESK_EXCLUDE_GROUP">
                    <source>permission.PERM_M2CDESK_EXCLUDE_GROUP</source>
                    <target>Exclude objects from this group (M2CDesk, M2C Support, ABI Bridge, Customer portal)</target>
                </trans-unit>                    
            </group>
            
            
            <group resname="dashboard">
                <note>Číselníky aplikace</note>
                <trans-unit id="dashboard.title">
                    <source>dashboard.title</source>
                    <target>Group labels</target>
                </trans-unit>
                <trans-unit id="country_city_group_label">
                    <source>country_city_group_label</source>
                    <target>Group label of countries and cities </target>
                </trans-unit>
                <trans-unit id="Companies">
                    <source>Companies</source>
                    <target>Companies</target>
                </trans-unit>
                <trans-unit id="company_list">
                    <source>company_list</source>
                    <target>List of companies</target>
                </trans-unit> 
                <trans-unit id="company_create">
                    <source>company_create</source>
                    <target>Create a company</target>
                </trans-unit>    
                <trans-unit id="company_edit">
                    <source>company_edit</source>
                    <target>Adjust a company</target>
                </trans-unit>    
                <trans-unit id="company_show">
                    <source>company_show</source>
                    <target>Company detail</target>
                </trans-unit>    
                <trans-unit id="company_delete">
                    <source>company_delete</source>
                    <target>Delete a company </target>
                </trans-unit>                
                <trans-unit id="company_history">
                    <source>company_history</source>
                    <target>Company editing history</target>
                </trans-unit>                
                <trans-unit id="company.title">
                    <source>company.title</source>
                    <target>Company</target>
                </trans-unit>
                <trans-unit id="company.ico">
                    <source>company.ico</source>
                    <target>Business ID</target>
                </trans-unit>
                <trans-unit id="company.code_help">
                    <source>company.code_help</source>
                    <target>Company code. It will be used as part of the object code.</target>
                </trans-unit>
                <trans-unit id="company.abbr_help">
                    <source>company.abbr_help</source>
                    <target>Company abbreviation. It must not exceed 5 characters.</target>
                </trans-unit>
                <trans-unit id="company.note">
                    <source>company.note</source>
                    <target>Note</target>
                </trans-unit>
            </group>
            
            
            <group resname="contactInfo">
                <note>Editace a zobrazení kontaktních údajů</note>           
                <trans-unit id="contact_info.title">
                    <source>contact_info.title</source>
                    <target>Contact details</target>
                </trans-unit>            
                <trans-unit id="contact_info.street">
                    <source>contact_info.street</source>
                    <target>Street</target>
                </trans-unit>
                <trans-unit id="contact_info.no">
                    <source>contact_info.no</source>
                    <target>Number</target>
                </trans-unit>
                <trans-unit id="contact_info.city">
                    <source>contact_info.city</source>
                    <target>City</target>
                </trans-unit>
                <trans-unit id="contact_info.zip">
                    <source>contact_info.zip</source>
                    <target>Zip Code</target>
                </trans-unit>
                <trans-unit id="contact_info.country">
                    <source>contact_info.country</source>
                    <target>Country</target>
                </trans-unit>
                <trans-unit id="contact_info.phone1">
                    <source>contact_info.phone1</source>
                    <target>Business telephone</target>
                </trans-unit>
                <trans-unit id="contact_info.phone1_help">
                    <source>contact_info.phone1_help</source>
                    <target>Format eg. +420xxxxxxxxx.</target>
                </trans-unit>
                <trans-unit id="contact_info.mobile1">
                    <source>contact_info.mobile1</source>
                    <target>Business mobile</target>
                </trans-unit>
                <trans-unit id="contact_info.mobile1_help">
                    <source>contact_info.mobile1_help</source>
                    <target>Format eg. +420xxxxxxxxx.</target>
                </trans-unit>
                <trans-unit id="contact_info.email1">
                    <source>contact_info.email1</source>
                    <target>Business e-mail </target>
                </trans-unit>
                <trans-unit id="contact_info.fax">
                    <source>contact_info.fax</source>
                    <target>Fax</target>
                </trans-unit>
                <trans-unit id="contact_info.phone2">
                    <source>contact_info.phone2</source>
                    <target>Private telephone</target>
                </trans-unit>
                <trans-unit id="contact_info.mobile2">
                    <source>contact_info.mobile2</source>
                    <target>Private mobile </target>
                </trans-unit>
                <trans-unit id="contact_info.email2">
                    <source>contact_info.email2</source>
                    <target>Private e-mail</target>
                </trans-unit>
                <trans-unit id="contact_info.email3">
                    <source>contact_info.email3</source>
                    <target>Contact email</target>
                </trans-unit>
                <trans-unit id="contact_info.email3_help">
                    <source>contact_info.email3_help</source>
                    <target>Email, which appears in Customer portal as an email of a contact person. Leaving this field blank causes appearance of business email as a contact one (considering contact person).</target>
                </trans-unit>
                <trans-unit id="contact_info.photo">
                    <source>contact_info.photo</source>
                    <target>Photo</target>
                </trans-unit>
                <trans-unit id="contact_info.photo_remove">
                    <source>contact_info.photo_remove</source>
                    <target>Delete photo</target>
                </trans-unit>                
                <trans-unit id="contact_info.photo_remove_confirm">
                    <source>contact_info.photo_remove_confirm</source>
                    <target>Do you really want to delete the photo?</target>
                </trans-unit>                
            </group>
            <group resname="agencies">
                <note>Agentury editace přehled</note>
                <trans-unit id="agency.title">
                    <source>agency.title</source>
                    <target>Agencies</target>
                </trans-unit>
                <trans-unit id="agency_list">
                    <source>agency_list</source>
                    <target>List of agencies</target>
                </trans-unit> 
                <trans-unit id="agency_create">
                    <source>agency_create</source>
                    <target>Create an agency</target>
                </trans-unit>    
                <trans-unit id="agency_edit">
                    <source>agency_edit</source>
                    <target>Adjust an agency</target>
                </trans-unit>    
                <trans-unit id="agency_show">
                    <source>agency_show</source>
                    <target>Agency detail</target>
                </trans-unit>    
                <trans-unit id="agency_delete">
                    <source>agency_delete</source>
                    <target>Delete an agency</target>
                </trans-unit>                
                <trans-unit id="agency_history">
                    <source>agency_history</source>
                    <target>Agency editing history</target>
                </trans-unit>                
                <trans-unit id="agency.name">
                    <source>agency.name</source>
                    <target>Agency name</target>
                </trans-unit>
            </group>
            <group resname="persons">
                <note>Osoby</note>
                <trans-unit id="person.title">
                    <source>person.title</source>
                    <target>Persons</target>
                </trans-unit>
                <trans-unit id="person_list">
                    <source>person_list</source>
                    <target>List of persons</target>
                </trans-unit>       
                <trans-unit id="person_create">
                    <source>person_create</source>
                    <target>Create a person </target>
                </trans-unit>    
                <trans-unit id="person_edit">
                    <source>person_edit</source>
                    <target>Adjust a person </target>
                </trans-unit>    
                <trans-unit id="person_show">
                    <source>person_show</source>
                    <target>Person's details</target>
                </trans-unit>    
                <trans-unit id="person_delete">
                    <source>person_delete</source>
                    <target>Delete a person </target>
                </trans-unit>                
                <trans-unit id="person_history">
                    <source>person_history</source>
                    <target>Person editing history</target>
                </trans-unit>  
                <trans-unit id="person.password_generated">
                    <source>person.password_generated</source>
                    <target>Operations Module access password has been generated and sent to the subject.</target>
                </trans-unit> 
                <trans-unit id="person.password_no_generated">
                    <source>person.password_no_generated</source>
                    <target>Operations Module access password hasn't been generated. The person hasn't an email address set.</target>
                </trans-unit>                 
                <trans-unit id="person.generate_password">
                    <source>person.generate_password</source>
                    <target>Generate password</target>
                </trans-unit>   
                <trans-unit id="person.generate_password_PM">
                    <source>person.generate_password_PM</source>
                    <target>PM password generate</target>
                </trans-unit> 
                <trans-unit id="person.generate_password_OPM">
                    <source>person.generate_password_OPM</source>
                    <target>Customer portal password generate</target>
                </trans-unit>
                <trans-unit id="person.cannot_generate_password_PM">
                    <source>person.cannot_generate_password_PM</source>
                    <target>Subject's Personal No isn't filled or subject's or logged in user's Business e-mail isn't set.</target>
                </trans-unit>
                <trans-unit id="person.cannot_generate_password_OPM">
                    <source>person.cannot_generate_password_OPM</source>
                    <target>Subject's business e-mail isn't set.</target>
                </trans-unit>
                <trans-unit id="person.generate_password_not_allowed">
                    <source>person.generate_password_not_allowed</source>
                    <target>The password cannot be generated. The business email isn't set.</target>
                </trans-unit>                  
                <trans-unit id="person.personalNo">
                    <source>person.personalNo</source>
                    <target>Personal number</target>
                </trans-unit>
                <trans-unit id="person.personalNo2">
                    <source>person.personalNo2</source>
                    <target>Persnoal number (original)</target>
                </trans-unit>                
                <trans-unit id="person.rc">
                    <source>person.rc</source>
                    <target>Personal ID</target>
                </trans-unit>
                <trans-unit id="personal_info.title">
                    <source>personal_info.title</source>
                    <target>Personal entries</target>
                </trans-unit> 
                <trans-unit id="personal_info_edit">
                    <source>personal_info_edit</source>
                    <target>Personal entries' editing</target>
                </trans-unit>
                <trans-unit id="personal_info.password_change">
                    <source>personal_info.password_change</source>
                    <target>Password change</target>
                </trans-unit>
                <trans-unit id="person.permission_role">
                    <source>person.permission_role</source>
                    <target>Permission role</target>
                </trans-unit>
                <trans-unit id="personal_info.opm_password_change">
                    <source>personal_info.opm_password_change</source>
                    <target>Password change for the Customer portal</target>
                </trans-unit>
                <trans-unit id="person.is_contact">
                    <source>person.is_contact</source>
                    <target>Contact person?</target>
                </trans-unit>   
                <trans-unit id="person.brigade_bonus_rate">
                    <source>person.brigade_bonus_rate</source>
                    <target>Bonus</target>
                </trans-unit>                             
                <trans-unit id="person.pay_point_code">
                    <source>person.pay_point_code</source>
                    <target>Pay point code</target>
                </trans-unit>                 
                <trans-unit id="person.can_perambulation_terminal">
                    <source>person.can_perambulation_terminal</source>
                    <target>Allow access to Mobile</target>
                </trans-unit>
                <trans-unit id="person.perambulationTerminalPin">
                    <source>person.perambulationTerminalPin</source>
                    <target>PIN to Mobile</target>
                </trans-unit>                                
                <trans-unit id="person.m2cdesk_title">
                    <source>person.m2cdesk_title</source>
                    <target>M2CDesk</target>
                </trans-unit>                   
                <trans-unit id="person.m2cdesk_is_approver">
                    <source>person.m2cdesk_is_approver</source>
                    <target>Approver M2CDesk</target>
                </trans-unit>                 
                <trans-unit id="person.m2cdesk_role">
                    <source>person.m2cdesk_role</source>
                    <target>Role in M2CDesk</target>
                </trans-unit>   
                <trans-unit id="person.m2cdesk_role_admin">
                    <source>person.m2cdesk_role_admin</source>
                    <target>administrator</target>
                </trans-unit>
                <trans-unit id="person.m2cdesk_role_applicant">
                    <source>person.m2cdesk_role_applicant</source>
                    <target>applicant</target>
                </trans-unit>
                <trans-unit id="person.m2cdesk_role_approver">
                    <source>person.m2cdesk_role_approver</source>
                    <target>approver</target>
                </trans-unit>
                <trans-unit id="person.m2cdesk_role_solver">
                    <source>person.m2cdesk_role_solver</source>
                    <target>solver</target>
                </trans-unit>
                <trans-unit id="person.m2cdesk_role_supervisor">
                    <source>person.m2cdesk_role_supervisor</source>
                    <target>supervisor</target>
                </trans-unit>
                <trans-unit id="person.department_role">
                    <source>person.department_role</source>
                    <target>Department</target>
                </trans-unit>                
                <trans-unit id="person.department_role_it">
                    <source>person.department_role_it</source>
                    <target>IT</target>
                </trans-unit>  
                <trans-unit id="person.department_role_tech">
                    <source>person.department_role_tech</source>
                    <target>TECH</target>
                </trans-unit>  
                <trans-unit id="person.department_role_ons">
                    <source>person.department_role_ons</source>
                    <target>ONS</target>
                </trans-unit>  
                <trans-unit id="person.department_role_all">
                    <source>person.department_role_all</source>
                    <target>ALL</target>
                </trans-unit>                                                                  
                <trans-unit id="person.m2cdesk_statistics">
                    <source>person.m2cdesk_statistics</source>
                    <target>Statistics in M2CDesk</target>
                </trans-unit>                  
            </group>
            
            <group resname="customers">
                <note>Zákazníci</note>           
                <trans-unit id="customer.title">
                    <source>customer.title</source>
                    <target>Customers</target>
                </trans-unit>
                <trans-unit id="customers_list">
                    <source>customers_list</source>
                    <target>List of customers</target>
                </trans-unit>       
                <trans-unit id="customers_create">
                    <source>customers_create</source>
                    <target>Create a customer </target>
                </trans-unit>    
                <trans-unit id="customers_edit">
                    <source>customers_edit</source>
                    <target>Adjust a customer </target>
                </trans-unit>    
                <trans-unit id="customers_show">
                    <source>customers_show</source>
                    <target>Customer detail</target>
                </trans-unit>    
                <trans-unit id="customers_delete">
                    <source>customers_delete</source>
                    <target>Delete a customer </target>
                </trans-unit>                
                <trans-unit id="customers_history">
                    <source>customers_history</source>
                    <target>Customer editing history </target>
                </trans-unit>   
                <trans-unit id="customer.objects">
                    <source>customer.objects</source>
                    <target>Connect with objects</target>
                </trans-unit> 
                <trans-unit id="customer.objects_show">
                    <source>customer.objects_show</source>
                    <target>Objects of the customer</target>
                </trans-unit>
                <trans-unit id="customer.password_generated">
                    <source>customer.password_generated</source>
                    <target>Password to customer access has been generated and sent to the customer.</target>
                </trans-unit> 
                <trans-unit id="customer.password_no_generated">
                    <source>customer.password_no_generated</source>
                    <target>Password to the Client's access hasn't been generated. The person hasn't an email address set. </target>
                </trans-unit>                 
                
                <trans-unit id="customer.generate_password_for_zp">
                    <source>customer.generate_password_for_zp</source>
                    <target>Generate password for customer access.</target>
                </trans-unit> 
                <trans-unit id="customer.generate_password_not_allowed">
                    <source>customer.generate_password_not_allowed</source>
                    <target>The password to Clients' access cannot be generated. The business email isn't set.</target>
                </trans-unit>     
                <trans-unit id="customer.contacts_show">
                    <source>customer.contacts_show</source>
                    <target>Client's contact persons</target>
                </trans-unit>
                <trans-unit id="customer.contacts">
                    <source>customer.contacts</source>
                    <target>Contact persons</target>
                </trans-unit>
                <trans-unit id="customer.contact_name">
                    <source>customer.contact_name</source>
                    <target>Name</target>
                </trans-unit>                
                <trans-unit id="customer.contact_position">
                    <source>customer.contact_position</source>
                    <target>Function</target>
                </trans-unit>
                <trans-unit id="customer.contact_objects">
                    <source>customer.contact_objects</source>
                    <target>Attached objects</target>
                </trans-unit>                                                                            
                <trans-unit id="customer.opm_settings">
                    <source>customer.opm_settings</source>
                    <target>Client's portal settings</target>
                </trans-unit>                 
                <trans-unit id="customer.perambulation_report">
                    <source>customer.perambulation_report</source>
                    <target>Report of patrols</target>
                </trans-unit>      
                <trans-unit id="customer.abi_user">
                    <source>customer.abi_user</source>
                    <target>Is ABI customer?</target>
                </trans-unit>                                
            </group>                                                  
            <group resname="currencies">
                <note>Měny</note>
                <trans-unit id="currency.title">
                    <source>currency.title</source>
                    <target>Currencies</target>
                </trans-unit>
                <trans-unit id="currency_list">
                    <source>currency_list</source>
                    <target>List of currencies</target>
                </trans-unit>          
                <trans-unit id="currency_create">
                    <source>currency_create</source>
                    <target>Create a currency </target>
                </trans-unit>    
                <trans-unit id="currency_edit">
                    <source>currency_edit</source>
                    <target>Adjust a currency </target>
                </trans-unit>    
                <trans-unit id="currency_show">
                    <source>currency_show</source>
                    <target>Currency detail</target>
                </trans-unit>    
                <trans-unit id="currency_delete">
                    <source>currency_delete</source>
                    <target>Delete a currency </target>
                </trans-unit>                
                <trans-unit id="currency_history">
                    <source>currency_history</source>
                    <target>Currency editing history </target>
                </trans-unit>                
                <trans-unit id="currency.name">
                    <source>currency.name</source>
                    <target>Currency name </target>
                </trans-unit>
                <trans-unit id="currency.abbr">
                    <source>currency.abbr</source>
                    <target>Currency abbreviation</target>
                </trans-unit>
            </group>
            <group resname="cities">
                <note>Města</note>
                <trans-unit id="city.title">
                    <source>city.title</source>
                    <target>Cities</target>
                </trans-unit>
                <trans-unit id="city_list">
                    <source>city_list</source>
                    <target>List of cities</target>
                </trans-unit>     
                <trans-unit id="city_create">
                    <source>city_create</source>
                    <target>Create a city </target>
                </trans-unit>    
                <trans-unit id="city_edit">
                    <source>city_edit</source>
                    <target>Adjust a city </target>
                </trans-unit>    
                <trans-unit id="city_show">
                    <source>city_show</source>
                    <target>City detail</target>
                </trans-unit>    
                <trans-unit id="city_delete">
                    <source>city_delete</source>
                    <target>Delete a city </target>
                </trans-unit>                
                <trans-unit id="city_history">
                    <source>city_history</source>
                    <target>City editing history </target>
                </trans-unit>                
                <trans-unit id="city.name">
                    <source>city.name</source>
                    <target>City name</target>
                </trans-unit>
            </group>
            <group resname="countries">
                <note>Města</note>
                <trans-unit id="country.title">
                    <source>country.title</source>
                    <target>Country</target>
                </trans-unit>
                <trans-unit id="country_list">
                    <source>country_list</source>
                    <target>List of countries </target>
                </trans-unit> 
                <trans-unit id="country_create">
                    <source>country_create</source>
                    <target>Create a country </target>
                </trans-unit>    
                <trans-unit id="country_edit">
                    <source>country_edit</source>
                    <target>Adjust a country </target>
                </trans-unit>    
                <trans-unit id="country_show">
                    <source>country_show</source>
                    <target>Country detail </target>
                </trans-unit>    
                <trans-unit id="country_delete">
                    <source>country_delete</source>
                    <target>Delete a country </target>
                </trans-unit>                
                <trans-unit id="country_history">
                    <source>country_history</source>
                    <target>Country editing history  </target>
                </trans-unit>                
                <trans-unit id="country.name">
                    <source>country.name</source>
                    <target>Country name </target>
                </trans-unit>
            </group>
            <group resname="absence">
                <note>Nepřítomností</note>
                <trans-unit id="absence.title">
                    <source>absence.title</source>
                    <target>Absence</target>
                </trans-unit>
                <trans-unit id="menu_group_absence">
                    <source>menu_group_absence</source>
                    <target>Absence</target>
                </trans-unit>                
                <trans-unit id="absence_list">
                    <source>absence_list</source>
                    <target>Absence list</target>
                </trans-unit>     
                <trans-unit id="absence_create">
                    <source>absence_create</source>
                    <target>Create an absence</target>
                </trans-unit>    
                <trans-unit id="absence_edit">
                    <source>absence_edit</source>
                    <target>Edit an absence</target>
                </trans-unit>    
                <trans-unit id="absence_show">
                    <source>absence_show</source>
                    <target>Absence detail</target>
                </trans-unit>    
                <trans-unit id="absence_delete">
                    <source>absence_delete</source>
                    <target>Delete an absence</target>
                </trans-unit>                
                <trans-unit id="absence_history">
                    <source>absence_history</source>
                    <target>History of absence</target>
                </trans-unit>                
                <trans-unit id="absence.subject">
                    <source>absence.subject</source>
                    <target>Subject</target>
                </trans-unit>
                <trans-unit id="absence.absence_type">
                    <source>absence.absence_type</source>
                    <target>Type of an absence</target>
                </trans-unit>
                <trans-unit id="absence.starts_at">
                    <source>absence.starts_at</source>
                    <target>Absent from</target>
                </trans-unit>
                <trans-unit id="absence.ends_at">
                    <source>absence.ends_at</source>
                    <target>Absent to</target>
                </trans-unit>
                
            </group>            
            <group resname="absenceTypes">
                <note>Typy nepřítomností</note>
                <trans-unit id="absence_type.title">
                    <source>absence_type.title</source>
                    <target>Types of absence </target>
                </trans-unit>
                <trans-unit id="absence_type_list">
                    <source>absence_type_list</source>
                    <target>Absence type list</target>
                </trans-unit>     
                <trans-unit id="absence_type_create">
                    <source>absence_type_create</source>
                    <target>Create a type of absence </target>
                </trans-unit>    
                <trans-unit id="absence_type_edit">
                    <source>absence_type_edit</source>
                    <target>Adjust a type of absence </target>
                </trans-unit>    
                <trans-unit id="absence_type_show">
                    <source>absence_type_show</source>
                    <target>Type of absence detail </target>
                </trans-unit>    
                <trans-unit id="absence_type_delete">
                    <source>absence_type_delete</source>
                    <target>Delete a type of absence </target>
                </trans-unit>                
                <trans-unit id="absence_type_history">
                    <source>absence_type_history</source>
                    <target>Type of absence editing history  </target>
                </trans-unit>                
                <trans-unit id="absence_type.name">
                    <source>absence_type.name</source>
                    <target>Name</target>
                </trans-unit>
                <trans-unit id="absence_type.code">
                    <source>absence_type.code</source>
                    <target>Absence code</target>
                </trans-unit>
            </group>
            
            <group resname="dimensionlevels">
                <note>Úrovně dimenzí</note>
                <trans-unit id="dimension_level.title">
                    <source>dimension_level.title</source>
                    <target>Dimension levels </target>
                </trans-unit>
                <trans-unit id="dimension_level_list">
                    <source>dimension_level_list</source>
                    <target>List of dimension levels </target>
                </trans-unit>   
                <trans-unit id="dimension_level_create">
                    <source>dimension_level_create</source>
                    <target>Create a dimension level </target>
                </trans-unit>    
                <trans-unit id="dimension_level_edit">
                    <source>dimension_level_edit</source>
                    <target>Adjust a dimension level </target>
                </trans-unit>    
                <trans-unit id="dimension_level_show">
                    <source>dimension_level_show</source>
                    <target>Dimension level detail </target>
                </trans-unit>    
                <trans-unit id="dimension_level_delete">
                    <source>dimension_level_delete</source>
                    <target>Delete a dimension level</target>
                </trans-unit>                
                <trans-unit id="dimension_level_history">
                    <source>dimension_level_history</source>
                    <target>Dimension level editing history</target>
                </trans-unit>                
                <trans-unit id="dimension_level.name">
                    <source>dimension_level.name</source>
                    <target>Level name</target>
                </trans-unit>
                <trans-unit id="dimension_level.message_min_level">
                    <source>dimension_level.message_min_level</source>
                    <target>Level starts from 1 and higher </target>
                </trans-unit>
            </group>
            
            <group resname="dimensions">
                <note>Dimense - nastavení logického členění položek</note>
                <trans-unit id="dimension.title">
                    <source>dimension.title</source>
                    <target>Dimension </target>
                </trans-unit>
                <trans-unit id="dimension_list">
                    <source>dimension_list</source>
                    <target>List of dimensions </target>
                </trans-unit>   
                <trans-unit id="dimension_create">
                    <source>dimension_create</source>
                    <target>Create a dimension </target>
                </trans-unit>    
                <trans-unit id="dimension_edit">
                    <source>dimension_edit</source>
                    <target>Adjust a dimension </target>
                </trans-unit>    
                <trans-unit id="dimension_show">
                    <source>dimension_show</source>
                    <target>Dimension detail </target>
                </trans-unit>    
                <trans-unit id="dimension_delete">
                    <source>dimension_delete</source>
                    <target>Delete a dimension </target>
                </trans-unit>                
                <trans-unit id="dimension_history">
                    <source>dimension_history</source>
                    <target>Dimension editing history  </target>
                </trans-unit>                
                <trans-unit id="dimension.name">
                    <source>dimension.name</source>
                    <target>Dimension name</target>
                </trans-unit>
                <trans-unit id="dimension.level">
                    <source>dimension.level</source>
                    <target>Level</target>
                </trans-unit>
                <trans-unit id="dimension.code">
                    <source>dimension.code</source>
                    <target>Dimension code</target>
                </trans-unit>
            </group>
            <group resname="object">
                <note>Objekty</note>
                <trans-unit id="object.title">
                    <source>object.title</source>
                    <target>Objects</target>
                </trans-unit>
                <trans-unit id="object_list">
                    <source>object_list</source>
                    <target>List of objects</target>
                </trans-unit>  
                <trans-unit id="object_create">
                    <source>object_create</source>
                    <target>Create an object</target>
                </trans-unit>    
                <trans-unit id="object_edit">
                    <source>object_edit</source>
                    <target>Adjust an object</target>
                </trans-unit>    
                <trans-unit id="object_show">
                    <source>object_show</source>
                    <target>Object detail </target>
                </trans-unit>    
                <trans-unit id="object_delete">
                    <source>object_delete</source>
                    <target>Delete an object</target>
                </trans-unit>                
                <trans-unit id="object_history">
                    <source>object_history</source>
                    <target>Object editing history </target>
                </trans-unit>                
                <trans-unit id="object.name">
                    <source>object.name</source>
                    <target>Object name</target>
                </trans-unit>
                <trans-unit id="object.company">
                    <source>object.company</source>
                    <target>Company name</target>
                </trans-unit>
                <trans-unit id="object.code">
                    <source>object.code</source>
                    <target>Object code</target>
                </trans-unit>
                <trans-unit id="object.currency">
                    <source>object.currency</source>
                    <target>Object currency </target>
                </trans-unit>
                <trans-unit id="object.dimensions">
                    <source>object.dimensions</source>
                    <target>Object dimension </target>
                </trans-unit>
                <trans-unit id="object.successfully_set">
                    <source>object.successfully_set</source>
                    <target>Object successfully set up </target>
                </trans-unit>
                <trans-unit id="object.group_successfully_set">
                    <source>object.group_successfully_set</source>
                    <target>Object group successfully set up </target>
                </trans-unit>
                <trans-unit id="object.select">
                    <source>object.select</source>
                    <target>Select an object</target>
                </trans-unit>
                <trans-unit id="object.all_objects">
                    <source>object.all_objects</source>
                    <target>All objects</target>
                </trans-unit>
                <trans-unit id="object.selection">
                    <source>object.selection</source>
                    <target>Object selection </target>
                </trans-unit>
                <trans-unit id="object.selection_message">
                    <source>object.selection_message</source>
                    <target>Please select object </target>
                </trans-unit>
                <trans-unit id="object.selection_label">
                    <source>object.selection_label</source>
                    <target>Object: %name%</target>
                </trans-unit>
                <trans-unit id="object.group_label">
                    <source>object.group_label</source>
                    <target>Group: %name%</target>
                </trans-unit>   
                <trans-unit id="object.pops_activity_title">
                    <source>object.pops_activity_title</source>
                    <target>SPR activity</target>
                </trans-unit> 
                <trans-unit id="object.pops_activity_description">
                    <source>object.pops_activity_description</source>
                    <target>SPR activity settings. Enter the timer interval in minutes. In the event of an inactivity notification, select the corresponding event from which the SPR event will be created.</target>
                </trans-unit>                 
                <trans-unit id="object.pops_activity_enable">
                    <source>object.pops_activity_enable</source>
                    <target>Turn on SPR activity</target>
                </trans-unit> 
                <trans-unit id="object.pops_activity_interval">
                    <source>object.pops_activity_interval</source>
                    <target>Interval (min)</target>
                </trans-unit>     
                <trans-unit id="object.pops_activity_event">
                    <source>object.pops_activity_event</source>
                    <target>Event</target>
                </trans-unit>                                                                             
                <trans-unit id="object.documentation_title">
                    <source>object.documentation_title</source>
                    <target>Documentation</target>
                </trans-unit> 
                <trans-unit id="object.documentation_object">
                    <source>object.documentation_object</source>
                    <target>Object documentation</target>
                </trans-unit> 
                <trans-unit id="object.documentation_bozp">
                    <source>object.documentation_bozp</source>
                    <target>FP OSH (Fire protection and Occupation safety and health)</target>
                </trans-unit>                                                 
                <trans-unit id="object.mode_title">
                    <source>object.mode_title</source>
                    <target>Object mode / parameters</target>
                </trans-unit> 
                <trans-unit id="object.mode_jaderky">
                    <source>object.mode_jaderky</source>
                    <target>Nuclear mode</target>
                </trans-unit>                                 
                <trans-unit id="object.object_space">
                    <source>object.object_space</source>
                    <target>Space object</target>
                </trans-unit>                  
            </group>
            <group resname="object_info">
                <note>Karta objektu</note>
                <trans-unit id="object_info.title">
                    <source>object_info.title</source>
                    <target>Object card</target>
                </trans-unit>
                <trans-unit id="object_info.title_plus">
                    <source>object_info.title_plus</source>
                    <target>Object card (for billing)</target>
                </trans-unit>                
                <trans-unit id="object_info.object_name">
                    <source>object_info.object_name</source>
                    <target>Object name</target>
                </trans-unit>  
                <trans-unit id="object_info.customer">
                    <source>object_info.customer</source>
                    <target>Client's name</target>
                </trans-unit>    
                <trans-unit id="object_info.ico">
                    <source>object_info.ico</source>
                    <target>Client's ID number</target>
                </trans-unit>    
                <trans-unit id="object_info.division">
                    <source>object_info.division</source>
                    <target>Division number</target>
                </trans-unit>    
                <trans-unit id="object_info.bm">
                    <source>object_info.bm</source>
                    <target>Security Manager</target>
                </trans-unit>                
                <trans-unit id="object_info.object_contact_info">
                    <source>object_info.object_contact_info</source>
                    <target>Address of an object</target>
                </trans-unit>                
                <trans-unit id="object_info.customer_contact_info">
                    <source>object_info.customer_contact_info</source>
                    <target>Address of the client</target>
                </trans-unit>
            </group>
            <group resname="pops">
                <note>Protokoly o průběhu služby</note>
                <trans-unit id="pops">
                    <source>pops</source>
                    <target>POPS</target>
                </trans-unit>                
                <trans-unit id="pops.title">
                    <source>pops.title</source>
                    <target>Service performance reports</target>
                </trans-unit>
                <trans-unit id="pops_event_list">
                    <source>pops_event_list</source>
                    <target>Service performance reports</target>
                </trans-unit>
                <trans-unit id="pops_event.title">
                    <source>pops_event.title</source>
                    <target>Service performance reports events</target>
                </trans-unit>
                <trans-unit id="pops_create">
                    <source>pops_create</source>
                    <target>Create a service performance report</target>
                </trans-unit>
                <trans-unit id="pops_edit">
                    <source>pops_edit</source>
                    <target>SPR editing</target>
                </trans-unit>
                <trans-unit id="pops_show">
                    <source>pops_show</source>
                    <target>Service performance report detail</target>
                </trans-unit>
                <trans-unit id="pops_event_create">
                    <source>pops_event_create</source>
                    <target>Create a service performance report event </target>
                </trans-unit>
                <trans-unit id="pops_event_edit">
                    <source>pops_event_edit</source>
                    <target>Adjust a service performance report event </target>
                </trans-unit>
                <trans-unit id="pops_event_show">
                    <source>pops_event_show</source>
                    <target>Detail of service performance report event </target>
                </trans-unit>
                <trans-unit id="pops.starts_at">
                    <source>pops.starts_at</source>
                    <target>Start</target>
                </trans-unit>
                <trans-unit id="pops.ends_at">
                    <source>pops.ends_at</source>
                    <target>End</target>
                </trans-unit>
                <trans-unit id="pops.serial_number">
                    <source>pops.serial_number</source>
                    <target>Serial number </target>
                </trans-unit>
                <trans-unit id="pops.event">
                    <source>pops.event</source>
                    <target>Event</target>
                </trans-unit>
                <trans-unit id="pops.category">
                    <source>pops.category</source>
                    <target>Category</target>
                </trans-unit>                
                <trans-unit id="pops.description">
                    <source>pops.description</source>
                    <target>Description </target>
                </trans-unit>
                <trans-unit id="pops.lock">
                    <source>pops.lock</source>
                    <target>Lock service performance report</target>
                </trans-unit>
                <trans-unit id="pops.confirm_lock">
                    <source>pops.confirm_lock</source>
                    <target>After locking this service performance report, no event can be added into it. Do you still wish to lock it?</target>
                </trans-unit>
                <trans-unit id="pops.lock_success">
                    <source>pops.lock_success</source>
                    <target>Service performance report has been locked and no events can be added into it.</target>
                </trans-unit>
                <trans-unit id="pops_list">
                    <source>pops_list</source>
                    <target>Service performance reports</target>
                </trans-unit>
                <trans-unit id="pops_event">
                    <source>pops_event</source>
                    <target>Service performance report event </target>
                </trans-unit>
                <trans-unit id="pops_event.list_title">
                    <source>pops_event.list_title</source>
                    <target>Events for service performance reports %from% %till%</target>
                </trans-unit>
                <trans-unit id="multi_pops_event.list_title">
                    <source>multi_pops_event.list_title</source>
                    <target>MultiPOPS events from %from% to %till%</target>
                </trans-unit>
                <trans-unit id="pops.event_time">
                    <source>pops.event_time</source>
                    <target>Event time </target>
                </trans-unit>
                <trans-unit id="pops.event_user_date">
                    <source>pops.event_user_date</source>
                    <target>Event time</target>
                </trans-unit>
                <trans-unit id="pops.description1">
                    <source>pops.description1</source>
                    <target>Description 1</target>
                </trans-unit>                                
                <trans-unit id="pops.no_active_pops">
                    <source>pops.no_active_pops</source>
                    <target>A service performance report has not been created for the current time. You can create it below.</target>
                </trans-unit>
                <trans-unit id="pops.period_validation_message1">
                    <source>pops.period_validation_message1</source>
                    <target>Start date must precede the end.</target>
                </trans-unit>
                <trans-unit id="pops.period_validation_message2">
                    <source>pops.period_validation_message2</source>
                    <target>The entered range of dates must not overlap with other service performance report. </target>
                </trans-unit>
                <trans-unit id="pops.period_validation_message3">
                    <source>pops.period_validation_message3</source>
                    <target>The current date is not within the range of dates entered on the form. </target>
                </trans-unit>
                <trans-unit id="pops.send_attachment">
                    <source>pops.send_attachment</source>
                    <target>Send attachments by e-mail?</target>
                </trans-unit>
                <trans-unit id="pops_event.datetime_validation_message">
                    <source>pops_event.datetime_validation_message</source>
                    <target>Event time must be within the range of service performance report.</target>
                </trans-unit>
                <trans-unit id="pops_event.datetime_validation_message_noaddpastevent">
                    <source>pops_event.datetime_validation_message_noaddpastevent</source>
                    <target>Time of the event has to be maximally 48 in the past.</target>
                </trans-unit>                
                <trans-unit id="pops_event.link_action_create">
                    <source>pops_event.link_action_create</source>
                    <target>Add event</target>
                </trans-unit>
                <trans-unit id="pops_event.new_event_mail_subject">
                    <source>pops_event.new_event_mail_subject</source>
                    <target>New event</target>
                </trans-unit>
                <trans-unit id="pops_event.new_event_mail_message">
                    <source>pops_event.new_event_mail_message</source>
                    <target>A new event "%event%" occurred on site "%object%".</target>
                </trans-unit>
                <trans-unit id="pops_event.new_event_message">
                    <source>pops_event.new_event_message</source>
                    <target>A new event "%event%" occurred on site "%object%". Time: "%eventTime% Description: %description%"</target>
                </trans-unit>
                <trans-unit id="object_pops_events">
                    <source>object_pops_events</source>
                    <target>SPR archive (on an object)</target>
                </trans-unit>
                <trans-unit id="object_events_list">
                    <source>object_events_list</source>
                    <target>Events list on an object</target>
                </trans-unit>
                <trans-unit id="object_events_show">
                    <source>object_events_show</source>
                    <target>Detail of an event</target>
                </trans-unit>
                <trans-unit id="object_events_edit">
                    <source>object_events_edit</source>
                    <target>Event edit</target>
                </trans-unit>
                <trans-unit id="object_events_delete">
                    <source>object_events_delete</source>
                    <target>Delete an event</target>
                </trans-unit>
                <trans-unit id="object_pops">
                    <source>object_pops</source>
                    <target>SPR of an object</target>
                </trans-unit>
                <trans-unit id="object_pops_list">
                    <source>object_pops_list</source>
                    <target>SPR list of an object</target>
                </trans-unit>
                <trans-unit id="object_pops_show">
                    <source>object_pops_show</source>
                    <target>SPR detail</target>
                </trans-unit>
                <trans-unit id="object_pops_edit">
                    <source>object_pops_edit</source>
                    <target>SPR edit</target>
                </trans-unit>
                <trans-unit id="object_events">
                    <source>object_events</source>
                    <target>Events on an object</target>
                </trans-unit>
                <trans-unit id="pops_event.translate_title">
                    <source>pops_event.translate_title</source>
                    <target>Choose the language for a translation</target>
                </trans-unit>
                <trans-unit id="pops_event.translate_action">
                    <source>pops_event.translate_action</source>
                    <target>Translate an entry</target>
                </trans-unit>
                <trans-unit id="pops_events_translate">
                    <source>pops_events_translate</source>
                    <target>Translation of an entry</target>
                </trans-unit>
                <trans-unit id="pops.show_to_customer">
                    <source>pops.show_to_customer</source>
                    <target>Show to clients</target>
                </trans-unit>
                <trans-unit id="for_customer">
                    <source>for_customer</source>
                    <target>Allow</target>
                </trans-unit>
                <trans-unit id="not_for_customer">
                    <source>not_for_customer</source>
                    <target>Forbid</target>
                </trans-unit>
                <trans-unit id="pops.events_count">
                    <source>pops.events_count</source>
                    <target>Events count</target>
                </trans-unit>
                <trans-unit id="pops.batch_pdf">
                    <source>pops.batch_pdf</source>
                    <target>Export events to PDF</target>
                </trans-unit>
                <trans-unit id="pops.batch_xls">
                    <source>pops.batch_xls</source>
                    <target>Export events to XLS</target>
                </trans-unit>
                <trans-unit id="popslist.batch_export_help">
                    <source>popslist.batch_export_help</source>
                    <target>Number of exported events is restricted to 3000 elements</target>
                </trans-unit>
                <trans-unit id="pops.top_events">
                    <source>pops.top_events</source>
                    <target>Events - top 20</target>
                </trans-unit>
                <trans-unit id="pops_top_20.title">
                    <source>pops_top_20.title</source>
                    <target>SPR Top 20</target>
                </trans-unit>
                <trans-unit id="pops_top_20_change">
                    <source>pops_top_20_change</source>
                    <target>Edit Top 20 events</target>
                </trans-unit>
                <trans-unit id="pops_top_20_edit">
                    <source>$sourcepops_top_20_edit</source>
                    <target>Edit Top 20 events</target>
                </trans-unit>
                <trans-unit id="event_tree.max_items_alert">
                    <source>event_tree.max_items_alert</source>
                    <target>You can't choose more events</target>
                </trans-unit>
                <trans-unit id="with_attachments">
                    <source>with_attachments</source>
                    <target>with attachments</target>
                </trans-unit>
                <trans-unit id="without_attachments">
                    <source>without_attachments</source>
                    <target>without attachments</target>
                </trans-unit>
                <trans-unit id="multipops_create_btn">
                    <source>multipops_create_btn</source>
                    <target>Create new MultiPOPS</target>
                </trans-unit>
                <trans-unit id="multi_pops.title">
                    <source>multi_pops.title</source>
                    <target>MultiPOPS</target>
                </trans-unit>
                <trans-unit id="multi_pops_list">
                    <source>multi_pops_list</source>
                    <target>MultiPOPS</target>
                </trans-unit>
                <trans-unit id="multi_pops_create">
                    <source>multi_pops_create</source>
                    <target>Create MultiPOPS</target>
                </trans-unit>
                <trans-unit id="multi_pops_edit">
                    <source>multi_pops_edit</source>
                    <target>Edit MultiPOPS</target>
                </trans-unit>
                <trans-unit id="multi_pops_event_create">
                    <source>multi_pops_event_create</source>
                    <target>Create POPS event</target>
                </trans-unit>
                <trans-unit id="multi_pops_event_edit">
                    <source>multi_pops_event_edit</source>
                    <target>Edit POPS event</target>
                </trans-unit>
                <trans-unit id="multipops_events_edit">
                    <source>multipops_events_edit</source>
                    <target>MultiPOPS events - input, edit</target>
                </trans-unit>
                <trans-unit id="multi_pops.not_running">
                    <source>multi_pops.not_running</source>
                    <target>You don't have any current MultiPOPS. You can create one now.</target>
                </trans-unit>
                <trans-unit id="back_to_multipops">
                    <source>back_to_multipops</source>
                    <target>Back to active MultiPOPS list</target>
                </trans-unit>
                <trans-unit id="multi_pops.period_validation_message2">
                    <source>multi_pops.period_validation_message2</source>
                    <target>MultiPOPS overlaps with another one, that contains the same object (objects).</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_CREATE">
                    <source>permission.PERM_MULTIPOPS_CREATE</source>
                    <target>Create MultiPOPS</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_EDIT">
                    <source>permission.PERM_MULTIPOPS_EDIT</source>
                    <target>Edit MultiPOPS</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_SHOW">
                    <source>permission.PERM_MULTIPOPS_SHOW</source>
                    <target>Show MultiPOPS</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_EVENT_CREATE">
                    <source>permission.PERM_MULTIPOPS_EVENT_CREATE</source>
                    <target>Create MultiPOPS event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_EVENT_EDIT">
                    <source>permission.PERM_MULTIPOPS_EVENT_EDIT</source>
                    <target>Edit MultiPOPS event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_EVENT_SHOW">
                    <source>permission.PERM_MULTIPOPS_EVENT_SHOW</source>
                    <target>Show MultiPOPS event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPS_SET_SEND_ATTACHMENT">
                    <source>permission.PERM_MULTIPOPS_SET_SEND_ATTACHMENT</source>
                    <target>Enable attachment submission settings</target>
                </trans-unit>
                <trans-unit id="permission.PERM_MULTIPOPSLIST_ADD_PAST_EVENT">
                    <source>permission.PERM_MULTIPOPSLIST_ADD_PAST_EVENT</source>
                    <target>MultiPOPS - Add an event even to locked ones</target>
                </trans-unit>                
                <trans-unit id="multi_popslist_list">
                    <source>multi_popslist_list</source>
                    <target>Locked MultiPOPS</target>
                </trans-unit>
                <trans-unit id="multi_popslist_stats_list">
                    <source>multi_popslist_stats_list</source>
                    <target>Overview MultiPOPS</target>
                </trans-unit>
            </group>
            <group resname="menu">
                <note>Texty pro všechna menu</note>            
                <trans-unit id="menu_object">
                    <source>menu_object</source>
                    <target>Object</target>
                </trans-unit>            
                <trans-unit id="menu_edit_services">
                    <source>menu_edit_services</source>
                    <target>Editor of services </target>
                </trans-unit>            
                <trans-unit id="menu_edit_services_edit">
                    <source>menu_edit_services_edit</source>
                    <target>Editor of services </target>
                </trans-unit>            
                <trans-unit id="menu_edit_services_approval_attendance">
                    <source>menu_edit_services_approval_attendance</source>
                    <target>Attendance approval </target>
                </trans-unit>            
                <trans-unit id="menu_edit_services_approval_billing">
                    <source>menu_edit_services_approval_billing</source>
                    <target>Billing approval </target>
                </trans-unit>            
                <trans-unit id="menu_add_services">
                    <source>menu_add_services</source>
                    <target>Add services </target>
                </trans-unit>   
                <trans-unit id="menu_persons">
                    <source>menu_persons</source>
                    <target>Persons and rates </target>
                </trans-unit>   
                <trans-unit id="menu_persons_subject_rates">
                    <source>menu_persons_subject_rates</source>
                    <target>Subject rates </target>
                </trans-unit>   
                <trans-unit id="menu_persons_position_rates">
                    <source>menu_persons_position_rates</source>
                    <target>Position rates </target>
                </trans-unit>   
                <trans-unit id="menu_persons_agency_rates">
                    <source>menu_persons_agency_rates</source>
                    <target>Agency rates </target>
                </trans-unit> 
                <trans-unit id="menu_persons_individual_rates">
                    <source>menu_persons_individual_rates</source>
                    <target>Individual rates </target>
                </trans-unit>  
                <trans-unit id="menu_persons_contract_rates">
                    <source>menu_persons_contract_rates</source>
                    <target>Rates according to the contract</target>
                </trans-unit>                             
                <trans-unit id="menu_periodic_payment">
                    <source>menu_periodic_payment</source>
                    <target>Periodic payments </target>
                </trans-unit>      
                <trans-unit id="menu_role">
                    <source>menu_role</source>
                    <target>Position</target>
                </trans-unit>   
                <trans-unit id="menu_role_role">
                    <source>menu_role_role</source>
                    <target>Position</target>
                </trans-unit>   
                <trans-unit id="menu_role_group">
                    <source>menu_role_group</source>
                    <target>Position groups</target>
                </trans-unit>  
                <trans-unit id="menu_purchase">
                    <source>menu_purchase</source>
                    <target>Additional purchase order</target>
                </trans-unit>    
                <trans-unit id="menu_object_select">
                    <source>menu_object_select</source>
                    <target>Change the object</target>
                </trans-unit>    
                <trans-unit id="menu_forward_service">
                    <source>menu_forward_service</source>
                    <target>Service handover </target>
                </trans-unit>              
                <trans-unit id="menu_group">
                    <source>menu_group</source>
                    <target>Group of objects</target>
                </trans-unit> 
                <trans-unit id="menu_group_attendance">
                    <source>menu_group_attendance</source>
                    <target>Attendance</target>
                </trans-unit>                             
                <trans-unit id="menu_group_attendance_start">
                    <source>menu_group_attendance_start</source>
                    <target>Beginning of an attendance</target>
                </trans-unit>            
                <trans-unit id="menu_group_attendance_end">
                    <source>menu_group_attendance_end</source>
                    <target>Termination of an attendance</target>
                </trans-unit>            
                <trans-unit id="menu_group_attendance_running">
                    <source>menu_group_attendance_running</source>
                    <target>Current attendances</target>
                </trans-unit>            
                <trans-unit id="menu_group_attendance_list">
                    <source>menu_group_attendance_list</source>
                    <target>Attendance summary</target>
                </trans-unit> 
                <trans-unit id="menu_group_attendance_length_list">
                    <source>menu_group_attendance_length_list</source>
                    <target>Attendance lenghts</target>
                </trans-unit>
                
                <trans-unit id="menu_group_control">
                    <source>menu_group_control</source>
                    <target>Plan and attendance check</target>
                </trans-unit> 
                <trans-unit id="menu_group_control_attendance_early">
                    <source>menu_group_control_attendance_early</source>
                    <target>Premature attendance</target>
                </trans-unit> 
                <trans-unit id="menu_group_control_noplan">
                    <source>menu_group_control_noplan</source>
                    <target>Non-existing plan</target>
                </trans-unit> 
                <trans-unit id="menu_group_attendance_not_approved">
                    <source>menu_group_attendance_not_approved</source>
                    <target>Not approved</target>
                </trans-unit> 
                <trans-unit id="menu_group_copy_months">
                    <source>menu_group_copy_months</source>
                    <target>Copy of a month</target>
                </trans-unit> 
                <trans-unit id="menu_group_agency_service">
                    <source>menu_group_agency_service</source>
                    <target>Suppliers' materials</target>
                </trans-unit>                                 
                <trans-unit id="menu_settings">
                    <source>menu_settings</source>
                    <target>Lookup tables' settings</target>
                </trans-unit>            
                <trans-unit id="menu_access">
                    <source>menu_access</source>
                    <target>Access rights </target>
                </trans-unit>            
                <trans-unit id="menu_access_groups">
                    <source>menu_access_groups</source>
                    <target>Rights' groups</target>
                </trans-unit>            
                <trans-unit id="menu_access_templates">
                    <source>menu_access_templates</source>
                    <target>Access templates</target>
                </trans-unit>            
                <trans-unit id="menu_statistics">
                    <source>menu_statistics</source>
                    <target>Statistics </target>
                </trans-unit>  
                <trans-unit id="menu_statistics_login">
                    <source>menu_statistics_login</source>
                    <target>Login/logoff</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_all">
                    <source>menu_statistics_logins_all</source>
                    <target>All logs</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_errors">
                    <source>menu_statistics_logins_errors</source>
                    <target>Captured errors</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_emergency">
                    <source>menu_statistics_logins_emergency</source>
                    <target>Emergency messages</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logins_reporting">
                    <source>menu_statistics_logins_reporting</source>
                    <target>Reports</target>
                </trans-unit>
                <trans-unit id="menu_statistics_logs">
                    <source>menu_statistics_logs</source>
                    <target>Logs</target>
                </trans-unit>
                <trans-unit id="menu_statistics_attendance_pops_check">
                    <source>menu_statistics_attendance_pops_check</source>
                    <target>Newest attendance/SPR</target>
                </trans-unit>                
                <trans-unit id="pops_attendance_check_list">
                    <source>pops_attendance_check_list</source>
                    <target>List of newest attendance and statistics</target>
                </trans-unit>  
                <trans-unit id="last_attendance">
                    <source>last_attendance</source>
                    <target>Last attendance</target>
                </trans-unit>
                <trans-unit id="last_pops">
                    <source>last_pops</source>
                    <target>Newest SPR event</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_POPS_CHECK_SHOW">
                    <source>permission.PERM_ATTENDANCE_POPS_CHECK_SHOW</source>
                    <target>Show newest attendance/SPR</target>
                </trans-unit>
                <trans-unit id="permission.PERM_ATTENDANCE_POPS_CHECK_EXPORT">
                    <source>permission.PERM_ATTENDANCE_POPS_CHECK_EXPORT</source>
                    <target>Export newest attendance/SPR</target>
                </trans-unit>
                <trans-unit id="login_log_list">
                    <source>login_log_list</source>
                    <target>Statistics - login/logoff</target>
                </trans-unit>  
                <trans-unit id="customer_login_log_list">
                    <source>customer_login_log_list</source>
                    <target>Customer portal login list</target>
                </trans-unit>
                <trans-unit id="logs_all_list">
                    <source>logs_all_list</source>
                    <target>Statistics - all activities</target>
                </trans-unit>  
                <trans-unit id="logs_errors_list">
                    <source>logs_errors_list</source>
                    <target>Statistics - Error report</target>
                </trans-unit>  
                <trans-unit id="logs_reporting_list">
                    <source>logs_reporting_list</source>
                    <target>Log of sended reports</target>
                </trans-unit>
                <trans-unit id="logs_emergency_list">
                    <source>logs_emergency_list</source>
                    <target>Statistics - Emergency messages</target>
                </trans-unit>  
                <trans-unit id="logs_data_list">
                    <source>logs_data_list</source>
                    <target>Statistics - data changes</target>
                </trans-unit>
                <trans-unit id="log.loginType​">
                    <source>log.loginType​</source>
                    <target>Login/logout</target>
                </trans-unit>  
                <trans-unit id="logsall.loginType">
                    <source>logsall.loginType</source>
                    <target>Type of activity</target>
                </trans-unit>  
                <trans-unit id="logsall.loginTime">
                    <source>logsall.loginTime</source>
                    <target>Date/time of a record</target>
                </trans-unit>  
                <trans-unit id="logsall.message">
                    <source>logsall.message</source>
                    <target>Description</target>
                </trans-unit>  
                <trans-unit id="logs.severity">
                    <source>logs.severity</source>
                    <target>Status</target>
                </trans-unit>  
                <trans-unit id="menu_statistics_logins_data_change">
                    <source>menu_statistics_logins_data_change</source>
                    <target>Data operations</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import">
                    <source>menu_exchange_import</source>
                    <target>Export/import</target>
                </trans-unit>    
                <trans-unit id="menu_group_objects_group">
                    <source>menu_group_objects_group</source>
                    <target>Groups of objects</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import_upload">
                    <source>menu_exchange_import_upload</source>
                    <target>Manual import</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import_file_list">
                    <source>menu_exchange_import_file_list</source>
                    <target>Imported files</target>
                </trans-unit>
                <trans-unit id="menu_exchange_import_result_list">
                    <source>menu_exchange_import_result_list</source>
                    <target>Import results</target>
                </trans-unit>
                <trans-unit id="menu_exchange_export_file_list">
                    <source>menu_exchange_export_file_list</source>
                    <target>Exported filess</target>
                </trans-unit>
                <trans-unit id="menu_exchange_manual_export">
                    <source>menu_exchange_manual_export</source>
                    <target>Manual export</target>
                </trans-unit>
                <trans-unit id="menu_statistics_customer_login">
                    <source>menu_statistics_customer_login</source>
                    <target>Customer portal login</target>
                </trans-unit>
            </group>
            <group resname="products">
                <note>Produkty</note>                                      
                <trans-unit id="product.title">
                    <source>product.title</source>
                    <target>Products </target>
                </trans-unit>
                <trans-unit id="product">
                    <source>product</source>
                    <target>Product</target>
                </trans-unit>                
                <trans-unit id="product_list">
                    <source>product_list</source>
                    <target>List of products </target>
                </trans-unit>
                <trans-unit id="product_create">
                    <source>product_create</source>
                    <target>Create a product</target>
                </trans-unit>    
                <trans-unit id="product_edit">
                    <source>product_edit</source>
                    <target>Adjust a product</target>
                </trans-unit>    
                <trans-unit id="product_show">
                    <source>product_show</source>
                    <target>Product detail </target>
                </trans-unit>    
                <trans-unit id="product_delete">
                    <source>product_delete</source>
                    <target>Delete a product</target>
                </trans-unit>                
                <trans-unit id="product_history">
                    <source>product_history</source>
                    <target>Product editing history </target>
                </trans-unit>                
                <trans-unit id="product.name">
                    <source>product.name</source>
                    <target>Product name</target>
                </trans-unit>
                <trans-unit id="product.isActiv">
                    <source>product.isActiv</source>
                    <target>Is active</target>
                </trans-unit>
                <trans-unit id="product.is_emergency">
                    <source>product.is_emergency</source>
                    <target>Emergency</target>
                </trans-unit>
                <trans-unit id="product.is_controlled_zone">
                    <source>product.is_controlled_zone</source>
                    <target>Controlled zone</target>
                </trans-unit>                
            </group>
            <group resname="roles">
                <note>Pracovní pozice</note>
                <trans-unit id="role.title">
                    <source>role.title</source>
                    <target>Position</target>
                </trans-unit>    
                <trans-unit id="role_list">
                    <source>role_list</source>
                    <target>List of positions</target>
                </trans-unit> 
                <trans-unit id="role_create">
                    <source>role_create</source>
                    <target>Create a position </target>
                </trans-unit>    
                <trans-unit id="role_edit">
                    <source>role_edit</source>
                    <target>Adjust a position </target>
                </trans-unit>    
                <trans-unit id="role_show">
                    <source>role_show</source>
                    <target>Position detail </target>
                </trans-unit>    
                <trans-unit id="role_delete">
                    <source>role_delete</source>
                    <target>Delete a position </target>
                </trans-unit>                
                <trans-unit id="role_history">
                    <source>role_history</source>
                    <target>Position editing history </target>
                </trans-unit>                
                <trans-unit id="role.name">
                    <source>role.name</source>
                    <target>Position name</target>
                </trans-unit>
                <trans-unit id="role.position_rate">
                    <source>role.position_rate</source>
                    <target>Position rate</target>
                </trans-unit>
                <trans-unit id="role.billing_rate">
                    <source>role.billing_rate</source>
                    <target>Billing rate</target>
                </trans-unit>
                <trans-unit id="role.managerial_allowance">
                    <source>role.managerial_allowance</source>
                    <target>Management surcharge</target>
                </trans-unit>                
                <trans-unit id="role.visibility">
                    <source>role.visibility</source>
                    <target>Visible</target>
                </trans-unit>
                <trans-unit id="role.is_active">
                    <source>role.is_active</source>
                    <target>Active</target>
                </trans-unit>
                <trans-unit id="role.inactivate_at">
                    <source>role.inactivate_at</source>
                    <target>Inactive</target>
                </trans-unit>
                <trans-unit id="role.break_type">
                    <source>role.break_type</source>
                    <target>Break type</target>
                </trans-unit>
                <trans-unit id="role.billing_type">
                    <source>role.billing_type</source>
                    <target>Billing type</target>
                </trans-unit>
                <trans-unit id="role.visible">
                    <source>role.visible</source>
                    <target>Visible</target>
                </trans-unit>
                <trans-unit id="role.secret">
                    <source>role.secret</source>
                    <target>SM only</target>
                </trans-unit>
                <trans-unit id="role.paid_break">
                    <source>role.paid_break</source>
                    <target>Paid</target>
                </trans-unit>
                <trans-unit id="role.non_paid_break">
                    <source>role.non_paid_break</source>
                    <target>Not paid</target>
                </trans-unit>
                <trans-unit id="role.non_paid_billed_break">
                    <source>role.non_paid_billed_break</source>
                    <target>non paid, but billed</target>
                </trans-unit>                
                <trans-unit id="role.billing_by_attendance">
                    <source>role.billing_by_attendance</source>
                    <target>According to attendance</target>
                </trans-unit>
                <trans-unit id="role.billing_by_plan">
                    <source>role.billing_by_plan</source>
                    <target>According to plan</target>      
                </trans-unit>
                <trans-unit id="role.no_billing">
                    <source>role.no_billing</source>
                    <target>No billing</target>
                </trans-unit>       
                <trans-unit id="role.role_groups">
                    <source>role.role_groups</source>
                    <target>Group of positions </target>
                </trans-unit>
                <trans-unit id="reorder_start">
                    <source>reorder_start</source>
                    <target>Change the order</target>
                </trans-unit>
                <trans-unit id="reorder_confirm">
                    <source>reorder_confirm</source>
                    <target>Save</target>
                </trans-unit>
                <trans-unit id="role.department">
                    <source>role.department</source>
                    <target>Unit</target>
                </trans-unit>                
            </group>
            <group resname="roleGroups">
                <note>Skupiny prac. pozic</note>
                <trans-unit id="role_groups.title">
                    <source>role_groups.title</source>
                    <target>Position groups</target>
                </trans-unit>
                <trans-unit id="role_group_list">
                    <source>role_group_list</source>
                    <target>List of position groups </target>
                </trans-unit>   
                <trans-unit id="role_group_create">
                    <source>role_group_create</source>
                    <target>Create a position group </target>
                </trans-unit>    
                <trans-unit id="role_group_edit">
                    <source>role_group_edit</source>
                    <target>Adjust a position group </target>
                </trans-unit>    
                <trans-unit id="role_group_show">
                    <source>role_group_show</source>
                    <target>Position group detail  </target>
                </trans-unit>    
                <trans-unit id="role_group_delete">
                    <source>role_group_delete</source>
                    <target>Delete a position group </target>
                </trans-unit>                
                <trans-unit id="role_group_history">
                    <source>role_group_history</source>
                    <target>Position group editing history </target>
                </trans-unit>                
                <trans-unit id="role_group.name ">
                    <source>role_group.name</source>
                    <target>Group name</target>
                </trans-unit>
                <trans-unit id="role_group.description">
                    <source>role_group.description</source>
                    <target>Group description</target>
                </trans-unit>
                <trans-unit id="role_group.roles">
                    <source>role_group.roles</source>
                    <target>Position in group</target>
                </trans-unit>
            </group>
            
            <group resname="breaks">
                <note>Přestávky</note>
                <trans-unit id="attendance_break.title">
                    <source>attendance_break.title</source>
                    <target>Breaks</target>
                </trans-unit>
                <trans-unit id="break.starts_at">
                    <source>break.starts_at</source>
                    <target>Break from:</target>
                </trans-unit>
                <trans-unit id="break.ends_at">
                    <source>break.ends_at</source>
                    <target>Break until:</target>
                </trans-unit>
            </group>
            
            <group resname="contracts">
                <note>Smlouvy zaměstnanců a agentur</note>
                <trans-unit id="contract.title">
                    <source>contract.title</source>
                    <target>Contracts</target>
                </trans-unit>  
                <trans-unit id="contract_list">
                    <source>contract_list</source>
                    <target>List of contracts </target>
                </trans-unit>     
                <trans-unit id="contract_create">
                    <source>contract_create</source>
                    <target>Create a contract </target>
                </trans-unit>    
                <trans-unit id="contract_edit">
                    <source>contract_edit</source>
                    <target>Adjust a contract </target>
                </trans-unit>    
                <trans-unit id="contract_show">
                    <source>contract_show</source>
                    <target>Contract detail </target>
                </trans-unit>    
                <trans-unit id="contract_delete">
                    <source>contract_delete</source>
                    <target>Delete a contract</target>
                </trans-unit>                
                <trans-unit id="contract_history">
                    <source>contract_history</source>
                    <target>Contract editing history</target>
                </trans-unit>                
                <trans-unit id="contract.person">
                    <source>contract.person</source>
                    <target>Worker</target>
                </trans-unit>
                <trans-unit id="contract.agency">
                    <source>contract.agency</source>
                    <target>Agency</target>
                </trans-unit>
                <trans-unit id="contract.company">
                    <source>contract.company</source>
                    <target>Company</target>
                </trans-unit>
                <trans-unit id="contract.code_name">
                    <source>contract.object_code_name</source>
                    <target>(Code) object name</target>
                </trans-unit>
                <trans-unit id="contract.contract_subject_is_immutable">
                    <source>contract.contract_subject_is_immutable</source>
                    <target>Subject of the contract cannot be modified </target>
                </trans-unit>
                <trans-unit id="contract.message_type_not_selected">
                    <source>contract.message_type_not_selected</source>
                    <target>You need to select a contract type.</target>
                </trans-unit>
                <trans-unit id="contract.agency_hasnt_company">
                    <source>contract.agency_hasnt_company</source>
                    <target>Companies are not assigned to agencies. </target>
                </trans-unit>
                <trans-unit id="contract.type">
                    <source>contract.type</source>
                    <target>Contract type</target>
                </trans-unit>
                <trans-unit id="contract.subject">
                    <source>contract.subject</source>
                    <target>Worker/Agency</target>
                </trans-unit> 
                <trans-unit id="contract.employment">
                    <source>contract.employment</source>
                    <target>Full time employment </target>
                </trans-unit>
                <trans-unit id="contract.dpc">
                    <source>contract.dpc</source>
                    <target>Contract of services </target>
                </trans-unit>
                <trans-unit id="contract.tradesman">
                    <source>contract.tradesman</source>
                    <target>Self-employed person</target>
                </trans-unit> 
                <trans-unit id="contract.dpp">
                    <source>contract.dpp</source>
                    <target>Work agreement</target>
                </trans-unit>
                <trans-unit id="contract.external">
                    <source>contract.external</source>
                    <target>External cooperation</target>
                </trans-unit>
                <trans-unit id="contract.other">
                    <source>contract.other</source>
                    <target>Other</target>
                </trans-unit>
                <trans-unit id="contract.parallel">
                    <source>contract.parallel</source>
                    <target>Parallel work contract</target>
                </trans-unit> 
                <trans-unit id="contract.vpp">
                    <source>contract.vpp</source>
                    <target>Adjoining work contract</target>
                </trans-unit> 
                <trans-unit id="contract.short">
                    <source>contract.short</source>
                    <target>Reduced work contract max. 20 hours</target>
                </trans-unit>
                <trans-unit id="contract.short10">
                    <source>contract.short10</source>
                    <target>Reduced work contract max. 10 hours</target>
                </trans-unit>                
                <trans-unit id="contract.alert_title">
                    <source>contract.alert_title</source>
                    <target>Warning! This subject already has these contracts:</target>
                </trans-unit>
                <trans-unit id="contract.employment_abbr">
                    <source>contract.employment_abbr</source>
                    <target>MWC</target>
                </trans-unit>
                <trans-unit id="contract.dpc_abbr">
                    <source>contract.dpc_abbr</source>
                    <target>WA</target>
                </trans-unit>
                <trans-unit id="contract.tradesman_abbr">
                    <source>contract.tradesman_abbr</source>
                    <target>ID number</target>                    
                </trans-unit>         
                <trans-unit id="contract.agency_abbr">
                    <source>contract.agency_abbr</source>
                    <target>A</target>
                </trans-unit>
                <trans-unit id="contract.dpp_abbr">
                    <source>contract.dpp_abbr</source>
                    <target>WA</target>
                </trans-unit>
                <trans-unit id="contract.external_abbr">
                    <source>contract.external_abbr</source>
                    <target>EXT</target>
                </trans-unit>
                <trans-unit id="contract.other_abbr">
                    <source>contract.other_abbr</source>
                    <target>OTH</target>
                </trans-unit>
                <trans-unit id="contract.parallel_abbr">
                    <source>contract.parallel_abbr</source>
                    <target>PWC</target>
                </trans-unit>  
                <trans-unit id="contract.short_abbr">
                    <source>contract.short_abbr</source>
                    <target>RedWC</target>
                </trans-unit> 
                <trans-unit id="contract.short10_abbr">
                    <source>contract.short10_abbr</source>
                    <target>Part-time 10</target>
                </trans-unit>                 
                <trans-unit id="contract.vpp_abbr">
                    <source>contract.vpp_abbr</source>
                    <target>AdjWC</target>
                </trans-unit>  
                <trans-unit id="link_action_create_contract_subjects">
                    <source>link_action_create_contract_subjects</source>
                    <target>Create for subjects</target>
                </trans-unit>
                <trans-unit id="link_action_create_contract_objects">
                    <source>link_action_create_contract_objects</source>
                    <target>Create for objects</target>
                </trans-unit>     
                <trans-unit id="link_action_create_contract_object_groups">
                    <source>link_action_create_contract_object_groups</source>
                    <target>Create for group of objects</target>
                </trans-unit>                   
                <trans-unit id="contract_batch_create_success">
                    <source>contract_batch_create_success</source>
                    <target>{0} No contract has been created|{1} One contract has been created|[2,4] %count% contracts have been created|[5,Inf] %count% contracts have been created</target>
                </trans-unit>
                <trans-unit id="contract_batch_create_error">
                    <source>contract_batch_create_error</source>
                    <target>{0} No contract has been created. Not created: %errors% Errors: %message% |{1} One contract has been created. Not created: %errors% Errors: %message% |[2,4] %count% contracts have been created. Not created: %errors% Errors: %message%|[5,Inf] %count% contracts have been created. Not created: %errors% Errors: %message%</target>
                </trans-unit>   
                <trans-unit id="contract.subject_not_selected">
                    <source>contract.subject_not_selected</source>
                    <target>At least 1 subject has to be chosen</target>
                </trans-unit> 
                <trans-unit id="contract.object_not_selected">
                    <source>contract.object_not_selected</source>
                    <target>At least 1 object has to be chosen</target>
                </trans-unit>    
                <trans-unit id="contract.object_group_not_selected">
                    <source>contract.object_group_not_selected</source>
                    <target>At least 1 group of objects has to be chosen</target>
                </trans-unit>   
                <trans-unit id="contract.end_contracts_batch_dialog_title">
                    <source>contract.end_contracts_batch_dialog_title</source>
                    <target>Batch contracts ends</target>
                </trans-unit>    
                <trans-unit id="contract.end_contracts_batch_title">
                    <source>contract.end_contracts_batch_title</source>
                    <target>End contracts at</target>
                </trans-unit>   
                <trans-unit id="contract.end_contract_batch_flash_error_date">
                    <source>contract.end_contract_batch_flash_error_date</source>
                    <target>One or more contracts ends before it starts.</target>
                </trans-unit>   
                <trans-unit id="contract.end_contract_batch_flash_error_nodate">
                    <source>contract.end_contract_batch_flash_error_nodate</source>
                    <target>Not selected date of ends.</target>
                </trans-unit>    
                <trans-unit id="contract.btn_check">
                    <source>contract.btn_check</source>
                    <target>Check</target>
                </trans-unit>                 
                <trans-unit id="contract.check_title">
                    <source>contract.check_title</source>
                    <target>Contract check</target>
                </trans-unit>   
                <trans-unit id="contract.check_message">
                    <source>contract.check_message</source>
                    <target>Status</target>
                </trans-unit> 
                <trans-unit id="contract.externCoopA">
                    <source>contract.externCoopA</source>
                    <target>Voluntary A</target>
                </trans-unit>  
                <trans-unit id="contract.externCoopB">
                    <source>contract.externCoopB</source>
                    <target>Voluntary B</target>
                </trans-unit>  
                <trans-unit id="contract.weeklyTimeJobPerc">
                    <source>contract.weeklyTimeJobPerc</source>
                    <target>Part-time employment</target>
                </trans-unit>                                                                                                             
            </group>
            
            <group resname="subjectGroups">
                <note>Skupiny subjektů</note>
                <trans-unit id="subject_group.title">
                    <source>subject_group.title</source>
                    <target>Groups of persons</target>
                </trans-unit>  
                <trans-unit id="subject_group_list">
                    <source>subject_group_list</source>
                    <target>List of groups of persons</target>
                </trans-unit>  
                <trans-unit id="subject_group_create">
                    <source>subject_group_create</source>
                    <target>Create a group of persons </target>
                </trans-unit>    
                <trans-unit id="subject_group_edit">
                    <source>subject_group_edit</source>
                    <target>Adjust a group of persons </target>
                </trans-unit>    
                <trans-unit id="subject_group_show">
                    <source>subject_group_show</source>
                    <target>Detail of group of persons </target>
                </trans-unit>    
                <trans-unit id="subject_group_delete">
                    <source>subject_group_delete</source>
                    <target>Delete a group of persons </target>
                </trans-unit>                
                <trans-unit id="subject_group_history">
                    <source>subject_group_history</source>
                    <target>Group of persons editing history  </target>
                </trans-unit> 
                <trans-unit id="subject_group.subjects">
                    <source>subject_group.subjects</source>
                    <target>Groups </target>
                </trans-unit>              
                <trans-unit id="subject_group.name">
                    <source>subject_group.name</source>
                    <target>Group name</target>
                </trans-unit>              
                <trans-unit id="subject_group.count">
                    <source>subject_group.count</source>
                    <target>Persons</target>
                </trans-unit>               
            </group>
            
            <group resname="subjects">
                <note>Subjekty</note>             
                <trans-unit id="subject.title">
                    <source>subject.title</source>
                    <target>Persons</target>
                </trans-unit> 
                <trans-unit id="subject_list">
                    <source>subject_list</source>
                    <target>List of persons</target>
                </trans-unit>
                <trans-unit id="subject_create">
                    <source>subject_create</source>
                    <target>Create a person </target>
                </trans-unit>    
                <trans-unit id="subject_edit">
                    <source>subject_edit</source>
                    <target>Adjust a person </target>
                </trans-unit>    
                <trans-unit id="subject_show">
                    <source>subject_show</source>
                    <target>Person's details</target>
                </trans-unit>    
                <trans-unit id="subject_delete">
                    <source>subject_delete</source>
                    <target>Delete a person </target>
                </trans-unit>                
                <trans-unit id="subject_history">
                    <source>subject_history</source>
                    <target>Person editing history</target>
                </trans-unit>                     
            </group>
            
            
            <group resname="periond">
                <note>Období (účetní a pod.)</note>
                <trans-unit id="period.title">
                    <source>period.title</source>
                    <target>Period</target>
                </trans-unit>  
                <trans-unit id="period_list">
                    <source>period_list</source>
                    <target>Period list</target>
                </trans-unit> 
                <trans-unit id="period_create">
                    <source>period_create</source>
                    <target>Create a period</target>
                </trans-unit>    
                <trans-unit id="period_edit">
                    <source>period_edit</source>
                    <target>Adjust a period</target>
                </trans-unit>    
                <trans-unit id="period_show">
                    <source>period_show</source>
                    <target>Period detail</target>
                </trans-unit>    
                <trans-unit id="period_delete">
                    <source>period_delete</source>
                    <target>Delete a period </target>
                </trans-unit>                
                <trans-unit id="period_history">
                    <source>period_history</source>
                    <target>Period editing history </target>
                </trans-unit>                
                <trans-unit id="period.object">
                    <source>period.object</source>
                    <target>Object</target>
                </trans-unit>
                <trans-unit id="period.objects">
                    <source>period.objects</source>
                    <target>Objects</target>
                </trans-unit>
                <trans-unit id="period.type">
                    <source>period.type</source>
                    <target>Period type</target>
                </trans-unit>
                <trans-unit id="period.billing_type">
                    <source>period.billing_type</source>
                    <target>Billing type</target>
                </trans-unit>
                <trans-unit id="period.salary_type">
                    <source>period.salary_type</source>
                    <target>Salary type</target>
                </trans-unit>
                <trans-unit id="period.is_overlaped">
                    <source>period.is_overlaped</source>
                    <target>Inputted period overlaps another one on the assigned objects.</target>
                </trans-unit>
            </group>
            <group resname="periodicPayments">
                <note>Pravidelné platby a jejich nastavení</note>
                <trans-unit id="periodic_payment.title">
                    <source>periodic_payment.title</source>
                    <target>Periodic payments </target>
                </trans-unit>
                <trans-unit id="periodic_payment_list">
                    <source>periodic_payment_list</source>
                    <target>List of periodic payments </target>
                </trans-unit>
                <trans-unit id="periodic_payment_create">
                    <source>periodic_payment_create</source>
                    <target>Create a periodic payment</target>
                </trans-unit>    
                <trans-unit id="periodic_payment_edit">
                    <source>periodic_payment_edit</source>
                    <target>Adjust a periodic payment</target>
                </trans-unit>    
                <trans-unit id="periodic_payment_show">
                    <source>periodic_payment_show</source>
                    <target>Periodic payment detail</target>
                </trans-unit>    
                <trans-unit id="periodic_payment_delete">
                    <source>periodic_payment_delete</source>
                    <target>Delete a periodic payment </target>
                </trans-unit>                
                <trans-unit id="periodic_payment_history">
                    <source>periodic_payment_history</source>
                    <target>Periodic payment editing history</target>
                </trans-unit>                
                <trans-unit id="periodic_payment.product">
                    <source>periodic_payment.product</source>
                    <target>Product</target>
                </trans-unit>
                <trans-unit id="periodic_payment.paymentType">
                    <source>periodic_payment.paymentType</source>
                    <target>Payment type </target>
                </trans-unit>
                <trans-unit id="periodic_payment.amount">
                    <source>periodic_payment.amount</source>
                    <target>Amount </target>
                </trans-unit>
                <trans-unit id="periodic_payment.period">
                    <source>periodic_payment.period</source>
                    <target>Period</target>
                </trans-unit>
                <trans-unit id="periodic_payment.approved">
                    <source>periodic_payment.approved</source>
                    <target>Status</target>
                </trans-unit>   
                <trans-unit id="periodic_payment.approved_filter">
                    <source>periodic_payment.approved_filter</source>
                    <target>Approved?</target>
                </trans-unit>              
                <trans-unit id="periodic_payment.note">
                    <source>periodic_payment.note</source>
                    <target>Note</target>
                </trans-unit>   
                <trans-unit id="periodic_payment.note_help">
                    <source>periodic_payment.note_help</source>
                    <target>Reflects in billing materials.</target>
                </trans-unit>                   
                <trans-unit id="periodic_payment.is_approved">
                    <source>periodic_payment.is_approved</source>
                    <target>Approved</target>
                </trans-unit>
                <trans-unit id="periodic_payment.not_approved">
                    <source>periodic_payment.not_approved</source>
                    <target>Not approved</target>
                </trans-unit>            
                <trans-unit id="periodic_payment.flash_approbate">
                    <source>periodic_payment.flash_approbate</source>
                    <target>Selected periodic payment has been approved.</target>
                </trans-unit>  
                <trans-unit id="periodic_payment.flash_with_draw_approval">
                    <source>periodic_payment.flash_with_draw_approval</source>
                    <target>Selected periodic payment has been marked as not approved.</target>
                </trans-unit>  
                <trans-unit id="periodic_payment.flash_error_is_exported">
                    <source>periodic_payment.flash_error_is_exported</source>
                    <target>The operation cannot be completed, periodical payment has already been exported</target>
                </trans-unit>
                <trans-unit id="periodic_payment_batch_unblock">
                    <source>periodic_payment_batch_unblock</source>
                    <target>Unblock after export</target>
                </trans-unit>
            </group>
            
            <group resname="paymentTypesSelect">
                <note>Výběr typu platby</note>
                <trans-unit id="periodic_payment.choose_periodic_payment">
                    <source>periodic_payment.choose_periodic_payment</source>
                    <target>-- Select a payment type --</target>
                </trans-unit>
                <trans-unit id="lump_sum_reward">
                    <source>lump_sum_reward</source>
                    <target>reward</target>
                </trans-unit>
                <trans-unit id="lump_sum_penalty">
                    <source>lump_sum_penalty</source>
                    <target>penalty</target>
                </trans-unit>
                <trans-unit id="recurring_reward">
                    <source>recurring_reward</source>
                    <target>repetitive reward</target>
                </trans-unit>
                <trans-unit id="recurring_penalty">
                    <source>recurring_penalty</source>
                    <target>repetitive penalty</target>
                </trans-unit>
            </group>
            
            
            
            <group resname="positionRates">
                <note>Poziční sazby</note>
                <trans-unit id="position_rate.title">
                    <source>position_rate.title</source>
                    <target>Position rates </target>
                </trans-unit>  
                <trans-unit id="position_rate_list">
                    <source>position_rate_list</source>
                    <target>List of position rates </target>
                </trans-unit>   
                <trans-unit id="position_rate_create">
                    <source>position_rate_create</source>
                    <target>Create a position rate </target>
                </trans-unit>    
                <trans-unit id="position_rate_edit">
                    <source>position_rate_edit</source>
                    <target>Adjust a position rate </target>
                </trans-unit>    
                <trans-unit id="position_rate_show">
                    <source>position_rate_show</source>
                    <target>Position rate detail </target>
                </trans-unit>    
                <trans-unit id="position_rate_delete">
                    <source>position_rate_delete</source>
                    <target>Delete a position rate </target>
                </trans-unit>                
                <trans-unit id="position_rate_history">
                    <source>position_rate_history</source>
                    <target>Position rate editing history  </target>
                </trans-unit>                
            </group>
            <group resname="personRates">
                <note>Osobní sazby</note>
                <trans-unit id="person_rates.title">
                    <source>person_rates.title</source>
                    <target>Person rates </target>
                </trans-unit>
                <trans-unit id="person_rate_list">
                    <source>persons_rate_list</source>
                    <target>List of person rates</target>
                </trans-unit>
                <trans-unit id="person_rate_create">
                    <source>persons_rate_create</source>
                    <target>Create a person rate</target>
                </trans-unit>    
                <trans-unit id="person_rate_edit">
                    <source>persons_rate_edit</source>
                    <target>Adjust a person rate</target>
                </trans-unit>    
                <trans-unit id="person_rate_show">
                    <source>persons_rate_show</source>
                    <target>Person rate detail</target>
                </trans-unit>    
                <trans-unit id="person_rate_delete">
                    <source>persons_rate_delete</source>
                    <target>Delete a person rate</target>
                </trans-unit>                
                <trans-unit id="person_rate_history">
                    <source>persons_rate_history</source>
                    <target>Person rate editing history </target>
                </trans-unit>        
                <trans-unit id="person_rate.rate">
                    <source>person_rate.rate</source>
                    <target>Person rate </target>
                </trans-unit>
                <trans-unit id="person_rate.starts_at">
                    <source>person_rate.starts_at</source>
                    <target>Valid from</target>
                </trans-unit>
                <trans-unit id="person_rate.ends_at">
                    <source>person_rate.ends_at</source>
                    <target>Valid until</target>
                </trans-unit>  
                <trans-unit id="person_rate.person_rate_subject_is_immutable">
                    <source>person_rate.person_rate_subject_is_immutable</source>
                    <target>Assigned person cannot be modified</target>
                </trans-unit> 
                <trans-unit id="person_rate.error_starts_before_ends">
                    <source>person_rate.error_starts_before_ends</source>
                    <target>The end of the period starts before the beginning</target>
                </trans-unit>            
            </group>
            <group resname="agencyRates">
                <note>Agenturní sazby</note>
                <trans-unit id="agency_rate.title">
                    <source>agency_rate.title</source>
                    <target>Agency rates </target>
                </trans-unit>  
                <trans-unit id="agency_rate_list">
                    <source>agency_rate_list</source>
                    <target>List of agency rates </target>
                </trans-unit>    
                <trans-unit id="agency_rate_create">
                    <source>agency_rate_create</source>
                    <target>Create an agency rate </target>
                </trans-unit>    
                <trans-unit id="agency_rate_edit">
                    <source>agency_rate_edit</source>
                    <target>Adjust an agency rate </target>
                </trans-unit>    
                <trans-unit id="agency_rate_show">
                    <source>agency_rate_show</source>
                    <target>Agency rate detail </target>
                </trans-unit>    
                <trans-unit id="agency_rate_delete">
                    <source>agency_rate_delete</source>
                    <target>Delete an agency rate</target>
                </trans-unit>                
                <trans-unit id="agency_rate_history">
                    <source>agency_rate_history</source>
                    <target>Agency rate editing history </target>
                </trans-unit>             
                <trans-unit id="agency_rate.rate">
                    <source>agency_rate.rate</source>
                    <target>Agency rate </target>
                </trans-unit>  
                <trans-unit id="agency_rate.role">
                    <source>agency_rate.role</source>
                    <target>Position</target>
                </trans-unit>  
                <trans-unit id="agency_rate.agency_rate_subject_is_immutable">
                    <source>agency_rate.person_rate_subject_is_immutable</source>
                    <target>Assigned person cannot be modified</target>
                </trans-unit>  
                <trans-unit id="agency_rate.positions_label">
                    <source>agency_rate.positions_label</source>
                    <target>Create rate for</target>
                </trans-unit>
                <trans-unit id="agency_rate.all_positions">
                    <source>agency_rate.all_positions</source>
                    <target>All positions</target>
                </trans-unit>
                <trans-unit id="agency_rate.position_group">
                    <source>agency_rate.position_group</source>
                    <target>Position group</target>
                </trans-unit>       
            </group>
            <group resname="individualRates">
                <note>Individuální sazby</note>
                <trans-unit id="individual_rate">
                    <source>individual_rate</source>
                    <target>Individual rates </target>
                </trans-unit>
                <trans-unit id="individual_rate_list">
                    <source>individual_rate_list</source>
                    <target>List of individual rates </target>
                </trans-unit>
                <trans-unit id="individual_rate_create">
                    <source>individual_rate_create</source>
                    <target>Create an individual rate</target>
                </trans-unit>    
                <trans-unit id="individual_rate_edit">
                    <source>individual_rate_edit</source>
                    <target>Adjust an individual rate</target>
                </trans-unit>    
                <trans-unit id="individual_rate_show">
                    <source>individual_rate_show</source>
                    <target>Individual rate detail</target>
                </trans-unit>    
                <trans-unit id="individual_rate_delete">
                    <source>individual_rate_delete</source>
                    <target>Delete an individual rate</target>
                </trans-unit>                
                <trans-unit id="individual_rate_history">
                    <source>individual_rate_history</source>
                    <target>Individual rate editing history</target>
                </trans-unit>           
                <trans-unit id="individual_rate.rate">
                    <source>individual_rate.rate</source>
                    <target>Individual rate </target>
                </trans-unit>
                <trans-unit id="individual_rate.position_is_immutable">
                    <source>individual_rate.position_is_immutable</source>
                    <target>Assigned position cannot be modified </target>
                </trans-unit> 
                <trans-unit id="individual_rate_hired_create">
                    <source>individual_rate_hired_create</source>
                    <target>Create individual rate - hired person</target>
                </trans-unit>
                <trans-unit id="individual_rate_hired_edit">
                    <source>individual_rate_hired_edit</source>
                    <target>Edit individual rate - hired person</target>
                </trans-unit>
                <trans-unit id="create_for_hired_button">
                    <source>create_for_hired_button</source>
                    <target>Create rate for hired persons</target>
                </trans-unit>
            </group>
            <group resname="contractRate">
                <note>Sazby dle smlouvy</note>
                <trans-unit id="contract_rate.title">
                    <source>contract_rate.title</source>
                    <target>Contract rate</target>
                </trans-unit>
                <trans-unit id="contract_rate_list">
                    <source>contract_rate_list</source>
                    <target>List of rates according to the contract</target>
                </trans-unit>
                <trans-unit id="contract_rate_create">
                    <source>contract_rate_create</source>
                    <target>Create a rate according to the contract</target>
                </trans-unit>    
                <trans-unit id="contract_rate_edit">
                    <source>contract_rate_edit</source>
                    <target>Adjust the rate according to the contract</target>
                </trans-unit>    
                <trans-unit id="contract_rate_show">
                    <source>contract_rate_show</source>
                    <target>Detail of the rate according to the contract</target>
                </trans-unit>    
                <trans-unit id="contract_rate_delete">
                    <source>contract_rate_delete</source>
                    <target>Remove the rate according to the contract</target>
                </trans-unit>                
                <trans-unit id="contract_rate_history">
                    <source>contract_rate_history</source>
                    <target>History of rate adjustments according to the contract</target>
                </trans-unit>        
                <trans-unit id="contract_rate.rate">
                    <source>contract_rate.rate</source>
                    <target>Contract rate</target>
                </trans-unit>
                <trans-unit id="contract_rate.time_wage">
                    <source>contract_rate.time_wage</source>
                    <target>Time wage</target>
                </trans-unit>
                <trans-unit id="contract_rate.personal_evaluation">
                    <source>contract_rate.personal_evaluation</source>
                    <target>Personal evaluation</target>
                </trans-unit>                                
                <trans-unit id="contract_rate.starts_at">
                    <source>contract_rate.starts_at</source>
                    <target>Effective date</target>
                </trans-unit>
                <trans-unit id="contract_rate.ends_at">
                    <source>contract_rate.ends_at</source>
                    <target>Expiration date</target>
                </trans-unit>  
                <trans-unit id="contract_rate.person_rate_subject_is_immutable">
                    <source>contract_rate.person_rate_subject_is_immutable</source>
                    <target>The assigned person cannot be changed</target>
                </trans-unit> 
                <trans-unit id="contract_rate.error_starts_before_ends">
                    <source>contract_rate.error_starts_before_ends</source>
                    <target>The end of the period is earlier than the beginning</target>
                </trans-unit>            
            </group>
            
            <group resname="objectGroups">
                <note>Skupiny objektů</note>
                <trans-unit id="object_group.title">
                    <source>object_group.title</source>
                    <target>Groups of objects</target>
                </trans-unit>
                <trans-unit id="object_group_list">
                    <source>object_group_list</source>
                    <target>Group of objects</target>
                </trans-unit>             
                <trans-unit id="object_group_create">
                    <source>object_group_create</source>
                    <target>Create a group of objects </target>
                </trans-unit>    
                <trans-unit id="object_group_edit">
                    <source>object_group_edit</source>
                    <target>Adjust a group of objects </target>
                </trans-unit>    
                <trans-unit id="object_group_show">
                    <source>object_group_show</source>
                    <target>Group of objects detail</target>
                </trans-unit>    
                <trans-unit id="object_group_delete">
                    <source>object_group_delete</source>
                    <target>Delete a group of objects</target>
                </trans-unit>                
                <trans-unit id="object_group_history">
                    <source>object_group_history</source>
                    <target>Group of objects editing history </target>
                </trans-unit>             
                <trans-unit id="object_group.name">
                    <source>object_group.name</source>
                    <target>Group name</target>
                </trans-unit>
                <trans-unit id="object_group.count">
                    <source>object_group.count</source>
                    <target>Number of objects</target>
                </trans-unit>
            </group>
            
            
            <group resname="calendar">
                <note>Kalendář pro nastavení svátků</note>            
                <trans-unit id="calendar.title">
                    <source>calendar.title</source>
                    <target>Calendar</target>
                </trans-unit>
                <trans-unit id="year">
                    <source>year</source>
                    <target>Year</target>
                </trans-unit> 
                <trans-unit id="calendar">
                    <source>calendar</source>
                    <target>Calendar</target>
                </trans-unit>           
            </group>
            <group resname="service_editor">
                <note>Service editors texts</note>
                <trans-unit id="service">
                    <source>service</source>
                    <target>Service</target>
                </trans-unit>                
                   
                <trans-unit id="service.service">
                    <source>service.service</source>
                    <target>Service</target>
                </trans-unit>
                <trans-unit id="service_edit_edit">
                    <source>service_edit_edit</source>
                    <target>Assigning a person to service </target>
                </trans-unit>
                <trans-unit id="service_billing_create">
                    <source>service_billing_create</source>
                    <target>Service creation</target>
                </trans-unit>
                <trans-unit id="service_billing_edit">
                    <source>service_billing_edit</source>
                    <target>Setting the service times </target>
                </trans-unit>
                <trans-unit id="service_billing_delete">
                    <source>service_billing_delete</source>
                    <target>Delete a service</target>
                </trans-unit>
                <trans-unit id="service.starts_at">
                    <source>service.starts_at</source>
                    <target>Service start</target>
                </trans-unit>
                <trans-unit id="service.ends_at">
                    <source>service.ends_at</source>
                    <target>Service end</target>
                </trans-unit>
                <trans-unit id="service_billing_list">
                    <source>service_billing_list</source>
                    <target>Service modification</target>
                </trans-unit>           
                <trans-unit id="service.position">
                    <source>service.position</source>
                    <target>Position</target>
                </trans-unit>
                <trans-unit id="date/time">
                    <source>date/time</source>
                    <target>Date/time</target>
                </trans-unit>
                <trans-unit id="service.service_billing">
                    <source>service.service_billing</source>
                    <target>Adjust a service </target>
                </trans-unit> 
                <trans-unit id="service_edit_list">
                    <source>service_edit_list</source>
                    <target>Editor of services </target>
                </trans-unit>
                <trans-unit id="service_action_batch_edit">
                    <source>service_action_batch_edit</source>
                    <target>Set times</target>
                </trans-unit>
                <trans-unit id="service_action_batch_billing_rates_update">
                    <source>service_action_batch_billing_rates_update</source>
                    <target>Update billing rates</target>
                </trans-unit>
                <trans-unit id="service.remove_subject">
                    <source>service.remove_subject</source>
                    <target>Remove a person from service</target>
                </trans-unit>
                <trans-unit id="service.service_edit">
                    <source>service.service_edit</source>
                    <target>Change a person </target>
                </trans-unit>
                <trans-unit id="service.subject">
                    <source>service.subject</source>
                    <target>Person / agency </target>
                </trans-unit>
                <trans-unit id="service.empty_day">
                    <source>service.empty_day</source>
                    <target>No service</target>
                </trans-unit>
                <trans-unit id="service.not_occupied">
                    <source>service.not_occupied</source>
                    <target>Not occupied </target>
                </trans-unit>
                <trans-unit id="service.person">
                    <source>service.person</source>
                    <target>Person</target>
                </trans-unit>
                <trans-unit id="service.service_date">
                    <source>service.service_date</source>
                    <target>Date and time of a service start</target>
                </trans-unit>
                <trans-unit id="service.billing_batch_dialog_title">
                    <source>service.billing_batch_dialog_title</source>
                    <target>Setting of a time of services</target>
                </trans-unit>
                <trans-unit id="service.change_person_batch_dialog_title">
                    <source>service.change_person_batch_dialog_title</source>
                    <target>Subject selection to chosen services</target>
                </trans-unit>
                <trans-unit id="service.change_person">
                    <source>service.change_person</source>
                    <target>Assign a person</target>
                </trans-unit>
                <trans-unit id="service.remove_person">
                    <source>service.remove_person</source>
                    <target>Remove persons from services</target>
                </trans-unit>
                <trans-unit id="service.subject_hire_show">
                    <source>service.subject_hire_show</source>
                    <target>Persons from other objects</target>
                </trans-unit>                
                <trans-unit id="service.date">
                    <source>service.date</source>
                    <target>Date of a service</target>
                </trans-unit>
                <trans-unit id="service.attendance">
                    <source>service.attendance</source>
                    <target>Attendance</target>
                </trans-unit>
                <trans-unit id="service.schedule">
                    <source>service.schedule</source>
                    <target>Plan</target>
                </trans-unit>
                <trans-unit id="service.shift">
                    <source>service.shift</source>
                    <target>Shift</target>
                </trans-unit>
                <trans-unit id="service_attendance_edit">
                    <source>service_attendance_edit</source>
                    <target>Editing of attendance</target>
                </trans-unit>
                <trans-unit id="service_attendance_delete">
                    <source>service_attendance_delete</source>
                    <target>Delete an attendance</target>
                </trans-unit>
                <trans-unit id="service.period">
                    <source>service.period</source>
                    <target>Period</target>
                </trans-unit>
                <trans-unit id="service.create_attendance">
                    <source>service.create_attendance</source>
                    <target>Input a new attendance</target>
                </trans-unit>
                <trans-unit id="service.approved">
                    <source>service.approved</source>
                    <target>APPROVED</target>
                </trans-unit>                
                <trans-unit id="service.set_correction_batch_dialog_title">
                    <source>service.set_correction_batch_dialog_title</source>
                    <target>Batch correction of a time of attendance</target>
                </trans-unit>
                <trans-unit id="attendance_batch_time_correction">
                    <source>attendance.batch_time_correction</source>
                    <target>Time correction</target>
                </trans-unit>
                <trans-unit id="btn_set_by_service">
                    <source>btn_set_by_service</source>
                    <target>According to a plan</target>
                </trans-unit>
                <trans-unit id="service_attendance_create">
                    <source>service_attendance_create</source>
                    <target>Attendance input</target>
                </trans-unit>
                <trans-unit id="service_attendance_list">
                    <source>service_attendance_list</source>
                    <target>Attendance editing and approval</target>
                </trans-unit>
                <trans-unit id="service_for_attendance_edit">
                    <source>service_for_attendance_edit</source>
                    <target>Input a new attendance to a service</target>
                </trans-unit>
                <trans-unit id="service_action_batch_approve_services">
                    <source>service_action_batch_approve_services</source>
                    <target>Approve services</target>
                </trans-unit>
                <trans-unit id="service_action_batch_unapprove_services">
                    <source>service_action_batch_unapprove_services</source>
                    <target>Disapprove services</target>
                </trans-unit>
                <trans-unit id="service_action_batch_approve_attendances">
                    <source>service_action_batch_approve_attendances</source>
                    <target>Approve attendance</target>
                </trans-unit>
                <trans-unit id="service_action_batch_unapprove_attendances">
                    <source>service_action_batch_unapprove_attendances</source>
                    <target>Disapprove attendance</target>
                </trans-unit>
                <trans-unit id="service_attendance_show">
                    <source>service_attendance_show</source>
                    <target>Attendance detail</target>
                </trans-unit>
                <trans-unit id="service_edit_print">
                    <source>service_edit_print</source>
                    <target>Object service schedule</target>
                </trans-unit>  
                <trans-unit id="service_edit_print.month">
                    <source>service_edit_print.month</source>
                    <target>in a month</target>
                </trans-unit>  
                <trans-unit id="service_edit_print.period">
                    <source>service_edit_print.period</source>
                    <target>in a period</target>
                </trans-unit> 
                <trans-unit id="service.person_in_shift_is_overlaped">
                    <source>service.person_in_shift_is_overlaped</source>
                    <target>Service times of a person overlap. They overlap with %object% object</target>
                </trans-unit>  
                <trans-unit id="attendance.person_in_attendance_is_overlaped">
                    <source>attendance.person_in_attendance_is_overlaped</source>
                    <target>Attendance times of a person overlap. They overlap with %object% object</target>
                </trans-unit>                 
                <trans-unit id="subject_select.agencies">
                    <source>subject_select.agencies</source>
                    <target>Agencies</target>
                </trans-unit>     
                <trans-unit id="service.flash_no_contract_or_rate">
                    <source>service.flash_no_contract_or_rate</source>
                    <target>Some services haven't attached subjects, as they don't have valid contracts or tariffs to these services</target>
                </trans-unit>  
                <trans-unit id="service_attendance.empty_subject_error">
                    <source>service_attendance.empty_subject_error</source>
                    <target>You have to choose a person to attendance</target>
                </trans-unit>      
                <trans-unit id="attendance.create_by_plan">
                    <source>attendance.create_by_plan</source>
                    <target>Create attendance according to a plan</target>
                </trans-unit>  
                <trans-unit id="service_attendance.delete">
                    <source>service_attendance.delete</source>
                    <target>Delete attendancies</target>
                </trans-unit>
                <trans-unit id="service_attendance_remove_attendance">
                    <source>service_attendance_remove_attendance</source>
                    <target>Deleting an attendance</target>
                </trans-unit>
                <trans-unit id="service_attendance.subject_rate_update">
                    <source>service_attendance.subject_rate_update</source>
                    <target>Update personal rates at attendances</target>
                </trans-unit>
                <trans-unit id="service_attendance.unapproved_attendances_in_past">
                    <source>service_attendance.unapproved_attendances_in_past</source>
                    <target>Can't be approved, there exists non-approved attendancies in last periods.</target>
                </trans-unit>
                <trans-unit id="service_attendance.unfinished_attendance">
                    <source>service_attendance.unfinished_attendance</source>
                    <target>An attendance cannot be created. Service contains unfinished attendance!</target>
                </trans-unit> 
                <trans-unit id="service_attendance.unfinished_attendance_approved">
                    <source>service_attendance.unfinished_attendance_approved</source>
                    <target>An attendance can't be approved. Its not finished.</target>
                </trans-unit>
                <trans-unit id="service_action_batch_unblock_services">
                    <source>service_action_batch_unblock_services</source>
                    <target>Billing unblock</target>
                </trans-unit>
                <trans-unit id="service_attendance.unblock_attendances">
                    <source>service_attendance.unblock_attendances</source>
                    <target>Attendance unblock</target>
                </trans-unit>  
                <trans-unit id="batch_error_partial">
                    <source>batch_error_partial</source>
                    <target>Some items have errors:</target>
                </trans-unit>
                <trans-unit id="service.update_product">
                    <source>service.update_product</source>
                    <target>Product update</target>
                </trans-unit>  
                <trans-unit id="service.overlap_conflict_flash">
                    <source>service.overlap_conflict_flash</source>
                    <target>Some attendances do overlap!</target>
                </trans-unit>  
                <trans-unit id="service_edit_batch">
                    <source>service_edit_batch</source>
                    <target>Selection of subject to selected services</target>
                </trans-unit>  
                <trans-unit id="service.batch_no_service">
                    <source>service.batch_no_service</source>
                    <target>not for services</target>
                </trans-unit>                  
            </group>
            <group resname="events">
                <note>Události ve službě</note>
                <trans-unit id="event.title">
                    <source>event.title</source>
                    <target>Event</target>
                </trans-unit>
                <trans-unit id="event.created">
                    <source>event.created</source>
                    <target>Event created </target>
                </trans-unit>
                <trans-unit id="event.renamed">
                    <source>event.renamed</source>
                    <target>Event renamed </target>
                </trans-unit>
                <trans-unit id="event.create_new">
                    <source>event.create_new</source>
                    <target>Create a new event </target>
                </trans-unit>
                <trans-unit id="event.rename">
                    <source>event.rename</source>
                    <target>Rename event </target>
                </trans-unit>
                <trans-unit id="event_list">
                    <source>event_list</source>
                    <target>Event list</target>
                </trans-unit>
                <trans-unit id="event.is_active_saved">
                    <source>event.is_active_saved</source>
                    <target>Option saved</target>
                </trans-unit>
                <trans-unit id="event.is_hidden_saved">
                    <source>event.is_hidden_saved</source>
                    <target>The choice has been saved</target>
                </trans-unit>                
                <trans-unit id="event.activate">
                    <source>event.activate</source>
                    <target>Activate </target>
                </trans-unit>
                <trans-unit id="event.deactivate">
                    <source>event.deactivate</source>
                    <target>Deactivate </target>
                </trans-unit>
                <trans-unit id="event.hide">
                    <source>event.hide</source>
                    <target>Hide an event</target>
                </trans-unit>
                <trans-unit id="event.show">
                    <source>event.show</source>
                    <target>Make an event visible</target>
                </trans-unit>                
                <trans-unit id="event.search_title">
                    <source>event.search_title</source>
                    <target>Find from the list of events </target>
                </trans-unit>
            </group>
            <group resname="reporting">
                <note>Nastavení hlášení pro zákazníka</note>
                <trans-unit id="reporting.title">
                    <source>reporting.title</source>
                    <target>Reporting setting</target>
                </trans-unit>
                <trans-unit id="reporting_edit">
                    <source>reporting_edit</source>
                    <target>Adjust reporting setting</target>
                </trans-unit>
                <trans-unit id="reporting_list">
                    <source>reporting_list</source>
                    <target>Reportings</target>
                </trans-unit>
                <trans-unit id="reporting_setting.none">
                    <source>reporting_setting.none</source>
                    <target>No sending</target>
                </trans-unit>           
                <trans-unit id="reporting_setting.sms">
                    <source>reporting_setting.sms</source>
                    <target>SMS</target>
                </trans-unit>
                <trans-unit id="reporting_setting.email">
                    <source>reporting_setting.email</source>
                    <target>E-mail</target>
                </trans-unit>
                <trans-unit id="reporting_setting.notification">
                    <source>reporting_setting.notification</source>
                    <target>Notification</target>
                </trans-unit>
                <trans-unit id="reporting_setting.smsemail">
                    <source>reporting_setting.smsemail</source>
                    <target>SMS + Email</target>
                </trans-unit>
                <trans-unit id="reporting_setting.smsnotification">
                    <source>reporting_setting.smsnotification</source>
                    <target>SMS + Notification</target>
                </trans-unit>
                <trans-unit id="reporting_setting.emailnotification">
                    <source>reporting_setting.emailnotification</source>
                    <target>Email + Notification</target>
                </trans-unit>
                <trans-unit id="reporting_setting.smsemailnotification">
                    <source>reporting_setting.smsemailnotification</source>
                    <target>Email + Notification</target>
                </trans-unit>
                <trans-unit id="reporting_setting.all">
                    <source>reporting_setting.all</source>
                    <target>SMS + E-mail</target>
                </trans-unit>  
                <trans-unit id="reporting.reporting_create">
                    <source>reporting_create</source>
                    <target>Create a new reporting setting </target>
                </trans-unit>
                <trans-unit id="reporting.object">
                    <source>reporting.object</source>
                    <target>Object</target>
                </trans-unit>
                <trans-unit id="reporting.subject">
                    <source>reporting.subject</source>
                    <target>Subject</target>
                </trans-unit>
                <trans-unit id="reporting.event">
                    <source>reporting.event</source>
                    <target>Event</target>
                </trans-unit>                
                <trans-unit id="reporting.object_group">
                    <source>reporting.object_group</source>
                    <target>Group of objects</target>
                </trans-unit>
                <trans-unit id="reporting.subject_group">
                    <source>reporting.subject_group</source>
                    <target>Group of subjects </target>
                </trans-unit>
                <trans-unit id="reporting.choose_objects">
                    <source>reporting.choose_objects</source>
                    <target>Select an object or an object group </target>
                </trans-unit>
                <trans-unit id="reporting.choose_subjects">
                    <source>reporting.choose_subjects</source>
                    <target>Select a subject or a subject group </target>
                </trans-unit>
                <trans-unit id="reporting.open_branch">
                    <source>reporting.open_branch</source>
                    <target>Unroll the branch</target>
                </trans-unit>
                <trans-unit id="reporting.close_branch">
                    <source>reporting.close_branch</source>
                    <target>Roll the branch</target>
                </trans-unit>
                <trans-unit id="reporting.template">
                    <source>reporting.template</source>
                    <target>Set according to the template</target>
                </trans-unit>
                <trans-unit id="reporting.template2">
                    <source>reporting.template2</source>
                    <target>Template</target>
                </trans-unit>                 
                <trans-unit id="reporting.template_customer">
                    <source>reporting.template_customer</source>
                    <target>Create reporting by template</target>
                </trans-unit>                       
                <trans-unit id="reporting.template_editing">
                    <source>reporting.template_editing</source>
                    <target>Template change</target>
                </trans-unit>  
                <trans-unit id="reporting.action_reset">
                    <source>reporting.action_reset</source>
                    <target>Back to original template</target>
                </trans-unit> 
                <trans-unit id="CUSTOM BM">
                    <source>CUSTOM BM</source>
                    <target>Employee</target>
                </trans-unit>  
                <trans-unit id="CUSTOM CL">
                    <source>CUSTOM CL</source>
                    <target>Customer</target>
                </trans-unit>
                <trans-unit id="CUSTOM BM, CUSTOM CL">
                    <source>CUSTOM BM, CUSTOM CL</source>
                    <target>Employee/Customer</target>
                </trans-unit>   
                <trans-unit id="reporting.export_reporting">
                    <source>reporting.export_reporting</source>
                    <target>Export</target>
                </trans-unit>   
            </group>
            <group resname="reporting_template">
                <note>Nastavení šablony hlášení pro zákazníka</note>
                <trans-unit id="reporting_template.title">
                    <source>reporting_template.title</source>
                    <target>Reporting template settings</target>
                </trans-unit>
                <trans-unit id="reporting_template_create">
                    <source>reporting_template_create</source>
                    <target>Create a reporting settings template</target>
                </trans-unit>                
                <trans-unit id="reporting_template_edit">
                    <source>reporting_template_edit</source>
                    <target>Edit a reporting settings template</target>
                </trans-unit>
                <trans-unit id="reporting_template_delete">
                    <source>reporting_template_delete</source>
                    <target>Delete a reporting settings template</target>
                </trans-unit>                
                <trans-unit id="reporting_template_list">
                    <source>reporting_template_list</source>
                    <target>Reporting templates list</target>
                </trans-unit>
                <trans-unit id="reporting_template.name">
                    <source>reporting_template.name</source>
                    <target>Name</target>
                </trans-unit>
                <trans-unit id="reporting_template.description">
                    <source>reporting_template.description</source>
                    <target>Description</target>
                </trans-unit>
            </group>            
            <group resname="customerContacts">
                <note>Kontakty pro zákazníka</note>
                <trans-unit id="customer_contact.title">
                    <source>customer_contact.title</source>
                    <target>Persons' contact details</target>
                </trans-unit>
                <trans-unit id="customer_contact_list">
                    <source>customer_contact_list</source>
                    <target>List of contacts </target>
                </trans-unit>
                <trans-unit id="contacts_list">
                    <source>contacts_list</source>
                    <target>List of contacts </target>
                </trans-unit>
                <trans-unit id="customer_contact_create">
                    <source>customer_contact_create</source>
                    <target>Create contact </target>
                </trans-unit>    
                <trans-unit id="customer_contact_edit">
                    <source>customer_contact_edit</source>
                    <target>Edit contact </target>
                </trans-unit>    
                <trans-unit id="customer_contact_show">
                    <source>customer_contact_show</source>
                    <target>Contact detail </target>
                </trans-unit>    
                <trans-unit id="customer_contact_delete">
                    <source>customer_contact_delete</source>
                    <target>Delete contact</target>
                </trans-unit>                
                <trans-unit id="customer_contact_history">
                    <source>customer_contact_history</source>
                    <target>Contact editing history </target>
                </trans-unit>                
                <trans-unit id="customer_contact.subject">
                    <source>customer_contact.subject</source>
                    <target>Customer</target>
                </trans-unit>                
                <trans-unit id="customer_contact.position">
                    <source>customer_contact.position</source>
                    <target>Function of the person </target>
                </trans-unit>       
                <trans-unit id="customer_contact.go_contacts">
                    <source>customer_contact.go_contacts</source>
                    <target>Show the list of contacts </target>
                </trans-unit>    
                <trans-unit id="customer_contact.list_title">
                    <source>customer_contact.list_title</source>
                    <target>List of the customer's contacts </target>
                </trans-unit>       
                <trans-unit id="customer_contact.link_customer_list">
                    <source>customer_contact.link_customer_list</source>
                    <target>Back to the list of customers </target>
                </trans-unit>  
                <trans-unit id="customer_contact.subject_select_title">
                    <source>customer_contact.subject_select_title</source>
                    <target>Choice of an existing contact person</target>
                </trans-unit>  
                <trans-unit id="customer_contact.subject_select">
                    <source>customer_contact.subject_select</source>
                    <target>Choose an existing contact person</target>
                </trans-unit> 
                <trans-unit id="customer_contact.contact">
                    <source>customer_contact.contact</source>
                    <target>Create a new contact person</target>
                </trans-unit>      
                <trans-unit id="customer_contact.flash_add_success">
                    <source>customer_contact.flash_add_success</source>
                    <target>Contact person has been added to a client</target>
                </trans-unit>   
                <trans-unit id="customer_contact.flash_removed">
                    <source>customer_contact.flash_removed</source>
                    <target>Contact person has been removed from a client</target>
                </trans-unit>   
                <trans-unit id="customer_contact.remove_action">
                    <source>customer_contact.remove_action</source>
                    <target>Remove a contact person</target>
                </trans-unit>  
                <trans-unit id="customer_contact.remove_action_confirm">
                    <source>customer_contact.remove_action_confirm</source>
                    <target>Do you really want to remove this contact person?</target>
                </trans-unit>                                   
            </group>        
            <group resname="subjectAccess">
                <note>Seznamy a nastavení přístupu pro subjekty</note>   
                <trans-unit id="subject_access_list">
                    <source>subject_access_list</source>
                    <target>List of subjects to be allowed access to Operations Module and online Operations Module</target>
                </trans-unit> 
                <trans-unit id="customer_access_list">
                    <source>customer_access_list</source>
                    <target>OPM access - list of clients</target>
                </trans-unit>
                <trans-unit id="menu_group_access">
                    <source>menu_group_access</source>
                    <target>Access grant</target>
                </trans-unit>                                                                                                                          
                <trans-unit id="menu_group_subject_access">
                    <source>menu_group_subject_access</source>
                    <target>Allowing subjects' access</target>
                </trans-unit>  
                <trans-unit id="menu_group_customer_access">
                    <source>menu_group_customer_access</source>
                    <target>Access grant for clients</target>
                </trans-unit>                                                                                                                         
                <trans-unit id="subject_access.password">
                    <source>subject_access.password</source>
                    <target>Allowing access to Operations Module</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.password_customer">
                    <source>subject_access.password_customer</source>
                    <target>Allowing access to online Operations Module</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.password_not_generated">
                    <source>subject_access.password_not_generated</source>
                    <target>The subject’s e-mail or personal number has not been set up! Access approval and access denial operations thus cannot be made</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.generate_password">
                    <source>subject_access.generate_password</source>
                    <target>Allow access to Operations Module</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.remove_password">
                    <source>subject_access.remove_password</source>
                    <target>Deny access to Operations Module</target>
                </trans-unit>     
                <trans-unit id="subject_access.generate_password_customer">
                    <source>subject_access.generate_password_customer</source>
                    <target>Allow access to online Operations Module</target>
                </trans-unit>                                                                                                                           
                <trans-unit id="subject_access.remove_password_customer">
                    <source>subject_access.remove_password_customer</source>
                    <target>Deny access to online Operations Module</target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_generated">
                    <source>subject_access.flash_password_generated</source>
                    <target>Subject has been allowed access to Operations Module</target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_removed">
                    <source>subject_access.flash_password_removed</source>
                    <target>Subject has been denied access to Operations Module</target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_customer_generated">
                    <source>subject_access.flash_password_customer_generated</source>
                    <target>Subject has been allowed access to online Operations Module</target>
                </trans-unit>                                                                                                                                   
                <trans-unit id="subject_access.flash_password_customer_removed">
                    <source>subject_access.flash_password_customer_removed</source>
                    <target>Subject has been denied access to online Operations Module</target>
                </trans-unit>    
                <trans-unit id="subject_access.flash_batch_generate_password_error">
                    <source>subject_access.flash_batch_generate_password_error</source>
                    <target>Error in allowing access to selected subjects to Operations Module</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_generate_password_success">
                    <source>subject_access.flash_batch_generate_password_success</source>
                    <target>Selected subjects were allowed access to Operations Module</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_error">
                    <source>subject_access.flash_batch_remove_password_error</source>
                    <target>Error in denying access to selected subjects to Operations Module</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_success">
                    <source>subject_access.flash_batch_remove_password_success</source>
                    <target>Selected subjects were denied access to Operations Module</target>
                </trans-unit>  
                <trans-unit id="subject_access.flash_batch_generate_password_customer_error">
                    <source>subject_access.flash_batch_generate_password_customer_error</source>
                    <target>Error in allowing access to selected subjects to online Operations Module</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_generate_password_customer_success">
                    <source>subject_access.flash_batch_generate_password_customer_success</source>
                    <target>Selected subjects were allowed access to online Operatins Module</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_customer_error">
                    <source>subject_access.flash_batch_remove_password_customer_error</source>
                    <target>Error in denying access to selected subjects to online Operations Module</target>
                </trans-unit>                            
                <trans-unit id="subject_access.flash_batch_remove_password_customer_success">
                    <source>subject_access.flash_batch_remove_password_customer_success</source>
                    <target>Selected subjects were denied access to online Operations Module</target>
                </trans-unit>  
                <trans-unit id="subject_access.new_pass_mail_subject_pm">
                    <source>subject_access.new_pass_mail_subject_pm</source>
                    <target>New password to the access to PM</target>
                </trans-unit>
                <trans-unit id="subject_access.new_pass_mail_message_pm">
                    <source>subject_access.new_pass_mail_message_pm</source>
                    <target>Hallo, a new access password to PM has been generated for You. Your access name: %username% . Your new password: %password% . You can change the password in PM.</target>
                </trans-unit>
                <trans-unit id="subject_access.new_pass_mail_subject_opm">
                    <source>subject_access.new_pass_mail_subject_opm</source>
                    <target>New password to the Client access</target>
                </trans-unit>
                <trans-unit id="subject_access.new_pass_mail_message_opm">
                    <source>subject_access.new_pass_mail_message_opm</source>
                    <target>Hallo, a new password to the Client access has been generated for You. Your access name: %username% . Your new password: %password% . You can change the password in Client access.</target>
                </trans-unit>                
            </group>            
            <group resname="serviceGenerate">
                <note>Vložit služby</note>
                <trans-unit id="service_create.from_to">
                    <source>service_create.from_to</source>
                    <target>Generated services parameters</target>
                </trans-unit>
                <trans-unit id="service_create_create">
                    <source>service_create_create</source>
                    <target>Service generation</target>
                </trans-unit>
                <trans-unit id="service_create.position">
                    <source>service_create.position</source>
                    <target>Position</target>
                </trans-unit>
                <trans-unit id="service_create.product">
                    <source>service_create.product</source>
                    <target>Product</target>
                </trans-unit>
                <trans-unit id="service_create.period">
                    <source>service_create.period</source>
                    <target>Period</target>
                </trans-unit>
                <trans-unit id="service_create.startsAt">
                    <source>service_create.startsAt</source>
                    <target>Generate from:</target>
                </trans-unit>
                <trans-unit id="service_create.endsAt">
                    <source>service_create.endsAt</source>
                    <target>Generate to:</target>
                </trans-unit>
                <trans-unit id="servise_create.shift_1">
                    <source>servise_create.shift_1</source>
                    <target>1st shift</target>
                </trans-unit>
                <trans-unit id="servise_create.shift_2">
                    <source>servise_create.shift_2</source>
                    <target>2nd shift</target>
                </trans-unit>
                <trans-unit id="servise_create.shift_3">
                    <source>servise_create.shift_3</source>
                    <target>3rd shift</target>
                </trans-unit>
                <trans-unit id="service_create.from_1">
                    <source>service_create.from_1</source>
                    <target>Beginning of the 1st shift</target>
                </trans-unit>
                <trans-unit id="service_create.from_2">
                    <source>service_create.from_2</source>
                    <target>Beginning of the 2nd shift</target>
                </trans-unit>
                <trans-unit id="service_create.from_3">
                    <source>service_create.from_3</source>
                    <target>Beginning of the 3rd shift</target>
                </trans-unit>
                <trans-unit id="service_create.to_1">
                    <source>service_create.to_1</source>
                    <target>Termination of the 1st shift</target>
                </trans-unit>
                <trans-unit id="service_create.to_2">
                    <source>service_create.to_2</source>
                    <target>Termination of the 2nd shift</target>
                </trans-unit>
                <trans-unit id="service_create.to_3">
                    <source>service_create.to_3</source>
                    <target>Termination of the 3rd shift</target>
                </trans-unit>
                <trans-unit id="servise_create.help_empty_time_not_generate">
                    <source>servise_create.help_empty_time_not_generate</source>
                    <target>If You don't input times of the beginning and end of the shift, the shift is not going to be generated WARNING: If the shift ends before the beginning, system terminates the shift next day!</target>
                </trans-unit>
                <trans-unit id="service_create.no_generate">
                    <source>service_create.no_generate</source>
                    <target>Can't generate</target>
                </trans-unit>

                <trans-unit id="service_create_training">
                    <source>service_create_training</source>
                    <target>Training for a specific day</target>
                </trans-unit> 
                <trans-unit id="service_create.training_base">
                    <source>service_create.training_base</source>
                    <target>Training parameters</target>
                </trans-unit>                                 
                <trans-unit id="service_create.from">
                    <source>service_create.from</source>
                    <target>Start of shift</target>
                </trans-unit>
                <trans-unit id="service_create.to">
                    <source>service_create.to</source>
                    <target>End of shift</target>
                </trans-unit>                
                <trans-unit id="service_create.trainingAt">
                    <source>service_create.trainingAt</source>
                    <target>Date of training</target>
                </trans-unit>      
                <trans-unit id="servise_create.student">
                    <source>servise_create.student</source>
                    <target>Employees</target>
                </trans-unit>                             
                <trans-unit id="servise_create.student_btn">
                    <source>servise_create.student_btn</source>
                    <target>Add employees</target>
                </trans-unit>    
                <trans-unit id="servise_create.flash_no_person">
                    <source>servise_create.flash_no_person</source>
                    <target>Employees with positions are not chosen!</target>
                </trans-unit>                               
                <trans-unit id="servise_create.flash_noone_person">
                    <source>servise_create.flash_noone_person</source>
                    <target>You cannot have more identical employees at the same time</target>
                </trans-unit> 
                <trans-unit id="servise_create.flash_noone_position">
                    <source>servise_create.flash_noone_position</source>
                    <target>You cannot have multiple identical positions!</target>
                </trans-unit>   
                <trans-unit id="servise_create.flash_no_subject">
                    <source>servise_create.flash_no_subject</source>
                    <target>Person is not selected</target>
                </trans-unit> 
                <trans-unit id="servise_create.flash_no_position">
                    <source>servise_create.flash_no_position</source>
                    <target>No position selected</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_SERVICE_CREATE_TRAINING">
                    <source>permission.PERM_SERVICE_CREATE_TRAINING</source>
                    <target>Insert services - Training for a specific day</target>
                </trans-unit>                  
                                                              
                                                                                                                                                          
            </group>                                        
                 
            <group resname="attendance">
                <note>Docházka</note>
                <trans-unit id="attendance">
                    <source>attendance</source>
                    <target>Attendance</target>
                </trans-unit>                
                <trans-unit id="attendance_list">
                    <source>attendance_list</source>
                    <target>Attendance summary</target>
                </trans-unit>
                <trans-unit id="attendance.title">
                    <source>attendance.title</source>
                    <target>Attendance summary</target>
                </trans-unit>
                <trans-unit id="attendance_edit">
                    <source>attendance_edit</source>
                    <target>Attendance edit</target>
                </trans-unit>
                <trans-unit id="attendance_view">
                    <source>attendance_show</source>
                    <target>Attendance detail</target>
                </trans-unit>                 
                
                <trans-unit id="attendance_start_list">
                    <source>attendance_start_list</source>
                    <target>Start of a service</target>
                </trans-unit>
                <trans-unit id="attendance_start.title">
                    <source>attendance_start.title</source>
                    <target>Start of a service</target>
                </trans-unit>
                <trans-unit id="attendance_start_edit">
                    <source>attendance_start_edit</source>
                    <target>Adding a person to service</target>
                </trans-unit>
                <trans-unit id="attendance_start_time">
                    <source>attendance_start_time</source>
                    <target>Set time of a service beginning</target>
                </trans-unit>                 
                <trans-unit id="attendance_start.now_action">
                    <source>attendance_start.now_action</source>
                    <target>Now</target>
                </trans-unit>
                <trans-unit id="attendance_start_notenter">
                    <source>attendance_start_notenter</source>
                    <target>Hasn't started the shift</target>
                </trans-unit>                
                <trans-unit id="attendance_start.plan_action">
                    <source>attendance_start.plan_action</source>
                    <target>According to a plan</target>
                </trans-unit>
                <trans-unit id="attendance_start.time_action">
                    <source>attendance_start.time_action</source>
                    <target>Time input</target>
                </trans-unit>
                <trans-unit id="attendance_start.notenter_action">
                    <source>attendance_start.notenter_action</source>
                    <target>Hasn't started the shift</target>
                </trans-unit>
                <trans-unit id="attendance_start.unoccupied_action">
                    <source>attendance_start.unoccupied_action</source>
                    <target>Not occupied</target>
                </trans-unit>
                <trans-unit id="attendance_start.unoccupied_partly_action">
                    <source>attendance_start.unoccupied_partly_action</source>
                    <target>Partly not occupied</target>
                </trans-unit>                
                <trans-unit id="attendance_start.unfinished_attendance">
                    <source>attendance_start.unfinished_attendance</source>
                    <target>Not finished attendance exists!</target>
                </trans-unit>                
                
                <trans-unit id="attendance.starts_at">
                    <source>attendance.starts_at</source>
                    <target>Planned beginning of a service</target>
                </trans-unit>
                <trans-unit id="attendance.ends_at">
                    <source>attendance.ends_at</source>
                    <target>Planned end of a service</target>
                </trans-unit>  
                <trans-unit id="attendance.starts_at_real">
                    <source>attendance.starts_at_real</source>
                    <target>Real start of a service</target>
                </trans-unit>
                <trans-unit id="attendance.ends_at_real">
                    <source>attendance.ends_at_real</source>
                    <target>Real end of a service</target>
                </trans-unit>                               
                <trans-unit id="attendance.subject">
                    <source>attendance.subject</source>
                    <target>Name</target>
                </trans-unit>    
                <trans-unit id="attendance.approved">
                    <source>attendance.approved</source>
                    <target>Approved</target>
                </trans-unit> 
                <trans-unit id="attendance.exported">
                    <source>attendance.exported</source>
                    <target>Export</target>
                </trans-unit>                 
                <trans-unit id="attendance_start.flash_success">
                    <source>attendance_start.flash_success</source>
                    <target>Start of a service of a chosen item has been successfully realized.</target>
                </trans-unit>     
                <trans-unit id="attendance_start.flash_error">
                    <source>attendance_start_flash_error</source>
                    <target>Start of a service of a chosen item has failed.</target>
                </trans-unit>   
                <trans-unit id="attendance_start.flash_notenter_success">
                    <source>attendance_start.flash_notenter_success</source>
                    <target>Start of a service of a chosen item of a type "hasn't started the service" has been successfully realized.</target>
                </trans-unit>     
                <trans-unit id="attendance_start.flash_notenter_error">
                    <source>attendance_start_flash_notenter_error</source>
                    <target>Start of a service of a chosen item of a type "hasn't started the service" has failed.</target>
                </trans-unit>
                <trans-unit id="attendance_start.flash_startsAt_novalid_error">
                    <source>attendance_start.flash_startsAt_novalid_error</source>
                    <target>Inputted time of the service beginning cannot be used. It resides out of the planned time of the beginning and end of the service.</target>
                </trans-unit>
                <trans-unit id="attendance_end.flash_before_startsAt_novalid_error">
                    <source>attendance_end.flash_before_startsAt_novalid_error</source>
                    <target>The specified service end time is before the start time.</target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_endsAt_novalid_error">
                    <source>attendance_end.flash_endsAt_novalid_error</source>
                    <target>The specified service end time cannot be used. It is located outside the scheduled start and end time of the service.</target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_startsAt_in_attendance_novalid_error">
                    <source>attendance_end.flash_startsAt_in_attendance_novalid_error</source>
                    <target>The specified service start time cannot be used. There exists another attendance in the given time.</target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_endsAt_in_attendance_novalid_error">
                    <source>attendance_end.flash_endsAt_in_attendance_novalid_error</source>
                    <target>The specified service end time cannot be used.. There exists another attendance in the given time.</target>
                </trans-unit>                                                
                
                
                
                <trans-unit id="attendance_running_list">
                    <source>attendance_running_list</source>
                    <target>Current services</target>
                </trans-unit> 
                <trans-unit id="attendance_running_edit">
                    <source>attendance_running_edit</source>
                    <target>Edit current service/attendance</target>
                </trans-unit> 
                <trans-unit id="attendance_running_show">
                    <source>attendance_running_show</source>
                    <target>Current service/attendance detail</target>
                </trans-unit>    
                <trans-unit id="attendance_running.title">
                    <source>attendance_running.title</source>
                    <target>Current services</target>
                </trans-unit> 
                <trans-unit id="attendance.note">
                    <source>attendance.note</source>
                    <target>Note</target>
                </trans-unit>    
                <trans-unit id="attendance.reward_penalty_type">
                    <source>attendance.reward_penalty_type</source>
                    <target>Reward/penalty</target>
                </trans-unit>    
                <trans-unit id="attendance.reward_penalty">
                    <source>attendance.reward_penalty</source>
                    <target>Amount</target>
                </trans-unit> 
                <trans-unit id="attendance.type_reward">
                    <source>attendance.type_reward</source>
                    <target>Reward</target>
                </trans-unit>  
                <trans-unit id="attendance.type_penalty">
                    <source>attendance.type_penalty</source>
                    <target>Penalty</target>
                </trans-unit>   
                <trans-unit id="attendance.state">
                    <source>attendance.state</source>
                    <target>Status of an attendance</target>
                </trans-unit>                 
                <trans-unit id="attendance.state_noplanned">
                    <source>attendance.state_noplanned</source>
                    <target>Not running</target>
                </trans-unit>  
                <trans-unit id="attendance.state_running">
                    <source>attendance.state_running</source>
                    <target>Running</target>
                </trans-unit>  
                <trans-unit id="attendance.state_end">
                    <source>attendance.state_end</source>
                    <target>Terminated</target>
                </trans-unit>  
                <trans-unit id="attendance.state_noenter">
                    <source>attendance.state_noenter</source>
                    <target>Hasn't started</target>
                </trans-unit>  
                <trans-unit id="attendance.state_unocuupied">
                    <source>attendance.state_unocuupied</source>
                    <target>Not occupied</target>
                </trans-unit>  
                <trans-unit id="attendance_running.flash_edit_success">
                    <source>attendance_running.flash_edit_success</source>
                    <target>The attendance item has been successfully edited</target>
                </trans-unit>  
                <trans-unit id="attendance_running.flash_edit_error">
                    <source>attendance_running.flash_edit_error</source>
                    <target>The attendance item edit has failed</target>
                </trans-unit> 
                
                <trans-unit id="attendance_end_list">
                    <source>attendance_end_list</source>
                    <target>Termination of services</target>
                </trans-unit>
                <trans-unit id="attendance_end.title">
                    <source>attendance_end.title</source>
                    <target>Termination of a service</target>
                </trans-unit>
                <trans-unit id="attendance_end_edit">
                    <source>attendance_end_edit</source>
                    <target>Attendance detail</target>
                </trans-unit>
                <trans-unit id="attendance_end.now_action">
                    <source>attendance_end.now_action</source>
                    <target>Now</target>
                </trans-unit>
                <trans-unit id="attendance_end.plan_action">
                    <source>attendance_end.plan_action</source>
                    <target>According to a plan</target>
                </trans-unit>
                <trans-unit id="attendance_end.flash_success">
                    <source>attendance_end.flash_success</source>
                    <target>Service termination of a selected item has been successfully realized</target>
                </trans-unit>  
                <trans-unit id="attendance_end.flash_error">
                    <source>attendance_end.flash_error</source>
                    <target>Service termination of a selected item has failed</target>
                </trans-unit>  
                <trans-unit id="attendance.rate">
                    <source>attendance.rate</source>
                    <target>Tariff</target>
                </trans-unit>
                <trans-unit id="attendance.subject_rate">
                    <source>attendance.subject_rate</source>
                    <target>Subject rate</target>
                </trans-unit>                
                <trans-unit id="attendance.null_attendance">
                    <source>attendance.null_attendance</source>
                    <target>Hasn't started s service</target>
                </trans-unit>
                <trans-unit id="attendance.time_correction">
                    <source>attendance.time_correction</source>
                    <target>Time correction</target>
                </trans-unit>
                <trans-unit id="attendance.time_correction_help">
                    <source>attendance.time_correction_help</source>
                    <target>Input attendance time correction in minutes (+/-)</target>
                </trans-unit>
                <trans-unit id="btn_update_and_approve_and_return_to_list">
                    <source>btn_update_and_approve_and_return_to_list</source>
                    <target>Save and approve</target>
                </trans-unit> 
                <trans-unit id="attendance_length_list">
                    <source>attendance_length_list</source>
                    <target>Attendance lenghts</target>
                </trans-unit>
                <trans-unit id="set_attendance_length_filter_warning">
                    <source>set_attendance_length_filter_warning</source>
                    <target>You have to set a period filter</target>
                </trans-unit>
                <trans-unit id="attendance.length">
                    <source>attendance.length</source>
                    <target>Attendance lenght</target>
                </trans-unit>                                
            </group>    

            <group resname="attendanceNotApproved">
                <note>Kontrola plánu - neschválené</note>
                <trans-unit id="attendance_not_approved_list">
                    <source>attendance_not_approved_list</source>
                    <target>Objects with existing non-approved attendancies or a plan</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_attendance_is_approved">
                    <source>attendance_not_approved.field_attendance_is_approved</source>
                    <target>Approved attendance</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_attendance_not_approved">
                    <source>attendance_not_approved.field_attendance_not_approved</source>
                    <target>Non-approved attendance</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_service_is_approved">
                    <source>attendance_not_approved.field_service_is_approved</source>
                    <target>Approved service</target>
                </trans-unit>
                <trans-unit id="attendance_not_approved.field_service_not_approved">
                    <source>attendance_not_approved.field_service_not_approved</source>
                    <target>Non-approved service</target>
                </trans-unit>                                                                
            </group>  

            <group resname="attendanceStartCheck">
                <note>Předčasná docházka</note>
                <trans-unit id="attendance_start_check_list">
                    <source>attendance_start_check_list</source>
                    <target>Premature attendance</target>
                </trans-unit>
                <trans-unit id="days">
                    <source>days</source>
                    <target>days</target>
                </trans-unit>
                <trans-unit id="hours">
                    <source>hours</source>
                    <target>hours</target>
                </trans-unit>
                <trans-unit id="attendance.more_than_month">
                    <source>attendance.more_than_month</source>
                    <target>more than a month</target>
                </trans-unit>
                <trans-unit id="attendance.difference">
                    <source>attendance.difference</source>
                    <target>Difference</target>
                </trans-unit>
            </group>    
            <group resname="attendanceNoPlan">
                <note>Neexistuje plán</note>
                <trans-unit id="attendance_no_plan_list">
                    <source>attendance_no_plan_list</source>
                    <target>Plan doesn't exist</target>
                </trans-unit>
            </group>    



            <group resname="panic_button">
                <note>Tísňové tlačítko</note>
                <trans-unit id="panic_button">
                    <source>panic_button</source>
                    <target>Panic button</target>
                </trans-unit>
                <trans-unit id="panic_button.form_title">
                    <source>panic_button.form_title</source>
                    <target>Send SMS (text message) in emergency situation</target>
                </trans-unit>
                <trans-unit id="panic_button_tenant">
                    <source>panic_button_tenant</source>
                    <target>Panic button (Tenants)</target>
                </trans-unit>
                <trans-unit id="panic_button.tenant_form_title">
                    <source>panic_button.tenant_form_title</source>
                    <target>Sending SMS messages to tenants in an emergency</target>
                </trans-unit>                
                <trans-unit id="panic_button.object_select">
                    <source>panic_button.object_select</source>
                    <target>Select object</target>
                </trans-unit>
                <trans-unit id="panic_button.message">
                    <source>panic_button.message</source>
                    <target>SMS message</target>
                </trans-unit>
                <trans-unit id="panic_button.submit">
                    <source>panic_button.submit</source>
                    <target>Send SMS</target>
                </trans-unit>
                <trans-unit id="panic_button.cancel">
                    <source>panic_button.cancel</source>
                    <target>Cancel</target>
                </trans-unit>
                <trans-unit id="panic_button.success_message">
                    <source>panic_button.success_message</source>
                    <target>Emmergency message was successfully sent</target>
                </trans-unit>
                <trans-unit id="panic_button.error_message">
                    <source>panic_button.error_message</source>
                    <target>Sending failed</target>
                </trans-unit>
                <trans-unit id="panic_button.sms_message">
                    <source>panic_button.sms_message</source>
                    <target>This is emergency message from PM 2.0 System. Object: %object% Time: %eventTime% Message: %text%</target>
                </trans-unit>
            </group>     
            <group resname="copy_period">
                <note>Generating of period copy</note>
                <trans-unit id="period_copy_list">
                    <source>period_copy_list</source>
                    <target>Create a copy of a period</target>
                </trans-unit>
                <trans-unit id="copy_period.not_possible">
                    <source>copy_period.not_possible</source>
                    <target>Generation not possible</target>
                </trans-unit>
                <trans-unit id="copy_period.from_period">
                    <source>copy_period.from_period</source>
                    <target>Copy from a period</target>
                </trans-unit>
                <trans-unit id="copy_period.to_period">
                    <source>copy_period.to_period</source>
                    <target>Copy to a period</target>
                </trans-unit>
                <trans-unit id="copy_period.batch_button">
                    <source>copy_period.batch_button</source>
                    <target>Gemerate copies</target>
                </trans-unit>
                <trans-unit id="period_copy.progress_title">
                    <source>period_copy.progress_title</source>
                    <target>Generation of the services is being processed</target>
                </trans-unit>
                <trans-unit id="copy_period.possible_copy_only">
                    <source>copy_period.possible_copy_only</source>
                    <target>Objects with ability to be copied only</target>
                </trans-unit>
                <trans-unit id="period_copy_error_description">
                    <source>period_copy_error_description</source>
                    <target>Created shifts validation error - object: %object%, position: %position%, starts: %starts_at%, shift: %shift%</target>
                </trans-unit>
            </group>                                                  

            <group resname="agencySubjects">
                <note>Seznam osob agentury</note>
                <trans-unit id="agency_subject.title">
                    <source>agency_subject.title</source>
                    <target>Agency persons' list</target>
                </trans-unit>
                <trans-unit id="agency_subject_list">
                    <source>agency_subject_list</source>
                    <target>Agency persons' list</target>
                </trans-unit>
                <trans-unit id="agency_subject_create">
                    <source>agency_subject_create</source>
                    <target>Choose or create a person</target>
                </trans-unit>    
                <trans-unit id="agency_subject_edit">
                    <source>agency_subject_edit</source>
                    <target>Edit a person</target>
                </trans-unit>    
                <trans-unit id="agency_subject_show">
                    <source>agency_subject_show</source>
                    <target>Detail of a person</target>
                </trans-unit>    
                <trans-unit id="agency_subject_delete">
                    <source>agency_subject_delete</source>
                    <target>Delete a person from an agency</target>
                </trans-unit>                
                <trans-unit id="agency_subject_history">
                    <source>agency_subject_history</source>
                    <target>Person's editing history</target>
                </trans-unit>                
                <trans-unit id="agency_subject.subject">
                    <source>agency_subject.subject</source>
                    <target>Person</target>
                </trans-unit>                
                <trans-unit id="agency_subject.position">
                    <source>agency_subject.position</source>
                    <target>Person's function</target>
                </trans-unit>       
                <trans-unit id="agency_subject.go_subjects">
                    <source>agency_subject.go_subjects</source>
                    <target>Show a list of agency persons</target>
                </trans-unit>    
                <trans-unit id="agency_subject.list_title">
                    <source>agency_subject.list_title</source>
                    <target>List of agency persons</target>
                </trans-unit>       
                <trans-unit id="agency_subject.link_agency_list">
                    <source>agency_subject.link_agency_list</source>
                    <target>Back to the list of agencies</target>
                </trans-unit>  
                <trans-unit id="agency_subject.subject_select_title">
                    <source>agency_subject.subject_select_title</source>
                    <target>Choose an existing person</target>
                </trans-unit>  
                <trans-unit id="agency_subject.subject_select">
                    <source>agency_subject.subject_select</source>
                    <target>Choose a person</target>
                </trans-unit> 
                <trans-unit id="agency_subject.contact">
                    <source>agency_subject.contact</source>
                    <target>Person</target>
                </trans-unit>      
                <trans-unit id="agency_subject.new_contact">
                    <source>agency_subject.new_contact</source>
                    <target>Add new person</target>
                </trans-unit>      
                <trans-unit id="agency_subject.flash_add_success">
                    <source>agency_subject.flash_add_success</source>
                    <target>Person has been added to an agency</target>
                </trans-unit>   
                <trans-unit id="agency_subject.flash_removed">
                    <source>agency_subject.flash_removed</source>
                    <target>Person has been removed from an agency</target>
                </trans-unit>   
                <trans-unit id="agency_subject.remove_action">
                    <source>agency_subject.remove_action</source>
                    <target>Remove a person from an agency</target>
                </trans-unit>  
                <trans-unit id="agency_subject.remove_action_confirm">
                    <source>agency_subject.remove_action_confirm</source>
                    <target>Do You really want to remove this person from an agency?</target>
                </trans-unit>                                   
            </group>        

            <group resname="agencyService">
                <note>Podklady pro dodavatele</note>
                <trans-unit id="agency_service.title">
                    <source>agency_service.title</source>
                    <target>Materials for a supplier</target>
                </trans-unit>
                <trans-unit id="agency_service_list">
                    <source>agency_service_list</source>
                    <target>Materials for a supplier</target>
                </trans-unit>
                <trans-unit id="agency_service.agency">
                    <source>agency_service.agency</source>
                    <target>Agency</target>
                </trans-unit>                 
                <trans-unit id="agency_service.object">
                    <source>agency_service.object</source>
                    <target>Object</target>
                </trans-unit>                
                <trans-unit id="agency_service.period">
                    <source>agency_service.period</source>
                    <target>Period</target>
                </trans-unit>       
                <trans-unit id="agency_service.field_service_count">
                    <source>agency_service.field_service_count</source>
                    <target>Number of services</target>
                </trans-unit>    
                <trans-unit id="agency_service.field_service_hours">
                    <source>agency_service.field_service_hours</source>
                    <target>Hours' count</target>
                </trans-unit>       
            </group>        
            <group resname="agencyServiceDetail">
                <note>Podklady pro dodavatele - detail</note>
                <trans-unit id="agency_service_detail.title">
                    <source>agency_service_detail.title</source>
                    <target>Materials for a supplier - detail</target>
                </trans-unit>
                <trans-unit id="agency_service_detail_list">
                    <source>agency_service_detail_list</source>
                    <target>Materials for a supplier</target>
                </trans-unit>
                <trans-unit id="agency_service_detail.subject">
                    <source>agency_service_detail.subject</source>
                    <target>Name of a worker</target>
                </trans-unit>                 
                <trans-unit id="agency_service_detail.object">
                    <source>agency_service_detail.object</source>
                    <target>Object</target>
                </trans-unit>  
                <trans-unit id="agency_service_detail.role">
                    <source>agency_service_detail.role</source>
                    <target>Position</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.product">
                    <source>agency_service_detail.product</source>
                    <target>Product</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.agency_rate">
                    <source>agency_service_detail.agency_rate</source>
                    <target>Tariff</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.startsAt">
                    <source>agency_service_detail.startsAt</source>
                    <target>Beginning of an attendance</target>
                </trans-unit>      
                <trans-unit id="agency_service_detail.endsAt">
                    <source>agency_service_detail.endsAt</source>
                    <target>End of an attendance</target>
                </trans-unit>  
                <trans-unit id="agency_service_detail.break_type">
                    <source>agency_service_detail.break_type</source>
                    <target>Paid break</target>
                </trans-unit>
                <trans-unit id="agency_service_detail.totaltime">
                    <source>agency_service_detail.totaltime</source>
                    <target>Total attendance time (excl. holidays)</target>
                </trans-unit> 
                <trans-unit id="agency_service_detail.totaltime_holiday">
                    <source>agency_service_detail.totaltime_holiday</source>
                    <target>Total attendance time (holidays)</target>
                </trans-unit>                                   
                <trans-unit id="agency_service_detail.reward_penalty">
                    <source>agency_service_detail.reward_penalty</source>
                    <target>Reward/penalty sum</target>
                </trans-unit>  
            </group>        
            <group resname="statistics">
                <note>Statistiky a přehledy</note>
                <trans-unit id="menu_statistics_pops">
                    <source>menu_statistics_pops</source>
                    <target>SPR archive</target>
                </trans-unit>
                <trans-unit id="menu_statistics_pops_list">
                    <source>menu_statistics_pops_list</source>
                    <target>SPR summary</target>
                </trans-unit>
                <trans-unit id="popslist_list">
                    <source>popslist_list</source>
                    <target>Statistics - POPS archive</target>
                </trans-unit>
                <trans-unit id="popslist_show">
                    <source>popslist_show</source>
                    <target>SPR</target>
                </trans-unit>
                <trans-unit id="pops.events">
                    <source>pops.events</source>
                    <target>SPR - detail</target>
                </trans-unit>
                <trans-unit id="popslist_edit">
                    <source>popslist_edit</source>
                    <target>SPR - editing</target>
                </trans-unit>
                <trans-unit id="menu_statistics_pops_events">
                    <source>menu_statistics_pops_events</source>
                    <target>SPR events' summary detail</target>
                </trans-unit>
                <trans-unit id="pops_events_list">
                    <source>pops_events_list</source>
                    <target>Statistics - SPR events</target>
                </trans-unit>
                <trans-unit id="pops.eventDateTime">
                    <source>pops.eventDateTime</source>
                    <target>Date</target>
                </trans-unit>
                <trans-unit id="pops.eventName">
                    <source>pops.eventName</source>
                    <target>Name</target>
                </trans-unit>
                <trans-unit id="pops_events.title">
                    <source>pops_events.title</source>
                    <target>SPR detail</target>
                </trans-unit>
                <trans-unit id="pops_events_show">
                    <source>pops_events_show</source>
                    <target>SPR detail</target>
                </trans-unit>
                <trans-unit id="pops_events_edit">
                    <source>pops_events_edit</source>
                    <target>SPR detail edit</target>
                </trans-unit>
            </group>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
            <group resname="billingDocument">
                <note>Podklady pro fakturaci</note>
                <trans-unit id="menu_group_billing_document">
                    <source>menu_group_billing_document</source>
                    <target>Billing materials</target>
                </trans-unit>
                <trans-unit id="billing_document.title">
                    <source>billing_document.title</source>
                    <target>Billing materials</target>
                </trans-unit>
                <trans-unit id="billing_document_list">
                    <source>billing_document_list</source>
                    <target>Billing materials</target>
                </trans-unit>
                <trans-unit id="billing_document_subject.title">
                    <source>billing_document_subject.title</source>
                    <target>Billing materials - attendance detail</target>
                </trans-unit>                  
                <trans-unit id="billing_document_subject_list">
                    <source>billing_document_subject_list</source>
                    <target>Billing materials - attendance detail</target>
                </trans-unit>                
                <trans-unit id="billing_document_service_period">
                    <source>billing_document_service_period</source>
                    <target>Plan - cost according to a period</target>
                </trans-unit>
                <trans-unit id="billing_document_service_month">
                    <source>billing_document_service_month</source>
                    <target>Plan - cost according to a month</target>
                </trans-unit>
                <trans-unit id="billing_document_attendance_period">
                    <source>billing_document_attendance_period</source>
                    <target>Attendance - cost according to a period</target>
                </trans-unit>
                <trans-unit id="billing_document_attendnace_month">
                    <source>billing_document_attendance_month</source>
                    <target>Attendance - cost according to a month</target>
                </trans-unit>
                <trans-unit id="billing_document_attendnace_subject">
                    <source>billing_document_attendance_subject</source>
                    <target>Attendance - according to a person</target>
                </trans-unit>                
                <trans-unit id="menu_group_billing_document_service_period">
                    <source>menu_group_billing_document_service_period</source>
                    <target>Plan - cost according to a period</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_service_month">
                    <source>menu_group_billing_document_service_month</source>
                    <target>Plan - cost according to a month</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_attendance_period">
                    <source>menu_group_billing_document_attendance_period</source>
                    <target>Attendance - cost according to a period</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_attendnace_month">
                    <source>menu_group_billing_document_attendance_month</source>
                    <target>Attendance - cost according to a month</target>
                </trans-unit>   
                <trans-unit id="menu_group_billing_document_attendance_subject">
                    <source>menu_group_billing_document_attendance_subject</source>
                    <target>Attendance - according to a person</target>
                </trans-unit>                             
                
                <trans-unit id="billing_document.subject">
                    <source>billing_document.subject</source>
                    <target>Name</target>
                </trans-unit>
                <trans-unit id="billing_document.personal_no">
                    <source>billing_document.personal_no</source>
                    <target>Personal number</target>
                </trans-unit>
                <trans-unit id="billing_document.pay_point_code">
                    <source>billing_document.pay_point_code</source>
                    <target>Pay point code</target>
                </trans-unit>
                <trans-unit id="billing_document.company">
                    <source>billing_document.company</source>
                    <target>Company</target>
                </trans-unit>
                <trans-unit id="billing_document.contract_type">
                    <source>billing_document.contract_type</source>
                    <target>Contract type</target>
                </trans-unit>
                
                <trans-unit id="billing_document.object">
                    <source>billing_document.object</source>
                    <target>Object</target>
                </trans-unit>                
                <trans-unit id="billing_document.reward_penalty">
                    <source>billing_document.reward_penalty</source>
                    <target>Reward/penalty</target>
                </trans-unit>  
                <trans-unit id="billing_document.holiday_hours">
                    <source>billing_document.holiday_hours</source>
                    <target>Holiday hours</target>
                </trans-unit>                                                              
                <trans-unit id="billing_document.emergency_hours">
                    <source>billing_document.emergency_hours</source>
                    <target>Emergency hours</target>
                </trans-unit>   
                <trans-unit id="billing_document.controlled_zone_hours">
                    <source>billing_document.controlled_zone_hours</source>
                    <target>Controlled zone - hours</target>
                </trans-unit>                 
                <trans-unit id="billing_document.night_hours">
                    <source>billing_document.night_hours</source>
                    <target>Night hours</target>
                </trans-unit> 
                <trans-unit id="billing_document.sasu_hours">
                    <source>billing_document.sasu_hours</source>
                    <target>Sa-Su hours</target>
                </trans-unit>     
                <trans-unit id="billing_document.total">
                    <source>billing_document.total</source>
                    <target>Altogether</target>
                </trans-unit>                                                                                                       
                <trans-unit id="billing_document.managerial_allowance">
                    <source>billing_document.managerial_allowance</source>
                    <target>Management surcharge (rate)</target>
                </trans-unit>
                <trans-unit id="billing_document.managerial_allowance_hours">
                    <source>billing_document.managerial_allowance_hours</source>
                    <target>Management surcharge (hours)</target>
                </trans-unit>                
                <trans-unit id="billing_document_annex.title">
                    <source>billing_document_annex.title</source>
                    <target>Issued invoice appendix</target>
                </trans-unit> 
                <trans-unit id="billing_document_annex_list">
                    <source>billing_document_annex_list</source>
                    <target>Issued invoice appendix</target>
                </trans-unit>                                  
                <trans-unit id="billing_document_annex.flash_not_open">
                    <source>billing_document_annex.flash_not_open</source>
                    <target>Material couldn't be generated. There are no data to show.</target>
                </trans-unit>   
                <trans-unit id="billing_document_annex.go_annex">
                    <source>billing_document_annex.go_annex</source>
                    <target>Generate materials</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.no_generate">
                    <source>billing_document_annex.no_generate</source>
                    <target>Impossible to generate! Use at first a needed filter</target>
                </trans-unit>
                <trans-unit id="billing_document_annex.product">
                    <source>billing_document_annex.product</source>
                    <target>Name of a product</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.position">
                    <source>billing_document_annex.position</source>
                    <target>Position</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.hours">
                    <source>billing_document_annex.hours</source>
                    <target>Hours</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.rate">
                    <source>billing_document_annex.rate</source>
                    <target>Tariff</target>
                </trans-unit>  
                <trans-unit id="billing_document_annex.amnout">
                    <source>billing_document_annex.amnout</source>
                    <target>Altogether</target>
                </trans-unit>
                <trans-unit id="billing_document.filter_message_object_required">
                    <source>billing_document.filter_message_object_required</source>
                    <target>For the results, set the Object filter.</target>
                </trans-unit>                              
                <trans-unit id="billing_document.filter_message_subject_required">
                    <source>billing_document.filter_message_subject_required</source>
                    <target>For the results, set the Subject and Period filters.</target>
                </trans-unit>                                                                       
                <trans-unit id="billing_document.filter_message_object_period_required">
                    <source>billing_document.filter_message_object_period_required</source>
                    <target>For the results, set the Object and Period filters.</target>
                </trans-unit>  
                
                <trans-unit id="menu_group_billing_document_group">
                    <source>menu_group_billing_document_group</source>
                    <target>Group billing materials</target>
                </trans-unit>
                <trans-unit id="billing_document_group.title">
                    <source>billing_document_group.title</source>
                    <target>Group billing materials</target>
                </trans-unit>
                <trans-unit id="billing_document_group_list">
                    <source>billing_document_group_list</source>
                    <target>Group billing materials</target>
                </trans-unit>
                <trans-unit id="billing_document_group_service_month">
                    <source>billing_document_group_service_month</source>
                    <target>Plan - cost according to a month</target>
                </trans-unit>
                <trans-unit id="billing_document_group_attendnace_month">
                    <source>billing_document_group_attendance_month</source>
                    <target>Attendance - cost according to a month</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_group_service_month">
                    <source>menu_group_billing_document_group_service_month</source>
                    <target>Plan - cost according to a month</target>
                </trans-unit>
                <trans-unit id="menu_group_billing_document_group_attendnace_month">
                    <source>menu_group_billing_document_group_attendance_month</source>
                    <target>Attendance - cost according to a month</target>
                </trans-unit> 
                <trans-unit id="billing_document_group.filter_message_object_required">
                    <source>billing_document_group.filter_message_object_required</source>
                    <target>For the results, set the Group of objects filter.</target>
                </trans-unit>  
                <trans-unit id="billing_document_group_annex.go_annex">
                    <source>billing_document_group_annex.go_annex</source>
                    <target>Generate materials (objecks)</target>
                </trans-unit>                                   
                                                                                                                                                                                                                                                                      
            </group>
            <group resname="exceptions">
                <note>Výjimky</note>
                <trans-unit id="exception.error_occurred">
                    <source>exception.error_occurred</source>
                    <target>An error occurred</target>
                </trans-unit>
                <trans-unit id="exception.error_occurred_1">
                    <source>exception.error_occurred_1</source>
                    <target>Oops! An Error Occurred!</target>
                </trans-unit>
                <trans-unit id="exception.restricted_access">
                    <source>exception.restricted_access</source>
                    <target>Restricted access</target>
                </trans-unit>
                <trans-unit id="exception.not_found">
                    <source>exception.not_found</source>
                    <target>Not found</target>
                </trans-unit>
                <trans-unit id="exception.server_returned">
                    <source>exception.server_returned</source>
                    <target>The server returned a</target>
                </trans-unit>
                <trans-unit id="exception.msg_error_1">
                    <source>exception.msg_error_1</source>
                    <target>Something is broken.</target>
                </trans-unit>
                <trans-unit id="exception.msg_error_2">
                    <source>exception.msg_error_2</source>
                    <target>Please e-mail us and let us know what you were doing when this error occurred.</target>
                </trans-unit>
                <trans-unit id="exception.msg_error_3">
                    <source>exception.msg_error_3</source>
                    <target>We will fix it as soon as possible. Sorry for any inconvenience caused.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_1">
                    <source>exception.msg_access_1</source>
                    <target>You don't have required permissions to access this page.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_2">
                    <source>exception.msg_access_2</source>
                    <target>Even thought you are logged in, this page requires special permissions, that you don't have right now.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_3">
                    <source>exception.msg_access_3</source>
                    <target>Please ask administrator to give you permissions.</target>
                </trans-unit>
                <trans-unit id="exception.msg_access_4">
                    <source>exception.msg_access_4</source>
                    <target>You don't have sufficient permission rights to this operation.</target>
                </trans-unit>
                <trans-unit id="exception.link_back">
                    <source>exception.link_back</source>
                    <target>Back to previous page</target>
                </trans-unit>
                <trans-unit id="exception.link_home">
                    <source>exception.link_home</source>
                    <target>Back to homepage</target>
                </trans-unit>
                
            </group>
            <group resname="auto_logout">
                <note>Automatické odhlašování při nečinnosti</note>
                <trans-unit id="auto_logout.title">
                    <source>auto_logout.title</source>
                    <target>15 minutes of inactivity</target>
                </trans-unit>
                <trans-unit id="auto_logout.message">
                    <source>auto_logout.message</source>
                    <target>You'll be logged off in 30 seconds!</target>
                </trans-unit>
                <trans-unit id="auto_logout.button">
                    <source>auto_logout.button</source>
                    <target>Not log off</target>
                </trans-unit>
                <trans-unit id="detect_logout.title">
                    <source>detect_logout.title</source>
                    <target>Logout detected</target>
                </trans-unit>
                <trans-unit id="detect_logout.message">
                    <source>detect_logout.message</source>
                    <target>You have been logged out of the application. Save or note the current work and log in again.</target>
                </trans-unit>                
            </group>   
            <group resname="control_presence">
                <note>Automatické odhlašování při nečinnosti</note>
                <trans-unit id="control_presence.title">
                    <source>control_presence.title</source>
                    <target>Presence check</target>
                </trans-unit>
                <trans-unit id="control_presence.message">
                    <source>control_presence.message</source>
                    <target>Add these two numbers. You've got 30 seconds to do it.</target>
                </trans-unit>
                <trans-unit id="control_presence.button">
                    <source>control_presence.button</source>
                    <target>Acknowledge</target>
                </trans-unit>
                <trans-unit id="control_presence.alert">
                    <source>control_presence.alert</source>
                    <target>No, You're mistaken. Please, do try again.</target>
                </trans-unit>                
            </group>                         
            <group resname="shortcuts">
                <note>Shortcuts</note>
                <trans-unit id="shortcuts">
                    <source>shortcuts</source>
                    <target>Quick choices</target>
                </trans-unit>
                <trans-unit id="shortcuts.title">
                    <source>shortcuts.title</source>
                    <target>Quick choices edit</target>
                </trans-unit>
                <trans-unit id="shortcuts.edit">
                    <source>shortcuts.edit</source>
                    <target>Edit settings</target>
                </trans-unit>
                <trans-unit id="shortcuts.configure">
                    <source>shortcuts.configure</source>
                    <target>Set</target>
                </trans-unit>
                <trans-unit id="shortcuts.max_items_alert">
                    <source>shortcuts.max_items_alert</source>
                    <target>You've already chosen maximal count of 5 items!</target>
                </trans-unit>
            </group>
            
            <group resname="offline_pops">
                <note>Offline POPS</note>
                <trans-unit id="offline_pops.offline_dialog_title">
                    <source>offline_pops.offline_dialog_title</source>
                    <target>Offline mode</target>
                </trans-unit>
                <trans-unit id="offline_pops.offline_dialog_message">
                    <source>offline_pops.offline_dialog_message</source>
                    <target>Sorry, the application has detected connection failure.</target>
                </trans-unit>
                <trans-unit id="offline_pops.online_dialog_title">
                    <source>offline_pops.online_dialog_title</source>
                    <target>Online mode</target>
                </trans-unit>
                <trans-unit id="offline_pops.online_dialog_message">
                    <source>offline_pops.online_dialog_message</source>
                    <target>Application has detected reconnection to the network.</target>
                </trans-unit>
                <trans-unit id="offline_pops.new_event_created">
                    <source>offline_pops.new_event_created</source>
                    <target>SPR event has been saved</target>
                </trans-unit>
                <trans-unit id="offline_pops.new_event_error">
                    <source>offline_pops.new_event_error</source>
                    <target>The SPR event saving has ended with an error. Please, do try again.</target>
                </trans-unit>                                
                <trans-unit id="offline_pops.online_dialog_pops">
                    <source>offline_pops.online_dialog_pops</source>
                    <target>Saved SPR events have been detected. Do you want to show them?</target>
                </trans-unit>
                <trans-unit id="offline_pops.show_offline_popsevent_title">
                    <source>offline_pops.show_offline_popsevent_title</source>
                    <target>A list of offline SPR events</target>
                </trans-unit>  
                <trans-unit id="offline_pops.show_offline_popsevent_message">
                    <source>offline_pops.show_offline_popsevent_message</source>
                    <target>The following SPR events have been detected. Approving "save" will cause these events to be sent to server and saved to the system.</target>
                </trans-unit> 
                <trans-unit id="offline_pops.show_offline_popsevent_sending">
                    <source>offline_pops.show_offline_popsevent_sending</source>
                    <target>Sending data to processing ...</target>
                </trans-unit>                 
                <trans-unit id="offline_pops.show_offline_popsevent_saved">
                    <source>offline_pops.show_offline_popsevent_saved</source>
                    <target>SPR evants have been saved to the system.</target>
                </trans-unit>     
                <trans-unit id="offline_pops.show_offline_popsevent_nodata">
                    <source>offline_pops.show_offline_popsevent_nodata</source>
                    <target>There haven't been detected any offline SPR events.</target>
                </trans-unit>                                              
            </group>  
            
            <group resname="statistics">
                <note>Statistiky</note>
                <trans-unit id="menu_statistics_statistics">
                    <source>menu_statistics_statistics</source>
                    <target>Statistics</target>
                </trans-unit>
                <trans-unit id="menu_statistics_statistics_fluctuation">
                    <source>menu_statistics_statistics_fluctuation</source>
                    <target>Fluctuations</target>
                </trans-unit>
                <trans-unit id="fluctuation_list">
                    <source>fluctuation_list</source>
                    <target>Fluctuations on objects</target>
                </trans-unit>
                <trans-unit id="fluctuation.month">
                    <source>fluctuation.month</source>
                    <target>Month</target>
                </trans-unit>
                <trans-unit id="fluctuation.year">
                    <source>fluctuation.year</source>
                    <target>Year</target>
                </trans-unit>
                <trans-unit id="fluctuation.personsCount">
                    <source>fluctuation.personsCount</source>
                    <target>Presons per month</target>
                </trans-unit>
                <trans-unit id="fluctuation.newPersons">
                    <source>fluctuation.newPersons</source>
                    <target>New persons</target>
                </trans-unit>
                <trans-unit id="fluctuation.leavingPersons">
                    <source>fluctuation.leavingPersons</source>
                    <target>Persons who has left</target>
                </trans-unit>
                <trans-unit id="fluctuation.percent">
                    <source>fluctuation.percent</source>
                    <target>Percent of fluctuations</target>
                </trans-unit>
                <trans-unit id="fluctuation.generate">
                    <source>fluctuation.generate</source>
                    <target>Generate last month</target>
                </trans-unit>
                <trans-unit id="fluctuation_generate_success">
                    <source>fluctuation_generate_success</source>
                    <target>Statistics of fluctuations for {month}/{year} have been successfully created</target>
                </trans-unit>
                <trans-unit id="fluctuation_generate_error">
                    <source>fluctuation_generate_error</source>
                    <target>An error has occured in the creation of fluctuations' statistics</target>
                </trans-unit>
            </group> 
            <group resname="app_settings">
                <note>Application settings</note>
                <trans-unit id="setting.title">
                    <source>setting.title</source>
                    <target>Application settings</target>
                </trans-unit>
                <trans-unit id="setting.parameters_title">
                    <source>setting.parameters_title</source>
                    <target>Application parametres</target>
                </trans-unit>
                <trans-unit id="setting.mail_description">
                    <source>setting.mail_description</source>
                    <target>Email sending parametres setting</target>
                </trans-unit>
                <trans-unit id="setting.style_description">
                    <source>setting.style_description</source>
                    <target>Application style choice. A style name has to correspond with the name of the styles' file.</target>
                </trans-unit>
                <trans-unit id="setting.cd_agency_description">
                    <source>setting.cd_agency_description</source>
                    <target>BA agency choice. The BA gain in the statistics is going to be calculated according to choosen agency.</target>
                </trans-unit>
                <trans-unit id="setting.mail_address">
                    <source>setting.mail_address</source>
                    <target>Email address</target>
                </trans-unit>
                <trans-unit id="setting.mail_sender">
                    <source>setting.mail_sender</source>
                    <target>Name of the sender</target>
                </trans-unit>
                <trans-unit id="setting.mail_user_name">
                    <source>setting.mail_user_name</source>
                    <target>Access name</target>
                </trans-unit>
                <trans-unit id="setting.mail_password">
                    <source>setting.mail_password</source>
                    <target>Password</target>
                </trans-unit>
                <trans-unit id="setting.mail_transport">
                    <source>setting.mail_transport</source>
                    <target>Type of sending</target>
                </trans-unit>
                <trans-unit id="setting.app_company_style">
                    <source>setting.app_company_style</source>
                    <target>Style</target>
                </trans-unit>
                <trans-unit id="setting.cd_agency">
                    <source>setting.cd_agency</source>
                    <target>BA agency</target>
                </trans-unit>
                <trans-unit id="btn_save_and_clear_cache">
                    <source>btn_save_and_clear_cache</source>
                    <target>Save and cache refresh</target>
                </trans-unit>
                <trans-unit id="setting.news_rss_title">
                    <source>setting.news_rss_title</source>
                    <target>Introductory articles' source of Client access</target>
                </trans-unit>
                <trans-unit id="setting.news_rss_description">
                    <source>setting.news_rss_description</source>
                    <target>Articles are being showed from RSS or DB application</target>
                </trans-unit>
                <trans-unit id="setting.rss_enable">
                    <source>setting.rss_enable</source>
                    <target>Source of the articles</target>
                </trans-unit>
                <trans-unit id="setting.disable">
                    <source>setting.disable</source>
                    <target>from the database</target>
                </trans-unit>
                <trans-unit id="setting.enable">
                    <source>setting.enable</source>
                    <target>from RSS</target>
                </trans-unit>
                <trans-unit id="setting.news_rss_url">
                    <source>setting.news_rss_url</source>
                    <target>URL sources of RSS news</target>
                </trans-unit>                
                <trans-unit id="setting.module_description">
                    <source>setting.module_description</source>
                    <target>Module settings (superstructure)</target>
                </trans-unit>                  
                <trans-unit id="setting.module_visibility_menu">
                    <source>setting.module_visibility_menu</source>
                    <target>Visibility in a menu</target>
                </trans-unit>   
                <trans-unit id="setting.module_visibility_menu_hide">
                    <source>setting.module_visibility_menu_hide</source>
                    <target>Hide non-active modules</target>
                </trans-unit>   
                <trans-unit id="setting.module_visibility_menu_inactive">
                    <source>setting.module_visibility_menu_inactive</source>
                    <target>Show non-active modules</target>
                </trans-unit>         
                <trans-unit id="setting.interval_title">
                    <source>setting.interval_title</source>
                    <target>Interval setting</target>
                </trans-unit>   
                <trans-unit id="setting.interval_description">
                    <source>setting.interval_description</source>
                    <target>Set an interval for an automatic logout and presence check (minutes).</target>
                </trans-unit>   
                <trans-unit id="setting.interval_autologout">
                    <source>setting.interval_autologout</source>
                    <target>Autologout</target>
                </trans-unit>   
                <trans-unit id="setting.interval_presence">
                    <source>setting.interval_presence</source>
                    <target>Presence check</target>
                </trans-unit>  
                <trans-unit id="setting.show_pops_attachment_to_customer">
                    <source>setting.show_pops_attachment_to_customer</source>
                    <target>Show to customers</target>
                </trans-unit> 
                <trans-unit id="setting.show_attachment_customer_description">
                    <source>setting.show_attachment_customer_description</source>
                    <target>Display of SPR events' attachments in Customer portal setting</target>
                </trans-unit>                  
                <trans-unit id="setting.customer_pops_event_delay">
                    <source>setting.customer_pops_event_delay</source>
                    <target>Delay of</target>
                </trans-unit>   
                <trans-unit id="setting.customer_pops_event_delay_description">
                    <source>setting.customer_pops_event_delay_description</source>
                    <target>Display of SPR events' in Customer portal delay setting</target>
                </trans-unit>      
                <trans-unit id="setting.panic_tenant_title">
                    <source>setting.panic_tenant_title</source>
                    <target>Panic button (Tenants)</target>
                </trans-unit>                                                                                                       
                <trans-unit id="setting.panic_tenant_description">
                    <source>setting.panic_tenant_description</source>
                    <target>Panic button (Tenants) setting.</target>
                </trans-unit>                                                                                                       
                <trans-unit id="setting.panic_tenant_enable">
                    <source>setting.panic_tenant_enable</source>
                    <target>Allow panic button?</target>
                </trans-unit>                                                                                                       
                <trans-unit id="setting.panic_tenant_message">
                    <source>setting.panic_tenant_message</source>
                    <target>SMS message</target>
                </trans-unit>                                                                                                       
                <trans-unit id="setting.panic_tenant_0">
                    <source>setting.panic_tenant_0</source>
                    <target>no</target>
                </trans-unit>                                                                                                       
                <trans-unit id="setting.panic_tenant_1">
                    <source>setting.panic_tenant_1</source>
                    <target>yes</target>
                </trans-unit>                                                                                                       
            </group>
            <group resname="cd_profit">
                <note>Vynos CD</note>
                <trans-unit id="menu_statistics_statistics_cd">
                    <source>menu_statistics_statistics_cd</source>
                    <target>BA gain</target>
                </trans-unit>
                <trans-unit id="btn_calculate">
                    <source>btn_calculate</source>
                    <target>Calculate</target>
                </trans-unit>
                <trans-unit id="cd_profit.title">
                    <source>cd_profit.title</source>
                    <target>BA gain</target>
                </trans-unit>
                <trans-unit id="cd_profit.month">
                    <source>cd_profit.month</source>
                    <target>Month and year</target>
                </trans-unit>
                <trans-unit id="cd_profit.objects">
                    <source>cd_profit.objects</source>
                    <target>Objects</target>
                </trans-unit>
                <trans-unit id="cd_profit.hours">
                    <source>cd_profit.hours</source>
                    <target>Hours</target>
                </trans-unit>
                <trans-unit id="cd_profit.no_cd_id">
                    <source>cd_profit.no_cd_id</source>
                    <target>Cannot be calculated. The BA agency isn't set</target>
                </trans-unit>
                <trans-unit id="billingRate">
                    <source>billingRate</source>
                    <target>Rate</target>
                </trans-unit>
                <trans-unit id="service_time">
                    <source>service_time</source>
                    <target>Service</target>
                </trans-unit>
                <trans-unit id="attendance_time">
                    <source>attendance_time</source>
                    <target>Attendancy</target>
                </trans-unit>
                <trans-unit id="diference">
                    <source>diference</source>
                    <target>Difference</target>
                </trans-unit>
                <trans-unit id="amount">
                    <source>amount</source>
                    <target>Amount</target>
                </trans-unit>
            </group>
            <group resname="news">
                <note>Zprávy</note>
                <trans-unit id="News">
                    <source>News</source>
                    <target>News</target>
                </trans-unit>                
                <trans-unit id="news.title">
                    <source>news.title</source>
                    <target>News</target>
                </trans-unit>
                <trans-unit id="news.text">
                    <source>news.text</source>
                    <target>Text</target>
                </trans-unit>                
                <trans-unit id="news_list">
                    <source>news_list</source>
                    <target>News</target>
                </trans-unit> 
                <trans-unit id="news.view_old_news">
                    <source>news.view_old_news</source>
                    <target>Show older news</target>
                </trans-unit>                               
                <trans-unit id="news_pm.title">
                    <source>news_pm.title</source>
                    <target>PM news</target>
                </trans-unit>
                <trans-unit id="news_pm_list">
                    <source>news_pm_list</source>
                    <target>PM news</target>
                </trans-unit>
                <trans-unit id="news_pm_create">
                    <source>news_pm_create</source>
                    <target>PM news create</target>
                </trans-unit>
                <trans-unit id="news_pm_edit">
                    <source>news_pm_edit</source>
                    <target>PM news edit</target>
                </trans-unit>
                <trans-unit id="news_pm_show">
                    <source>news_pm_show</source>
                    <target>PM news show</target>
                </trans-unit>                
                <trans-unit id="news_opm.title">
                    <source>news_opm.title</source>
                    <target>OPM news</target>
                </trans-unit>
                <trans-unit id="news_opm_list">
                    <source>news_opm_list</source>
                    <target>OPM news</target>
                </trans-unit>
                <trans-unit id="news_opm_create">
                    <source>news_opm_create</source>
                    <target>OPM news create</target>
                </trans-unit>
                <trans-unit id="news_opm_edit">
                    <source>news_opm_edit</source>
                    <target>OPM news edit</target>
                </trans-unit>
                <trans-unit id="news_opm_show">
                    <source>news_opm_show</source>
                    <target>OPM news show</target>
                </trans-unit>                 
            </group>             
            <group resname="ckeditor">
                <note>CKEditor</note>
                <trans-unit id="ckeditor.filename">
                    <source>ckeditor.filename</source>
                    <target>File</target>
                </trans-unit>
                <trans-unit id="ckeditor.mimetype">
                    <source>ckeditor.mimetype</source>
                    <target>Type</target>
                </trans-unit> 
                <trans-unit id="ckeditor.size">
                    <source>ckeditor.size</source>
                    <target>Size</target>
                </trans-unit>                               
                <trans-unit id="ckeditor.created">
                    <source>ckeditor.created</source>
                    <target>Inserted</target>
                </trans-unit>
                <trans-unit id="ckeditor.select_file">
                    <source>ckeditor.select_file</source>
                    <target>Choose a file</target>
                </trans-unit>                
                <trans-unit id="ckeditor.upload_error">
                    <source>ckeditor.upload_error</source>
                    <target>File upload error</target>
                </trans-unit>
                <trans-unit id="ckeditor.file_not_image">
                    <source>ckeditor.file_not_image</source>
                    <target>Uploaded file isn't a picture.</target>
                </trans-unit>
            </group>  
            <group resname="module">
                <note>Nadstavby</note>
                <trans-unit id="module.not_installed">
                    <source>module.not_installed</source>
                    <target>Module isn't installed</target>
                </trans-unit>
            </group>                             
            
            <group resname="archives">
                <note>Archiv</note>
                <trans-unit id="menu_archives">
                    <source>menu_archives</source>
                    <target>Archive</target>
                </trans-unit>
                <trans-unit id="menu_archives_service">
                    <source>menu_archives_service</source>
                    <target>Services' archive</target>
                </trans-unit>  
                <trans-unit id="menu_archives_attendance">
                    <source>menu_archives_attendance</source>
                    <target>Attendance archive</target>
                </trans-unit>  
                <trans-unit id="menu_archives_pops">
                    <source>menu_archives_pops</source>
                    <target>SPR archive</target>
                </trans-unit>    
                <trans-unit id="menu_archives_pops_parent">
                    <source>menu_archives_pops_parent</source>
                    <target>SPR archive</target>
                </trans-unit>                   
                <trans-unit id="menu_archives_pops_event">
                    <source>menu_archives_pops_event</source>
                    <target>SPR events' archive</target>
                </trans-unit>
                <trans-unit id="menu_archives_log">
                    <source>menu_archives_log</source>
                    <target>Logs</target>
                </trans-unit>                                                                                   
                <trans-unit id="menu_archives_settings">
                    <source>menu_archives_settings</source>
                    <target>Archive settings</target>
                </trans-unit>                                
                
                <trans-unit id="archives_attendance.title">
                    <source>archives_attendance.title</source>
                    <target>Attendance archive</target>
                </trans-unit> 
                <trans-unit id="archives_attendance_list">
                    <source>archives_attendance_list</source>
                    <target>Attendance archive</target>
                </trans-unit> 
                <trans-unit id="archives_attendance_show">
                    <source>archives_attendance_show</source>
                    <target>Attendance archive detail</target>
                </trans-unit> 
                <trans-unit id="archives_attendance_delete">
                    <source>archives_attendance_delete</source>
                    <target>Delete attendance from the archive</target>
                </trans-unit>    
                <trans-unit id="archives_service.title">
                    <source>archives_service.title</source>
                    <target>Services' archive</target>
                </trans-unit> 
                <trans-unit id="archives_service_list">
                    <source>archives_service_list</source>
                    <target>Services' archive</target>
                </trans-unit> 
                <trans-unit id="archives_service_show">
                    <source>archives_service_show</source>
                    <target>Services' archive detail</target>
                </trans-unit> 
                <trans-unit id="archives_service_delete">
                    <source>archives_service_delete</source>
                    <target>Delete service from the archive</target>
                </trans-unit>     
                <trans-unit id="archives_pops.title">
                    <source>archives_pops.title</source>
                    <target>SPR archive</target>
                </trans-unit> 
                <trans-unit id="archives_pops_list">
                    <source>archives_pops_list</source>
                    <target>SPR archive</target>
                </trans-unit> 
                <trans-unit id="archives_pops_show">
                    <source>archives_pops_show</source>
                    <target>SPR archive detail</target>
                </trans-unit> 
                <trans-unit id="archives_pops_delete">
                    <source>archives_pops_delete</source>
                    <target>Delete SPR including it's events from the archive?</target>
                </trans-unit>   
                <trans-unit id="archives_pops_event.title">
                    <source>archives_pops_event.title</source>
                    <target>SPR events' archive</target>
                </trans-unit> 
                <trans-unit id="archives_pops_event_list">
                    <source>archives_pops_event_list</source>
                    <target>SPR events archive</target>
                </trans-unit> 
                <trans-unit id="archives_pops_event_show">
                    <source>archives_pops_event_show</source>
                    <target>SPR events archive detail</target>
                </trans-unit> 
                <trans-unit id="archives_pops_event_delete">
                    <source>archives_pops_event_delete</source>
                    <target>Delete SPR event from the archive</target>
                </trans-unit> 
                <trans-unit id="archives.link_action_pops">
                    <source>archives.link_action_pops</source>
                    <target>Back to SPR</target>
                </trans-unit>   
                <trans-unit id="menu_archives_multipops">
                    <source>menu_archives_multipops</source>
                    <target>MultiPOPS archive</target>
                </trans-unit>
                <trans-unit id="archives_multi_pops_list">
                    <source>archives_multi_pops_list</source>
                    <target>MultiPOPS archive</target>
                </trans-unit>
              
                <trans-unit id="archives.file_not_found">
                    <source>archives.file_not_found</source>
                    <target>File "%file%" can't be downloaded.</target>
                </trans-unit>
                <trans-unit id="archives.path_info">
                    <source>archives.path_info</source>
                    <target>The file apparently resides in external archive as "%path%"</target>
                </trans-unit>                                               


                <trans-unit id="menu_archives_pops_event_nooccur">
                    <source>menu_archives_pops_event_nooccur</source>
                    <target>Negative SPR event filtering</target>
                </trans-unit>  
                <trans-unit id="permission.PERM_ARCHIVES_POPS_EVENT_NOOCCUR_SHOW">
                    <source>permission.PERM_ARCHIVES_POPS_EVENT_NOOCCUR_SHOW</source>
                    <target>Archive - Negative SPR event filtering</target>
                </trans-unit>  
                
                <trans-unit id="archives_pops_event_no_occur_list">
                    <source>archives_pops_event_no_occur_list</source>
                    <target>Negative SPR event filtering</target>
                </trans-unit>  
                <trans-unit id="archives_pops_event.filter_message_event_period_required">
                    <source>archives_pops_event.filter_message_event_period_required</source>
                    <target>To view the results, select the event and time period.</target>
                </trans-unit>  
                <trans-unit id="archives_pops_event.eventName">
                    <source>archives_pops_event.eventName</source>
                    <target>Event</target>
                </trans-unit>                                                                                                                   
                                                                                                                                  
                                                                                                                                                                                                                                                                    
            </group>             
            
            <group resname="archives.setting">
                <note>Nastavení archivu</note>
                <trans-unit id="archives.setting.title">
                    <source>archives.setting.title</source>
                    <target>Archive setting</target>
                </trans-unit>
                <trans-unit id="archives.setting.description">
                    <source>archives.setting.description</source>
                    <target>Set time interval for (0 = unlimited period)</target>
                </trans-unit>                                
                <trans-unit id="archives.setting.keeping_data_in_application">
                    <source>archives.setting.keeping_data_in_application</source>
                    <target>keeping data in the application</target>
                </trans-unit>
                <trans-unit id="archives.setting.keeping_data_in_archives">
                    <source>archives.setting.keeping_data_in_archives</source>
                    <target>keeping data in the archive</target>
                </trans-unit>    
                <trans-unit id="archives.days">
                    <source>archives.days</source>
                    <target>days</target>
                </trans-unit>      
                <trans-unit id="archives.weeks">
                    <source>archives.weeks</source>
                    <target>weeks</target>
                </trans-unit>   
                <trans-unit id="archives.months">
                    <source>archives.months</source>
                    <target>months</target>
                </trans-unit>   
                <trans-unit id="archives.years">
                    <source>archives.years</source>
                    <target>years</target>
                </trans-unit> 
            </group> 
            
            <group resname="archives_log">
                <note>Log archivu</note>
                <trans-unit id="archives_log_list">
                    <source>archives_log_list</source>
                    <target>Log</target>
                </trans-unit>
                <trans-unit id="archives_log_delete">
                    <source>archives_log_delete</source>
                    <target>Delete log entry</target>
                </trans-unit>
                <trans-unit id="archives_log.executed_at">
                    <source>archives_log.executed_at</source>
                    <target>Done at</target>
                </trans-unit>
                <trans-unit id="archives_log.executed_by">
                    <source>archives_log.executed_by</source>
                    <target>Done by</target>
                </trans-unit>
                <trans-unit id="archives_log.type">
                    <source>archives_log.type</source>
                    <target>Activity</target>
                </trans-unit>
                <trans-unit id="archives_log.severity">
                    <source>archives_log.severity</source>
                    <target>Stav</target>
                </trans-unit>
                <trans-unit id="archives_log.message">
                    <source>archives_log.message</source>
                    <target>Message</target>
                </trans-unit>
                <trans-unit id="archives.exported">
                    <source>archives.exported</source>
                    <target>Exported</target>
                </trans-unit>
            </group>         
            <group resname="billing_annex">
                <note></note>
                <trans-unit id="billing_annex.annex">
                    <source>billing_annex.annex</source>
                    <target>billing annex</target>
                </trans-unit>
                <trans-unit id="billing_annex.period">
                    <source>billing_annex.period</source>
                    <target>BILLING PERIOD</target>
                </trans-unit>
                <trans-unit id="billing_annex.annex_title">
                    <source>billing_annex.annex_title</source>
                    <target>ANNEX TO AN ISSUED INVOICE</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_name">
                    <source>billing_annex.client_name</source>
                    <target>Client's name</target>
                </trans-unit>
                <trans-unit id="billing_annex.object_name">
                    <source>billing_annex.object_name</source>
                    <target>Name of an object</target>
                </trans-unit>
                <trans-unit id="billing_annex.annex_no">
                    <source>billing_annex.annex_no</source>
                    <target>Annex to the invoice no.</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_ico">
                    <source>billing_annex.client_ico</source>
                    <target>Client's ID number</target>
                </trans-unit>
                <trans-unit id="billing_annex.division_no">
                    <source>billing_annex.division_no</source>
                    <target>Division ID</target>
                </trans-unit>
                <trans-unit id="billing_annex.security_manager">
                    <source>billing_annex.security_manager</source>
                    <target>Security Manager</target>
                </trans-unit>
                <trans-unit id="billing_annex.contract_no">
                    <source>billing_annex.contract_no</source>
                    <target>Contract ID</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_person">
                    <source>billing_annex.client_person</source>
                    <target>Client's responsible person</target>
                </trans-unit>
                <trans-unit id="billing_annex.client_person_role">
                    <source>billing_annex.client_person_role</source>
                    <target>Position of customer's responsible person</target>
                </trans-unit>                
                <trans-unit id="billing_annex.contract">
                    <source>billing_annex.contract</source>
                    <target>Contract / Order no.</target>
                </trans-unit>
                <trans-unit id="billing_annex.product_name">
                    <source>billing_annex.product_name</source>
                    <target>Name of a product</target>
                </trans-unit>
                <trans-unit id="billing_annex.billing_of_services">
                    <source>billing_annex.billing_of_services</source>
                    <target>Service according to an agreement/order</target>
                </trans-unit>
                <trans-unit id="billing_annex.responsive_person">
                    <source>billing_annex.responsive_person</source>
                    <target>Person, responsible for the correctness of the annex (by %company%) + signature</target>
                </trans-unit>
                <trans-unit id="billing_annex.hours">
                    <source>billing_annex.hours</source>
                    <target>Hours</target>
                </trans-unit>
                <trans-unit id="billing_annex.rate">
                    <source>billing_annex.rate</source>
                    <target>Rate</target>
                </trans-unit>
                <trans-unit id="billing_annex.sum">
                    <source>billing_annex.sum</source>
                    <target>Total</target>
                </trans-unit>
                <trans-unit id="billing_annex.date">
                    <source>billing_annex.date</source>
                    <target>Day</target>
                </trans-unit>
                <trans-unit id="billing_annex.telefon_abbr">
                    <source>billing_annex.telefon_abbr</source>
                    <target>Phone</target>
                </trans-unit>
                <trans-unit id="billing_annex.ico">
                    <source>billing_annex.ico</source>
                    <target>Company's ID number</target>
                </trans-unit>
            </group>
            <group resname="modal">
                <note>modal editation texts</note>
                <trans-unit id="modal_admin_text.success">
                    <source>modal_admin_text.success</source>
                    <target>Item has been successfully updated.</target>
                </trans-unit>    
                <trans-unit id="modal_admin_text.error">
                    <source>modal_admin_text.error</source>
                    <target>An Error has occurred during sending!</target>
                </trans-unit>
                <trans-unit id="modal_admin_text.confirm_delete">
                    <source>modal_admin_text.confirm_delete</source>
                    <target>Are you sure you want to delete the selected element?</target>
                </trans-unit>
                <trans-unit id="modal_admin_text.sending">
                    <source>modal_admin_text.sending</source>
                    <target>Sending...</target>
                </trans-unit>  
                <trans-unit id="loading">
                    <source>loading</source>
                    <target>Loading...</target>
                </trans-unit>    
            </group>
            <group resname="service_template">
                <note>Service Templates</note>
                <trans-unit id="service_template.batch_generate">
                    <source>service_template.batch_generate</source>
                    <target>Generate to period %period_name%</target>
                </trans-unit>
                <trans-unit id="service_template.cant_batch_generate">
                    <source>service_template.cant_batch_generate</source>
                    <target>Impossible to generate</target>
                </trans-unit>
                <trans-unit id="service_template.title">
                    <source>service_template.title</source>
                    <target>Service template</target>
                </trans-unit>
                <trans-unit id="service_template_list">
                    <source>service_template_list</source>
                    <target>Service templates</target>
                </trans-unit>
                <trans-unit id="service_template_edit">
                    <source>service_template_edit</source>
                    <target>Edit service template</target>
                </trans-unit>
                <trans-unit id="service_template_create">
                    <source>service_template_create</source>
                    <target>Create service template</target>
                </trans-unit>
                <trans-unit id="service_template_delete">
                    <source>service_template_delete</source>
                    <target>Delete service template</target>
                </trans-unit>
                <trans-unit id="menu_service_templates">
                    <source>menu_service_templates</source>
                    <target>Service templates</target>
                </trans-unit>
                <trans-unit id="service_template_generated">
                    <source>service_template_generated</source>
                    <target>Services have been generated</target>
                </trans-unit>
                <trans-unit id="service_template_can_not_generate_error">
                    <source>service_template_can_not_generate_error</source>
                    <target>Impossible to generate - there do exist services in the last accessible period</target>
                </trans-unit>
                <trans-unit id="service_template.cant_remove">
                    <source>service_template.cant_remove</source>
                    <target>Impossible to remove. Templates can be removed for each position only according to shifts descending</target>
                </trans-unit>
            </group>
            <group resname="disciplinary measure">
                <note></note>
                <trans-unit id="menu_disciplinary_measure_create">
                    <source>menu_disciplinary_measure_create</source>
                    <target>New disciplinary measure</target>
                </trans-unit>
                <trans-unit id="menu_disciplinary_measure">
                    <source>menu_disciplinary_measure</source>
                    <target>Disciplinary measures</target>
                </trans-unit>
                <trans-unit id="disciplinary_measure_list">
                    <source>disciplinary_measure_list</source>
                    <target>Disciplinary measures' list</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_CREATE">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_CREATE</source>
                    <target>Disciplinary measure create</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_SHOW">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_SHOW</source>
                    <target>Disciplinary measure show</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_DELETE">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_DELETE</source>
                    <target>Disciplinary measure delete</target>
                </trans-unit>
                <trans-unit id="permission.PERM_DISCIPLINARY_MEASURE_EXPORT">
                    <source>permission.PERM_DISCIPLINARY_MEASURE_EXPORT</source>
                    <target>Export disciplinary measures</target>
                </trans-unit>
                <trans-unit id="setting.disciplinary_mail_title">
                    <source>setting.disciplinary_mail_title</source>
                    <target>Email with disciplinary measures recipients</target>
                </trans-unit>
                <trans-unit id="setting.disciplinary_mail_description">
                    <source>setting.disciplinary_mail_description</source>
                    <target>Input emails, to which the disciplinary measures are going to be sent, divided by semicolon.</target>
                </trans-unit>
                <trans-unit id="setting.disciplinary_mail_help">
                    <source>setting.disciplinary_mail_help</source>
                    <target>Can be left blank, in case you do not want disciplinary measures to be sent</target>
                </trans-unit>       
                <trans-unit id="setting.disciplinary_measure_mail_recipients">
                    <source>setting.disciplinary_measure_mail_recipients</source>
                    <target>Recipients</target>
                </trans-unit>  
                <trans-unit id="disciplinary_measure.title">
                    <source>disciplinary_measure.title</source>
                    <target>Disciplinary measures</target>
                </trans-unit>  
                <trans-unit id="disciplinary_measure_create">
                    <source>disciplinary_measure_create</source>
                    <target>Create a disciplinary measure</target>
                </trans-unit>                
            </group>
            <group resname="tenant">
                <note></note>
                <trans-unit id="menu_tenant">
                    <source>menu_tenant</source>
                    <target>Tenants</target>
                </trans-unit>
                <trans-unit id="tenant_list">
                    <source>tenant_list</source>
                    <target>Tenants</target>
                </trans-unit>
                <trans-unit id="tenant_edit">
                    <source>tenant_edit</source>
                    <target>Edit tenants</target>
                </trans-unit>                
                <trans-unit id="tenant_show">
                    <source>tenant_show</source>
                    <target>Tenant detail</target>
                </trans-unit>  
                <trans-unit id="tenant_create">
                    <source>tenant_create</source>
                    <target>Create a tenant</target>
                </trans-unit> 
                <trans-unit id="tenant_group.title">
                    <source>tenant_group.title</source>
                    <target>Group of tenants</target>
                </trans-unit> 
                <trans-unit id="tenant_group">
                    <source>tenant_group</source>
                    <target>Group</target>
                </trans-unit>                               
                <trans-unit id="tenant_group_list">
                    <source>tenant_group_list</source>
                    <target>Group of tenants</target>
                </trans-unit>
                <trans-unit id="tenant_group_edit">
                    <source>tenant_group_edit</source>
                    <target>Edit group of tenants</target>
                </trans-unit>                
                <trans-unit id="tenant_group_show">
                    <source>tenant_group_show</source>
                    <target>Group of tenants detail</target>
                </trans-unit>  
                <trans-unit id="tenant_group_create">
                    <source>tenant_group_create</source>
                    <target>Create group of tenants</target>
                </trans-unit>                                                 
                <trans-unit id="permission.PERM_TENANT_CREATE">
                    <source>permission.PERM_TENANT_CREATE</source>
                    <target>Create a tenant</target>
                </trans-unit>
                <trans-unit id="permission.PERM_TENANT_EDIT">
                    <source>permission.PERM_TENANT_EDIT</source>
                    <target>Edit a tenant</target>
                </trans-unit>                
                <trans-unit id="permission.PERM_TENANT_SHOW">
                    <source>permission.PERM_TENANT_SHOW</source>
                    <target>Show a tenant</target>
                </trans-unit>
                <trans-unit id="permission.PERM_TENANT_DELETE">
                    <source>permission.PERM_TENANT_DELETE</source>
                    <target>Delete a tenant</target>
                </trans-unit>
                <trans-unit id="permission.PERM_TENANT_EXPORT">
                    <source>permission.PERM_TENANT_EXPORT</source>
                    <target>Export tenants</target>
                </trans-unit>
                <trans-unit id="tenant.title">
                    <source>tenant.title</source>
                    <target>Tenants</target>
                </trans-unit>  
            </group>
            <group resname="suggestion">
                <note></note>
                <trans-unit id="menu_suggestion">
                    <source>menu_suggestion</source>
                    <target>Suggestions</target>
                </trans-unit>
                <trans-unit id="suggestion_list">
                    <source>suggestion_list</source>
                    <target>Suggestions</target>
                </trans-unit>
                <trans-unit id="suggestion_edit">
                    <source>suggestion_edit</source>
                    <target>Acknowledge a suggestion</target>
                </trans-unit>
                <trans-unit id="suggestion_show">
                    <source>suggestion_show</source>
                    <target>Suggestion detail</target>
                </trans-unit>
                <trans-unit id="menu_suggestion_group">
                    <source>menu_suggestion_group</source>
                    <target>Suggestions</target>
                </trans-unit>
                <trans-unit id="suggestion_group_list">
                    <source>suggestion_group_list</source>
                    <target>Suggestions</target>
                </trans-unit>  
                <trans-unit id="suggestion_group_edit">
                    <source>suggestion_group_edit</source>
                    <target>Acknowledge a suggestion</target>
                </trans-unit>
                <trans-unit id="suggestion_group_show">
                    <source>suggestion_group_show</source>
                    <target>Suggestion detail</target>
                </trans-unit>                              
                <trans-unit id="suggestion.title">
                    <source>suggestion.title</source>
                    <target>Suggestion</target>
                </trans-unit>  
                <trans-unit id="suggestion.note">
                    <source>suggestion.note</source>
                    <target>Notice</target>
                </trans-unit> 
                <trans-unit id="suggestion.solved_note">
                    <source>suggestion.solved_note</source>
                    <target>Response</target>
                </trans-unit> 
                <trans-unit id="suggestion.is_solved">
                    <source>suggestion.is_solved</source>
                    <target>Status</target>
                </trans-unit>  
                <trans-unit id="suggestion.solved">
                    <source>suggestion.solved</source>
                    <target>Solved</target>
                </trans-unit>  
                <trans-unit id="suggestion.non_solved">
                    <source>suggestion.non_solved</source>
                    <target>Not resolved</target>
                </trans-unit>                                                
                <trans-unit id="suggestion.solved_by">
                    <source>suggestion.solved_by</source>
                    <target>Solver</target>
                </trans-unit> 
                <trans-unit id="suggestion.solved_at">
                    <source>suggestion.solved_at</source>
                    <target>Solved</target>
                </trans-unit>                                                                 
                <trans-unit id="suggestion.gps">
                    <source>suggestion.gps</source>
                    <target>GPS</target>
                </trans-unit> 
                <trans-unit id="suggestion.show_map">
                    <source>suggestion.show_map</source>
                    <target>Show on a map</target>
                </trans-unit>                 
                <trans-unit id="suggestion.photos">
                    <source>suggestion.photos</source>
                    <target>Photos</target>
                </trans-unit>    
                <trans-unit id="suggestion.solved_email_subject">
                    <source>suggestion.solved_email_subject</source>
                    <target>Solving M2C suggestion ID %suggestionId% from the object %object%</target>
                </trans-unit> 
                <trans-unit id="suggestion.solved_email_message">
                    <source>suggestion.solved_email_message</source>
                    <target xml:space="preserve">Hello, 
we have solved Your suggestion ID %suggestionId% (%note%) from the object %object% in the following way: 

%message% 

Your M2C team.</target>
                </trans-unit>                                                              
            </group>            
            <group resname="export">
                <note></note>
                <trans-unit id="export.emergency_hours">
                    <source>export.emergency_hours</source>
                    <target>Readiness</target>
                </trans-unit>  
                <trans-unit id="export.controlled_zone_hours">
                    <source>export.controlled_zone_hours</source>
                    <target>Controlled zone</target>
                </trans-unit>                  
                <trans-unit id="export.weekend_hours">
                    <source>export.weekend_hours</source>
                    <target>Weekend</target>
                </trans-unit>  
                <trans-unit id="export.saturday_hours">
                    <source>export.saturday_hours</source>
                    <target>Saturday</target>
                </trans-unit> 
                <trans-unit id="export.sunday_hours">
                    <source>export.sunday_hours</source>
                    <target>Sunday</target>
                </trans-unit>                                 
                <trans-unit id="export.night_hours">
                    <source>export.night_hours</source>
                    <target>Night</target>
                </trans-unit>  
                <trans-unit id="holiday_hours">
                    <source>holiday_hours</source>
                    <target>Holiday</target>
                </trans-unit>  
                <trans-unit id="export.managerial_allowance">
                    <source>export.managerial_allowance</source>
                    <target>Management surcharge (rate)</target>
                </trans-unit>                
                <trans-unit id="export.managerial_allowance_hours">
                    <source>export.managerial_allowance_hours</source>
                    <target>Management surcharge (hours)</target>
                </trans-unit>                
            </group>  
            <group resname="pool">
                <note></note>
                <trans-unit id="pool.title">
                    <source>pool.title</source>
                    <target>Swimming pool</target>
                </trans-unit>  
                <trans-unit id="pool_list">
                    <source>pool_list</source>
                    <target>Swimming pool</target>
                </trans-unit>  
                <trans-unit id="menu_pool">
                    <source>menu_pool</source>
                    <target>Swimming pool</target>
                </trans-unit>                
                <trans-unit id="pool_delete">
                    <source>pool_delete</source>
                    <target>Remove subject from the pool</target>
                </trans-unit>  
                <trans-unit id="pool_edit">
                    <source>pool_edit</source>
                    <target>Edit subject in the pool</target>
                </trans-unit> 
                <trans-unit id="pool_show">
                    <source>pool_show</source>
                    <target>Subject detail</target>
                </trans-unit>                                                               
                <trans-unit id="pool.subject">
                    <source>pool.subject</source>
                    <target>Subject</target>
                </trans-unit>  
                <trans-unit id="pool.contract">
                    <source>pool.contract</source>
                    <target>Contract</target>
                </trans-unit>  
                <trans-unit id="pool.object">
                    <source>pool.object</source>
                    <target>Object</target>
                </trans-unit>  
                <trans-unit id="pool.object2">
                    <source>pool.object2</source>
                    <target>Target object</target>
                </trans-unit>                 
                <trans-unit id="pool.date">
                    <source>pool.date</source>
                    <target>Date</target>
                </trans-unit>  
                <trans-unit id="pool.shift">
                    <source>pool.shift</source>
                    <target>Shift</target>
                </trans-unit>  
                <trans-unit id="pool.note">
                    <source>pool.note</source>
                    <target>Note</target>
                </trans-unit>                 
                <trans-unit id="pool.rate">
                    <source>pool.rate</source>
                    <target>Rate</target>
                </trans-unit>  
                <trans-unit id="pool.phone">
                    <source>pool.phone</source>
                    <target>Telephone</target>
                </trans-unit>   
                <trans-unit id="pool.day">
                    <source>pool.day</source>
                    <target>daily</target>
                </trans-unit>  
                <trans-unit id="pool.night">
                    <source>pool.night</source>
                    <target>night</target>
                </trans-unit>  
                <trans-unit id="pool.allday">
                    <source>pool.allday</source>
                    <target>whenever</target>
                </trans-unit>                                                                
                <trans-unit id="pool.check_contract_failed">
                    <source>pool.check_contract_failed</source>
                    <target>Unable to save. The subject does not have a valid contract on the specified date.</target>
                </trans-unit>   
                <trans-unit id="pool.flash_delete_failed">
                    <source>pool.flash_delete_failed</source>
                    <target>You do not have permission to delete this record.</target>
                </trans-unit>                                   
                <trans-unit id="permission.PERM_POOL_SHOW">
                    <source>permission.PERM_POOL_SHOW</source>
                    <target>Swimming pool</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_POOL_RATE_SHOW">
                    <source>permission.PERM_POOL_RATE_SHOW</source>
                    <target>Pool - view rate</target>
                </trans-unit>                              
            </group>                       
            <group resname="documentation">
                <note></note>
                <trans-unit id="documentation.title">
                    <source>documentation.title</source>
                    <target>Documentation</target>
                </trans-unit>  
                <trans-unit id="documentation_list">
                    <source>documentation_list</source>
                    <target>Central work regulations</target>
                </trans-unit>  
                <trans-unit id="documentation_object">
                    <source>documentation_object</source>
                    <target>Operational documentation</target>
                </trans-unit>
                <trans-unit id="documentation_bozp">
                    <source>documentation_bozp</source>
                    <target>FP + OSH documentation (Fire protection and Occupation safety and health)</target>
                </trans-unit>                                
                <trans-unit id="menu_documentation">
                    <source>menu_documentation</source>
                    <target>Documentation</target>
                </trans-unit>   
                <trans-unit id="menu_documentation_cpr">
                    <source>menu_documentation_cpr</source>
                    <target>Central work regulations</target>
                </trans-unit>  
                <trans-unit id="menu_documentation_object">
                    <source>menu_documentation_object</source>
                    <target>Operational documentation</target>
                </trans-unit>  
                <trans-unit id="menu_documentation_bozp">
                    <source>menu_documentation_bozp</source>
                    <target>FP + OSH documentation (Fire protection and Occupation safety and health)</target>
                </trans-unit>                                                               
                <trans-unit id="documentation_delete">
                    <source>documentation_delete</source>
                    <target>Delete the documentation</target>
                </trans-unit>  
                <trans-unit id="documentation_create">
                    <source>documentation_create</source>
                    <target>Create documentation</target>
                </trans-unit>                 
                <trans-unit id="documentation_edit">
                    <source>documentation_edit</source>
                    <target>Edit the documentation</target>
                </trans-unit> 
                <trans-unit id="documentation_show">
                    <source>documentation_show</source>
                    <target>Contents of documentation</target>
                </trans-unit> 
                <trans-unit id="documentation_m2csol.title">
                    <source>documentation_m2csol.title</source>
                    <target>Specialized documentation</target>
                </trans-unit>  
                <trans-unit id="documentation_m2csol_list">
                    <source>documentation_m2csol_list</source>
                    <target>Central work regulations</target>
                </trans-unit>  
                <trans-unit id="documentation_m2csol_object">
                    <source>documentation_m2csol_object</source>
                    <target>Operational documentation</target>
                </trans-unit>
                <trans-unit id="documentation_m2csol_bozp">
                    <source>documentation_m2csol_bozp</source>
                    <target>FP + OSH documentation (Fire protection and Occupation safety and health)</target>
                </trans-unit>                 
                <trans-unit id="menu_documentation_m2csol">
                    <source>menu_documentation_m2csol</source>
                    <target>Specialized documentation</target>
                </trans-unit>     
                <trans-unit id="menu_documentation_m2csol_cpr">
                    <source>menu_documentation_m2csol_cpr</source>
                    <target>Central work regulations</target>
                </trans-unit>  
                <trans-unit id="menu_documentation_m2csol_object">
                    <source>menu_documentation_m2csol_object</source>
                    <target>Operational documentation</target>
                </trans-unit>  
                <trans-unit id="menu_documentation_m2csol_bozp">
                    <source>menu_documentation_m2csol_bozp</source>
                    <target>FP + OSH documentation (Fire protection and Occupation safety and health)</target>
                </trans-unit>                            
                <trans-unit id="documentation_m2csol_delete">
                    <source>documentation_m2csol_delete</source>
                    <target>Delete the documentation</target>
                </trans-unit>  
                <trans-unit id="documentation_m2csol_create">
                    <source>documentation_m2csol_create</source>
                    <target>Create documentation</target>
                </trans-unit>                 
                <trans-unit id="documentation_m2csol_edit">
                    <source>documentation_m2csol_edit</source>
                    <target>Edit the documentation</target>
                </trans-unit> 
                <trans-unit id="documentation_m2csol_show">
                    <source>documentation_m2csol_show</source>
                    <target>Contents of documentation</target>
                </trans-unit>                                                                               
                <trans-unit id="documentation.action">
                    <source>documentation.action</source>
                    <target>Action</target>
                </trans-unit>                 
                <trans-unit id="documentation.titlef">
                    <source>documentation.titlef</source>
                    <target>Name</target>
                </trans-unit>  
                <trans-unit id="documentation.text">
                    <source>documentation.text</source>
                    <target>Content</target>
                </trans-unit>  
                <trans-unit id="documentation.pdf_loading">
                    <source>documentation.pdf_loading</source>
                    <target>Loading content ...</target>
                </trans-unit>  
                <trans-unit id="documentation.pdf_not_loaded">
                    <source>documentation.pdf_not_loaded</source>
                    <target>Content failed to load!</target>
                </trans-unit>                                  
                <trans-unit id="documentation.flash_delete">
                    <source>documentation.flash_delete</source>
                    <target>File deleted</target>
                </trans-unit>                    
                <trans-unit id="documentation.flash_delete_failed">
                    <source>documentation.flash_delete_failed</source>
                    <target>The file could not be deleted</target>
                </trans-unit>                 
                <trans-unit id="permission.PERM_DOCUMENTATION_SHOW">
                    <source>permission.PERM_DOCUMENTATION_SHOW</source>
                    <target>Documentation - Central work order view</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_DOCUMENTATION_OBJECT_SHOW">
                    <source>permission.PERM_DOCUMENTATION_OBJECT_SHOW</source>
                    <target>Documentation - Operational documentation view</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_DOCUMENTATION_BOZP_SHOW">
                    <source>permission.PERM_DOCUMENTATION_BOZP_SHOW</source>
                    <target>Documentation - Health and safety documentation view</target>
                </trans-unit>                                   
                <trans-unit id="permission.PERM_DOCUMENTATION_EDIT">
                    <source>permission.PERM_DOCUMENTATION_EDIT</source>
                    <target>Documentation - create and edit Central work order</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_DOCUMENTATION_DELETE">
                    <source>permission.PERM_DOCUMENTATION_DELETE</source>
                    <target>Documentation - delete Central work order</target>
                </trans-unit> 
                <trans-unit id="permission.PERM_DOCUMENTATION_OBJECT_DELETE">
                    <source>permission.PERM_DOCUMENTATION_OBJECT_DELETE</source>
                    <target>Documentation - delete Operational documentation</target>
                </trans-unit>                   
                <trans-unit id="permission.PERM_DOCUMENTATION_BOZP_DELETE">
                    <source>permission.PERM_DOCUMENTATION_BOZP_DELETE</source>
                    <target>Documentation - delete Health and safety documentation</target>
                </trans-unit>                  
                <trans-unit id="permission.PERM_DOCUMENTATION_M2CSOL_SHOW">
                    <source>permission.PERM_DOCUMENTATION_M2CSOL_SHOW</source>
                    <target>Specialized documentation</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_DOCUMENTATION_M2CSOL_EDIT">
                    <source>permission.PERM_DOCUMENTATION_M2CSOL_EDIT</source>
                    <target>Spec. doc - create and edit Central work order</target>
                </trans-unit>   
                <trans-unit id="permission.PERM_DOCUMENTATION_M2CSOL_DELETE">
                    <source>permission.PERM_DOCUMENTATION_M2CSOL_DELETE</source>
                    <target>Spec. doc - delete Central work order</target>
                </trans-unit>                   
                <trans-unit id="permission.PERM_DOCUMENTATION_M2CSOL_OBJECT_DELETE">
                    <source>permission.PERM_DOCUMENTATION_M2CSOL_OBJECT_DELETE</source>
                    <target>Spec. doc - delete Operational documentation</target>
                </trans-unit>                   
                <trans-unit id="permission.PERM_DOCUMENTATION_M2CSOL_BOZP_DELETE">
                    <source>permission.PERM_DOCUMENTATION_M2CSOL_BOZP_DELETE</source>
                    <target>Spec. doc - delete Health and safety documentation</target>
                </trans-unit>                   
                
            </group>      
            <group resname="pops_activity">
                <note></note>
                <trans-unit id="pops_activity.title">
                    <source>pops_activity.title</source>
                    <target>SPR activity</target>
                </trans-unit>  
                <trans-unit id="pops_activity.dialog_description">
                    <source>pops_activity.dialog_description</source>
                    <target>Immediately inspect the object and write to the PM!</target>
                </trans-unit>  
                <trans-unit id="pops_activity.dialog_description1">
                    <source>pops_activity.dialog_description1</source>
                    <target>Immediately check the "%object%" object and write to the PM!</target>
                </trans-unit>  
                <trans-unit id="pops_activity.dialog_description2">
                    <source>pops_activity.dialog_description2</source>
                    <target>Immediately inspect the objects and write to the PM! Objects: %objects%</target>
                </trans-unit>                                  
                <trans-unit id="pops_activity.dialog_button">
                    <source>pops_activity.dialog_button</source>
                    <target>OK</target>
                </trans-unit>                
                <trans-unit id="pops_activity.dialog_getpops">
                    <source>pops_activity.dialog_getpops</source>
                    <target>Add an event</target>
                </trans-unit>  
            </group>     
            <group resname="overtime_control">
                <note></note>
                <trans-unit id="menu_controlset">
                    <source>menu_controlset</source>
                    <target>Control reports</target>
                </trans-unit> 
                <trans-unit id="menu_overtime_control">
                    <source>menu_overtime_control</source>
                    <target>Control report "overtime" according to plan</target>
                </trans-unit> 
                <trans-unit id="menu_overtime_control_setting">
                    <source>menu_overtime_control_setting</source>
                    <target>Overtime control report - Settings</target>
                </trans-unit>                                                 
                
                
                
                <trans-unit id="overtime_control.title">
                    <source>overtime_control.title</source>
                    <target>Overtime control report - Settings</target>
                </trans-unit>  
                <trans-unit id="overtime_control_setting.title">
                    <source>overtime_control_setting.title</source>
                    <target>Overtime control report - Settings</target>
                </trans-unit>  

                <trans-unit id="overtime_control_list">
                    <source>overtime_control_list</source>
                    <target>Control report "overtime" according to plan</target>
                </trans-unit>                 
                                                
                <trans-unit id="overtime_control_setting_list">
                    <source>overtime_control_setting_list</source>
                    <target>Overtime control report - Settings</target>
                </trans-unit> 
                <trans-unit id="overtime_control_setting_edit">
                    <source>overtime_control_setting_edit</source>
                    <target>Editing an overtime control report item</target>
                </trans-unit> 
                <trans-unit id="overtime_control_setting_show">
                    <source>overtime_control_setting_show</source>
                    <target>View the "overtime" checklist item</target>
                </trans-unit> 
                <trans-unit id="overtime_control_setting_create">
                    <source>overtime_control_setting_create</source>
                    <target>Create an overtime control report item</target>
                </trans-unit> 
                
                <trans-unit id="overtime_control.extern_coop">
                    <source>overtime_control.extern_coop</source>
                    <target>Voluntary</target>
                </trans-unit> 
                <trans-unit id="overtime_control.extern_coop_a">
                    <source>overtime_control.extern_coop_a</source>
                    <target>Voluntary A</target>
                </trans-unit> 
                <trans-unit id="overtime_control.extern_coop_b">
                    <source>overtime_control.extern_coop_b</source>
                    <target>Voluntary B</target>
                </trans-unit>
                <trans-unit id="overtime_control.hours">
                    <source>overtime_control.hours</source>
                    <target>Hours</target>
                </trans-unit>
                <trans-unit id="overtime_control.amount">
                    <source>overtime_control.amount</source>
                    <target>Served</target>
                </trans-unit>
                <trans-unit id="overtime_control.rate">
                    <source>overtime_control.rate</source>
                    <target>Rate</target>
                </trans-unit>
                <trans-unit id="overtime_control.company">
                    <source>overtime_control.company</source>
                    <target>Company</target>
                </trans-unit>
                <trans-unit id="overtime_control.personalno">
                    <source>overtime_control.personalno</source>
                    <target>Personal number</target>
                </trans-unit>                                
                <trans-unit id="overtime_control.subject">
                    <source>overtime_control.subject</source>
                    <target>Surname and name</target>
                </trans-unit>  
                <trans-unit id="overtime_control.pay_point_code">
                    <source>overtime_control.pay_point_code</source>
                    <target>Payout point</target>
                </trans-unit>  
                <trans-unit id="overtime_control.object">
                    <source>overtime_control.object</source>
                    <target>Object</target>
                </trans-unit>    
                <trans-unit id="overtime_control.filter_date">
                    <source>overtime_control.filter_date</source>
                    <target>Period</target>
                </trans-unit>
                <trans-unit id="overtime_control.filter_message_period_required">
                    <source>overtime_control.filter_message_period_required</source>
                    <target>Set the Period filter to display the results.</target>
                </trans-unit>

                <trans-unit id="permission.PERM_OVERTIME_CONTROL_SHOW">
                    <source>permission.PERM_OVERTIME_CONTROL_SHOW</source>
                    <target>Show overtime checklist</target>
                </trans-unit>                                
                <trans-unit id="permission.PERM_OVERTIME_CONTROL_EXPORT">
                    <source>permission.PERM_OVERTIME_CONTROL_EXPORT</source>
                    <target>Export overtime checklist</target>
                </trans-unit>                                

                <trans-unit id="permission.PERM_OVERTIME_CONTROL_SETTING_SHOW">
                    <source>permission.PERM_OVERTIME_CONTROL_SETTING_SHOW</source>
                    <target>View overtime control report settings</target>
                </trans-unit>                                
                <trans-unit id="permission.PERM_OVERTIME_CONTROL_SETTING_CREATE">
                    <source>permission.PERM_OVERTIME_CONTROL_SETTING_CREATE</source>
                    <target>Create "overtime" checklist settings</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OVERTIME_CONTROL_SETTING_EDIT">
                    <source>permission.PERM_OVERTIME_CONTROL_SETTING_EDIT</source>
                    <target>Adjust "overtime" control report settings</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OVERTIME_CONTROL_SETTING_EDIT_FULL">
                    <source>permission.PERM_OVERTIME_CONTROL_SETTING_EDIT_FULL</source>
                    <target>Full adjustment of "overtime" control report settings</target>
                </trans-unit>
                <trans-unit id="permission.PERM_OVERTIME_CONTROL_SETTING_DELETE">
                    <source>permission.PERM_OVERTIME_CONTROL_SETTING_DELETE</source>
                    <target>Delete overtime checklist settings</target>
                </trans-unit>
                                                
            </group>                              
        </body>
    </file>
</xliff>