<?php

declare(strict_types = 1);

namespace Pm\Core\Repository;

use DateTime;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Query\Expr\Andx;
use Doctrine\ORM\Query\Expr\Comparison;
use Doctrine\ORM\Query\Expr\Composite;
use Doctrine\ORM\Query\Expr\Func;
use Doctrine\ORM\Query\Expr\Orx;
use Doctrine\ORM\QueryBuilder;
use Pm\Account\Entity\Account;
use Pm\Core\Enum\FilterTypeEnum;
use Pm\Core\Enum\OperatorTypeEnum;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;
use RuntimeException;
use function array_map;
use function array_unshift;
use function count;
use function explode;
use function in_array;
use function lcfirst;
use function str_contains;
use function str_ends_with;
use function str_replace;
use function ucwords;

class BaseRepository
{

    public function __construct(protected EntityManager $entityManager)
    {
    }

    /**
     * To be overriden in entity repositories to implement custom expressions that might need additional subqueries
     */
    protected function createCriteriaExpression(FilterInput $filterInput): Comparison|Func|null
    {
        return null;
    }

    protected function applyCriteria(
        ?Account $account,
        QueryBuilder $queryBuilder,
        ?FilterInputInterface $filters,
        ?int $limit = null,
        ?int $offset = null,
        ?string $sortBy = null,
        ?string $sortMethod = null,
    ): QueryBuilder
    {
        $rootAliases = $queryBuilder->getRootAliases();
        $rootAlias = $rootAliases[0];

        if ($account !== null) {
            $queryBuilder->andWhere($rootAlias . '.account = ' . $account->getId());
        }

        /**
         * @var array<Composite> $statements
         */
        $statements = [];

        if ($filters !== null && $filters->getFilterArgs() !== null) {
            $inc = 1;

            foreach ($filters->getFilterArgs() as $filterInput) {
                $field = $filterInput->getField();

                // custom expressions (to be overriden in entity repo)
                $expression = $this->createCriteriaExpression($filterInput);

                // standard expressions
                if (!$expression instanceof Comparison && !$expression instanceof Func) {
                    // prepend alias if not from input
                    $field = !str_contains($field, '.') ? $rootAlias . '.' . $field : $field;

                    // auto-join if possible
                    if (str_contains($field, '.')) {
                        [$alias] = explode('.', $field);
                        if (!in_array($alias, $queryBuilder->getAllAliases(), true)) {
                            $queryBuilder->leftJoin($rootAlias . '.' . $alias, $alias);
                        }
                    } else {
                        $field = $rootAlias . '.' . $field;
                    }

                    // get filter variabe input type
                    $varType = $filters->getFilterVarType($filterInput);

                    // unique param key for query builder
                    $param = ':p' . $inc;
                    $param2 = ':p' . $inc . 'b';
                    $inc++;
                    switch ($varType) {
                        case Types::DATE_IMMUTABLE:
                        case Types::DATETIME_IMMUTABLE:
                            if ($filterInput->getType() === FilterTypeEnum::BETWEEN) {
                                $values = explode(',', str_replace('+', ' ', $filterInput->getValue()));
                                if (count($values) !== 2) {
                                    throw new RuntimeException('#[' . $field . ']#: Invalid Input Value for Filter Type Between.');
                                }
                                $queryBuilder->setParameter($param, (new DateTimeImmutable(str_replace('+', ' ', $values[0])))->format(DateTime::W3C));
                                $queryBuilder->setParameter($param2, (new DateTimeImmutable(str_replace('+', ' ', $values[1])))->format(DateTime::W3C));
                            } else {
                                $queryBuilder->setParameter($param, $filterInput->getDateTimeValue());
                            }
                            break;
                        case Types::INTEGER:
                            if ($filterInput->getType() === FilterTypeEnum::BETWEEN) {
                                $values = explode(',', $filterInput->getValue());
                                if (count($values) !== 2) {
                                    throw new RuntimeException('#[' . $field . ']#: Invalid Input Value for Filter Type Between.');
                                }
                                $queryBuilder->setParameter($param, $filterInput->getDateTimeValue());
                                $queryBuilder->setParameter($param2, $filterInput->getDateTimeValue());
                            } elseif ($filterInput->getType() === FilterTypeEnum::IN || $filterInput->getType() === FilterTypeEnum::NOT_IN) {
                                $values = array_map('intval', explode(',', $filterInput->getValue()));
                                $queryBuilder->setParameter($param, $values);
                            } else {
                                $queryBuilder->setParameter($param, $filterInput->getIntValue());
                            }
                            break;
                        default:
                            if ($filterInput->getType() === FilterTypeEnum::BETWEEN) {
                                throw new RuntimeException('#[' . $field . ']#: Invalid Filter Type for Var Type: #[' . $varType . ']#');
                            }

                            if ($filterInput->getType() === FilterTypeEnum::IN || $filterInput->getType() === FilterTypeEnum::NOT_IN) {
                                $values = explode(',', $filterInput->getValue());
                                $queryBuilder->setParameter($param, $values);
                            } elseif (!in_array($filterInput->getType(), [FilterTypeEnum::CONTAINS, FilterTypeEnum::STARTS_WITH, FilterTypeEnum::ENDS_WITH], true)) {
                                $queryBuilder->setParameter($param, $filterInput->getValue());
                            }
                    }

                    $expression = match ($filterInput->getType()) {
                        FilterTypeEnum::CONTAINS, FilterTypeEnum::STARTS_WITH, FilterTypeEnum::ENDS_WITH =>
                            $queryBuilder->expr()->like(
                                (string) $queryBuilder->expr()->lower($field),
                                (string) $queryBuilder->expr()->lower($filterInput->getPattern()),
                            ),
                        FilterTypeEnum::IN => $queryBuilder->expr()->in($field, $param),
                        FilterTypeEnum::NOT_IN => $queryBuilder->expr()->notIn($field, $param),
                        FilterTypeEnum::GREATER_THAN => $queryBuilder->expr()->gt($field, $param),
                        FilterTypeEnum::LESS_THAN => $queryBuilder->expr()->lt($field, $param),
                        FilterTypeEnum::MATCH => $queryBuilder->expr()->eq($field, $param),
                        FilterTypeEnum::BETWEEN => $queryBuilder->expr()->andX($queryBuilder->expr()->gt($field, $param), $queryBuilder->expr()->lt($field, $param2)) ,
                    };
                }

                if ($filterInput->getOperator() === OperatorTypeEnum::OR) {
                    // or
                    if (count($statements) > 0 && $statements[0] instanceof Orx) {
                        $statements[0]->add($expression);
                    } else {
                        array_unshift($statements, $queryBuilder->expr()->orX($expression));
                    }
                } else {
                    // and
                    if (count($statements) > 0 && $statements[0] instanceof Andx) {
                        $statements[0]->add($expression);
                    } else {
                        array_unshift($statements, $queryBuilder->expr()->andX($expression));
                    }
                }

            }
        }

        if ($sortBy !== null && $sortMethod !== null) {
            // snake to camel
            if (str_contains($sortBy, '.')) {
                [$alias, $sortBy] = explode('.', $sortBy);
                $sortBy = lcfirst(str_replace(' ', '', ucwords(str_replace('_', ' ', $sortBy))));
                if (!in_array($alias, $queryBuilder->getAllAliases(), true)) {
                    $queryBuilder->leftJoin($rootAlias . '.' . $alias, $alias);
                }
            } else {
                $alias = $rootAlias;
            }

            if (str_ends_with($sortBy, 'Count')) {
                $queryBuilder->addSelect('(CASE WHEN ' . $alias . '.' . $sortBy . ' IS NULL THEN 0 ELSE '
                    . $alias . '.' . $sortBy . ' END) AS HIDDEN SortByCountWithNull');
                $queryBuilder->addOrderBy('SortByCountWithNull', $sortMethod);
            } else {
                $queryBuilder->addOrderBy($alias . '.' . $sortBy, $sortMethod);
            }
        }

        $queryBuilder->setFirstResult($offset);
        $queryBuilder->setMaxResults($limit);

        foreach ($statements as $statment) {
            $queryBuilder->andWhere($statment);
        }

        return $queryBuilder;
    }

}
