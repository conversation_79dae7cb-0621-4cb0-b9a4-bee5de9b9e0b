<?php

declare(strict_types = 1);

namespace Pm\Core\Entity\Traits;

use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Gedmo\Mapping\Annotation\Blameable;
use Pm\User\Entity\User;

trait BlameableInsertEntity
{

    #[ManyToOne]
    #[JoinColumn(name: 'inserted_by', nullable: true)]
    #[Blameable(on: 'create')]
    protected ?User $insertedBy = null;

    public function setInsertedBy(User $insertedBy): self
    {
        $this->insertedBy = $insertedBy;

        return $this;
    }

    public function getInsertedBy(): ?User
    {
        return $this->insertedBy;
    }

}
