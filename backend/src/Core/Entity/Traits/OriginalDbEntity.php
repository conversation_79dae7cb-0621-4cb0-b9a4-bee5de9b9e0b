<?php

declare(strict_types = 1);

namespace Pm\Core\Entity\Traits;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Pm\Account\Entity\Account;

/**
 * Temporary trait for entities that are migrated from the original PM2 database.
 * It will be removed when PM2 is completely replaced by PM3.
 */
trait OriginalDbEntity
{

    #[Column(type: Types::STRING, length: 50, unique: true, nullable: true)]
    protected ?string $originalDbId = null;

    #[Column(type: Types::BOOLEAN, nullable: false, options: ['default' => false])]
    protected bool $migratedCompletely = false;

    #[ManyToOne(targetEntity: Account::class)]
    #[JoinColumn(nullable: false)]
    protected Account $account;

    public function getOriginalDbId(): ?string
    {
        return $this->originalDbId;
    }

    public function setOriginalDbId(?string $originalDbId): void
    {
        $this->originalDbId = $originalDbId;
    }

    public function isMigratedCompletely(): bool
    {
        return $this->migratedCompletely;
    }

    public function setMigratedCompletely(bool $migratedCompletely): void
    {
        $this->migratedCompletely = $migratedCompletely;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function setAccount(Account $account): void
    {
        $this->account = $account;
    }

}
