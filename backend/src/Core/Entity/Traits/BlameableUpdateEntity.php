<?php

declare(strict_types = 1);

namespace Pm\Core\Entity\Traits;

use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Gedmo\Mapping\Annotation\Blameable;
use Pm\User\Entity\User;

trait BlameableUpdateEntity
{

    #[ManyToOne]
    #[JoinColumn(name: 'updated_by', nullable: true)]
    #[Blameable(on: 'update')]
    protected ?User $updatedBy;

    public function setUpdatedBy(User $updatedBy): self
    {
        $this->updatedBy = $updatedBy;

        return $this;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

}
