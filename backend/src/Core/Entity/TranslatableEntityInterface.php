<?php

declare(strict_types = 1);

namespace Pm\Core\Entity;

use Gedmo\Translatable\Translatable;
use Pm\Core\Input\TranslationsInput;

/**
 * Class for translatable interface entity.
 */
interface TranslatableEntityInterface extends EntityInterface, Translatable
{

    /**
     * @return array<int, TranslationsInput>
     */
    public function getTranslations(): array;

    /**
     * @param array<int, TranslationsInput> $translations
     */
    public function setTranslations(array $translations): void;

}
