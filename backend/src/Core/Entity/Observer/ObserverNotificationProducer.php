<?php

declare(strict_types = 1);

namespace Pm\Core\Entity\Observer;

use Nette\Utils\Json;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AbstractConnection;
use Pm\Core\Controller\ApiOutput;
use Pm\Core\Producer\BaseProducer;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function time;

class ObserverNotificationProducer extends BaseProducer
{

    /**
     * @param AMQPChannel|null ...$otherParams
     */
    public function __construct(
        AbstractConnection $conn,
        #[Autowire('%env(ENTITY_OBSERVER_QUEUE_NAME)%')]
        private readonly string $queueName,
        #[Autowire('%env(ENTITY_OBSERVER_EXCHANGE_NAME)%')]
        private readonly string $exchangeName,
        mixed ...$otherParams,
    )
    {
        parent::__construct($conn, ...$otherParams);
    }

    public function handle(string $event, ApiOutput $apiOutput, string $class, int $accountId, ?int $entityId): void
    {
        $this->setExchangeOptions(['name' => $this->exchangeName, 'type' => 'direct']);
        $this->setQueueOptions(['name' => $this->queueName, 'routing_keys' => [$this->exchangeName]]);

        $this->publish(
            msgBody: Json::encode([
                'event' => $event,
                'account_id' => $accountId,
                'entity_class' => $class,
                'entity_id' => $entityId,
                'entity_data' => $apiOutput,
            ]),
            routingKey: $this->exchangeName,
            additionalProperties: [
                'delivery_mode' => 2,
                'timestamp' => time(),
            ],
        );
    }

}
