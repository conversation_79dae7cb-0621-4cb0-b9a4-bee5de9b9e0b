<?php

declare(strict_types = 1);

namespace Pm\Core\Entity\Observer;

use Exception;
use Pm\Core\Exception\LogicException;
use Pm\Core\Http\ObserverResponseCreatorFactory;
use Pm\Log\Logger;

readonly class EntityObserver
{

    public const EVENT_PERSIST = 'persist';
    public const EVENT_UPDATE = 'update';
    public const EVENT_DELETE_ATTACHMENT = 'delete-attachment';

    public function __construct(
        private ObserverNotificationProducer $observedEntityProducer,
        private ObserverResponseCreatorFactory $observerResponseCreatorFactory,
        private Logger $logger,
    )
    {
    }

    public function handleEvent(string $event, ObservedEntityInterface $entity, ?string $deletedOriginalDbId = null): void
    {
        try {
            $observerResponse = $this->observerResponseCreatorFactory->create($entity::class, $entity, $event, $deletedOriginalDbId);
        } catch (Exception $exception) {
            $this->logger->logInfoMessage($exception->getMessage());
            return;
        }

        if ($event !== self::EVENT_DELETE_ATTACHMENT) {
            try {
                // deleted entities won't have an ID
                $entityId = $entity->getId();
            } catch (LogicException) {
                $entityId = null;
            }
        }

        $this->observedEntityProducer->handle($event, $observerResponse, $entity::class, $entity->getAccount()->getId(), $entityId ?? null);
    }

}
