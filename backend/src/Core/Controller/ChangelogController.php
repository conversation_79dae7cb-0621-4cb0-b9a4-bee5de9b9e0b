<?php

declare(strict_types = 1);

namespace Pm\Core\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use Nette\Utils\FileSystem;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ChangelogController extends BaseController
{

    #[Get(path: '/api/v1/changelog')]
    public function renderChangelog(): JsonResponse
    {
        $json = FileSystem::read(__DIR__ . '/../../../docs/changelog.json');

        return new JsonResponse($json, Response::HTTP_OK, [], true);
    }

}
