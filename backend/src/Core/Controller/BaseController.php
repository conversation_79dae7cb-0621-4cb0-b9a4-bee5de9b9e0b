<?php

declare(strict_types = 1);

namespace Pm\Core\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Pm\Core\Input\InputArgs;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\Response;
use Yectep\PhpSpreadsheetBundle\PhpSpreadsheet\Response\XlsxResponse;
use function iconv;
use function is_string;
use function ob_get_clean;
use function ob_start;

abstract class BaseController extends AbstractFOSRestController
{

    protected function getInputArgs(ParameterBag $bag): InputArgs
    {
        return InputArgs::create($bag->all());
    }

    public function createPdfResponse(string $filename, string $data): Response
    {
        $response = new Response($data);

        $response->headers->set('Content-Type', 'application/pdf');

        $sanitizedFilename = iconv('UTF-8', 'ASCII//TRANSLIT', $filename);
        if (!is_string($sanitizedFilename)) {
            $filenameFallback = 'export.pdf';
        } else {
            $filenameFallback = $sanitizedFilename;
        }

        $response->headers->set('Content-Disposition', HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            $filename,
            $filenameFallback,
        ));

        return $response;
    }

    protected function createSpreadsheetResponse(string $filename, Spreadsheet $spreadsheet): XlsxResponse
    {
        $writer = new Xlsx($spreadsheet);

        ob_start();
        $writer->save('php://output');
        $excelOutput = ob_get_clean();

        return new XlsxResponse($excelOutput, $filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

}
