<?php
// phpcs:disable PSR1.Files.SideEffects


declare(strict_types = 1);

namespace Pm\Core\Controller;

use Symfony\Component\HttpFoundation\Response;

final readonly class SuccessCountableOutput implements ApiOutput
{

    /**
     * @param array<mixed> $list
     */
    public function __construct(public array $list = [], public int $totalCount = 0, public int $code = Response::HTTP_OK)
    {
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getTotalCount(): int
    {
        return $this->totalCount;
    }

    /**
     * @return array<mixed>
     */
    public function getList(): array
    {
        return $this->list;
    }

}
