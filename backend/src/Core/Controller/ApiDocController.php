<?php

declare(strict_types = 1);

namespace Pm\Core\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use Nette\Utils\FileSystem;
use Symfony\Component\HttpFoundation\Response;
use function str_replace;

class ApiDocController extends BaseController
{

    #[Get(path: '/api/doc')]
    public function renderApiDoc(): Response
    {
        $response = new Response();
        $response->headers->add([
            'Content-Type' => 'text/html',
            'charset' => 'utf-8',
        ]);

        $html = FileSystem::read(__DIR__ . '/../../../docs/openApi-template.html');
        $json = FileSystem::read(__DIR__ . '/../../../docs/openApi/backend.json');
        $json = '{"spec": ' . $json . '}';

        $finalOpenApi = str_replace('{"replace": "here"}', $json, $html);

        $response->setContent($finalOpenApi);

        return $response;
    }

    #[Get(path: '/api/doc.json')]
    public function renderApiDocJson(): Response
    {
        $response = new Response();
        $response->headers->add([
            'Content-Type' => 'application/json',
            'charset' => 'utf-8',
        ]);

        $json = FileSystem::read(__DIR__ . '/../../../docs/openApi/backend.json');

        $response->setContent($json);

        return $response;
    }

}
