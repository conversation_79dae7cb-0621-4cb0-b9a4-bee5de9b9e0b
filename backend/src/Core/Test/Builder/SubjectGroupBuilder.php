<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Subject\Entity\Subject;
use Pm\Subject\Entity\SubjectGroup;

class SubjectGroupBuilder implements EntityBuilder
{

    private ?string $name = null;

    private bool $active = true;

    /**
     * @var array<int, Subject>
     */
    private array $subjects = [];

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): SubjectGroup
    {
        $seqInt = $this->objectFactory->createAutoIncrementNumber(self::class);
        $name = $this->name ?? "Subject Group {$seqInt}";

        $subjectGroup = new SubjectGroup(
            $name,
            $this->active,
        );

        foreach ($this->subjects as $subject) {
            $subjectGroup->addSubject($subject);
        }

        $this->objectFactory->getEntityManager()->persist($subjectGroup);

        return $subjectGroup;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;
        return $this;
    }

    public function addSubject(Subject $subject): self
    {
        $this->subjects[] = $subject;
        return $this;
    }

    /**
     * @param array<int, Subject> $subjects
     */
    public function setSubjects(array $subjects): self
    {
        $this->subjects = $subjects;
        return $this;
    }

}
