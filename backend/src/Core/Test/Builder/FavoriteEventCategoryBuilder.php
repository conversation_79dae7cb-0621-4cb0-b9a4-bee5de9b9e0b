<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\EventCategory\Entity\EventCategory;
use Pm\Facility\Entity\Facility;
use Pm\Facility\Entity\FavoriteEventCategory;

class FavoriteEventCategoryBuilder implements EntityBuilder
{

    private EventCategory $eventCategory;

    protected Facility $facility;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): FavoriteEventCategory
    {
        $favoriteEventCategory = new FavoriteEventCategory($this->facility, $this->eventCategory);
        $favoriteEventCategory->setAccount($this->facility->getAccount());
        $this->objectFactory->getEntityManager()->persist($favoriteEventCategory);

        return $favoriteEventCategory;
    }

    public function setFacility(Facility $facility): self
    {
        $this->facility = $facility;
        return $this;
    }

    public function setEventCategory(EventCategory $eventCategory): self
    {
        $this->eventCategory = $eventCategory;
        return $this;
    }

}
