<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Account\Entity\Account;
use Pm\Facility\Entity\Facility;
use Pm\Tenant\Entity\Tenant;

class TenantBuilder implements EntityBuilder
{

    private ?string $name = null;

    private ?string $phone = null;

    private ?string $email = null;

    private bool $active = true;

    private ?Account $account = null;

    private ?Facility $facility = null;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): Tenant
    {
        $seqInt = $this->objectFactory->createAutoIncrementNumber(self::class);
        $name = $this->name ?? "Tenant Name {$seqInt}";
        $phone = $this->phone ?? "+***********{$seqInt}";
        $email = $this->email ?? "email{$seqInt}@foo.com";
        $active = $this->active ?? true;
        $account = $this->account ?? $this->objectFactory->getAccountBuilder()->build();
        $facility = $this->facility ?? $this->objectFactory->getFacilityBuilder()
            ->setAccount($account)
            ->build();

        $tenant = new Tenant(
            $name,
            $phone,
            $email,
            $active,
            $facility,
        );
        $tenant->setAccount($account);
        $this->objectFactory->getEntityManager()->persist($tenant);

        return $tenant;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function setPhone(string $phone): self
    {
        $this->phone = $phone;
        return $this;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;
        return $this;
    }

    public function setAccount(Account $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function setFacility(Facility $facility): self
    {
        $this->facility = $facility;
        return $this;
    }

}
