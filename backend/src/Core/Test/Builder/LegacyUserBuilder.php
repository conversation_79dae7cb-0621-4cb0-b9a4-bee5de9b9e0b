<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use DateTimeImmutable;
use Pm\Account\Entity\Account;
use Pm\User\Entity\User;
use Pm\User\Enum\UserRole;
use function md5;
use function sha1;
use function substr;
use function uniqid;

class LegacyUserBuilder implements EntityBuilder
{

    public const USER_PASSWORD = 'userPassword';
    public const CUSTOMER_PASSWORD = 'customerPassword';

    private ?string $personalNo = null;

    private ?Account $account = null;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): User
    {
        $manager = $this->objectFactory->getEntityManager();

        if ($this->personalNo !== null) {
            $personalNo = $this->personalNo;
        } else {
            /* random string of 4 characters */
            $randomPrefixString = substr(md5(uniqid()), 0, 4);
            $personalNo = "gsuser{$randomPrefixString}";
        }

        $user = new User($personalNo, UserRole::USER, self::USER_PASSWORD);
        $user->setEmail(substr(md5(uniqid()), 0, 4) . '<EMAIL>');
        $user->setCellPhone('+************');
        $user->setPermissionRole('ROLE_USER');
        $user->setPersonalNo($personalNo);
        $user->setPassword(sha1(self::USER_PASSWORD));
        $user->setPm2Password(sha1(self::USER_PASSWORD));
        $user->setPasswordCustomer(sha1(self::CUSTOMER_PASSWORD));
        $user->setPm2PasswordCustomer(sha1(self::CUSTOMER_PASSWORD));
        $user->setFirstName('Test');
        $user->setLastName('User');
        $user->setSubjectType(0);
        $user->setPosition('GS zaměstnanec');
        $user->setIsContact(false);
        $user->setUpdatedAt(new DateTimeImmutable());
        $user->setCreatedAt(new DateTimeImmutable());
        $user->enable();
        if ($this->account !== null) {
            $user->setAccount($this->account);
        } else {
            $user->setAccount($this->objectFactory->getAccountBuilder()->build());
        }

        $manager->persist($user);

        return $user;
    }

    public function setAccount(?Account $account): self
    {
        $this->account = $account;
        return $this;
    }

    public function setPersonalNo(?string $personalNo): self
    {
        $this->personalNo = $personalNo;
        return $this;
    }

}
