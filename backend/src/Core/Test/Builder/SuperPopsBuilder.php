<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use DateTimeImmutable;
use Pm\Account\Entity\Account;
use Pm\Facility\Entity\Facility;
use Pm\SuperPops\Entity\SuperPops;
use Pm\User\Entity\User;

class SuperPopsBuilder implements EntityBuilder
{

    /**
     * @var array<Facility>|null
     */
    private ?array $facilities = null;

    private ?Account $account = null;

    private ?DateTimeImmutable $createdAt = null;

    private ?User $insertedBy = null;

    private ?DateTimeImmutable $startsAt = null;

    private ?DateTimeImmutable $endsAt = null;

    public function __construct(
        private readonly ObjectFactory $objectFactory,
    )
    {
    }

    public function build(): SuperPops
    {
        $now = new DateTimeImmutable();

        if (!$this->account instanceof Account) {
            $this->account = $this->objectFactory->getAccountBuilder()->build();
        }

        $superPops = new SuperPops(
            $this->account,
            $this->startsAt ?? new DateTimeImmutable($now->format('Y-m-d 00:00:00')),
            $this->endsAt ?? new DateTimeImmutable($now->format('Y-m-d 23:59:59')),
        );

        if ($this->createdAt !== null) {
            $superPops->setCreatedAt($this->createdAt);
        }

        if ($this->insertedBy !== null) {
            $superPops->setInsertedBy($this->insertedBy);
        }

        if ($this->facilities !== null) {
            $superPops->addFacilities($this->facilities);
        }

        $this->objectFactory->getEntityManager()->persist($superPops);

        return $superPops;
    }

    /**
     * @param array<Facility> $facilities
     * @return $this
     */
    public function setFacilities(array $facilities): self
    {
        $this->facilities = $facilities;
        return $this;
    }

    public function setAccount(Account $account): self
    {
        $this->account = $account;
        return $this;
    }

    public function setStartsAt(DateTimeImmutable $startsAt): self
    {
        $this->startsAt = $startsAt;
        return $this;
    }

    public function setEndsAt(DateTimeImmutable $endsAt): self
    {
        $this->endsAt = $endsAt;
        return $this;
    }

    public function setCreatedAt(DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function setInsertedBy(User $insertedBy): self
    {
        $this->insertedBy = $insertedBy;
        return $this;
    }

}
