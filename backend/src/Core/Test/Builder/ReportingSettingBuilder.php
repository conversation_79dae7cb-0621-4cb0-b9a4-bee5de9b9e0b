<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\EventCategory\Entity\EventCategory;
use Pm\Reporting\Entity\Reporting;
use Pm\Reporting\Entity\ReportingSetting;
use RuntimeException;

class ReportingSettingBuilder implements EntityBuilder
{

    private ?EventCategory $eventCategory = null;

    private ?Reporting $reporting = null;

    private ?int $sendingType = null;

    public function __construct(
        private readonly ObjectFactory $objectFactory,
    )
    {
    }

    public function withEventCategory(EventCategory $eventCategory): self
    {
        $this->eventCategory = $eventCategory;

        return $this;
    }

    public function withReporting(Reporting $reporting): self
    {
        $this->reporting = $reporting;

        return $this;
    }

    public function withSendingType(int $sendingType): self
    {
        $this->sendingType = $sendingType;

        return $this;
    }

    public function build(): ReportingSetting
    {
        if (!$this->reporting instanceof Reporting) {
            throw new RuntimeException('Reporting not set.');
        }
        if (!$this->eventCategory instanceof EventCategory) {
            throw new RuntimeException('Event category not set.');
        }
        $reportingSetting = new ReportingSetting(
            $this->reporting,
            $this->eventCategory,
            $this->sendingType ?? 7,
        );

        $this->objectFactory->getEntityManager()->persist($reportingSetting);

        return $reportingSetting;
    }

}
