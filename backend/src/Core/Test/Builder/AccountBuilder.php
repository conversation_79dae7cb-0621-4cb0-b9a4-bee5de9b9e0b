<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Account\Entity\Account;

class AccountBuilder implements EntityBuilder
{

    private ?string $name = null;

    private bool $isActive = true;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): Account
    {
        $account = new Account(
            $this->name ?? "Account {$this->objectFactory->createAutoIncrementNumber(self::class)}",
        );

        if (!$this->isActive) {
            $account->deactivate();
        }

        $this->objectFactory->getEntityManager()->persist($account);
        return $account;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function deactivated(): self
    {
        $this->isActive = false;
        return $this;
    }

}
