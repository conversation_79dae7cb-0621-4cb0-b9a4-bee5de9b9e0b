<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder\Permission;

use Pm\Core\Test\Builder\EntityBuilder;
use Pm\Core\Test\Builder\ObjectFactory;
use Pm\Permission\Entity\Permission;
use Pm\Permission\Entity\PermissionApplication;
use Pm\Permission\Enum\PermissionEnum;
use function uniqid;

class PermissionBuilder implements EntityBuilder
{

    private ?PermissionApplication $permissionApplication = null;

    private ?PermissionEnum $code = null;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): Permission
    {
        if ($this->permissionApplication === null) {
            $this->permissionApplication = $this->objectFactory->getPermissionApplicationBuilder()
                ->setCode('APP_' . uniqid())
                ->build();
        }

        $permission = new Permission(
            $this->permissionApplication,
            $this->code ?? PermissionEnum::PERM_COMMON,
        );

        $this->objectFactory->getEntityManager()->persist($permission);

        return $permission;
    }

    public function setPermissionApplication(PermissionApplication $permissionApplication): self
    {
        $this->permissionApplication = $permissionApplication;
        return $this;
    }

    public function setCode(PermissionEnum $code): self
    {
        $this->code = $code;
        return $this;
    }

}
