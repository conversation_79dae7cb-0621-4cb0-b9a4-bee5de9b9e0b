<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder\Permission;

use Doctrine\Common\Collections\ArrayCollection;
use Pm\Core\Test\Builder\EntityBuilder;
use Pm\Core\Test\Builder\ObjectFactory;
use Pm\Facility\Entity\Facility;
use Pm\Permission\Entity\Permission;
use Pm\Permission\Entity\PermissionGroup;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\User\Entity\User;
use function count;
use function in_array;
use function uniqid;

class PermissionGroupBuilder implements EntityBuilder
{

    private ?string $permissionGroupName = null;

    private ?string $permissionTemplateName = null;

    /**
     * @var array<PermissionTemplate>
     */
    private array $permissionTemplates = [];

    /**
     * @var array<Permission>
     */
    private array $permissions = [];

    private User $user;

    /**
     * @var array<int, Facility>
     */
    private array $facilities = [];

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): PermissionGroup
    {
        $this->permissionTemplates ??= [];
        $this->permissions ??= [];
        $this->facilities ??= [];

        $permissionGroup = new PermissionGroup(
            $this->permissionGroupName ?? 'Test Group' . uniqid(),
            $this->user->getAccount(),
        );

        $permissionTemplates = new ArrayCollection();
        $permissionGroup->addUser($this->user);
        if (count($this->permissionTemplates) > 0) {
            foreach ($this->permissionTemplates as $permissionTemplate) {
                if ($this->permissionTemplateName !== null) {
                    $permissionTemplate->setName($this->permissionTemplateName);
                }
                foreach ($this->permissions as $permission) {
                    $permissionTemplate->addPermission($permission);
                }
                $permissionTemplates->add($permissionTemplate);
                $this->user->addPermissionTemplate($permissionTemplate);
            }
        } else {
            $permissionTemplate = $this->objectFactory->getPermissionTemplateBuilder()
                ->setUser($this->user)
                ->build();
            if ($this->permissionTemplateName !== null) {
                $permissionTemplate->setName($this->permissionTemplateName);
            }

            foreach ($this->permissions as $permission) {
                $permissionTemplate->addPermission($permission);
            }
            $permissionTemplates->add($permissionTemplate);
        }

        foreach ($permissionTemplates as $permissionTemplate) {
            $permissionGroup->addPermissionTemplate($permissionTemplate);
            $this->user->addPermissionTemplate($permissionTemplate);
        }

        foreach ($this->facilities as $facility) {
            $permissionGroup->addFacility($facility);
        }
        $permissionGroup->setInsertedBy($this->user);
        $permissionGroup->setUpdatedBy($this->user);

        $this->objectFactory->getEntityManager()->persist($permissionGroup);

        return $permissionGroup;
    }

    public function setPermissionGroupName(string $permissionGroupName): self
    {
        $this->permissionGroupName = $permissionGroupName;
        return $this;
    }

    public function setPermissionTemplateName(string $permissionTemplateName): self
    {
        $this->permissionTemplateName = $permissionTemplateName;
        return $this;
    }

    public function setPermissionTemplate(PermissionTemplate $permissionTemplate): self
    {
        $this->permissionTemplates[] = $permissionTemplate;
        return $this;
    }

    /**
     * @param list<PermissionTemplate> $permissionTemplates
     */
    public function setPermissionTemplates(array $permissionTemplates): self
    {
        $this->permissionTemplates = $permissionTemplates;
        return $this;
    }

    public function addPermission(Permission $permission): self
    {
        if (!in_array($permission, $this->permissions, true)) {
            $this->permissions[] = $permission;
        }
        return $this;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function addFacility(Facility $facility): self
    {
        $this->facilities[] = $facility;

        return $this;
    }

}
