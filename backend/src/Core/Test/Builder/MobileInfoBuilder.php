<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Mobile\Entity\MobileInfo;
use Pm\User\Entity\User;
use RuntimeException;

class MobileInfoBuilder implements EntityBuilder
{

    private ?User $user = null;

    private string $registrationToken = 'aaabbbccc';

    private string $locale = 'cs_CZ';

    public function __construct(
        private readonly ObjectFactory $objectFactory,
    )
    {
    }

    public function withUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function build(): MobileInfo
    {
        if (!$this->user instanceof User) {
            throw new RuntimeException('User not set.');
        }

        $mobileInfo = new MobileInfo();
        $mobileInfo->setUser($this->user);
        $mobileInfo->setRegistrationToken($this->registrationToken);
        $mobileInfo->setLocale($this->locale);
        $mobileInfo->setAccount($this->user->getAccount());

        $this->objectFactory->getEntityManager()->persist($mobileInfo);

        return $mobileInfo;
    }

}
