<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Notification\Entity\Notification;
use Pm\Notification\Enum\NotificationPriority;
use Pm\Notification\Enum\NotificationSource;
use Pm\Notification\Enum\NotificationType;
use Pm\Pops\Entity\PopsEvent;
use Pm\User\Entity\User;
use RuntimeException;

class NotificationBuilder implements EntityBuilder
{

    private ?User $user = null;

    private ?PopsEvent $popsEvent = null;

    private ?NotificationSource $source = null;

    private ?NotificationPriority $priority = null;

    private ?NotificationType $type = null;

    public function __construct(
        private readonly ObjectFactory $objectFactory,
    )
    {
    }

    public function withUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function withPopsEvent(PopsEvent $popsEvent): self
    {
        $this->popsEvent = $popsEvent;

        return $this;
    }

    public function withSource(NotificationSource $notificationSource): self
    {
        $this->source = $notificationSource;

        return $this;
    }

    public function withType(NotificationType $notificationType): self
    {
        $this->type = $notificationType;

        return $this;
    }

    public function withPriority(NotificationPriority $notificationPriority): self
    {
        $this->priority = $notificationPriority;

        return $this;
    }

    public function build(): Notification
    {
        if (!$this->source instanceof NotificationSource) {
            throw new RuntimeException('Source not set.');
        }
        if (!$this->type instanceof NotificationType) {
            throw new RuntimeException('Type not set.');
        }
        if (!$this->priority instanceof NotificationPriority) {
            throw new RuntimeException('Priority not set.');
        }
        if (!$this->user instanceof User) {
            throw new RuntimeException('User not set.');
        }
        if (!$this->popsEvent instanceof PopsEvent) {
            throw new RuntimeException('PopsEvent not set.');
        }
        $notification = new Notification(
            $this->source,
            $this->type,
            $this->priority,
            $this->user,
            $this->popsEvent,
        );

        $this->objectFactory->getEntityManager()->persist($notification);

        return $notification;
    }

}
