<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Currency\Entity\Currency;
use function uniqid;

class CurrencyBuilder implements EntityBuilder
{

    private ?string $name = null;

    private ?string $abbr = null;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): Currency
    {
        $name = $this->name ?? 'Currency Name' . uniqid();
        $abbr = $this->abbr ?? 'CUR' . uniqid();

        $currency = new Currency($name, $abbr);
        $this->objectFactory->getEntityManager()->persist($currency);

        return $currency;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function setAbbr(string $abbr): self
    {
        $this->abbr = $abbr;

        return $this;
    }

}
