<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Mobile\Entity\MobileSetting;
use Pm\User\Entity\User;
use RuntimeException;

class MobileSettingBuilder implements EntityBuilder
{

    private ?User $user = null;

    public function __construct(
        private readonly ObjectFactory $objectFactory,
    )
    {
    }

    public function withUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function build(): MobileSetting
    {
        if (!$this->user instanceof User) {
            throw new RuntimeException('User not set.');
        }

        $mobileSetting = new MobileSetting(
            $this->user,
            true,
            true,
        );
        $mobileSetting->setAccount($this->user->getAccount());

        $this->objectFactory->getEntityManager()->persist($mobileSetting);

        return $mobileSetting;
    }

}
