<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Pm\Subject\Entity\Subject;

class SubjectBuilder implements EntityBuilder
{

    private ?string $firstName = null;

    private ?string $lastName = null;

    private ?string $gid = null;

    private ?string $titleBeforeName = null;

    private ?string $titleAfterName = null;

    private ?string $email = null;

    private ?string $phone = null;

    private bool $active = true;

    public function __construct(private readonly ObjectFactory $objectFactory)
    {
    }

    public function build(): Subject
    {
        $seqInt = $this->objectFactory->createAutoIncrementNumber(self::class);
        $firstName = $this->firstName ?? "John{$seqInt}";
        $lastName = $this->lastName ?? "Doe{$seqInt}";
        $gid = $this->gid ?? "123456789{$seqInt}";
        $titleBeforeName = $this->titleBeforeName;
        $titleAfterName = $this->titleAfterName;
        $email = $this->email ?? "subject{$seqInt}@example.com";
        $phone = $this->phone ?? "+42077712345{$seqInt}";
        $active = $this->active ?? true;

        $subject = new Subject(
            $firstName,
            $lastName,
            $gid,
            $titleBeforeName,
            $titleAfterName,
            $email,
            $phone,
            $active,
        );
        $this->objectFactory->getEntityManager()->persist($subject);

        return $subject;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function setGid(?string $gid): self
    {
        $this->gid = $gid;
        return $this;
    }

    public function setTitleBeforeName(?string $titleBeforeName): self
    {
        $this->titleBeforeName = $titleBeforeName;
        return $this;
    }

    public function setTitleAfterName(?string $titleAfterName): self
    {
        $this->titleAfterName = $titleAfterName;
        return $this;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;
        return $this;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;
        return $this;
    }

}
