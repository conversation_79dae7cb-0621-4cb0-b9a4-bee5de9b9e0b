<?php

declare(strict_types = 1);

namespace Pm\Core\Test\Builder;

use Doctrine\ORM\EntityManager;
use Pm\Core\Test\Builder\Permission\PermissionApplicationBuilder;
use Pm\Core\Test\Builder\Permission\PermissionBuilder;
use Pm\Core\Test\Builder\Permission\PermissionGroupBuilder;
use Pm\Core\Test\Builder\Permission\PermissionTemplateBuilder;
use function array_key_exists;

class ObjectFactory
{

    /**
     * @var int[]
     */
    private array $autoIncrementNumbers = [];

    public function __construct(private readonly EntityManager $entityManager)
    {
    }

    public function resetAutoIncrementNumber(): void
    {
        $this->autoIncrementNumbers = [];
    }

    public function createAutoIncrementNumber(string $namespace): int
    {
        if (!array_key_exists($namespace, $this->autoIncrementNumbers)) {
            $this->autoIncrementNumbers[$namespace] = 0;
        }

        return ++$this->autoIncrementNumbers[$namespace];
    }

    public function getCompanyBuilder(): CompanyBuilder
    {
        return new CompanyBuilder($this);
    }

    public function getContractBuilder(): ContractBuilder
    {
        return new ContractBuilder($this);
    }

    public function getCurrencyBuilder(): CurrencyBuilder
    {
        return new CurrencyBuilder($this);
    }

    public function getCustomerBuilder(): CustomerBuilder
    {
        return new CustomerBuilder($this);
    }

    public function getCustomerUserBuilder(): CustomerUserBuilder
    {
        return new CustomerUserBuilder($this);
    }

    public function getEntityManager(): EntityManager
    {
        return $this->entityManager;
    }

    public function getUserBuilder(): UserBuilder
    {
        return new UserBuilder($this);
    }

    public function getLegacyUserBuilder(): LegacyUserBuilder
    {
        return new LegacyUserBuilder($this);
    }

    public function getFacilityBuilder(): FacilityBuilder
    {
        return new FacilityBuilder($this);
    }

    public function getPermissionGroupBuilder(): PermissionGroupBuilder
    {
        return new PermissionGroupBuilder($this);
    }

    public function getFavoriteEventCategoryBuilder(): FavoriteEventCategoryBuilder
    {
        return new FavoriteEventCategoryBuilder($this);
    }

    public function getPopsBuilder(): PopsBuilder
    {
        return new PopsBuilder($this);
    }

    public function getSuperPopsBuilder(): SuperPopsBuilder
    {
        return new SuperPopsBuilder($this);
    }

    public function getPopsAlertBuilder(): PopsAlertBuilder
    {
        return new PopsAlertBuilder($this);
    }

    public function getAccountBuilder(): AccountBuilder
    {
        return new AccountBuilder($this);
    }

    public function getEventCategoryBuilder(): EventCategoryBuilder
    {
        return new EventCategoryBuilder($this);
    }

    public function getPermissionBuilder(): PermissionBuilder
    {
        return new PermissionBuilder($this);
    }

    public function getPermissionApplicationBuilder(): PermissionApplicationBuilder
    {
        return new PermissionApplicationBuilder($this);
    }

    public function getPopsEventBuilder(): PopsEventBuilder
    {
        return new PopsEventBuilder($this);
    }

    public function getProjectBuilder(): ProjectBuilder
    {
        return new ProjectBuilder($this);
    }

    public function getPermissionTemplateBuilder(): PermissionTemplateBuilder
    {
        return new PermissionTemplateBuilder($this);
    }

    public function getTenantGroupBuilder(): TenantGroupBuilder
    {
        return new TenantGroupBuilder($this);
    }

    public function getTenantBuilder(): TenantBuilder
    {
        return new TenantBuilder($this);
    }

    public function getUnitBuilder(): UnitBuilder
    {
        return new UnitBuilder($this);
    }

    public function getReportingBuilder(): ReportingBuilder
    {
        return new ReportingBuilder($this);
    }

    public function getReportingSettingBuilder(): ReportingSettingBuilder
    {
        return new ReportingSettingBuilder($this);
    }

    public function getMobileInfoBuilder(): MobileInfoBuilder
    {
        return new MobileInfoBuilder($this);
    }

    public function getMobileSettingBuilder(): MobileSettingBuilder
    {
        return new MobileSettingBuilder($this);
    }

    public function getNotificationBuilder(): NotificationBuilder
    {
        return new NotificationBuilder($this);
    }

    public function getFacilityGroupBuilder(): FacilityGroupBuilder
    {
        return new FacilityGroupBuilder($this);
    }

    public function getLegislationBuilder(): LegislationBuilder
    {
        return new LegislationBuilder($this);
    }

    public function getSubjectBuilder(): SubjectBuilder
    {
        return new SubjectBuilder($this);
    }

    public function getSubjectGroupBuilder(): SubjectGroupBuilder
    {
        return new SubjectGroupBuilder($this);
    }

}
