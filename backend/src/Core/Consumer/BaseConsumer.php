<?php

declare(strict_types = 1);

namespace Pm\Core\Consumer;

use Nette\Utils\Json;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Input\InputArgs;

abstract class BaseConsumer implements ConsumerInterface
{

    /**
     * @throws InvalidInputException
     */
    protected function getInputArgs(AMQPMessage $message): InputArgs
    {
        return InputArgs::create(
            Json::decode($message->body, Json::FORCE_ARRAY),
        );
    }

}
