<?php

declare(strict_types = 1);

namespace Pm\Core\Validator\Constraints;

use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumber as PhoneNumberObject;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberType;
use libphonenumber\PhoneNumberUtil;
use Override;
use Pm\Core\Exception\UnexpectedTypeException;
use Symfony\Component\PropertyAccess\Exception\NoSuchPropertyException;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\ConstraintDefinitionException;
use Symfony\Component\Validator\Exception\LogicException;
use function array_unique;
use function class_exists;
use function count;
use function get_debug_type;
use function implode;
use function in_array;
use function is_object;
use function is_scalar;
use function method_exists;
use function preg_match;
use function sprintf;
use function strlen;

/**
 * Phone number validator.
 */
class PhoneNumberValidator extends ConstraintValidator
{

    private readonly PhoneNumberUtil $phoneUtil;

    private ?PropertyAccessorInterface $propertyAccessor = null;

    public function __construct(
        ?PhoneNumberUtil $phoneUtil = null,
        private readonly string $defaultRegion = PhoneNumberUtil::UNKNOWN_REGION,
        private readonly int $format = PhoneNumberFormat::INTERNATIONAL,
    )
    {
        $this->phoneUtil = $phoneUtil ?? PhoneNumberUtil::getInstance();
    }

    #[Override]
    public function validate(mixed $value, Constraint $constraint): void
    {
        /**
         * @var PhoneNumber $phoneNumberConstraint
         */
        $phoneNumberConstraint = $constraint;

        if ($value === null || $value === '') {
            return;
        }

        if (!is_scalar($value) && !(is_object($value) && method_exists($value, '__toString'))) {
            throw new UnexpectedTypeException($value, 'string');
        }

        if ($value instanceof PhoneNumberObject) {
            $phoneNumber = $value;

            $stringValue = $this->phoneUtil->format($phoneNumber, $this->format);
        } else {
            $stringValue = (string) $value;

            try {
                $phoneNumber = $this->phoneUtil->parse($stringValue, $this->getRegion($constraint));
            } catch (NumberParseException $e) {
                $this->addViolation($stringValue, $constraint, $e->getMessage());
                return;
            }
        }

        if ($this->phoneUtil->isValidNumber($phoneNumber) === false) {
            // check international prefix
            $regionCode = $this->phoneUtil->getRegionCodeForNumber($phoneNumber);
            if ($regionCode === null) {
                $this->addViolation($stringValue, $constraint, 'Invalid phone region prefix.');
                return;
            }
            // check characters
            if (preg_match('/^[\d ()+-]+$/', $stringValue) === false) {
                $this->addViolation($stringValue, $constraint, 'Phone number contains invalid characters.');
                return;
            }
            // check phone number length
            $nationalNumber = $this->phoneUtil->getNationalSignificantNumber($phoneNumber);
            if (strlen($nationalNumber) !== 9) {
                $this->addViolation($stringValue, $constraint, 'Invalid phone number length.');
                return;
            }

            // check local operator prefix
            $numberType = $this->phoneUtil->getNumberType($phoneNumber);
            if ($numberType === PhoneNumberType::UNKNOWN) {
                // only other option is invalid local prefix type
                $this->addViolation($stringValue, $constraint, 'Invalid local prefix or number format.');
                return;
            }

            // unknown error
            $this->addViolation($stringValue, $constraint);
            return;
        }

        $validTypes = [];
        foreach ($phoneNumberConstraint->getTypes() as $type) {
            switch ($type) {
                case PhoneNumber::FIXED_LINE:
                    $validTypes[] = PhoneNumberType::FIXED_LINE;
                    $validTypes[] = PhoneNumberType::FIXED_LINE_OR_MOBILE;
                    break;
                case PhoneNumber::MOBILE:
                    $validTypes[] = PhoneNumberType::MOBILE;
                    $validTypes[] = PhoneNumberType::FIXED_LINE_OR_MOBILE;
                    break;
                case PhoneNumber::PAGER:
                    $validTypes[] = PhoneNumberType::PAGER;
                    break;
                case PhoneNumber::PERSONAL_NUMBER:
                    $validTypes[] = PhoneNumberType::PERSONAL_NUMBER;
                    break;
                case PhoneNumber::PREMIUM_RATE:
                    $validTypes[] = PhoneNumberType::PREMIUM_RATE;
                    break;
                case PhoneNumber::SHARED_COST:
                    $validTypes[] = PhoneNumberType::SHARED_COST;
                    break;
                case PhoneNumber::TOLL_FREE:
                    $validTypes[] = PhoneNumberType::TOLL_FREE;
                    break;
                case PhoneNumber::UAN:
                    $validTypes[] = PhoneNumberType::UAN;
                    break;
                case PhoneNumber::VOIP:
                    $validTypes[] = PhoneNumberType::VOIP;
                    break;
                case PhoneNumber::VOICEMAIL:
                    $validTypes[] = PhoneNumberType::VOICEMAIL;
                    break;
            }
        }

        $validTypes = array_unique($validTypes);

        if (0 < count($validTypes)) {
            $type = $this->phoneUtil->getNumberType($phoneNumber);

            if (!in_array($type, $validTypes, true)) {
                $typeName = $phoneNumberConstraint->getTypeName((string) $validTypes[0]);
                $message = "This value is not a valid $typeName.";
                $this->addViolation($stringValue, $constraint, $message);
            }
        }
    }

    private function getRegion(Constraint $constraint): string
    {
        /**
         * @var PhoneNumber $phoneNumberConstraint
         */
        $phoneNumberConstraint = $constraint;

        $defaultRegion = null;
        $path = $phoneNumberConstraint->regionPath;
        if ($path !== null) {
            $object = $this->context->getObject();
            if ($object === null) {
                throw new LogicException('The current validation does not concern an object.');
            }

            try {
                $defaultRegion = $this->getPropertyAccessor()->getValue($object, $path);
            } catch (NoSuchPropertyException $e) {
                throw new ConstraintDefinitionException(sprintf('Invalid property path "#[%s]#" provided to "#[%s]#" constraint: ', $path, get_debug_type($constraint)) . $e->getMessage(), 0, $e);
            }
        }

        return $defaultRegion ?? $phoneNumberConstraint->defaultRegion ?? $this->defaultRegion;
    }

    public function setPropertyAccessor(PropertyAccessorInterface $propertyAccessor): void
    {
        $this->propertyAccessor = $propertyAccessor;
    }

    private function getPropertyAccessor(): PropertyAccessorInterface
    {
        if ($this->propertyAccessor === null) {
            if (!class_exists(PropertyAccess::class)) {
                throw new LogicException('Unable to use property path as the Symfony PropertyAccess component is not installed.');
            }
            $this->propertyAccessor = PropertyAccess::createPropertyAccessor();
        }

        return $this->propertyAccessor;
    }

    /**
     * Add a violation.
     *
     * @param Constraint $constraint the constraint for the validation
     */
    private function addViolation(mixed $value, Constraint $constraint, ?string $error_message = null): void
    {
        /**
         * @var PhoneNumber $phoneNumberConstraint
         */
        $phoneNumberConstraint = $constraint;

        $message = $phoneNumberConstraint->getMessage();
        if ($message === null) {
            if ($error_message !== null) {
                $message = $error_message;
            } else {
                $types = $phoneNumberConstraint->getTypes();

                if (count($types) === 1) {
                    $typeName = $phoneNumberConstraint->getTypeName($types[0]);
                    $message = "This value is not a valid $typeName.";
                }

                $message = 'This value is not a valid phone number.';
            }
        }

        $this->context->buildViolation($message)
            ->setParameter('{{ types }}', implode(', ', $phoneNumberConstraint->getTypeNames()))
            ->setParameter('{{ value }}', $this->formatValue($value))
            ->setCode($error_message ?? PhoneNumber::INVALID_PHONE_NUMBER_ERROR)
            ->addViolation();
    }

}
