<?php

declare(strict_types = 1);

namespace Pm\Core\Validator;

use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Validator\Exception\ValidationFailedException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use function count;

class EntityValidator
{

    public function __construct(
        private readonly ValidatorInterface $validator,
    )
    {
    }

    /**
     * Validates an entity and returns constraint violations.
     *
     * @throws UnprocessableEntityHttpException with ValidationFailedException as previous exception
     */
    public function validateEntity(object $entity): void
    {
        $violations = $this->validator->validate($entity);

        if (count($violations) > 0) {
            $validationException = new ValidationFailedException($entity, $violations);

            throw new UnprocessableEntityHttpException('Validation failed', $validationException, 422);
        }
    }

}
