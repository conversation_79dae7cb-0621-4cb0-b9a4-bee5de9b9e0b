<?php

declare(strict_types = 1);

namespace Pm\Core\Enum;

use BackedEnum;

/**
 * @implements BackedEnum<string>
 */
enum FilterTypeEnum: string
{

    case MATCH = 'match';
    case STARTS_WITH = 'startsWith';
    case ENDS_WITH = 'endsWith';
    case CONTAINS = 'contains';
    case GREATER_THAN = 'greaterThan';
    case BETWEEN = 'between';
    case LESS_THAN = 'lessThan';
    case IN = 'in';

    case NOT_IN = 'notIn';

}
