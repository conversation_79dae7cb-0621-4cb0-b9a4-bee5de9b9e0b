<?php

declare(strict_types = 1);

namespace Pm\Pops\Event;

use Pm\Pops\Entity\Pops;
use Psr\EventDispatcher\StoppableEventInterface;
use Symfony\Contracts\EventDispatcher\Event;

class PopsUpdatedEvent extends Event implements StoppableEventInterface
{

    public function __construct(
        private readonly Pops $pops,
    )
    {
    }

    public function getPops(): Pops
    {
        return $this->pops;
    }

}
