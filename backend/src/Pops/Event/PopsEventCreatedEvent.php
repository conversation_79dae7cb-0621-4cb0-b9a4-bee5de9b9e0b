<?php

declare(strict_types = 1);

namespace Pm\Pops\Event;

use Pm\Pops\Entity\PopsEvent;
use Psr\EventDispatcher\StoppableEventInterface;
use Symfony\Contracts\EventDispatcher\Event;

class PopsEventCreatedEvent extends Event implements StoppableEventInterface
{

    public function __construct(
        private readonly PopsEvent $popsEvent,
    )
    {
    }

    public function getPopsEvent(): PopsEvent
    {
        return $this->popsEvent;
    }

}
