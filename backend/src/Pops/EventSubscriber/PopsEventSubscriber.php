<?php

declare(strict_types = 1);

namespace Pm\Pops\EventSubscriber;

use DateTimeImmutable;
use Doctrine\ORM\NonUniqueResultException;
use Pm\Core\Entity\Observer\EntityObserver;
use Pm\Core\SSE\Publisher;
use Pm\Core\SSE\TopicFactory;
use Pm\Facility\Service\PopsEventHttpResponseCreator;
use Pm\Pops\Entity\PopsEvent;
use Pm\Pops\Event\PopsEventAttachmentDeletedEvent;
use Pm\Pops\Event\PopsEventCompletedEvent;
use Pm\Pops\Event\PopsEventCreatedEvent;
use Pm\Pops\Event\PopsEventUpdatedEvent;
use Pm\Pops\Facade\PopsFacade;
use Pm\SuperPops\Repository\SuperPopsRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

readonly class PopsEventSubscriber implements EventSubscriberInterface
{

    public function __construct(
        private PopsFacade $popsFacade,
        private EntityObserver $entityObserver,
        private Publisher $publisher,
        private TopicFactory $topicFactory,
        private PopsEventHttpResponseCreator $httpResponseCreator,
        #[Autowire('%date_time_format%')]
        private string $dateTimeFormat,
        #[Autowire('%kernel.environment%')]
        private string $environment,
        private SuperPopsRepository $superPopsRepository,
    )
    {
    }

    public function onPopsEventCreatedEvent(PopsEventCreatedEvent $e): void
    {
        $this->popsFacade->updateStats($e->getPopsEvent()->getPops());
    }

    public function onPopsEventCompletedEvent(PopsEventCompletedEvent $event): void
    {
        $this->publishSSE($event->getPopsEvent(), Publisher::ACTION_INSERT);

        if (!$this->canBeHandled($event->getPopsEvent())) {
            return;
        }

        $this->entityObserver->handleEvent(EntityObserver::EVENT_PERSIST, $event->getPopsEvent());
    }

    public function onPopsEventUpdatedEvent(PopsEventUpdatedEvent $event): void
    {
        $this->publishSSE($event->getPopsEvent(), Publisher::ACTION_UPDATE);

        if (!$this->canBeHandled($event->getPopsEvent())) {
            return;
        }

        $this->entityObserver->handleEvent(EntityObserver::EVENT_UPDATE, $event->getPopsEvent());
    }

    public function onPopsEventAttachmentDeletedEvent(PopsEventAttachmentDeletedEvent $event): void
    {
        if (!$this->canBeHandled($event->getPopsEvent())) {
            return;
        }

        $this->entityObserver->handleEvent(EntityObserver::EVENT_DELETE_ATTACHMENT, $event->getPopsEvent(), $event->getAttachmentOriginalDbId());
    }

    /**
     * {@inheritDoc}
     */
    public static function getSubscribedEvents(): array
    {
        return [
            PopsEventCreatedEvent::class => 'onPopsEventCreatedEvent',
            PopsEventCompletedEvent::class => 'onPopsEventCompletedEvent',
            PopsEventUpdatedEvent::class => 'onPopsEventUpdatedEvent',
            PopsEventAttachmentDeletedEvent::class => 'onPopsEventAttachmentDeletedEvent',
        ];
    }

    private function canBeHandled(PopsEvent $popsEvent): bool
    {
        return $popsEvent->getEventCategory()->getOriginalDbId() !== null
            &&
            $popsEvent->getPops()->getFacility()->getOriginalDbId() !== null;
    }

    /**
     * @throws NonUniqueResultException
     */
    private function publishSSE(PopsEvent $popsEvent, string $event): void
    {
        if ($this->environment === 'test') {
            // do not publish SSE in test environment
            return;
        }

        $this->publisher->send(
            $this->topicFactory->create($popsEvent),
            $event,
            (new DateTimeImmutable())->format($this->dateTimeFormat),
            $this->httpResponseCreator->create($popsEvent),
        );

        $superPops = $this->superPopsRepository->getUnlockedSuperPopsByFacility($popsEvent->getPops()->getFacility());
        if ($superPops !== null) {
            $this->publisher->send(
                $this->topicFactory->create($superPops),
                $event,
                (new DateTimeImmutable())->format($this->dateTimeFormat),
                $this->httpResponseCreator->create($popsEvent),
            );
        }
    }

}
