<?php

declare(strict_types = 1);

namespace Pm\Pops\Export;

use DateTimeImmutable;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;
use Pm\Facility\Entity\Facility;
use Pm\Pops\Exception\NoPopsFoundException;
use Pm\Pops\HttpResponse\PopsEventHttpResponse;
use Pm\Pops\Repository\PopsRepository;
use Pm\Tools\Sheet;
use Pm\User\Entity\User;
use Symfony\Contracts\Translation\TranslatorInterface;
use function max;
use function mb_strlen;

readonly class SpreadsheetFactory
{

    private const MAX_DESCRIPTION_LENGTH = 100;

    public function __construct(
        private readonly TranslatorInterface $translator,
        private readonly PopsRepository $popsRepository,
    )
    {
    }

    /**
     * @param list<PopsEventHttpResponse> $popsEvents
     *
     * @throws NoPopsFoundException
     */
    public function create(
        array $popsEvents,
        ?DateTimeImmutable $startsAt,
        ?DateTimeImmutable $endsAt,
        ?User $author,
        ?Facility $facility,
        bool $extended = false,

    ): Spreadsheet
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $header = [
            $this->translator->trans('Date'),
            $this->translator->trans('Category'),
            $this->translator->trans('Title'),
            $this->translator->trans('Description'),
            $this->translator->trans('Created At'),
        ];
        if ($extended) {
            $header[] = $this->translator->trans('Facility');
            $header[] = $this->translator->trans('Author');
        }

        $headerStyleArray = [
            'font' => [
                'bold' => true,
            ],
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $sheet->fromArray($header, 'null', 'A1');
        $sheet->getStyle('A1:' . ($extended === true ? 'G' : 'E') . '1')->applyFromArray($headerStyleArray);

        $row = 2;
        $maxDescriptionLength = 0;
        foreach ($popsEvents as $popsEvent) {
            // Skip internal events (NONE severity)
            if ($popsEvent->eventCategory->severity === EventCategorySeverityEnum::NONE->name) {
                continue;
            }

            $col = 1;
            // set cell values
            Sheet::setCellDatetime($sheet, [$col++, $row], $popsEvent->eventDateTime);
            $sheet->setCellValueExplicit([$col++, $row], $this->translator->trans($popsEvent->eventCategory->severity), DataType::TYPE_STRING);
            $sheet->setCellValueExplicit([$col++, $row], $popsEvent->eventCategory->label, DataType::TYPE_STRING);
            $sheet->setCellValueExplicit([$col++, $row], $popsEvent->description, DataType::TYPE_STRING);
            $maxDescriptionLength = max($maxDescriptionLength, mb_strlen($popsEvent->description));
            Sheet::setCellDatetime($sheet, [$col++, $row], $popsEvent->createdAt);
            if ($extended) {
                $pops = $this->popsRepository->get($popsEvent->popsId);
                $sheet->setCellValueExplicit([$col++, $row], $pops->getFacility()->getName(), DataType::TYPE_STRING);
                $author = (string) $popsEvent->insertedBy?->firstName . ' ' . (string) $popsEvent->insertedBy?->lastName;
                $sheet->setCellValueExplicit([$col++, $row], $author, DataType::TYPE_STRING);
            }
            // next row in active sheet
            $row++;
        }

        $sheet->getColumnDimension('A')->setAutoSize(true);
        $sheet->getColumnDimension('B')->setAutoSize(true);
        $sheet->getColumnDimension('C')->setAutoSize(true);
        $sheet->getColumnDimension('D')->setAutoSize(false);
        $sheet->getColumnDimension('E')->setAutoSize(true);
        $sheet->getColumnDimension('F')->setAutoSize(true);
        $sheet->getColumnDimension('G')->setAutoSize(true);
        $sheet->getColumnDimension('H')->setAutoSize(true);
        $sheet->getColumnDimension('J')->setAutoSize(true);
        $sheet->getColumnDimension('L')->setAutoSize(true);

        if ($maxDescriptionLength > self::MAX_DESCRIPTION_LENGTH) {
            $sheet->getColumnDimension('D')->setWidth(self::MAX_DESCRIPTION_LENGTH);
        } else {
            $sheet->getColumnDimension('D')->setAutoSize(true);
        }

        return $spreadsheet;
    }

}
