<?php

declare(strict_types = 1);

namespace Pm\Pops\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\EventCategory\HttpResponse\EventCategoryResponse;
use Pm\Facility\Service\PopsHttpResponseCreator;
use Pm\Pops\Entity\PopsEvent;
use Pm\User\Entity\User;
use Pm\User\HttpResponse\UserBasicHttpResponse;

class PopsEventHttpResponse implements ApiOutput
{

    public readonly int $id;

    public readonly int $popsId;

    public readonly ?PopsHttpResponseSimple $pops;

    public readonly int $facilityId;

    public readonly string $facilityName;

    public readonly string $facilityCode;

    public readonly string $description;

    public readonly DateTimeImmutable $eventDateTime;

    public readonly ?DateTimeImmutable $createdAt;

    public readonly ?UserBasicHttpResponse $insertedBy;

    public readonly EventCategoryResponse $eventCategory;

    /**
     * @var array<int, PopsEventAttachmentHttpResponse>
     */
    protected array $attachments;

    public readonly int $attachmentsCount;

    public readonly int $attachmentsReceivedCount;

    public function __construct(PopsEvent $popsEvent, ?PopsHttpResponseCreator $popsHttpResponseCreator = null)
    {
        $this->id = $popsEvent->getId();
        $this->popsId = $popsEvent->getPops()->getId();
        $pops = $popsEvent->getPops();
        if ($popsHttpResponseCreator !== null) {
            $this->pops = $popsHttpResponseCreator->createSimple($pops);
        } else {
            $this->pops = null;
        }
        $this->facilityId = $popsEvent->getPops()->getFacility()->getId();
        $this->facilityName = $popsEvent->getPops()->getFacility()->getName();
        $this->facilityCode = $popsEvent->getPops()->getFacility()->getCode();
        $this->description = $popsEvent->getDescription();
        $this->eventDateTime = $popsEvent->getEventDateTime();
        $this->createdAt = $popsEvent->getCreatedAt();
        $this->insertedBy = $popsEvent->getInsertedBy() instanceof User ? new UserBasicHttpResponse($popsEvent->getInsertedBy()) : null;
        $this->eventCategory = new EventCategoryResponse($popsEvent->getEventCategory());
        $this->attachmentsCount = $popsEvent->getAttachmentsCount();
        $this->attachmentsReceivedCount = $popsEvent->getAttachmentsReceivedCount();

        $this->attachments = [];
    }

    /**
     * @return array<int, PopsEventAttachmentHttpResponse>
     */
    public function getAttachments(): array
    {
        return $this->attachments;
    }

    public function addAttachment(PopsEventAttachmentHttpResponse $attachment): void
    {
        $this->attachments[] = $attachment;
    }

}
