<?php

declare(strict_types = 1);

namespace Pm\Pops\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\Pops\Entity\PopsEvent;
use Pm\SuperPops\Entity\SuperPops;

class SuperPopsHttpResponse implements ApiOutput
{

    public readonly int $id;

    public readonly DateTimeImmutable $startsAt;

    public readonly DateTimeImmutable $endsAt;

    public readonly bool $isLocked;

    /**
     * @var array<int> $facilitiesIds
     */
    public readonly array $facilitiesIds;

    public readonly ?PopsEventHttpResponse $lastPopsEvent;

    public function __construct(
        SuperPops $superPops,
        public readonly ?string $highestPopsEventPriority,
        public readonly int $countOfCriticalEvents,
        public readonly int $countOfMediumEvents,
        public readonly int $countOfInternalEvents,
        public readonly int $countOfLowEvents,
        ?PopsEvent $lastPopsEvent,
        /**
         * @var array<string> $permissions
         */
        public readonly array $permissions = [],
    )
    {
        $this->id = $superPops->getId();
        $this->startsAt = $superPops->getStartsAt();
        $this->endsAt = $superPops->getEndsAt();
        $this->isLocked = $superPops->isLocked();

        $facilitiesIds = [];
        foreach ($superPops->getFacilities() as $facility) {
            $facilitiesIds[] = $facility->getId();
        }
        $this->facilitiesIds = $facilitiesIds;

        $this->lastPopsEvent = $lastPopsEvent !== null ? new PopsEventHttpResponse($lastPopsEvent) : null;
    }

}
