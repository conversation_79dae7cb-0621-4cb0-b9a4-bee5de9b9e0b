<?php

declare(strict_types = 1);

namespace Pm\Pops\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\Pops\Entity\PopsEventAttachment;

class PopsEventAttachmentHttpResponse implements ApiOutput
{

    public readonly int $id;

    public readonly string $path;

    public readonly string $mime;

    public readonly string $name;

    public readonly ?int $size;

    public readonly ?DateTimeImmutable $createdAt;

    public function __construct(PopsEventAttachment $popsEventAttachement, public readonly string $url, public readonly string $thumbnailUrl)
    {
        $this->id = $popsEventAttachement->getId();
        $this->path = $popsEventAttachement->getPath();
        $this->mime = $popsEventAttachement->getMime();
        $this->name = $popsEventAttachement->getName();
        $this->createdAt = $popsEventAttachement->getCreatedAt();
        $this->size = $popsEventAttachement->getSize();
    }

}
