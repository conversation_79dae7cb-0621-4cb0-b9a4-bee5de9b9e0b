<?php

declare(strict_types = 1);

namespace Pm\Pops\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;

class PopsAlertHttpResponse implements ApiOutput
{

    public function __construct(
        public readonly int $id,
        public readonly int $popsId,
        public readonly ?DateTimeImmutable $createdAt,
        public readonly DateTimeImmutable $startsAt,
        public readonly DateTimeImmutable $endsAt,
        public readonly int $facilityId,
    )
    {
    }

}
