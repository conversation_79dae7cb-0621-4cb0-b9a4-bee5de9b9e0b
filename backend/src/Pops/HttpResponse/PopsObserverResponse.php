<?php

declare(strict_types = 1);

namespace Pm\Pops\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Core\Exception\LogicException;
use Pm\Pops\Entity\Pops;

readonly class PopsObserverResponse implements ApiOutput
{

    public int $id;

    public string $startsAt;

    public string $endsAt;

    public bool $isLocked;

    public int $facilityId;

    public string $facilityOriginalDbId;

    public bool $isSendAttachment;

    public string $originalDbId;

    public function __construct(Pops $pops)
    {
        $this->id = $pops->getId();
        $this->startsAt = $pops->getStartsAt()->format('c');
        $this->endsAt = $pops->getEndsAt()->format('c');
        $this->isLocked = $pops->isLocked();
        $this->facilityId = $pops->getFacility()->getId();
        if ($pops->getFacility()->getOriginalDbId() === null) {
            throw new LogicException('Facility originalDbId can not be null on observed entity.');
        }
        $this->facilityOriginalDbId = $pops->getFacility()->getOriginalDbId();
        $this->isSendAttachment = $pops->isSendAttachment();
        $this->originalDbId = (string) $pops->getOriginalDbId();
    }

}
