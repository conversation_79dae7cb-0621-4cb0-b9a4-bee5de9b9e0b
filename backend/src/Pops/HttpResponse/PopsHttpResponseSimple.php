<?php

declare(strict_types = 1);

namespace Pm\Pops\HttpResponse;

use DateTimeImmutable;
use Pm\Core\Controller\ApiOutput;
use Pm\Pops\Entity\Pops;

class PopsHttpResponseSimple implements ApiOutput
{

    public readonly int $id;

    public readonly DateTimeImmutable $startsAt;

    public readonly DateTimeImmutable $endsAt;

    public readonly bool $isLocked;

    public readonly int $facilityId;

    public readonly string $facilityName;

    public readonly string $facilityCode;

    public function __construct(
        Pops $pops,
    )
    {
        $this->id = $pops->getId();
        $this->startsAt = $pops->getStartsAt();
        $this->endsAt = $pops->getEndsAt();
        $this->isLocked = $pops->isLocked();
        $this->facilityId = $pops->getFacility()->getId();
        $this->facilityName = $pops->getFacility()->getName();
        $this->facilityCode = $pops->getFacility()->getCode();
    }

}
