<?php

declare(strict_types = 1);

namespace Pm\Pops\Validator;

use BackedEnum;

/**
 * @implements BackedEnum<string>
 */
enum PopsSortFields: string
{

    case Id = 'id';
    case CreatedAt = 'created_at';

    case CriticalEventsCount = 'pops_stats.critical_events_count';
    case MediumEventsCount = 'pops_stats.medium_events_count';
    case LowEventsCount = 'pops_stats.low_events_count';
    case InternalEventsCount = 'pops_stats.internal_events_count';

}
