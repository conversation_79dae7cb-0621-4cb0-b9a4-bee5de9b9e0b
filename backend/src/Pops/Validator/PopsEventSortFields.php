<?php

declare(strict_types = 1);

namespace Pm\Pops\Validator;

use BackedEnum;

/**
 * @implements BackedEnum<string>
 */
enum PopsEventSortFields: string
{

    case Id = 'id';
    case CreatedAt = 'created_at';
    case EventDateTime = 'event_date_time';
    case Description = 'description';
    case InsertedBy = 'inserted_by';
    case EventCategoryLabel = 'event_category.label';
    case EventCategoryType = 'event_category.type';

}
