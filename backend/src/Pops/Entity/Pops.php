<?php

declare(strict_types = 1);

namespace Pm\Pops\Entity;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\OneToOne;
use Doctrine\ORM\Mapping\Table;
use Pm\Account\Entity\Account;
use Pm\Core\Entity\Observer\ObservedEntityInterface;
use Pm\Facility\Entity\Facility;

#[Table(name: 'pops')]
#[Entity]
class Pops extends PopsEntity implements ObservedEntityInterface
{

    #[OneToOne(targetEntity: PopsStats::class)]
    private ?PopsStats $stats = null;

    #[ManyToOne(targetEntity: Account::class, inversedBy: 'pops')]
    #[JoinColumn(nullable: false)]
    protected Account $account;

    #[ManyToOne(targetEntity: Facility::class, inversedBy: 'pops')]
    #[JoinColumn(nullable: false)]
    protected Facility $facility;

    #[Column(type: Types::BOOLEAN, options: ['default' => true])]
    private bool $sendAttachment;

    public function __construct(Account $account, Facility $facility, DateTimeImmutable $startsAt, DateTimeImmutable $endsAt, bool $sendAttachment = true)
    {
        $this->account = $account;
        $this->facility = $facility;
        $this->startsAt = $startsAt;
        $this->endsAt = $endsAt;
        $this->sendAttachment = $sendAttachment;
    }

    public function getFacility(): Facility
    {
        return $this->facility;
    }

    public function getStats(): ?PopsStats
    {
        return $this->stats;
    }

    public function setStats(PopsStats $stats): void
    {
        $this->stats = $stats;
    }

    public function isSendAttachment(): bool
    {
        return $this->sendAttachment;
    }

    public function setSendAttachment(bool $sendAttachment): void
    {
        $this->sendAttachment = $sendAttachment;
    }

}
