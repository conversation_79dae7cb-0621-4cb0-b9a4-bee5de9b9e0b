<?php

declare(strict_types = 1);

namespace Pm\Pops\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Table;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\EventCategory\Enum\EventCategorySeverity;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;
use Pm\Log\Entity\LoggableEntity;
use Pm\Log\Enum\LoggableEntityName;

#[Table(name: 'pops_stats')]
#[Entity]
class PopsStats extends BaseEntity implements LoggableEntity
{

    use TimestampableEntity;

    #[Column(type: EventCategorySeverity::NAME, nullable: true)]
    private EventCategorySeverityEnum $highestPopsEventPriority;

    #[Column(type: Types::INTEGER, nullable: true)]
    private ?int $criticalEventsCount = null;

    #[Column(type: Types::INTEGER, nullable: true)]
    private ?int $mediumEventsCount = null;

    #[Column(type: Types::INTEGER, nullable: true)]
    private ?int $lowEventsCount = null;

    #[Column(type: Types::INTEGER, nullable: true)]
    private ?int $internalEventsCount = null;

    public function getLoggableName(): LoggableEntityName
    {
        return LoggableEntityName::PopsStats;
    }

    public function getCriticalEventsCount(): int
    {
        return $this->criticalEventsCount ?? 0;
    }

    public function setCriticalEventsCount(int $criticalEventsCount): void
    {
        $this->criticalEventsCount = $criticalEventsCount;
    }

    public function getMediumEventsCount(): int
    {
        return $this->mediumEventsCount ?? 0;
    }

    public function setMediumEventsCount(int $mediumEventsCount): void
    {
        $this->mediumEventsCount = $mediumEventsCount;
    }

    public function getLowEventsCount(): int
    {
        return $this->lowEventsCount ?? 0;
    }

    public function setLowEventsCount(int $lowEventsCount): void
    {
        $this->lowEventsCount = $lowEventsCount;
    }

    public function getInternalEventsCount(): int
    {
        return $this->internalEventsCount ?? 0;
    }

    public function setInternalEventsCount(int $internalEventsCount): void
    {
        $this->internalEventsCount = $internalEventsCount;
    }

    public function getHighestPopsEventPriority(): EventCategorySeverityEnum
    {
        return $this->highestPopsEventPriority;
    }

    public function setHighestPopsEventPriority(EventCategorySeverityEnum $highestPopsEventPriority): void
    {
        $this->highestPopsEventPriority = $highestPopsEventPriority;
    }

}
