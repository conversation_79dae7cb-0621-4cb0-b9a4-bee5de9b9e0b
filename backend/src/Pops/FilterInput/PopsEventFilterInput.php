<?php

declare(strict_types = 1);

namespace Pm\Pops\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class PopsEventFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_DESCRIPTION,
        self::FILTER_ID,
        self::FILTER_EVENT_CATEGORY_SEVERITY,
        self::FILTER_EVENT_CATEGORY_LABEL,
        self::FILTER_EVENT_CATEGORY_IS_HIDDEN,
        self::FILTER_EVENT_DATE_TIME,
        self::FILTER_INSERTED_BY,
    ];
    final public const FILTER_DESCRIPTION = 'description';
    final public const FILTER_ID = 'id';
    final public const FILTER_EVENT_CATEGORY_SEVERITY = 'eventCategory.severity';
    final public const FILTER_EVENT_CATEGORY_LABEL = 'eventCategory.label';
    final public const FILTER_EVENT_CATEGORY_IS_HIDDEN = 'eventCategory.isHidden';
    final public const FILTER_EVENT_DATE_TIME = 'eventDateTime';
    final public const FILTER_INSERTED_BY = 'insertedBy.id';

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID => Types::INTEGER,
            self::FILTER_EVENT_DATE_TIME => Types::DATETIME_IMMUTABLE,
            self::FILTER_INSERTED_BY => Types::INTEGER,
            default => Types::STRING,
        };
    }

}
