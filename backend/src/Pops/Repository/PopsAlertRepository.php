<?php

declare(strict_types = 1);

namespace Pm\Pops\Repository;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Pm\Pops\Entity\Pops;
use Pm\Pops\Entity\PopsAlert;
use Pm\Pops\Exception\NoPopsAlertFoundException;
use Pm\User\Entity\User;

class PopsAlertRepository
{

    public function __construct(
        private readonly EntityManager $entityManager,
    )
    {
    }

    /**
     * @throws NoPopsAlertFoundException
     */
    public function get(int $id): PopsAlert
    {
        $pops = $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(PopsAlert::class, 'p')
            ->andWhere('p.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($pops === null) {
            throw NoPopsAlertFoundException::byId($id);
        }

        return $pops;
    }

    /**
     * @return array<PopsAlert>
     */
    public function getActiveAlertsByUserList(User $user): array
    {
        // retrive all alert for user (all acknowledged or failed recorded alerts should be deleted)
        return $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(PopsAlert::class, 'p')
            ->andWhere('p.alertedUser = :user')
            ->setParameter('user', $user)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array<PopsAlert>
     */
    public function getFailedAlerts(): array
    {
        // retrive all alerts where endsAt is in past - user did not react in time
        return $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(PopsAlert::class, 'p')
            ->andWhere('p.endsAt < :now')
            ->setParameter('now', new DateTimeImmutable())
            ->getQuery()
            ->getResult();
    }

    /**
     * @throws NoPopsAlertFoundException
     */
    public function getByPops(Pops $pops): PopsAlert
    {
        $popsAlert = $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(PopsAlert::class, 'p')
            ->andWhere('p.pops = :pops_id')
            ->setParameter('pops_id', $pops->getId())
            ->getQuery()
            ->getOneOrNullResult();

        if ($popsAlert === null) {
            throw NoPopsAlertFoundException::byPopsId($pops->getId());
        }

        return $popsAlert;
    }

    public function removeByPops(Pops $pops): void
    {
        $popsAlert = $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(PopsAlert::class, 'p')
            ->andWhere('p.pops = :pops_id')
            ->setParameter('pops_id', $pops->getId())
            ->getQuery()
            ->getOneOrNullResult();
        if ($popsAlert instanceof PopsAlert) {
            $this->entityManager->remove($popsAlert);
        }
    }

}
