<?php

declare(strict_types = 1);

namespace Pm\Pops\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Pops\Entity\Pops;
use Pm\Pops\Entity\PopsEvent;

readonly class PopsEventVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canView(PopsEvent $popsEvent): void
    {
        $this->canAccessByAccountId($popsEvent->getPops()->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_POPS_SHOW, $popsEvent->getPops()->getFacility())) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canCreate(Pops $pops): void
    {
        $this->canAccessByAccountId($pops->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_POPS_CREATE, $pops->getFacility())) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdate(PopsEvent $popsEvent): void
    {
        $this->canAccessByAccountId($popsEvent->getPops()->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_POPS_EVENT_EDIT, $popsEvent->getPops()->getFacility())) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUploadAttachment(PopsEvent $popsEvent): void
    {
        $this->canAccessByAccountId($popsEvent->getPops()->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_POPS_SET_SEND_ATTACHMENT, $popsEvent->getPops()->getFacility())) {
                return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdateEventDateTime(PopsEvent $popsEvent): void
    {
        $this->canAccessByAccountId($popsEvent->getPops()->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_POPS_EVENT_TIME, $popsEvent->getPops()->getFacility())) {
            return;
        }

        throw new AclException();
    }

}
