<?php

declare(strict_types = 1);

namespace Pm\Pops\Input;

use DateTimeImmutable;
use OpenApi\InputMapper\Input;
use function get_object_vars;

class PopsCreateUpdateInput implements Input
{

    public function __construct(

        private readonly int $accountId,

        private readonly int $facilityId,

        private readonly DateTimeImmutable $startsAt,

        private readonly DateTimeImmutable $endsAt,

        private readonly bool $isLocked = false,

        private readonly bool $isSendAttachment = true,
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function getAccountId(): int
    {
        return $this->accountId;
    }

    public function getFacilityId(): int
    {
        return $this->facilityId;
    }

    public function getStartsAt(): DateTimeImmutable
    {
        return $this->startsAt;
    }

    public function getEndsAt(): DateTimeImmutable
    {
        return $this->endsAt;
    }

    public function isLocked(): bool
    {
        return $this->isLocked;
    }

    public function isSendAttachment(): bool
    {
        return $this->isSendAttachment;
    }

}
