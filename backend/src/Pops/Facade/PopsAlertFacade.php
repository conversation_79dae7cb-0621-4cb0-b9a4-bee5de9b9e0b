<?php

declare(strict_types = 1);

namespace Pm\Pops\Facade;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Pm\Core\Service\TimeService;
use Pm\EventCategory\Entity\EventCategory;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;
use Pm\EventCategory\Exception\NoEventCategoryFoundException;
use Pm\EventCategory\Repository\EventCategoryRepository;
use Pm\Pops\Entity\PopsAlert;
use Pm\Pops\Entity\PopsEvent;
use Pm\Pops\Exception\NoPopsAlertFoundException;
use Pm\Pops\HttpResponse\PopsAlertHttpResponse;
use Pm\Pops\Repository\PopsAlertRepository;
use Pm\Pops\Repository\PopsEventRepository;
use Pm\Pops\Repository\PopsRepository;
use Pm\User\Entity\User;
use RuntimeException;
use function count;

class PopsAlertFacade
{

    public function __construct(
        private readonly int $defaultAlertDuration,
        private readonly int $activityAcknowledgedEventCategoryNumber,
        private readonly string $activityAcknowledgedEventCategoryName,
        private readonly int $popsEventNotCreatedCategoryNumber,
        private readonly EntityManager $entityManager,
        private readonly PopsRepository $popsRepository,
        private readonly PopsEventRepository $popsEventRepository,
        private readonly PopsAlertRepository $popsAlertRepository,
        private readonly EventCategoryRepository $eventCategoryRepository,
        private readonly TimeService $timeService,
    )
    {
    }

    /**
     * List all active alerts for given user
     *
     * @return list<PopsAlertHttpResponse>
     */
    public function getActiveAlertsByUserList(User $user): array
    {
        $popsAlertList = $this->popsAlertRepository->getActiveAlertsByUserList($user);

        /** @var list<PopsAlertHttpResponse> $responseList */
        $responseList = [];
        foreach ($popsAlertList as $popsAlert) {
            $responseList[] = new PopsAlertHttpResponse(
                $popsAlert->getId(),
                $popsAlert->getPops()->getId(),
                $popsAlert->getCreatedAt(),
                $popsAlert->getStartsAt(),
                $popsAlert->getEndsAt(),
                $popsAlert->getPops()->getFacility()->getId(),
            );
        }

        return $responseList;
    }

    /**
     * @throws NoPopsAlertFoundException
     */
    public function acknowledgePopsAlert(int $id): void
    {
        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $popsAlert = $this->popsAlertRepository->get($id);

            $this->entityManager->remove($popsAlert);

            try {
                $account = $popsAlert->getPops()->getAccount();
                $eventCategory = $this->eventCategoryRepository->getByCode($this->activityAcknowledgedEventCategoryNumber, $account);
            } catch (NoEventCategoryFoundException) {
                // create category
                $eventCategory = new EventCategory(
                    $this->activityAcknowledgedEventCategoryName,
                    (string) $this->activityAcknowledgedEventCategoryNumber,
                    EventCategorySeverityEnum::LOW,
                    true,
                    null,
                    $popsAlert->getPops()->getAccount(),
                );
                $this->entityManager->persist($eventCategory);
            }

            $popsEvent = new PopsEvent($popsAlert->getPops(), $eventCategory, 'Activity Alert Acknowledged', new DateTimeImmutable(), new DateTimeImmutable(), 0);

            $this->entityManager->persist($popsEvent);
        });
    }

    /**
     * Find all existing alerts where endsAt is in past, delete those and create "attantion check" failed event
     */
    public function checkPopsAlertsTimeout(): void
    {
        $popsAlertList = $this->popsAlertRepository->getFailedAlerts();

        if (count($popsAlertList) === 0) {
            // not active pops
            return;
        }

        $this->entityManager->wrapInTransaction(function () use ($popsAlertList): void {
            foreach ($popsAlertList as $popsAlert) {
                $account = $popsAlert->getPops()->getAccount();

                $eventCategory = $this->eventCategoryRepository->getByCode($this->popsEventNotCreatedCategoryNumber, $account);

                $this->entityManager->remove($popsAlert);

                // create event
                $popsEvent = new PopsEvent($popsAlert->getPops(), $eventCategory, '', new DateTimeImmutable(), new DateTimeImmutable(), 0);

                $popsEvent->setInsertedBy($popsAlert->getAlertedUser());

                $this->entityManager->persist($popsEvent);

            }
        });
    }

    /**
     * @throws RuntimeException
     */
    public function checkPopsAttentionAlerts(): void
    {
        // get list of active pops
        $activePopsList = $this->popsRepository->getActiveWithAlertPops();

        if (count($activePopsList) === 0) {
            // not active pops
            return;
        }

        $this->entityManager->wrapInTransaction(function () use ($activePopsList): void {
            foreach ($activePopsList as $pops) {
                $popsAlert = null;
                // check if alert exists
                try {
                    $popsAlert = $this->popsAlertRepository->getByPops($pops);
                } catch (NoPopsAlertFoundException) {
                    // pops alert does not exist for given pops
                }

                if ($popsAlert !== null) {
                    // alert already exists for given pops
                    continue;
                }

                // time for alert in case there are no events created at all
                $lastTime = $pops->getCreatedAt() ?? new DateTimeImmutable();

                // check if interval time is more than last event (ignore attention failed events - category type not internal)
                $popsEvent = $this->popsEventRepository->getLastNonInternalEventByPops($pops);
                if ($popsEvent instanceof PopsEvent) {
                    $lastTime = $popsEvent->getEventDateTime();
                }

                // check if is there internal "alert acknowledged event"
                try {
                    // find category
                    $eventCategory = $this->eventCategoryRepository->getByCode($this->activityAcknowledgedEventCategoryNumber, $pops->getAccount());
                    // check for event
                    $acknowledgedPopsEvent = $this->popsEventRepository->getLastByCategory($pops, $eventCategory);

                    // if event exists and is newer than non-internal events use it for creating alert
                    if ($acknowledgedPopsEvent instanceof PopsEvent && $acknowledgedPopsEvent->getEventDateTime() > $lastTime) {
                        $lastTime = $acknowledgedPopsEvent->getEventDateTime();
                    }
                } catch (NoEventCategoryFoundException) {
                    // eventCategory might not exist yet, will be created on first acknowledge action
                }

                // convert for correct comparison
                $lastTime = $this->timeService->convertToDefaultTimezone($lastTime);

                if ($pops->getFacility()->getPopsAlertInterval() === null) {
                    // should never happen because popRepository->getActiveWithAlertPops only returns items with interval, but phpstan does not know about it
                    throw new RuntimeException('Facility missing alert interval.', 400);

                }

                $timeWhenFacilityAlertIntervalIsExceeded = $this->timeService->addMinutesToTime($lastTime, $pops->getFacility()->getPopsAlertInterval());

                if ($this->timeService->isInFuture($timeWhenFacilityAlertIntervalIsExceeded)) {
                    // time from pops creation or last event did not exceeded the time for attention alert to be created
                    continue;
                }

                $alertDuration = $this->defaultAlertDuration;

                if (!$pops->getInsertedBy() instanceof User) {
                    throw new RuntimeException('Invalid value POPS InsertedBy.', 400);
                }

                // check if alert check has not failed (timeouted) before - and if yes use that as lastTime
                $eventCategory = $this->eventCategoryRepository->getByCode($this->popsEventNotCreatedCategoryNumber, $pops->getAccount());
                $attentionAlertFailedPopsEvent = $this->popsEventRepository->getLastByCategory($pops, $eventCategory);
                if ($attentionAlertFailedPopsEvent instanceof PopsEvent) {
                    $lastTime = $this->timeService->convertToDefaultTimezone($attentionAlertFailedPopsEvent->getEventDateTime());
                }
                $popsAlertStart = $this->timeService->addMinutesToTime($lastTime, $pops->getFacility()->getPopsAlertInterval());

                // create alert
                $popsAlert = new PopsAlert($pops, $pops->getInsertedBy(), $popsAlertStart, $this->timeService->addMinutesToTime($popsAlertStart, $alertDuration));

                $this->entityManager->persist($popsAlert);

            }
        });
    }

}
