<?php

declare(strict_types = 1);

namespace Pm\Pops\Log\Event;

use Pm\Log\Enum\LogEventName;
use Pm\Log\Event\LogEvent;
use Pm\User\Entity\User;

class SuperPopsCreatedLog extends LogEvent
{

    public function __construct(string $message, ?User $user = null, ?array $data = null)
    {
        parent::__construct($message, [], $user, $data);
    }

    public function getEventName(): LogEventName
    {
        return LogEventName::SuperPopsCreated;
    }

}
