<?php

declare(strict_types = 1);

namespace Pm\Subject\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Subject\Facade\SubjectGroupFacade;
use Pm\Subject\HttpResponse\SubjectGroupResponse;
use Pm\Subject\Input\SubjectGroupCreateUpdateInput;
use Pm\Subject\Input\SubjectGroupGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'SubjectGroup')]
class SubjectGroupController extends BaseController
{

    public function __construct(
        private readonly SubjectGroupFacade $subjectGroupFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(SubjectGroupResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/subject-group')]
    public function getListAction(
        #[MapQueryString]
        ?SubjectGroupGetInput $subjectGroupGetInput,
    ): SuccessCountableOutput
    {
        $subjectGroupList = $this->subjectGroupFacade->getSubjectGroupPaginatedListResponse(
            $subjectGroupGetInput ?? new SubjectGroupGetInput(),
        );

        /**
         * @var list<SubjectGroupResponse> $list
         */
        $list = $subjectGroupList->getArrayCopy();
        return new SuccessCountableOutput($list, $subjectGroupList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(SubjectGroupResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/subject-group/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $subjectGroup = $this->subjectGroupFacade->getSubjectGroup($id);

        return new SuccessOutput($subjectGroup);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(SubjectGroupResponse::class, 'subject-group-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/subject-group')]
    public function createSubjectGroupAction(
        #[MapRequestPayload]
        SubjectGroupCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->subjectGroupFacade->createSubjectGroup($request),
            Response::HTTP_CREATED,
            'subject-group-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(SubjectGroupResponse::class, 'subject-group-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/subject-group/{id}')]
    public function updateSubjectGroupAction(
        int $id,
        #[MapRequestPayload]
        SubjectGroupCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->subjectGroupFacade->updateSubjectGroup($id, $request),
            Response::HTTP_OK,
            'subject-group-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'subject-group-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/subject-group/{id}')]
    public function deleteSubjectGroupAction(int $id): SuccessOutput
    {
        $this->subjectGroupFacade->deleteSubjectGroup($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'subject-group-deleted',
        );
    }

}
