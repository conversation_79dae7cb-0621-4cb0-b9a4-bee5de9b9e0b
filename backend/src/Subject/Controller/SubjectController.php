<?php

declare(strict_types = 1);

namespace Pm\Subject\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Subject\Facade\SubjectFacade;
use Pm\Subject\HttpResponse\SubjectResponse;
use Pm\Subject\Input\SubjectCreateUpdateInput;
use Pm\Subject\Input\SubjectGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Subject')]
class SubjectController extends BaseController
{

    public function __construct(
        private readonly SubjectFacade $subjectFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(SubjectResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/subject')]
    public function getListAction(
        #[MapQueryString]
        ?SubjectGetInput $subjectGetInput,
    ): SuccessCountableOutput
    {
        $subjectList = $this->subjectFacade->getSubjectPaginatedListResponse(
            $subjectGetInput ?? new SubjectGetInput(),
        );

        /**
         * @var list<SubjectResponse> $list
         */
        $list = $subjectList->getArrayCopy();
        return new SuccessCountableOutput($list, $subjectList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(SubjectResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/subject/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $subject = $this->subjectFacade->getSubject($id);

        return new SuccessOutput($subject);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(SubjectResponse::class, 'subject-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/subject')]
    public function createSubjectAction(
        #[MapRequestPayload]
        SubjectCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->subjectFacade->createSubject($request),
            Response::HTTP_CREATED,
            'subject-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(SubjectResponse::class, 'subject-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/subject/{id}')]
    public function updateSubjectAction(
        int $id,
        #[MapRequestPayload]
        SubjectCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->subjectFacade->updateSubject($id, $request),
            Response::HTTP_OK,
            'subject-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'subject-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/subject/{id}')]
    public function deleteSubjectAction(int $id): SuccessOutput
    {
        $this->subjectFacade->deleteSubject($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'subject-deleted',
        );
    }

}
