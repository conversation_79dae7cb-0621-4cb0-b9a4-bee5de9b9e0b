<?php

declare(strict_types = 1);

namespace Pm\Subject\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Subject\Facade\SubjectGroupFacade;
use Pm\Subject\Validator\SubjectGroupSortFields;

readonly class SubjectGroupGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?string $search = null,
        public ?int $limit = SubjectGroupFacade::PAGE_MAX_LIMIT,
        public ?int $offset = 0,
        public ?SubjectGroupSortFields $sortBy = null,
        public ?Sort $sortMethod = null,
        public ?array $filter = null,
    )
    {
    }

}
