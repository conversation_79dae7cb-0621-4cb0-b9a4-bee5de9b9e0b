<?php

declare(strict_types = 1);

namespace Pm\Subject\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class SubjectGroupCreateUpdateInput implements Input
{

    public function __construct(
        #[NotBlank(message: 'validation.field.required')]
        #[Length(min: 1, max: 255, minMessage: 'validation.field.min_length', maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly string $name,

        #[Type('bool')]
        public readonly ?bool $active = true,

        /** @var list<int>|null */
        public readonly ?array $subjects = null,
    )
    {
    }

}
