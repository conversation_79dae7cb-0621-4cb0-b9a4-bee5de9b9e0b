<?php

declare(strict_types = 1);

namespace Pm\Subject\Input;

use Misd\PhoneNumberBundle\Validator\Constraints\PhoneNumber;
use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class SubjectCreateUpdateInput implements Input
{

    public function __construct(

        #[NotBlank(message: 'validation.field.required')]
        #[Type('string')]
        public readonly string $firstName,

        #[NotBlank(message: 'validation.field.required')]
        #[Type('string')]
        public readonly string $lastName,

        #[Length(max: 50, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $gid = null,

        #[Length(max: 20, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $titleBeforeName = null,

        #[Length(max: 20, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $titleAfterName = null,

        #[Email(message: 'validation.email.invalid')]
        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $email = null,

        #[PhoneNumber(message: 'validation.phone.invalid')]
        #[Length(max: 20, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $phone = null,

        #[Type('bool')]
        public readonly bool $active = true,
    )
    {
    }

}
