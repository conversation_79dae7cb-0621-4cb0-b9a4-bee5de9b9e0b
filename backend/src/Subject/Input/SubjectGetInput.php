<?php

declare(strict_types = 1);

namespace Pm\Subject\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Subject\Facade\SubjectFacade;
use Pm\Subject\Validator\SubjectSortFields;

class SubjectGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public readonly ?string $search = null,
        public readonly ?int $limit = SubjectFacade::PAGE_MAX_LIMIT,
        public readonly ?int $offset = 0,
        public readonly ?SubjectSortFields $sortBy = null,
        public readonly ?Sort $sortMethod = null,
        public readonly ?array $filter = null,
    )
    {
    }

}
