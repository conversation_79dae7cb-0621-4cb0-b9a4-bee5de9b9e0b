<?php

declare(strict_types = 1);

namespace Pm\Subject\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Subject\Entity\Subject;

readonly class SubjectResponse implements ApiOutput
{

    public int $id;

    public string $first_name;

    public string $last_name;

    public ?string $gid;

    public ?string $title_before_name;

    public ?string $title_after_name;

    public ?string $email;

    public ?string $phone;

    public bool $active;

    public function __construct(Subject $subject)
    {
        $this->id = $subject->getId();
        $this->first_name = $subject->getFirstName();
        $this->last_name = $subject->getLastName();
        $this->gid = $subject->getGid();
        $this->title_before_name = $subject->getTitleBeforeName();
        $this->title_after_name = $subject->getTitleAfterName();
        $this->email = $subject->getEmail();
        $this->phone = $subject->getPhone();
        $this->active = $subject->isActive();
    }

}
