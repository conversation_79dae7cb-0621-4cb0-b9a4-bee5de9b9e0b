<?php

declare(strict_types = 1);

namespace Pm\Subject\HttpResponse;

use InvalidArgumentException;
use Pm\Core\Controller\ApiOutput;
use Pm\Subject\Entity\Subject;
use Pm\Subject\Entity\SubjectGroup;

class SubjectGroupResponse implements ApiOutput
{

    public readonly int $id;

    public readonly string $name;

    public readonly ?bool $active;

    /**
     * @var list<SubjectResponse>
     */
    public readonly array $subjects;

    public function __construct(SubjectGroup $subjectGroup)
    {
        $this->id = $subjectGroup->getId();
        $this->name = $subjectGroup->getName();
        $this->active = $subjectGroup->isActive();

        $subjects = [];
        foreach ($subjectGroup->getSubjects() as $subject) {
            if (!$subject instanceof Subject) {
                throw new InvalidArgumentException('Expected instance of Subject.');
            }
            $subjects[] = new SubjectResponse($subject);
        }
        $this->subjects = $subjects;
    }

}
