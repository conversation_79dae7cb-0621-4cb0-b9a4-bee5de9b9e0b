<?php

declare(strict_types = 1);

namespace Pm\Subject\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class SubjectFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_ID,
        self::FILTER_FIRST_NAME,
        self::FILTER_LAST_NAME,
        self::FILTER_GID,
        self::FILTER_EMAIL,
        self::FILTER_PHONE,
        self::FILTER_ACTIVE,
    ];

    final public const FILTER_ID = 'id';
    final public const FILTER_FIRST_NAME = 'firstName';
    final public const FILTER_LAST_NAME = 'lastName';
    final public const FILTER_GID = 'gid';
    final public const FILTER_EMAIL = 'email';
    final public const FILTER_PHONE = 'phone';
    final public const FILTER_ACTIVE = 'active';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID => Types::INTEGER,
            self::FILTER_ACTIVE => Types::BOOLEAN,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
