<?php

declare(strict_types = 1);

namespace Pm\Subject\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\InverseJoinColumn;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\JoinTable;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\Table;
use Pm\Core\Entity\BaseEntity;

#[Table(name: 'subject_group')]
#[Entity]
class SubjectGroup extends BaseEntity
{

    /**
     * @var Collection<int, Subject>
     */
    #[JoinTable(name: 'subject_group_subject_m2n')]
    #[JoinColumn(name: 'subject_group_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[InverseJoinColumn(name: 'subject_id', referencedColumnName: 'id', unique: false, onDelete: 'CASCADE')]
    #[ManyToMany(targetEntity: Subject::class, inversedBy: 'subjectGroups')]
    private Collection $subjects;

    public function __construct(
        #[Column(type: Types::STRING, length: 255)]
        private string $name,
        #[Column(type: Types::BOOLEAN, nullable: true)]
        private ?bool $active,
    )
    {
        $this->subjects = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): void
    {
        $this->active = $active;
    }

    /**
     * @return Collection<int, Subject>
     */
    public function getSubjects(): Collection
    {
        return $this->subjects;
    }

    public function addSubject(Subject $subject): void
    {
        if (!$this->subjects->contains($subject)) {
            $this->subjects->add($subject);
        }
    }

    public function removeSubject(Subject $subject): void
    {
        $this->subjects->removeElement($subject);
    }

    public function clearSubjects(): void
    {
        $this->subjects->clear();
    }

}
