<?php

declare(strict_types = 1);

namespace Pm\Subject\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\Table;
use Pm\Core\Entity\BaseEntity;

#[Entity]
#[Table(name: 'subject')]
class Subject extends BaseEntity
{

    #[Column(type: Types::STRING, length: 255, nullable: false)]
    private string $firstName;

    #[Column(type: Types::STRING, length: 255, nullable: false)]
    private string $lastName;

    #[Column(type: Types::STRING, length: 50, nullable: true)]
    private ?string $gid;

    #[Column(type: Types::STRING, length: 20, nullable: true)]
    private ?string $titleBeforeName;

    #[Column(type: Types::STRING, length: 20, nullable: true)]
    private ?string $titleAfterName;

    #[Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $email;

    #[Column(type: Types::STRING, length: 20, nullable: true)]
    private ?string $phone;

    #[Column(type: Types::BOOLEAN, nullable: false, options: ['default' => true])]
    private bool $active;

    /**
     * @var Collection<int, SubjectGroup>
     */
    #[ManyToMany(targetEntity: SubjectGroup::class, mappedBy: 'subjects')]
    private Collection $subjectGroups;

    public function __construct(
        string $firstName,
        string $lastName,
        ?string $gid,
        ?string $titleBeforeName,
        ?string $titleAfterName,
        ?string $email,
        ?string $phone,
        bool $active,
    )
    {
        $this->setFirstName($firstName);
        $this->setLastName($lastName);
        $this->setGid($gid);
        $this->setTitleBeforeName($titleBeforeName);
        $this->setTitleAfterName($titleAfterName);
        $this->setEmail($email);
        $this->setPhone($phone);
        $this->setActive($active);
        $this->subjectGroups = new ArrayCollection();
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): void
    {
        $this->firstName = $firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): void
    {
        $this->lastName = $lastName;
    }

    public function getGid(): ?string
    {
        return $this->gid;
    }

    public function setGid(?string $gid): void
    {
        $this->gid = $gid;
    }

    public function getTitleBeforeName(): ?string
    {
        return $this->titleBeforeName;
    }

    public function setTitleBeforeName(?string $titleBeforeName): void
    {
        $this->titleBeforeName = $titleBeforeName;
    }

    public function getTitleAfterName(): ?string
    {
        return $this->titleAfterName;
    }

    public function setTitleAfterName(?string $titleAfterName): void
    {
        $this->titleAfterName = $titleAfterName;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): void
    {
        $this->phone = $phone;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    /**
     * @return Collection<int, SubjectGroup>
     */
    public function getSubjectGroups(): Collection
    {
        return $this->subjectGroups;
    }

    public function addSubjectGroup(SubjectGroup $subjectGroup): void
    {
        if (!$this->subjectGroups->contains($subjectGroup)) {
            $this->subjectGroups->add($subjectGroup);
            $subjectGroup->addSubject($this);
        }
    }

    public function removeSubjectGroup(SubjectGroup $subjectGroup): void
    {
        if ($this->subjectGroups->contains($subjectGroup)) {
            $this->subjectGroups->removeElement($subjectGroup);
            $subjectGroup->removeSubject($this);
        }
    }

}
