<?php

declare(strict_types = 1);

namespace Pm\Subject\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Permission\Enum\PermissionEnum;

readonly class SubjectVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canRead(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_SUBJECT_READ)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canWrite(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_SUBJECT_WRITE)) {
            return;
        }

        throw new AclException();
    }

}
