<?php

declare(strict_types = 1);

namespace Pm\Subject\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Permission\Enum\PermissionEnum;

readonly class SubjectGroupVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canRead(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_SUBJECT_GROUP_READ)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canWrite(): void
    {
        if ($this->hasPermission(PermissionEnum::PERM_SUBJECT_GROUP_WRITE)) {
            return;
        }

        throw new AclException();
    }

}
