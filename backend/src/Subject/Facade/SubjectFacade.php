<?php

declare(strict_types = 1);

namespace Pm\Subject\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Core\Validator\EntityValidator;
use Pm\Subject\Entity\Subject;
use Pm\Subject\HttpResponse\SubjectResponse;
use Pm\Subject\Input\SubjectCreateUpdateInput;
use Pm\Subject\Input\SubjectGetInput;
use Pm\Subject\Repository\SubjectRepository;
use Pm\Subject\Security\SubjectVoter;
use function count;

class SubjectFacade
{

    public const PAGE_MAX_LIMIT = 50;

    public function __construct(
        private readonly SubjectRepository $subjectRepository,
        private readonly SubjectVoter $subjectVoter,
        private readonly EntityManager $entityManager,
        private readonly EntityValidator $entityValidator,
    )
    {
    }

    /**
     * @return PaginatedCollection<SubjectResponse>
     *
     * @throws AclException
     */
    public function getSubjectPaginatedListResponse(SubjectGetInput $subjectGetInput): PaginatedCollection
    {
        $this->subjectVoter->canRead();

        $subjectList = $this->subjectRepository->getSubjectList($subjectGetInput);

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($subjectList));
        foreach ($subjectList as $subject) {
            $responseList[] = new SubjectResponse($subject);
        }

        return $responseList;
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function getSubject(int $id): SubjectResponse
    {
        $this->subjectVoter->canRead();

        $subject = $this->subjectRepository->get($id);

        return new SubjectResponse($subject);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function createSubject(SubjectCreateUpdateInput $subjectCreate): SubjectResponse
    {
        $this->subjectVoter->canWrite();

        /** @var Subject $subject */
        $subject = $this->entityManager->wrapInTransaction(function () use ($subjectCreate): Subject {
            $subject = new Subject(
                $subjectCreate->firstName,
                $subjectCreate->lastName,
                $subjectCreate->gid,
                $subjectCreate->titleBeforeName,
                $subjectCreate->titleAfterName,
                $subjectCreate->email,
                $subjectCreate->phone,
                $subjectCreate->active,
            );

            $this->entityValidator->validateEntity($subject);
            $this->entityManager->persist($subject);

            return $subject;
        });

        return new SubjectResponse($subject);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function updateSubject(int $id, SubjectCreateUpdateInput $subjectUpdate): SubjectResponse
    {
        $this->subjectVoter->canWrite();

        /** @var Subject $subject */
        $subject = $this->entityManager->wrapInTransaction(function () use ($id, $subjectUpdate): Subject {
            $subject = $this->subjectRepository->get($id);

            $subject->setFirstName($subjectUpdate->firstName);
            $subject->setLastName($subjectUpdate->lastName);
            $subject->setGid($subjectUpdate->gid);
            $subject->setTitleBeforeName($subjectUpdate->titleBeforeName);
            $subject->setTitleAfterName($subjectUpdate->titleAfterName);
            $subject->setEmail($subjectUpdate->email);
            $subject->setPhone($subjectUpdate->phone);
            $subject->setActive($subjectUpdate->active);

            $this->entityValidator->validateEntity($subject);

            return $subject;
        });

        return new SubjectResponse($subject);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function deleteSubject(int $id): void
    {
        $this->subjectVoter->canWrite();

        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $subject = $this->subjectRepository->get($id);
            $this->entityManager->remove($subject);
        });
    }

}
