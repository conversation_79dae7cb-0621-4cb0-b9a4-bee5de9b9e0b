<?php

declare(strict_types = 1);

namespace Pm\Subject\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Core\Validator\EntityValidator;
use Pm\Subject\Entity\SubjectGroup;
use Pm\Subject\HttpResponse\SubjectGroupResponse;
use Pm\Subject\Input\SubjectGroupCreateUpdateInput;
use Pm\Subject\Input\SubjectGroupGetInput;
use Pm\Subject\Repository\SubjectGroupRepository;
use Pm\Subject\Repository\SubjectRepository;
use Pm\Subject\Security\SubjectGroupVoter;

class SubjectGroupFacade
{

    public const PAGE_MAX_LIMIT = 50;

    public function __construct(
        private readonly SubjectGroupRepository $subjectGroupRepository,
        private readonly SubjectRepository $subjectRepository,
        private readonly SubjectGroupVoter $subjectGroupVoter,
        private readonly EntityManager $entityManager,
        private readonly EntityValidator $entityValidator,
    )
    {
    }

    /**
     * @return PaginatedCollection<SubjectGroupResponse>
     *
     * @throws AclException
     */
    public function getSubjectGroupPaginatedListResponse(SubjectGroupGetInput $subjectGroupGetInput): PaginatedCollection
    {
        $this->subjectGroupVoter->canRead();

        $subjectGroupList = $this->subjectGroupRepository->getSubjectGroupList(
            $subjectGroupGetInput,
        );

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount($subjectGroupList->count());
        foreach ($subjectGroupList as $subjectGroup) {
            $responseList[] = new SubjectGroupResponse($subjectGroup);
        }

        return $responseList;
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function getSubjectGroup(int $id): SubjectGroupResponse
    {
        $this->subjectGroupVoter->canRead();

        $subjectGroup = $this->subjectGroupRepository->get($id);

        return new SubjectGroupResponse($subjectGroup);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function createSubjectGroup(SubjectGroupCreateUpdateInput $subjectGroupCreate): SubjectGroupResponse
    {
        $this->subjectGroupVoter->canWrite();

        /** @var SubjectGroup $subjectGroup */
        $subjectGroup = $this->entityManager->wrapInTransaction(function () use ($subjectGroupCreate): SubjectGroup {
            $subjectGroup = new SubjectGroup(
                $subjectGroupCreate->name,
                $subjectGroupCreate->active ?? true,
            );

            foreach ($subjectGroupCreate->subjects ?? [] as $subjectId) {
                $subject = $this->subjectRepository->get((int) $subjectId);
                $subjectGroup->addSubject($subject);
            }

            $this->entityValidator->validateEntity($subjectGroup);
            $this->entityManager->persist($subjectGroup);

            return $subjectGroup;
        });

        return new SubjectGroupResponse($subjectGroup);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function updateSubjectGroup(int $id, SubjectGroupCreateUpdateInput $subjectGroupUpdate): SubjectGroupResponse
    {
        $this->subjectGroupVoter->canWrite();

        /** @var SubjectGroup $subjectGroup */
        $subjectGroup = $this->entityManager->wrapInTransaction(function () use ($id, $subjectGroupUpdate): SubjectGroup {
            $subjectGroup = $this->subjectGroupRepository->get($id);

            $subjectGroup->setName($subjectGroupUpdate->name);
            $subjectGroup->setActive($subjectGroupUpdate->active);

            $subjectGroup->clearSubjects();
            foreach ($subjectGroupUpdate->subjects ?? [] as $subjectId) {
                $subject = $this->subjectRepository->get((int) $subjectId);
                $subjectGroup->addSubject($subject);
            }

            $this->entityValidator->validateEntity($subjectGroup);

            return $subjectGroup;
        });

        return new SubjectGroupResponse($subjectGroup);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function deleteSubjectGroup(int $id): void
    {
        $this->subjectGroupVoter->canWrite();

        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $subjectGroup = $this->subjectGroupRepository->get($id);
            $this->entityManager->remove($subjectGroup);
        });
    }

}
