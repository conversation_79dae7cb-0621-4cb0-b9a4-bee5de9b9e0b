<?php

declare(strict_types = 1);

namespace Pm\Subject\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Http\ResponseStatusCode;
use Pm\Core\Repository\BaseRepository;
use Pm\Subject\Entity\Subject;
use Pm\Subject\FilterInput\SubjectFilterInput;
use Pm\Subject\Input\SubjectGetInput;
use Pm\Tools\SortingHelper;

class SubjectRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): Subject
    {
        $subject = $this->entityManager->createQueryBuilder()
            ->select('s')
            ->from(Subject::class, 's')
            ->where('s.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($subject === null) {
            throw new InvalidInputException("Entity with ID {$id} not found.", null, ResponseStatusCode::NOT_FOUND_404);
        }

        return $subject;
    }

    /**
     * @return Paginator<Subject>
     */
    public function getSubjectList(SubjectGetInput $subjectGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('s')
            ->from(Subject::class, 's');

        if ($subjectGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like(
                        (string) $queryBuilder->expr()->lower('s.firstName'),
                        (string) $queryBuilder->expr()->lower(':search'),
                    ),
                    $queryBuilder->expr()->like(
                        (string) $queryBuilder->expr()->lower('s.lastName'),
                        (string) $queryBuilder->expr()->lower(':search'),
                    ),
                    $queryBuilder->expr()->like(
                        (string) $queryBuilder->expr()->lower('s.email'),
                        (string) $queryBuilder->expr()->lower(':search'),
                    ),
                ),
            )->setParameter('search', '%' . $subjectGetInput->search . '%');
        }

        $this->applyCriteria(
            null,
            $queryBuilder,
            new SubjectFilterInput($subjectGetInput->filter),
            $subjectGetInput->limit,
            $subjectGetInput->offset,
            SortingHelper::sanitizeSortBy($subjectGetInput->sortBy, 's', 'firstName'),
            SortingHelper::sanitizeSortMethod($subjectGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder->getQuery());
    }

}
