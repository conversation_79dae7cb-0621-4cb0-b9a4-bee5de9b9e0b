<?php

declare(strict_types = 1);

namespace Pm\Subject\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Http\ResponseStatusCode;
use Pm\Core\Repository\BaseRepository;
use Pm\Subject\Entity\SubjectGroup;
use Pm\Subject\FilterInput\SubjectGroupFilterInput;
use Pm\Subject\Input\SubjectGroupGetInput;
use Pm\Tools\SortingHelper;

class SubjectGroupRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): SubjectGroup
    {
        $subjectGroup = $this->entityManager->createQueryBuilder()
            ->select('sg')
            ->from(SubjectGroup::class, 'sg')
            ->where('sg.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($subjectGroup === null) {
            throw new InvalidInputException("Entity with ID {$id} not found.", null, ResponseStatusCode::NOT_FOUND_404);
        }

        return $subjectGroup;
    }

    /**
     * @return Paginator<SubjectGroup>
     */
    public function getSubjectGroupList(SubjectGroupGetInput $subjectGroupGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('sg')
            ->from(SubjectGroup::class, 'sg');

        if ($subjectGroupGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('sg.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $subjectGroupGetInput->search . '%');
        }

        $this->applyCriteria(
            null,
            $queryBuilder,
            new SubjectGroupFilterInput($subjectGroupGetInput->filter),
            $subjectGroupGetInput->limit,
            $subjectGroupGetInput->offset,
            SortingHelper::sanitizeSortBy($subjectGroupGetInput->sortBy, 'sg', 'name'),
            SortingHelper::sanitizeSortMethod($subjectGroupGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder->getQuery());
    }

}
