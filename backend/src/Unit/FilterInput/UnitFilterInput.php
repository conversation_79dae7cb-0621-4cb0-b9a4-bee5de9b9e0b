<?php

declare(strict_types = 1);

namespace Pm\Unit\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class UnitFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_ACTIVE,
        self::FILTER_NAME,
        self::FILTER_CODE,
        self::FILTER_ID,
    ];

    final public const FILTER_ACTIVE = 'active';
    final public const FILTER_NAME = 'name';
    final public const FILTER_CODE = 'code';
    final public const FILTER_ID = 'id';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ACTIVE => Types::BOOLEAN,
            self::FILTER_ID => Types::INTEGER,
            self::FILTER_NAME, self::FILTER_CODE => Types::STRING,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
