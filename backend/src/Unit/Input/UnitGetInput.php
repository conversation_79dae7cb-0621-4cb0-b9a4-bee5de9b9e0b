<?php

declare(strict_types = 1);

namespace Pm\Unit\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Unit\Facade\UnitFacade;
use Pm\Unit\Validator\UnitSortFields;

class UnitGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public readonly ?string $search = null,
        public readonly ?int $limit = UnitFacade::PAGE_MAX_LIMIT,
        public readonly ?int $offset = 0,
        public readonly ?UnitSortFields $sortBy = null,
        public readonly ?Sort $sortMethod = null,
        public readonly ?array $filter = null,
    )
    {
    }

}
