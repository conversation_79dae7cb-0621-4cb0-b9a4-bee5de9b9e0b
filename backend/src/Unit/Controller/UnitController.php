<?php

declare(strict_types = 1);

namespace Pm\Unit\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Unit\Facade\UnitFacade;
use Pm\Unit\HttpResponse\UnitResponse;
use Pm\Unit\Input\UnitCreateUpdateInput;
use Pm\Unit\Input\UnitGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

class UnitController extends BaseController
{

    public function __construct(
        private readonly UnitFacade $unitFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[Get(path: '/api/v1/unit')]
    public function getListAction(
        #[MapQueryString]
        ?UnitGetInput $unitGetInput,
    ): SuccessCountableOutput
    {
        $unitList = $this->unitFacade->getUnitPaginatedListResponse(
            $unitGetInput ?? new UnitGetInput(),
        );

        /**
         * @var list<UnitResponse> $list
         */
        $list = $unitList->getArrayCopy();
        return new SuccessCountableOutput($list, $unitList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Get(path: '/api/v1/unit/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $unit = $this->unitFacade->getUnit($id);

        return new SuccessOutput($unit);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Post(path: '/api/v1/unit')]
    public function createUnitAction(
        #[MapRequestPayload]
        UnitCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->unitFacade->createUnit($request),
            Response::HTTP_CREATED,
            'unit-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Put(path: '/api/v1/unit/{id}')]
    public function updateUnitAction(
        int $id,
        #[MapRequestPayload]
        UnitCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->unitFacade->updateUnit($id, $request),
            Response::HTTP_OK,
            'unit-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Delete(path: '/api/v1/unit/{id}')]
    public function deleteUnitAction(int $id): SuccessOutput
    {
        $this->unitFacade->deleteUnit($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'unit-deleted',
        );
    }

}
