<?php

declare(strict_types = 1);

namespace Pm\Unit\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Repository\BaseRepository;
use Pm\Tools\SortingHelper;
use Pm\Unit\Entity\Unit;
use Pm\Unit\FilterInput\UnitFilterInput;
use Pm\Unit\Input\UnitGetInput;

class UnitRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): Unit
    {
        $unit = $this->entityManager->createQueryBuilder()
            ->select('u')
            ->from(Unit::class, 'u')
            ->where('u.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($unit === null) {
            throw new InvalidInputException("Unit with ID {$id} not found.");
        }

        return $unit;
    }

    /**
     * @return Paginator<Unit>
     */
    public function getUnitList(UnitGetInput $unitGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('u')
            ->from(Unit::class, 'u');

        if ($unitGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('u.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->orWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('u.code'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $unitGetInput->search . '%');
        }

        $this->applyCriteria(
            null,
            $queryBuilder,
            new UnitFilterInput($unitGetInput->filter),
            $unitGetInput->limit,
            $unitGetInput->offset,
            SortingHelper::sanitizeSortBy($unitGetInput->sortBy, 'u', 'name'),
            SortingHelper::sanitizeSortMethod($unitGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    public function existsByNameOrCode(string $name, string $code): bool
    {
        $queryBuilder = $this->entityManager->createQueryBuilder();
        $queryBuilder->select('1')
            ->from(Unit::class, 'u')
            ->where('u.name = :name')
            ->orWhere('u.code = :code')
            ->setParameter('name', $name)
            ->setParameter('code', $code)
            ->setMaxResults(1);

        return (bool) $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findOneByNameOrCode(string $name, string $code): ?Unit
    {
        return $this->entityManager->createQueryBuilder()
            ->select('u')
            ->from(Unit::class, 'u')
            ->where('u.name = :name OR u.code = :code')
            ->setParameter('name', $name)
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
