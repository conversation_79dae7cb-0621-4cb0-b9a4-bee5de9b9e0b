<?php

declare(strict_types = 1);

namespace Pm\Tools;

use enshrined\svgSanitize\Sanitizer;
use Exception;
use League\Flysystem\FilesystemOperator;
use Nette\Utils\FileSystem;
use Nette\Utils\Image;
use Nette\Utils\ImageException;
use Nette\Utils\Strings;
use Pm\Tools\Exception\ImageOptimizationException;
use Pm\Tools\Mime\InvalidImageException;
use Pm\Tools\Mime\MimeType;
use Spatie\ImageOptimizer\OptimizerChainFactory;
use function exif_read_data;
use function explode;
use function fclose;
use function fwrite;
use function is_int;
use function is_numeric;
use function sys_get_temp_dir;
use const DIRECTORY_SEPARATOR;

class ImageSanitizer
{

    private readonly Sanitizer $svgSanitizer;

    public function __construct(
        private readonly FileSanitizer $fileSanitizer,
        private readonly FilesystemOperator $storage,
    )
    {
        $this->svgSanitizer = new Sanitizer();
        $this->svgSanitizer->minify(true);
        $this->svgSanitizer->removeRemoteReferences(true);
    }

    public function getImageThumbnailFilename(string $imageFilename): string
    {
        return 'thumb.' . $imageFilename;
    }

    public function getImageThumbnailPublicUrl(string $imageFilename): string
    {
        return $this->storage->publicUrl($this->getImageThumbnailFilename($imageFilename));
    }

    /**
     * @throws InvalidImageException
     */
    public function sanitizeAndResizeIfNeededImage(string $content, string $maxImageDimensionInPixels): SafeImage
    {
        if (Strings::startsWith($content, '<?xml')) {
            $content = $this->svgSanitizer->sanitize($content);
            if ($content === false) {
                throw InvalidImageException::createWithMessage('SVG sanitize failed. Try to use another image format.');
            }

            return SafeImage::create($content, MimeType::Svg);
        }
        try {
            $image = Image::fromString($content);
            [$maxWidth, $maxHeight] = explode('x', $maxImageDimensionInPixels);
            if ($image->getWidth() > (int) $maxWidth || $image->getHeight() > (int) $maxHeight) {
                $this->ensureProperOrientationIsKept($content, $image);
                $content = $image->resize((int) $maxWidth, (int) $maxHeight)->toString();
            }
            return SafeImage::create($content, $this->fileSanitizer->resolveMimeType($content));
        } catch (ImageException $e) {
            throw InvalidImageException::create($e);
        }
    }

    /**
     * @throws ImageOptimizationException
     */
    public function optimize(string $filename, string $content): string
    {
        try {
            $tmpFile = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $filename;
            FileSystem::write($tmpFile, $content);
            OptimizerChainFactory::create()->optimize($tmpFile);
            $optimizedContent = FileSystem::read($tmpFile);
            FileSystem::delete($tmpFile);

            return $optimizedContent;
        } catch (Exception $exception) {
            throw ImageOptimizationException::create($exception);
        }
    }

    private function ensureProperOrientationIsKept(
        string $content,
        Image $image,
    ): void
    {
        $resource = ResourceHelper::createMemoryResource();
        fwrite($resource, $content);
        $exif = @exif_read_data($resource, 'EXIF');
        fclose($resource);
        if ($exif === false) {
            return;
        }
        if (isset($exif['Orientation']) && (is_int($exif['Orientation']) || is_numeric($exif['Orientation']))) {
            $imageOrientation = (int) $exif['Orientation'];
            if ($imageOrientation === 8) {
                $image->rotate(90, 0);
            } elseif ($imageOrientation === 3) {
                $image->rotate(180, 0);
            } elseif ($imageOrientation === 6) {
                $image->rotate(-90, 0);
            }
        }
    }

}
