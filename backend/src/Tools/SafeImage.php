<?php

declare(strict_types = 1);

namespace Pm\Tools;

use Pm\Tools\Mime\MimeType;

class SafeImage
{

    private function __construct(private readonly string $content, private readonly MimeType $mimeType)
    {
    }

    /**
     * @internal
     */
    public static function create(
        string $content,
        MimeType $mimeType,
    ): self
    {
        return new self($content, $mimeType);
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function getMimeType(): MimeType
    {
        return $this->mimeType;
    }

}
