<?php

declare(strict_types = 1);

namespace Pm\Tools;

use DateTimeInterface;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class Sheet
{

    /**
     * Set datetime format for sheet cell
     *
     * @param array<int> $cell
     */
    public static function setCellDatetime(Worksheet $sheet, array $cell, ?DateTimeInterface $value): void
    {
        if ($value instanceof DateTimeInterface) {
            $sheet->getStyle($cell)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_DATETIME);

            $sheet->setCellValueExplicit($cell, Date::PHPToExcel($value), DataType::TYPE_NUMERIC);
        }
    }

    /**
     * Set date format for sheet cell
     *
     * @param array<int> $cell
     */
    public static function setCellDate(Worksheet $sheet, array $cell, ?DateTimeInterface $value): void
    {
        if ($value instanceof DateTimeInterface) {
            $sheet->getStyle($cell)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_DDMMYYYY);

            $sheet->setCellValueExplicit($cell, Date::PHPToExcel($value), DataType::TYPE_NUMERIC);
        }
    }

}
