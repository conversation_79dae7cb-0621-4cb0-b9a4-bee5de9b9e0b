<?php

declare(strict_types = 1);

namespace Pm\Tools;

use BackedEnum;
use Pm\Core\Enum\Sort;
use function str_contains;
use function strtoupper;

class SortingHelper
{

    /**
     * @param BackedEnum<string>|null $sortBy
     */
    public static function sanitizeSortBy(?BackedEnum $sortBy, string $defaultTablePrefix = '', string $defaultSortBy = ''): string
    {
        if ($sortBy === null) {
            $sortByStr = $defaultSortBy;
        } else {
            $sortByStr = $sortBy->value;
        }

        if (!str_contains($sortByStr, '.')) {
            $sortByStr = $defaultTablePrefix . '.' . $sortByStr;
        }

        return $sortByStr;
    }

    /**
     * @param BackedEnum<string>|null $sortMethod
     */
    public static function sanitizeSortMethod(?BackedEnum $sortMethod, Sort $defaultSortMethod = Sort::Desc): string
    {
        return $sortMethod === null ? $defaultSortMethod->value : strtoupper($sortMethod->value);
    }

}
