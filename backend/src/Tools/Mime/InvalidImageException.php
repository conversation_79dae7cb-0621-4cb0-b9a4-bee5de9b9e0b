<?php

declare(strict_types = 1);

namespace Pm\Tools\Mime;

use Exception;
use Pm\Core\Exception\RuntimeException;

class InvalidImageException extends RuntimeException
{

    private const DEFAULT_ERROR_MESSAGE = 'Invalid image data. Try another format (jpg, png, webp, svg).';

    public static function createWithMessage(string $message): self
    {
        $prefix = self::DEFAULT_ERROR_MESSAGE;

        return new self("{$prefix} {$message}");
    }

    public static function create(Exception $previous): self
    {
        $prefix = self::DEFAULT_ERROR_MESSAGE;

        return new self("{$prefix} {$previous->getMessage()}", $previous);
    }

}
