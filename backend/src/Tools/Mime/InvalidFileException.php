<?php

declare(strict_types = 1);

namespace Pm\Tools\Mime;

use Pm\Core\Exception\RuntimeException;

class InvalidFileException extends RuntimeException
{

    public static function byMessage(string $message): self
    {
        return new self($message);
    }

    public static function bySize(string $filename, float $filesize, int $limit): self
    {
        return new self("Invalid file #[{$filename}]# with size #[{$filesize}]#MB. Max file size is #[{$limit}]#MB.");
    }

}
