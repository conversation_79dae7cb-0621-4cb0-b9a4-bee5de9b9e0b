<?php

declare(strict_types = 1);

namespace Pm\Tools\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\Migrations\Query\Query;

readonly class PermissionHelper
{

    /**
     * @param array<string, string> $newPermissions
     * @return list<Query>
     */
    public static function prepareNewQueries(array $newPermissions): array
    {
        $queries = [];
        foreach ($newPermissions as $code => $application) {
            $queries[] = new Query('INSERT INTO permission (code, application)
                    SELECT :code, :application
                    WHERE NOT EXISTS (SELECT 1 FROM permission WHERE code = :code)', [
                'code' => $code,
                'application' => $application,
            ]);
        }

        return $queries;
    }

    /**
     * @param array<string, string> $permissionMappings
     * @return list<Query>
     *
     * @throws Exception
     */
    public static function prepareReplaceUpQueries(array $permissionMappings, Connection $connection): array
    {
        $queries = [];

        // Group old permissions by new permission code
        $groupedPermissions = [];
        foreach ($permissionMappings as $oldCode => $newCode) {
            if ($newCode !== '') {
                if (!isset($groupedPermissions[$newCode])) {
                    $groupedPermissions[$newCode] = [];
                }
                $groupedPermissions[$newCode][] = $oldCode;
            }
        }

        // For each template, handle the permission updates
        $queries[] = new Query('SELECT DISTINCT permission_template_id FROM permission_template_permission');
        $templateIds = $connection->fetchAllAssociative('SELECT DISTINCT permission_template_id FROM permission_template_permission');

        foreach ($templateIds as $template) {
            $templateId = $template['permission_template_id'];

            // For each group of permissions
            foreach ($groupedPermissions as $newCode => $oldCodes) {
                // Check if the template already has the new permission
                $hasNewPermission = (int) $connection->fetchOne(
                    'SELECT COUNT(*) FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :newCode',
                    ['templateId' => $templateId, 'newCode' => $newCode],
                );

                if ($hasNewPermission > 0) {
                    // If the template already has the new permission, just delete the old ones
                    foreach ($oldCodes as $oldCode) {
                        $queries[] = new Query(
                            'DELETE FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :oldCode',
                            ['templateId' => $templateId, 'oldCode' => $oldCode],
                        );
                    }
                } else {
                    // If the template doesn't have the new permission, convert the first old one found
                    $foundOldPermission = false;

                    foreach ($oldCodes as $oldCode) {
                        $hasOldPermission = (int) $connection->fetchOne(
                            'SELECT COUNT(*) FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :oldCode',
                            ['templateId' => $templateId, 'oldCode' => $oldCode],
                        );

                        if ($hasOldPermission > 0 && !$foundOldPermission) {
                            // Update the first old permission to the new one
                            $queries[] = new Query(
                                'UPDATE permission_template_permission SET permission = :newCode WHERE permission_template_id = :templateId AND permission = :oldCode',
                                ['templateId' => $templateId, 'newCode' => $newCode, 'oldCode' => $oldCode],
                            );
                            $foundOldPermission = true;
                        } elseif ($hasOldPermission > 0) {
                            // Delete any additional old permissions
                            $queries[] = new Query(
                                'DELETE FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :oldCode',
                                ['templateId' => $templateId, 'oldCode' => $oldCode],
                            );
                        }
                    }
                }
            }

            // Handle permissions with empty mappings
            foreach ($permissionMappings as $oldCode => $newCode) {
                if ($newCode === '') {
                    $queries[] = new Query(
                        'DELETE FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :oldCode',
                        ['templateId' => $templateId, 'oldCode' => $oldCode],
                    );
                }
            }
        }

        // Delete all old permissions from the permission table
        foreach ($permissionMappings as $oldCode => $newCode) {
            $queries[] = new Query('DELETE FROM permission WHERE code = :oldCode', ['oldCode' => $oldCode]);
        }

        return $queries;
    }

    /**
     * @param array<string,string> $permissionMappings
     * @return list<Query>
     *
     * @throws Exception
     */
    public static function prepareReplaceDownQueries(array $permissionMappings, Connection $connection): array
    {
        $reverseGroupedPermissions = [];
        foreach ($permissionMappings as $oldCode => $newCode) {
            if ($newCode !== '') {
                if (!isset($reverseGroupedPermissions[$oldCode])) {
                    $reverseGroupedPermissions[$oldCode] = $newCode;
                }
            }
        }

        $queries = [];

        // For each template, handle the permission reversals
        $templateIds = $connection->fetchAllAssociative('SELECT DISTINCT permission_template_id FROM permission_template_permission');
        foreach ($templateIds as $template) {
            $templateId = $template['permission_template_id'];

            // For each old permission code
            foreach ($reverseGroupedPermissions as $oldCode => $newCode) {
                // Check if the template has the new permission
                $hasNewPermission = (int) $connection->fetchOne(
                    'SELECT COUNT(*) FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :newCode',
                    ['templateId' => $templateId, 'newCode' => $newCode],
                );

                if ($hasNewPermission > 0) {
                    // Check if the old permission already exists for this template
                    $hasOldPermission = (int) $connection->fetchOne(
                        'SELECT COUNT(*) FROM permission_template_permission WHERE permission_template_id = :templateId AND permission = :oldCode',
                        ['templateId' => $templateId, 'oldCode' => $oldCode],
                    );

                    if ($hasOldPermission === 0) {
                        // If the template has the new permission but not the old one,
                        // create a new entry with the old permission code
                        $queries[] = new Query(
                            'INSERT INTO permission_template_permission (permission_template_id, permission) VALUES (:templateId, :oldCode)',
                            ['templateId' => $templateId, 'oldCode' => $oldCode],
                        );
                    }
                }
            }
        }

        return $queries;
    }

}
