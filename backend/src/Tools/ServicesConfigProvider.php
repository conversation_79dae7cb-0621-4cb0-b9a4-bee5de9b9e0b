<?php

declare(strict_types = 1);

namespace Pm\Tools;

use Symfony\Component\Yaml\Yaml;
use function ksort;
use function str_replace;
use const SORT_FLAG_CASE;
use const SORT_STRING;

class ServicesConfigProvider
{

    public function __construct(private readonly string $serviceConfigPath)
    {
    }

    /**
     * @return mixed[]
     */
    public function getServices(): array
    {
        return Yaml::parseFile($this->serviceConfigPath, Yaml::PARSE_CONSTANT | Yaml::PARSE_CUSTOM_TAGS)['services'];
    }

    /**
     * @param mixed[] $services
     * @return mixed[]
     */
    public function sort(array $services): array
    {
        ksort($services, SORT_FLAG_CASE | SORT_STRING);

        return $services;
    }

    /**
     * @param mixed[] $services
     */
    public function formatForConfig(array $services): string
    {
        return str_replace(': null', ':', Yaml::dump(['services' => $services], 99, 2));
    }

    public function getServicePath(): string
    {
        return $this->serviceConfigPath;
    }

}
