<?php

declare(strict_types = 1);

namespace Pm\Project\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Project\Facade\ProjectFacade;
use Pm\Project\HttpResponse\ProjectResponse;
use Pm\Project\Input\ProjectCreateUpdateInput;
use Pm\Project\Input\ProjectGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

class ProjectController extends BaseController
{

    public function __construct(
        private readonly ProjectFacade $projectFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[Get(path: '/api/v1/project')]
    public function getListAction(
        #[MapQueryString]
        ?ProjectGetInput $projectGetInput,
    ): SuccessCountableOutput
    {
        $projectList = $this->projectFacade->getProjectPaginatedListResponse(
            $projectGetInput ?? new ProjectGetInput(),
        );

        /**
         * @var list<ProjectResponse> $list
         */
        $list = $projectList->getArrayCopy();
        return new SuccessCountableOutput($list, $projectList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Get(path: '/api/v1/project/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $project = $this->projectFacade->getProject($id);

        return new SuccessOutput($project);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Post(path: '/api/v1/project')]
    public function createProjectAction(
        #[MapRequestPayload]
        ProjectCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->projectFacade->createProject($request),
            Response::HTTP_CREATED,
            'project-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Put(path: '/api/v1/project/{id}')]
    public function updateProjectAction(
        int $id,
        #[MapRequestPayload]
        ProjectCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->projectFacade->updateProject($id, $request),
            Response::HTTP_OK,
            'project-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Delete(path: '/api/v1/project/{id}')]
    public function deleteProjectAction(int $id): SuccessOutput
    {
        $this->projectFacade->deleteProject($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'project-deleted',
        );
    }

}
