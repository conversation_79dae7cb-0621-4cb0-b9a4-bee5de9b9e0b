<?php

declare(strict_types = 1);

namespace Pm\Project\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class ProjectCreateUpdateInput implements Input
{

    public function __construct(
        #[NotBlank(message: 'validation.field.required')]
        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly string $name,

        #[NotBlank(message: 'validation.field.required')]
        #[Length(max: 50, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly string $code,

        #[Type('bool')]
        public readonly bool $active,
    )
    {
    }

}
