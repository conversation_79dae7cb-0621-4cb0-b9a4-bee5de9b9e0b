<?php

declare(strict_types = 1);

namespace Pm\Project\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Project\Entity\Project;

class ProjectResponse implements ApiOutput
{

    public readonly int $id;

    public readonly string $name;

    public readonly string $code;

    public readonly bool $active;

    public function __construct(Project $project)
    {
        $this->id = $project->getId();
        $this->name = $project->getName();
        $this->code = $project->getCode();
        $this->active = $project->isActive();
    }

}
