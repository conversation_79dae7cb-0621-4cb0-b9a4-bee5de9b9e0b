<?php

declare(strict_types = 1);

namespace Pm\Tenant\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Tenant\Facade\TenantGroupFacade;
use Pm\Tenant\Validator\TenantGroupSortFields;

readonly class TenantGroupGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?string $search = null,
        public ?int $limit = TenantGroupFacade::PAGE_MAX_LIMIT,
        public ?int $offset = 0,
        public ?TenantGroupSortFields $sortBy = null,
        public ?Sort $sortMethod = null,
        public ?array $filter = null,
    )
    {
    }

}
