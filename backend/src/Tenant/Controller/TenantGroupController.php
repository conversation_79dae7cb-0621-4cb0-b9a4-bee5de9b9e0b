<?php

declare(strict_types = 1);

namespace Pm\Tenant\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Facility\Exception\NoFacilityFoundException;
use Pm\Tenant\Facade\TenantGroupFacade;
use Pm\Tenant\HttpResponse\TenantGroupResponse;
use Pm\Tenant\Input\TenantGroupCreateUpdateInput;
use Pm\Tenant\Input\TenantGroupGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

class TenantGroupController extends BaseController
{

    public function __construct(
        private readonly TenantGroupFacade $tenantGroupFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[Get(path: '/api/v1/tenant-group')]
    public function getListAction(
        #[MapQueryString]
        ?TenantGroupGetInput $tenantGroupGetInput,
    ): SuccessCountableOutput
    {
        $tenantGroupList = $this->tenantGroupFacade->getTenantGroupPaginatedListResponse(
            $tenantGroupGetInput ?? new TenantGroupGetInput(),
        );

        /**
         * @var list<TenantGroupResponse> $list
         */
        $list = $tenantGroupList->getArrayCopy();
        return new SuccessCountableOutput($list, $tenantGroupList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Get(path: '/api/v1/tenant-group/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $tenantGroup = $this->tenantGroupFacade->getTenantGroup($id);

        return new SuccessOutput($tenantGroup);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Post(path: '/api/v1/tenant-group')]
    public function createTenantGroupAction(
        #[MapRequestPayload]
        TenantGroupCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->tenantGroupFacade->createTenantGroup($request),
            Response::HTTP_CREATED,
            'tenant-group-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Put(path: '/api/v1/tenant-group/{id}')]
    public function updateTenantGroupAction(
        int $id,
        #[MapRequestPayload]
        TenantGroupCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->tenantGroupFacade->updateTenantGroup($id, $request),
            Response::HTTP_OK,
            'tenant-group-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[Delete(path: '/api/v1/tenant-group/{id}')]
    public function deleteTenantGroupAction(int $id): SuccessOutput
    {
        $this->tenantGroupFacade->deleteTenantGroup($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'tenant-group-deleted',
        );
    }

    /**
     * @throws NoFacilityFoundException
     * @throws AclException
     */
    #[Get('/api/v1/tenant-group/facility/{facilityId}')]
    public function getTenantGroupPaginatedByFacilityAction(
        int $facilityId,
        #[MapQueryString]
        ?TenantGroupGetInput $tenantGroupGetInput,
    ): SuccessCountableOutput
    {
        $tenantGroupList = $this->tenantGroupFacade->getTenantGroupPaginatedByFacility(
            $facilityId,
            $tenantGroupGetInput ?? new TenantGroupGetInput(),
        );

        return new SuccessCountableOutput(
            $tenantGroupList->getArrayCopy(),
            $tenantGroupList->getTotalCount(),
        );
    }

}
