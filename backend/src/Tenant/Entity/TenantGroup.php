<?php

declare(strict_types = 1);

namespace Pm\Tenant\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\InverseJoinColumn;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\JoinTable;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Pm\Account\Entity\Account;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\OriginalDbEntity;
use Pm\Facility\Entity\Facility;

#[Table(name: 'tenant_group')]
#[Entity]
class TenantGroup extends BaseEntity
{

    use OriginalDbEntity;

    #[Column(type: Types::STRING, length: 255)]
    private string $name;

    #[Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $active;

    #[ManyToOne(targetEntity: Facility::class)]
    #[JoinColumn(name: 'facility_id', referencedColumnName: 'id', nullable: false, onDelete: 'CASCADE')]
    private Facility $facility;

    /**
     * @var Collection<int, Tenant>
     */
    #[JoinTable(name: 'tenant_group_tenant_m2n')]
    #[JoinColumn(name: 'tenant_group_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[InverseJoinColumn(name: 'tenant_id', referencedColumnName: 'id', unique: false, onDelete: 'CASCADE')]
    #[ManyToMany(targetEntity: Tenant::class, inversedBy: 'tenantGroups')]
    private Collection $tenants;

    public function __construct(string $name, bool $active, Facility $facility, Account $account)
    {
        $this->name = $name;
        $this->active = $active;
        $this->facility = $facility;
        $this->account = $account;
        $this->tenants = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): void
    {
        $this->active = $active;
    }

    public function getFacility(): Facility
    {
        return $this->facility;
    }

    public function setFacility(Facility $facility): void
    {
        $this->facility = $facility;
    }

    /**
     * @return Collection<int, Tenant>
     */
    public function getTenants(): Collection
    {
        return $this->tenants;
    }

    public function addTenant(Tenant $tenant): void
    {
        if (!$this->tenants->contains($tenant)) {
            $this->tenants->add($tenant);
        }
    }

    public function removeTenant(Tenant $tenant): void
    {
        if ($this->tenants->contains($tenant)) {
            $this->tenants->removeElement($tenant);
        }
    }

    public function clearTenants(): void
    {
        $this->tenants->clear();
    }

}
