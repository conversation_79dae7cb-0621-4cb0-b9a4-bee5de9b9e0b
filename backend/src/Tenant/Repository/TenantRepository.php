<?php

declare(strict_types = 1);

namespace Pm\Tenant\Repository;

use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Account\Entity\Account;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Input\FilterInputInterface;
use Pm\Core\Repository\BaseRepository;
use Pm\Facility\Entity\Facility;
use Pm\Tenant\Entity\Tenant;
use Pm\Tenant\FilterInput\TenantFilterInput;
use Pm\Tenant\Input\TenantGetInput;
use Pm\Tools\SortingHelper;
use function str_contains;
use function str_replace;

class TenantRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): Tenant
    {
        $tenant = $this->entityManager->createQueryBuilder()
            ->select('t')
            ->from(Tenant::class, 't')
            ->where('t.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($tenant === null) {
            throw new InvalidInputException("Tenant with ID {$id} not found.");
        }

        return $tenant;
    }

    /**
     * @return Paginator<Tenant>
     */
    public function getTenantList(Account $account, TenantGetInput $tenantGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('t')
            ->from(Tenant::class, 't');

        if ($tenantGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('t.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $tenantGetInput->search . '%');
        }

        $this->applyCriteria(
            $account,
            $queryBuilder,
            new TenantFilterInput($tenantGetInput->filter),
            $tenantGetInput->limit,
            $tenantGetInput->offset,
            SortingHelper::sanitizeSortBy($tenantGetInput->sortBy, 't', 'name'),
            SortingHelper::sanitizeSortMethod($tenantGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    /**
     * @return array<Tenant>
     */
    public function getByFacilityAndAccount(Facility $facility, Account $account): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('t')
            ->from(Tenant::class, 't')
            ->innerJoin('t.facility', 'facility')
            ->andWhere('t.facility = :facility')
            ->setParameter('facility', $facility)
            ->andWhere('facility.account = :account')
            ->setParameter('account', $account)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Paginator<Tenant>
     */
    public function getByFacilityPaginated(Facility $facility, TenantGetInput $tenantGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('t')
            ->from(Tenant::class, 't')
            ->innerJoin('t.facility', 'facility')
            ->andWhere('t.facility = :facility')
            ->setParameter('facility', $facility);

        $this->applyCriteria(
            $facility->getAccount(),
            $queryBuilder,
            new TenantFilterInput($tenantGetInput->filter),
            $tenantGetInput->limit,
            $tenantGetInput->offset,
            SortingHelper::sanitizeSortBy($tenantGetInput->sortBy, 't', 'name'),
            SortingHelper::sanitizeSortMethod($tenantGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    protected function applyCriteria(
        ?Account $account,
        QueryBuilder $queryBuilder,
        ?FilterInputInterface $filters,
        ?int $limit = null,
        ?int $offset = null,
        ?string $sortBy = null,
        ?string $sortMethod = null,
    ): QueryBuilder
    {
        if ($sortBy !== null && str_contains($sortBy, 'read_only')) {
            $sortBy = str_replace('read_only', 'originalDbId', $sortBy);
        }

        return parent::applyCriteria($account, $queryBuilder, $filters, $limit, $offset, $sortBy, $sortMethod);
    }

}
