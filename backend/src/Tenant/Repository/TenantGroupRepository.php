<?php

declare(strict_types = 1);

namespace Pm\Tenant\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Account\Entity\Account;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Repository\BaseRepository;
use Pm\Facility\Entity\Facility;
use Pm\Tenant\Entity\TenantGroup;
use Pm\Tenant\FilterInput\TenantGroupFilterInput;
use Pm\Tenant\Input\TenantGroupGetInput;
use Pm\Tools\SortingHelper;

class TenantGroupRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): TenantGroup
    {
        $tenantGroup = $this->entityManager->createQueryBuilder()
            ->select('tg')
            ->from(TenantGroup::class, 'tg')
            ->where('tg.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($tenantGroup === null) {
            throw new InvalidInputException("Tenant group with ID {$id} not found.");
        }

        return $tenantGroup;
    }

    /**
     * @return Paginator<TenantGroup>
     */
    public function getTenantGroupList(Account $account, TenantGroupGetInput $tenantGroupGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('tg')
            ->from(TenantGroup::class, 'tg');

        if ($tenantGroupGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('tg.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $tenantGroupGetInput->search . '%');
        }

        $this->applyCriteria(
            $account,
            $queryBuilder,
            new TenantGroupFilterInput($tenantGroupGetInput->filter),
            $tenantGroupGetInput->limit,
            $tenantGroupGetInput->offset,
            SortingHelper::sanitizeSortBy($tenantGroupGetInput->sortBy, 'tg', 'name'),
            SortingHelper::sanitizeSortMethod($tenantGroupGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

    /**
     * @return array<TenantGroup>
     */
    public function getByFacilityAndAccount(Facility $facility, Account $account): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('tenantGroup')
            ->from(TenantGroup::class, 'tenantGroup')
            ->andWhere('tenantGroup.facility = :facility')
            ->setParameter('facility', $facility)
            ->andWhere('tenantGroup.account = :account')
            ->setParameter('account', $account)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Paginator<TenantGroup>
     */
    public function getByFacilityPaginated(Facility $facility, TenantGroupGetInput $tenantGroupGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('tg')
            ->from(TenantGroup::class, 'tg')
            ->andWhere('tg.facility = :facility')
            ->setParameter('facility', $facility);

        $this->applyCriteria(
            $facility->getAccount(),
            $queryBuilder,
            new TenantGroupFilterInput($tenantGroupGetInput->filter),
            $tenantGroupGetInput->limit,
            $tenantGroupGetInput->offset,
            SortingHelper::sanitizeSortBy($tenantGroupGetInput->sortBy, 'tg', 'name'),
            SortingHelper::sanitizeSortMethod($tenantGroupGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

}
