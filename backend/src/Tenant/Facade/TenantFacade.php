<?php

declare(strict_types = 1);

namespace Pm\Tenant\Facade;

use Doctrine\ORM\EntityManager;
use InvalidArgumentException;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Core\Security\Security;
use Pm\Facility\Exception\NoFacilityFoundException;
use Pm\Facility\Repository\FacilityRepository;
use Pm\Facility\Security\FacilityVoter;
use Pm\Tenant\Entity\Tenant;
use Pm\Tenant\HttpResponse\TenantResponse;
use Pm\Tenant\Input\TenantCreateUpdateInput;
use Pm\Tenant\Input\TenantGetInput;
use Pm\Tenant\Repository\TenantRepository;
use Pm\Tenant\Security\TenantVoter;
use function count;

class TenantFacade
{

    public const PAGE_MAX_LIMIT = 100;

    public function __construct(
        private readonly TenantVoter $tenantVoter,
        private readonly EntityManager $entityManager,
        private readonly TenantRepository $tenantRepository,
        private readonly FacilityRepository $facilityRepository,
        private readonly FacilityVoter $facilityVoter,
        private readonly Security $security,
    )
    {
    }

    /**
     * @return PaginatedCollection<TenantResponse>
     *
     * @throws AclException
     */
    public function getTenantPaginatedListResponse(TenantGetInput $tenantGetInput): PaginatedCollection
    {
        $this->tenantVoter->canRead();

        $currentUser = $this->security->getUser();
        $tenantList = $this->tenantRepository->getTenantList($currentUser->getAccount(), $tenantGetInput);

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($tenantList));
        foreach ($tenantList as $tenant) {
            if (!$tenant instanceof Tenant) {
                throw new InvalidArgumentException('Expected instance of Tenant.');
            }
            $responseList[] = new TenantResponse($tenant);
        }

        return $responseList;
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    public function getTenant(int $id): TenantResponse
    {
        $this->tenantVoter->canRead();

        $tenant = $this->tenantRepository->get($id);

        return new TenantResponse($tenant);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityFoundException
     */
    public function createTenant(TenantCreateUpdateInput $tenantCreate): TenantResponse
    {
        $this->tenantVoter->canWrite();

        $facilityId = $tenantCreate->facilityId;
        if ($facilityId === null) {
            throw new InvalidInputException('Facility ID cannot be null.');
        }

        $facility = $this->facilityRepository->getById($facilityId);
        $account = $facility->getAccount();

        /** @var Tenant $tenant */
        $tenant = $this->entityManager->wrapInTransaction(function () use ($tenantCreate, $facility, $account): Tenant {
            $tenant = new Tenant(
                $tenantCreate->name,
                $tenantCreate->phone,
                $tenantCreate->email,
                $tenantCreate->active ?? true,
                $facility,
            );

            $tenant->setAccount($account);
            $this->entityManager->persist($tenant);

            return $tenant;
        });

        return new TenantResponse($tenant);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityFoundException
     */
    public function updateTenant(int $id, TenantCreateUpdateInput $tenantUpdate): TenantResponse
    {
        $this->tenantVoter->canWrite();

        $tenant = $this->tenantRepository->get($id);

        $facilityId = $tenantUpdate->facilityId;
        if ($facilityId === null) {
            throw new InvalidInputException('Facility ID cannot be null.');
        }

        $facility = $this->facilityRepository->getById($facilityId);

        /** @var Tenant $tenant */
        $tenant = $this->entityManager->wrapInTransaction(function () use ($tenant, $tenantUpdate, $facility): Tenant {
            $tenant->setName($tenantUpdate->name);
            $tenant->setPhone($tenantUpdate->phone);
            $tenant->setEmail($tenantUpdate->email);
            $tenant->setActive($tenantUpdate->active ?? true);
            $tenant->setFacility($facility);

            $this->entityManager->persist($tenant);

            return $tenant;
        });

        return new TenantResponse($tenant);
    }

    /**
     * @throws AclException
     */
    public function deleteTenant(int $id): void
    {
        $this->tenantVoter->canWrite();

        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $tenant = $this->tenantRepository->get($id);

            $this->entityManager->remove($tenant);
        });
    }

    /**
     * @return PaginatedCollection<TenantResponse>
     *
     * @throws NoFacilityFoundException
     * @throws AclException
     */
    public function getTenantPaginatedByFacility(int $facilityId, TenantGetInput $tenantGetInput): PaginatedCollection
    {
        $this->tenantVoter->canRead();

        $facility = $this->facilityRepository->getById($facilityId);
        $this->facilityVoter->canView($facility);

        $tenantList = $this->tenantRepository->getByFacilityPaginated($facility, $tenantGetInput);

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($tenantList));
        foreach ($tenantList as $tenant) {
            if (!$tenant instanceof Tenant) {
                throw new InvalidArgumentException('Expected instance of Tenant.');
            }
            $responseList[] = new TenantResponse($tenant);
        }

        return $responseList;
    }

}
