<?php

declare(strict_types = 1);

namespace Pm\Tenant\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class TenantFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_ID,
        self::FILTER_NAME,
        self::FILTER_PHONE,
        self::FILTER_EMAIL,
        self::FILTER_ACTIVE,
        self::FILTER_FACILITY_ID,
    ];

    final public const FILTER_ID = 'id';
    final public const FILTER_NAME = 'name';
    final public const FILTER_PHONE = 'phone';
    final public const FILTER_EMAIL = 'email';
    final public const FILTER_ACTIVE = 'active';
    final public const FILTER_FACILITY_ID = 'facility';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_ID, self::FILTER_FACILITY_ID => Types::INTEGER,
            self::FILTER_ACTIVE => Types::BOOLEAN,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
