<?php

declare(strict_types = 1);

namespace Pm\EventCategory\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\EventCategory\Entity\EventCategory;

class EventCategoryWithChildrenResponse implements ApiOutput
{

    public readonly int $id;

    public readonly string $code;

    public readonly string $label;

    public readonly string $severity;

    public readonly bool $isHidden;

    public readonly ?int $parentId;

    /**
     * @var EventCategoryWithChildrenResponse[]
     */
    public array $children = []; // @phpstan-ignore-line shipmonk.publicPropertyNotReadonly

    public function __construct(EventCategory $eventCategory, public readonly bool $searchMatch = false)
    {
        $this->id = $eventCategory->getId();
        $this->code = $eventCategory->getCode();
        $this->label = $eventCategory->getLabel();
        $this->severity = $eventCategory->getSeverity()->name;
        $this->isHidden = $eventCategory->isHidden();
        $this->parentId = $eventCategory->getParentCategory()?->getId();
    }

    public function addChild(self $eventCategory): void
    {
        $this->children[] = $eventCategory;
    }

}
