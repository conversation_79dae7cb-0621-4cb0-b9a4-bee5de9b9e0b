<?php

declare(strict_types = 1);

namespace Pm\EventCategory\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\EventCategory\Entity\EventCategory;

readonly class EventCategoryResponse implements ApiOutput
{

    public int $id;

    public string $code;

    public string $label;

    public string $severity;

    public bool $isHidden;

    public ?int $parentId;

    public function __construct(EventCategory $eventCategory)
    {
        $this->id = $eventCategory->getId();
        $this->code = $eventCategory->getCode();
        $this->label = $eventCategory->getLabel();
        $this->severity = $eventCategory->getSeverity()->name;
        $this->isHidden = $eventCategory->isHidden();
        $this->parentId = $eventCategory->getParentCategory()?->getId();
    }

}
