<?php

declare(strict_types = 1);

namespace Pm\EventCategory\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Core\Input\TranslationsInput;
use Pm\EventCategory\Entity\EventCategory;

readonly class EventCategoryEditResponse implements ApiOutput
{

    public int $id;

    public string $code;

    public string $label;

    public string $severity;

    public bool $isHidden;

    public ?int $parentId;

    /**
     * @var array<int, TranslationsInput>
     */
    public array $translations;

    public function __construct(EventCategory $eventCategory)
    {
        $this->id = $eventCategory->getId();
        $this->code = $eventCategory->getCode();
        $this->label = $eventCategory->getLabel();
        $this->severity = $eventCategory->getSeverity()->name;
        $this->isHidden = $eventCategory->isHidden();
        $this->parentId = $eventCategory->getParentCategory()?->getId();
        $this->translations = $eventCategory->getTranslations();
    }

}
