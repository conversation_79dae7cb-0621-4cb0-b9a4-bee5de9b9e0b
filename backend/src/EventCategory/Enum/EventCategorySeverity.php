<?php

declare(strict_types = 1);

namespace Pm\EventCategory\Enum;

use Pm\Core\Doctrine\StringEnumType;

class EventCategorySeverity extends StringEnumType
{

    final public const NAME = 'event_category_severity';

    public function getEnumClass(): string
    {
        return EventCategorySeverityEnum::class;
    }

    public function getName(): string
    {
        return self::NAME;
    }

}
