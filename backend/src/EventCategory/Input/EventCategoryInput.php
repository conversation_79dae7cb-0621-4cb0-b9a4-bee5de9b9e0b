<?php

declare(strict_types = 1);

namespace Pm\EventCategory\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Input\FilterInput;
use Pm\EventCategory\Facade\EventCategoryFacade;

readonly class EventCategoryInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public ?int $accountId = null,
        public ?string $search = null,
        public ?int $limit = EventCategoryFacade::PAGE_MAX_LIMIT,
        public ?int $offset = 0,
        public string $language = 'en',
        public ?array $filter = null,
    )
    {
    }

}
