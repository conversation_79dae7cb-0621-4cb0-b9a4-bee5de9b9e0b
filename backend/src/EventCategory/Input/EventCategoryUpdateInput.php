<?php

declare(strict_types = 1);

namespace Pm\EventCategory\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Input\TranslationsInput;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;

readonly class EventCategoryUpdateInput implements Input
{

    /**
     * @param array<int, TranslationsInput>|null $translations
     */
    public function __construct(
        public string $label,
        public string $code,
        public bool $isHidden,
        public EventCategorySeverityEnum $severity,
        public ?int $parentCategory,
        public ?array $translations,
    )
    {
    }

}
