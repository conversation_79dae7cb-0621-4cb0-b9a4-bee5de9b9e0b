<?php

declare(strict_types = 1);

namespace Pm\EventCategory\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;
use Gedmo\Mapping\Annotation\Locale;
use Gedmo\Mapping\Annotation\Translatable;
use Pm\Account\Entity\Account;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\BlameableInsertEntity;
use Pm\Core\Entity\Traits\BlameableUpdateEntity;
use Pm\Core\Entity\Traits\OriginalDbEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\Core\Entity\TranslatableEntityInterface;
use Pm\Core\Exception\LogicException;
use Pm\Core\Input\TranslationsInput;
use Pm\EventCategory\Enum\EventCategorySeverity;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;

#[Entity]
#[Table(name: 'event_category')]
class EventCategory extends BaseEntity implements TranslatableEntityInterface
{

    use TimestampableEntity;
    use BlameableInsertEntity;
    use BlameableUpdateEntity;
    use OriginalDbEntity;

    #[Column(type: Types::STRING)]
    #[Translatable()]
    private string $label;

    #[Locale]
    private string $locale = 'en';

    #[Column(type: Types::STRING)]
    private string $code;

    #[Column(type: Types::BOOLEAN, options: ['default' => false])]
    private bool $isHidden;

    #[Column(type: Types::BOOLEAN, options: ['default' => true])]
    private bool $isActive = true;

    #[Column(type: EventCategorySeverity::NAME, nullable: false, options: ['default' => EventCategorySeverityEnum::LOW])]
    private EventCategorySeverityEnum $severity;

    #[ManyToOne(targetEntity: self::class, inversedBy: 'children')]
    private ?self $parentCategory;

    /**
     * @var Collection<int, self>
     */
    #[OneToMany(mappedBy: 'parentCategory', targetEntity: self::class)]
    private Collection $children;

    /**
     * @var array<int, TranslationsInput>
     */
    private array $translations = [];

    public function __construct(
        string $label,
        string $code,
        EventCategorySeverityEnum $severity,
        bool $isHidden,
        ?self $parentCategory,
        Account $account,
        ?string $originalDbId = null,
        bool $migratedCompletely = false,
    )
    {
        $this->label = $label;
        $this->code = $code;
        $this->severity = $severity;
        $this->isHidden = $isHidden;
        $this->parentCategory = $parentCategory;
        $this->account = $account;
        $this->originalDbId = $originalDbId;
        $this->migratedCompletely = $migratedCompletely;

        $this->children = new ArrayCollection();
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function isHidden(): bool
    {
        return $this->isHidden;
    }

    public function getSeverity(): EventCategorySeverityEnum
    {
        return $this->severity;
    }

    public function getParentCategory(): ?self
    {
        return $this->parentCategory;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function setParentCategory(?self $parentCategory): void
    {
        if ($parentCategory instanceof self && $parentCategory->getId() === $this->getId()) {
            throw new LogicException('Parent category cannot be the same as the category itself.');
        }
        $this->parentCategory = $parentCategory;
    }

    public function setLabel(string $label): void
    {
        $this->label = $label;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function setIsActive(bool $isActive): void
    {
        $this->isActive = $isActive;
    }

    public function disableRecursive(): void
    {
        $this->isActive = false;
        foreach ($this->getChildren() as $child) {
            $child->disableRecursive();
        }
    }

    public function setIsHidden(bool $isHidden): void
    {
        $this->isHidden = $isHidden;
    }

    public function setSeverity(EventCategorySeverityEnum $severity): void
    {
        $this->severity = $severity;
    }

    public function getOriginalDbId(): ?string
    {
        return $this->originalDbId;
    }

    public function isMigratedCompletely(): bool
    {
        return $this->migratedCompletely;
    }

    public function setTranslatableLocale(string $locale): void
    {
        $this->locale = $locale;
    }

    /**
     * @return array<int, TranslationsInput>
     */
    public function getTranslations(): array
    {
        return $this->translations;
    }

    /**
     * @param array<int, TranslationsInput> $translations
     */
    public function setTranslations(array $translations): void
    {
        $this->translations = $translations;
    }

    /**
     * @return Collection<int, self>
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    /**
     * @param Collection<int, self> $children
     */
    public function setChildren(Collection $children): void
    {
        $this->children = $children;
    }

}
