<?php

declare(strict_types = 1);

namespace Pm\EventCategory\Facade;

use Doctrine\ORM\EntityManager;
use Pm\Account\Exception\NoAccountFoundException;
use Pm\Account\Repository\AccountRepository;
use Pm\Core\Exception\AclException;
use Pm\Core\Result\PaginatedCollection;
use Pm\Core\Security\Security;
use Pm\EventCategory\Entity\EventCategory;
use Pm\EventCategory\Exception\NoEventCategoryFoundException;
use Pm\EventCategory\HttpResponse\EventCategoryEditWithChildrenResponse;
use Pm\EventCategory\HttpResponse\EventCategoryResponse;
use Pm\EventCategory\HttpResponse\EventCategoryWithChildrenResponse;
use Pm\EventCategory\Input\EventCategoryCreateInput;
use Pm\EventCategory\Input\EventCategoryInput;
use Pm\EventCategory\Input\EventCategoryUpdateInput;
use Pm\EventCategory\Repository\EventCategoryRepository;
use Pm\EventCategory\Security\EventCategoryVoter;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use function array_reverse;
use function count;
use function is_array;
use function is_int;
use function key;
use function krsort;
use function mb_stripos;

class EventCategoryFacade
{

    public const PAGE_MAX_LIMIT = 20;
    private const FULL_TREE_LIMIT = 5000;

    public function __construct(
        private readonly EntityManager $entityManager,
        private readonly EventCategoryRepository $eventCategoryRepository,
        private readonly EventCategoryVoter $eventCategoryVoter,
        private readonly Security $security,
        private readonly AccountRepository $accountRepository,
        private readonly ParameterBagInterface $parameterBag,
    )
    {
    }

    /**
     * @return PaginatedCollection<EventCategoryResponse>
     *
     * @throws AclException|NoAccountFoundException
     */
    public function getEventsCategoryPaginatedListResponse(EventCategoryInput $eventCategoryInput): PaginatedCollection
    {
        if ($eventCategoryInput->accountId !== null) {
            $this->eventCategoryVoter->canAccessList($eventCategoryInput->accountId);
        }
        $requestedAccountId = $this->eventCategoryVoter->filterAccountIdForNonAdmins($eventCategoryInput->accountId);
        $account = $this->accountRepository->getById($requestedAccountId);

        $paginatorList = $this->eventCategoryRepository->getEventsCategoryList($eventCategoryInput, $account);

        foreach ($paginatorList as $item) {
            $this->eventCategoryRepository->loadTranslations($item);
        }

        $responseList = new PaginatedCollection();
        $responseList->setTotalCount(count($paginatorList));
        foreach ($paginatorList as $item) {
            $responseList[] = new EventCategoryResponse($item);
        }

        return $responseList;
    }

    /**
     * @return list<EventCategoryWithChildrenResponse>
     *
     * @throws AclException|NoEventCategoryFoundException
     */
    public function getEventsCategoryTree(?string $search = null): array
    {
        $user = $this->security->getUser();

        $eventCategoryInput = new EventCategoryInput($user->getAccount()->getId(), $search ?? '', self::FULL_TREE_LIMIT);

        $eventsCategoryList = $this->eventCategoryRepository->getEventsCategoryListSearch($eventCategoryInput, $user->getAccount());

        $uniquePaths = [];
        foreach ($eventsCategoryList as $eventCategory) {
            $uniquePaths = $this->mergePaths($uniquePaths, $this->getBranchPath($eventCategory));
        }

        // tree structure always has one root
        if (count($uniquePaths) === 0) {
            return [];
        }
        $firstId = key($uniquePaths);

        $eventCategory = $this->eventCategoryRepository->getCached($firstId);

        $eventCategoryResponse = new EventCategoryWithChildrenResponse($eventCategory, $this->matchSearch($eventCategory, $search ?? ''));

        if (count($uniquePaths[$firstId]) > 0) {
            $this->buildTree($uniquePaths[$firstId], $eventCategoryResponse, $search ?? '');
        }

        return [$eventCategoryResponse];
    }

    /**
     * @return list<EventCategoryEditWithChildrenResponse>
     *
     * @throws AclException|NoEventCategoryFoundException
     */
    public function getEventsCategoryEditTree(?string $search = null): array
    {
        $user = $this->security->getUser();

        $this->eventCategoryRepository->setLocale($this->parameterBag->get('kernel.default_locale'));

        if ($search === null) {
            $user = $this->security->getUser();
            $accountId = $user->getAccount()->getId();
            $eventsCategoryList = $this->eventCategoryRepository->getAllEventsCategoryByAccount($accountId);
        } else {
            $eventCategoryInput = new EventCategoryInput($user->getAccount()->getId(), $search, self::FULL_TREE_LIMIT);
            $eventsCategoryList = $this->eventCategoryRepository->getEventsCategoryListSearch($eventCategoryInput, $user->getAccount());
        }

        foreach ($eventsCategoryList as $eventCategory) {
            $this->eventCategoryRepository->loadTranslations($eventCategory);
        }

        $uniquePaths = [];
        foreach ($eventsCategoryList as $eventCategory) {
            $uniquePaths = $this->mergePaths($uniquePaths, $this->getBranchPath($eventCategory));
        }

        // tree structure always has one root
        if (count($uniquePaths) === 0) {
            return [];
        }
        $firstId = key($uniquePaths);

        $eventCategory = $this->eventCategoryRepository->getCached($firstId, true);

        $eventCategoryResponse = new EventCategoryEditWithChildrenResponse($eventCategory, $this->matchSearch($eventCategory, $search ?? ''));

        if (count($uniquePaths[$firstId]) > 0) {
            $this->buildEditTree($uniquePaths[$firstId], $eventCategoryResponse, $search ?? '');
        }

        return [$eventCategoryResponse];
    }

    /**
     * @return list<EventCategoryWithChildrenResponse>
     *
     * @throws AclException
     */
    public function getEventsCategoryTreeByParentCategory(?int $parentCategoryId = null): array
    {
        $user = $this->security->getUser();
        $accountId = $user->getAccount()->getId();

        $rootCategories = $this->eventCategoryRepository->getEventsCategoryByParentEventCategory($accountId, $parentCategoryId);

        $categories = [];
        foreach ($rootCategories as $rootCategory) {
            $categoryWithChildren = new EventCategoryWithChildrenResponse($rootCategory);
            $children = $this->getEventsCategoryTreeByParentCategory($rootCategory->getId());
            foreach ($children as $child) {
                $categoryWithChildren->addChild($child);
            }
            $categories[] = $categoryWithChildren;
        }

        return $categories;
    }

    /**
     * @return array<int, mixed>
     */
    private function getBranchPath(EventCategory $eventCategory): array
    {
        $branchIdList = [$eventCategory->getId()];
        $parentEventCategory = $eventCategory->getParentCategory();
        if ($parentEventCategory === null) {
            return [$eventCategory->getId() => []];
        }
        do {
            $branchIdList[] = $parentEventCategory->getId();
            $parentEventCategory = $parentEventCategory->getParentCategory();

        } while ($parentEventCategory !== null);
        krsort($branchIdList);
        $branchPath = [];
        foreach (array_reverse($branchIdList) as $value) {
            $branchPath = [$value => $branchPath];
        }

        return $branchPath;
    }

    /**
     * @param array<int, mixed> $path1
     * @param array<int, mixed> $path2
     * @return array<int, mixed>
     */
    private function mergePaths(array $path1, array $path2): array
    {
        $merged = $path1;

        foreach ($path2 as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = $this->mergePaths($merged[$key], $value);
            } else {
                if (isset($merged[$key])) {
                    if (!is_array($merged[$key])) {
                        $merged[$key] = [$merged[$key]];
                    }
                    $merged[$key][] = $value;
                } else {
                    $merged[$key] = $value;
                }
            }
        }

        return $merged;
    }

    /**
     * @param array<int, mixed> $uniquePath
     *
     * @throws NoEventCategoryFoundException
     */
    private function buildTree(array $uniquePath, EventCategoryWithChildrenResponse $eventCategoryResponse, string $search = ''): void
    {
        foreach ($uniquePath as $id => $children) {
            $eventCategory = $this->eventCategoryRepository->getCached($id);
            $childResponse = new EventCategoryWithChildrenResponse($eventCategory, $this->matchSearch($eventCategory, $search));
            $eventCategoryResponse->addChild($childResponse);
            if (count($children) > 0) {
                $this->buildTree($children, $childResponse, $search);
            }
        }
    }

    /**
     * @param array<int, mixed> $uniquePath
     *
     * @throws NoEventCategoryFoundException
     */
    private function buildEditTree(array $uniquePath, EventCategoryEditWithChildrenResponse $eventCategoryResponse, string $search = ''): void
    {
        foreach ($uniquePath as $id => $children) {
            $eventCategory = $this->eventCategoryRepository->getCached($id, true);
            $childResponse = new EventCategoryEditWithChildrenResponse($eventCategory, $this->matchSearch($eventCategory, $search));
            $eventCategoryResponse->addChild($childResponse);
            if (count($children) > 0) {
                $this->buildEditTree($children, $childResponse, $search);
            }
        }
    }

    /**
     * @throws AclException
     */
    public function create(EventCategoryCreateInput $eventCategoryCreateInput): EventCategoryEditWithChildrenResponse
    {
        $this->eventCategoryRepository->setLocale($this->parameterBag->get('kernel.default_locale'));

        /** @var EventCategory $eventCategory */
        $eventCategory = $this->entityManager->wrapInTransaction(function () use ($eventCategoryCreateInput): EventCategory {
            $this->eventCategoryVoter->canCreate();
            $eventCategory = new EventCategory(
                $eventCategoryCreateInput->label,
                $eventCategoryCreateInput->code,
                $eventCategoryCreateInput->severity,
                $eventCategoryCreateInput->isHidden,
                is_int($eventCategoryCreateInput->parentCategory) ? $this->eventCategoryRepository->get($eventCategoryCreateInput->parentCategory) : null,
                $this->security->getUser()->getAccount(),
            );

            if (is_array($eventCategoryCreateInput->translations)) {
                $this->eventCategoryRepository->setTranslations($eventCategory, $eventCategoryCreateInput->translations);
            }

            $this->entityManager->persist($eventCategory);

            return $eventCategory;
        });

        return $this->branchEditResponse($eventCategory);
    }

    /**
     * @throws NoEventCategoryFoundException
     * @throws AclException
     */
    public function update(
        int $id,
        EventCategoryUpdateInput $eventCategoryUpdateInput,
    ): EventCategoryEditWithChildrenResponse
    {
        $this->eventCategoryRepository->setLocale($this->parameterBag->get('kernel.default_locale'));

        /** @var EventCategory $eventCategory */
        $eventCategory = $this->entityManager->wrapInTransaction(function () use ($id, $eventCategoryUpdateInput): EventCategory {
            $eventCategory = $this->eventCategoryRepository->get($id);

            $this->eventCategoryVoter->canUpdate($eventCategory);

            $eventCategory->setLabel($eventCategoryUpdateInput->label);
            $eventCategory->setCode($eventCategoryUpdateInput->code);
            $eventCategory->setSeverity($eventCategoryUpdateInput->severity);
            $eventCategory->setIsHidden($eventCategoryUpdateInput->isHidden);

            if ($eventCategoryUpdateInput->parentCategory !== $eventCategory->getParentCategory()?->getId()) {
                $eventCategory->setParentCategory($eventCategoryUpdateInput->parentCategory !== null ? $this->eventCategoryRepository->get($eventCategoryUpdateInput->parentCategory) : null);
            }

            if (is_array($eventCategoryUpdateInput->translations)) {
                $this->eventCategoryRepository->setTranslations($eventCategory, $eventCategoryUpdateInput->translations);
            }

            return $eventCategory;
        });

        return $this->branchEditResponse($eventCategory);
    }

    protected function branchEditResponse(EventCategory $eventCategory): EventCategoryEditWithChildrenResponse
    {
        $this->eventCategoryRepository->loadTranslations($eventCategory);

        $response = new EventCategoryEditWithChildrenResponse($eventCategory);

        $children = $eventCategory->getChildren();
        foreach ($children as $child) {
            $response->addChild($this->branchEditResponse($child));
        }

        return $response;
    }

    /**
     * @throws NoEventCategoryFoundException
     * @throws AclException
     */
    public function delete(int $id): void
    {
        $this->entityManager->wrapInTransaction(function () use ($id): void {
            $eventCategory = $this->eventCategoryRepository->get($id);

            $this->eventCategoryVoter->canDelete($eventCategory);

            $eventCategory->disableRecursive();
        });
    }

    private function matchSearch(EventCategory $eventCategory, string $search): bool
    {
        return mb_stripos($eventCategory->getLabel(), $search) !== false || mb_stripos($eventCategory->getCode(), $search) !== false;
    }

}
