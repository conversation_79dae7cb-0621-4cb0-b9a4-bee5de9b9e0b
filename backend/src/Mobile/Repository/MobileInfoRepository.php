<?php

declare(strict_types = 1);

namespace Pm\Mobile\Repository;

use Doctrine\ORM\EntityManager;
use Pm\Mobile\Entity\MobileInfo;
use Pm\User\Entity\User;

class MobileInfoRepository
{

    public function __construct(
        private readonly EntityManager $entityManager,
    )
    {
    }

    public function findByUser(User $user): ?MobileInfo
    {
        return $this->entityManager->createQueryBuilder()
            ->select('mi')
            ->from(MobileInfo::class, 'mi')
            ->andWhere('mi.user = :user')
            ->setParameter('user', $user)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
