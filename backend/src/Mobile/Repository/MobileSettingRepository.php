<?php

declare(strict_types = 1);

namespace Pm\Mobile\Repository;

use Doctrine\ORM\EntityManager;
use Pm\Mobile\Entity\MobileSetting;
use Pm\User\Entity\User;

class MobileSettingRepository
{

    public function __construct(
        private readonly EntityManager $entityManager,
    )
    {
    }

    public function findByUser(User $user): ?MobileSetting
    {
        return $this->entityManager->createQueryBuilder()
            ->select('ms')
            ->from(MobileSetting::class, 'ms')
            ->andWhere('ms.user = :user')
            ->setParameter('user', $user)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
