<?php

declare(strict_types = 1);

namespace Pm\SuperPops\Entity;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\JoinTable;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use Pm\Account\Entity\Account;
use Pm\Facility\Entity\Facility;
use Pm\Pops\Entity\PopsEntity;

#[Table(name: 'super_pops')]
#[Entity]
class SuperPops extends PopsEntity
{

    #[ManyToOne(targetEntity: Account::class, inversedBy: 'superPops')]
    #[JoinColumn(nullable: false)]
    protected Account $account;

    /**
     * @var Collection<int, Facility>
     */
    #[JoinTable(name: 'super_pops_facility')]
    #[ManyToMany(targetEntity: Facility::class, inversedBy: 'superPops')]
    private Collection $facilities;

    public function __construct(Account $account, DateTimeImmutable $startsAt, DateTimeImmutable $endsAt)
    {
        $this->account = $account;
        $this->startsAt = $startsAt;
        $this->endsAt = $endsAt;

        $this->facilities = new ArrayCollection();
    }

    public function addFacility(Facility $facility): void
    {
        if (!$this->facilities->contains($facility)) {
            $this->facilities->add($facility);
        }
    }

    /**
     * @param array<Facility> $facilities
     */
    public function addFacilities(array $facilities): void
    {
        foreach ($facilities as $facility) {
            if (!$this->facilities->contains($facility)) {
                $this->facilities->add($facility);
            }
        }
    }

    public function removeFacilities(): void
    {
        $facilities = $this->getFacilities();
        foreach ($facilities as $facility) {
            $this->facilities->removeElement($facility);
        }
    }

    /**
     * @return Collection<int, Facility>
     */
    public function getFacilities(): Collection
    {
        return $this->facilities;
    }

    public function getFacility(): null
    {
        return null;
    }

}
