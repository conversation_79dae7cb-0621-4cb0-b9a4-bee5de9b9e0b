<?php

declare(strict_types = 1);

namespace Pm\SuperPops\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Security\Security;
use Pm\Pops\Enum\TemplatePdfEnum;
use Pm\Pops\Export\PdfFactory;
use Pm\Pops\Export\SpreadsheetFactory;
use Pm\Pops\Facade\PopsEventFacade;
use Pm\Pops\Filters\PopsEventListFilters;
use Pm\Pops\HttpResponse\PopsEventHttpResponse;
use Pm\Pops\HttpResponse\SuperPopsHttpResponse;
use Pm\SuperPops\Facade\SuperPopsFacade;
use Pm\SuperPops\Input\SuperPopsCreateUpdateInput;
use Pm\SuperPops\Input\SuperPopsEventGetInput;
use Pm\SuperPops\Input\SuperPopsGetInput;
use Pm\SuperPops\Repository\SuperPopsRepository;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Throwable;

class SuperPopsController extends BaseController
{

    public function __construct(
        private readonly SuperPopsFacade $superPopsFacade,
        private readonly PopsEventFacade $popsEventFacade,
        private readonly SpreadsheetFactory $spreadsheetFactory,
        private readonly PdfFactory $pdfFactory,
    )
    {
    }

    /**
     * @throws Throwable
     */
    #[Get(path: '/api/v1/super-pops')]
    public function getListAction(
        #[MapQueryString]
        ?SuperPopsGetInput $superPopsGetInput,
    ): SuccessCountableOutput
    {
        if ($superPopsGetInput === null) {
            $superPopsGetInput = new SuperPopsGetInput();
        }
        $superPopsList = $this->superPopsFacade->getSuperPopsPaginatedListResponse($superPopsGetInput);

        /**
         * @var list<SuperPopsHttpResponse> $list
         */
        $list = $superPopsList->getArrayCopy();
        return new SuccessCountableOutput($list, $superPopsList->getTotalCount());
    }

    /**
     * @throws Throwable
     */
    #[Get(path: '/api/v1/super-pops/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $superPops = $this->superPopsFacade->getSuperPops($id);

        return new SuccessOutput($superPops);
    }

    /**
     * @throws Throwable
     */
    #[Post(path: '/api/v1/super-pops')]
    public function createSuperPopsAction(
        #[MapRequestPayload]
        SuperPopsCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->superPopsFacade->createSuperPops($request),
            Response::HTTP_CREATED,
            'super-pops-created',
        );
    }

    /**
     * @throws Throwable
     */
    #[Put(path: '/api/v1/super-pops/{id}')]
    public function updateSuperPopsAction(
        int $id,
        #[MapRequestPayload]
        SuperPopsCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->superPopsFacade->updateSuperPops($id, $request),
            Response::HTTP_OK,
            'super-pops-updated',
        );
    }

    /**
     * @throws Throwable
     */
    #[Put(path: '/api/v1/super-pops/{id}/lock')]
    public function lockSuperPopsAction(
        int $id,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->superPopsFacade->lockSuperPops($id),
            Response::HTTP_OK,
            'super-pops-locked',
        );
    }

    /**
     * @throws Throwable
     */
    #[Get(path: '/api/v1/super-pops/{id}/pops-event', requirements: ['id' => '\d+'])]
    public function getSuperPopsEventListAction(
        int $id,
        #[MapQueryString]
        ?SuperPopsEventGetInput $superPopsEventGetInput,
    ): SuccessCountableOutput
    {
        if ($superPopsEventGetInput === null) {
            $superPopsEventGetInput = new SuperPopsEventGetInput();
        }
        $superPopsEventGetInput->setId($id);

        $superPopsEvents = $this->popsEventFacade->getSuperPopsEventPaginatedListResponse($superPopsEventGetInput);

        /**
         * @var list<PopsEventHttpResponse> $superPopsEventArray
         */
        $superPopsEventArray = $superPopsEvents->getArrayCopy();

        return new SuccessCountableOutput($superPopsEventArray, $superPopsEvents->getTotalCount());
    }

    /**
     * @throws Throwable
     */
    #[Get(path: '/api/v1/super-pops/{id}/pops-event-xls', requirements: ['id' => '\d+'])]
    public function getListXlsAction(
        int $id,
        #[MapQueryString]
        ?SuperPopsEventGetInput $superPopsEventGetInput,
        Security $security,
        SuperPopsRepository $superPopsRepository,
    ): Response
    {
        if ($superPopsEventGetInput === null) {
            $superPopsEventGetInput = new SuperPopsEventGetInput();
        }
        $superPopsEventGetInput->setId($id);
        $popsEventListFilters = new PopsEventListFilters();
        $popsEventListFilters->setEventCategoryHidden(false);

        $popsEvents = $this->popsEventFacade->getSuperPopsEventListHttpResponse($this->popsEventFacade->cleanSuperPopsEventGetInputForExports($superPopsEventGetInput), $popsEventListFilters);

        $superPops = $superPopsRepository->get($id);

        return $this->createSpreadsheetResponse(
            'super-pops-events-' . $id . '.xlsx',
            $this->spreadsheetFactory->create(
                $popsEvents,
                $superPops->getStartsAt(),
                $superPops->getEndsAt(),
                $superPops->getInsertedBy(),
                null,
                true,
            ),
        );
    }

    /**
     * @throws Throwable
     */
    #[Get(path: '/api/v1/super-pops/{id}/pops-event-pdf', requirements: ['id' => '\d+'])]
    public function getListPdfAction(
        int $id,
        #[MapQueryString]
        ?SuperPopsEventGetInput $superPopsEventGetInput,
        Security $security,
        SuperPopsRepository $superPopsRepository,
    ): Response
    {
        if ($superPopsEventGetInput === null) {
            $superPopsEventGetInput = new SuperPopsEventGetInput();
        }
        $superPopsEventGetInput->setId($id);

        $superPops = $superPopsRepository->get($id);

        $popsEventListFilters = new PopsEventListFilters();
        $popsEventListFilters->setEventCategoryHidden(false);

        $popsEvents = $this->popsEventFacade->getSuperPopsEventListHttpResponse($this->popsEventFacade->cleanSuperPopsEventGetInputForExports($superPopsEventGetInput), $popsEventListFilters);

        // PDF mime type response
        return $this->createPdfResponse(
            'super-pops-events-' . $id . '.pdf',
            $this->pdfFactory->create(
                TemplatePdfEnum::SUPERPOPSEVENTS,
                $popsEvents,
                $superPops->getStartsAt(),
                $superPops->getEndsAt(),
                $superPops->getInsertedBy(),
                null,
                true,
            ),
        );
    }

}
