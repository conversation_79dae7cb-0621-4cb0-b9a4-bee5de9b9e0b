<?php

declare(strict_types = 1);

namespace Pm\SuperPops\Input;

use DateTimeImmutable;
use OpenApi\InputMapper\Input;
use function get_object_vars;

class SuperPopsCreateUpdateInput implements Input
{

    /**
     * @param array<int, mixed> $facilitiesIds
     */
    public function __construct(

        private readonly int $accountId,

        private readonly array $facilitiesIds,

        private readonly DateTimeImmutable $startsAt,

        private readonly DateTimeImmutable $endsAt,

        private readonly bool $isLocked = false,
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function getAccountId(): int
    {
        return $this->accountId;
    }

    /**
     * @return array<int, mixed>
     */
    public function getFacilitiesIds(): array
    {
        return $this->facilitiesIds;
    }

    public function getStartsAt(): DateTimeImmutable
    {
        return $this->startsAt;
    }

    public function getEndsAt(): DateTimeImmutable
    {
        return $this->endsAt;
    }

    public function isLocked(): bool
    {
        return $this->isLocked;
    }

}
