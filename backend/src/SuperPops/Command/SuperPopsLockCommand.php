<?php

declare(strict_types = 1);

namespace Pm\SuperPops\Command;

use Pm\Core\Command\BaseCommand;
use Pm\Log\Logger;
use Pm\SuperPops\Facade\SuperPopsFacade;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SuperPopsLockCommand extends BaseCommand
{

    use LockableTrait;

    public function __construct(
        private readonly Logger $logger,
        private readonly SuperPopsFacade $superPopsFacade,
    )
    {
        parent::__construct();
    }

    public function execute(
        InputInterface $input,
        OutputInterface $output,
    ): int
    {
        if (!$this->lock()) {
            $this->logger->logInfoMessage('SuperPopsLockCommand: The command is already running in another process.');
            return Command::FAILURE;
        }

        $this->logger->logInfoMessage('SuperPopsLockCommand: Started.');

        $this->superPopsFacade->lockElapsedSuperPops();

        $this->release();

        $this->logger->logInfoMessage('SuperPopsLockCommand: Finished.');

        return Command::SUCCESS;
    }

    public static function getDefaultName(): string
    {
        return 'super-pops:lock';
    }

}
