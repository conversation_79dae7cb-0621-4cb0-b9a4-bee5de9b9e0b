<?php

declare(strict_types = 1);

namespace Pm\Customer\FilterInput;

use Doctrine\DBAL\Types\Types;
use Pm\Core\FilterInput\BaseFilterInput;
use Pm\Core\Input\FilterInput;
use Pm\Core\Input\FilterInputInterface;

class CustomerUserFilterInput extends BaseFilterInput implements FilterInputInterface
{

    protected const ALLOWED_FIELDS = [
        self::FILTER_CUSTOMER_ID,
        self::FILTER_USER_ID,
        self::FILTER_CUSTOMER_NAME,
        self::FILTER_USER_IDENTIFIER,
        self::FILTER_USER_FIRST_NAME,
        self::FILTER_USER_LAST_NAME,
    ];

    final public const FILTER_CUSTOMER_ID = 'c.id';
    final public const FILTER_USER_ID = 'u.id';
    final public const FILTER_CUSTOMER_NAME = 'c.name';
    final public const FILTER_USER_IDENTIFIER = 'u.identifier';
    final public const FILTER_USER_FIRST_NAME = 'u.firstName';
    final public const FILTER_USER_LAST_NAME = 'u.lastName';

    public function getFilterVarType(FilterInput $filterInput): string
    {
        return match ($filterInput->getField()) {
            self::FILTER_CUSTOMER_ID, self::FILTER_USER_ID => Types::INTEGER,
            self::FILTER_CUSTOMER_NAME, self::FILTER_USER_IDENTIFIER, self::FILTER_USER_FIRST_NAME, self::FILTER_USER_LAST_NAME => Types::STRING,
            default => Types::STRING,
        };
    }

    /**
     * @return list<string>
     */
    public function getAllowedFields(): array
    {
        return self::ALLOWED_FIELDS;
    }

}
