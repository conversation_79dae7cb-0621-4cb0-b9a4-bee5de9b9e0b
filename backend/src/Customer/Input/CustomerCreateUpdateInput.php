<?php

declare(strict_types = 1);

namespace Pm\Customer\Input;

use OpenApi\InputMapper\Input;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class CustomerCreateUpdateInput implements Input
{

    public function __construct(
        #[NotBlank(message: 'validation.field.required')]
        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly string $name,

        #[NotBlank(message: 'validation.field.required')]
        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly string $cid,

        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $country = null,

        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $city = null,

        #[Length(max: 255, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $street = null,

        #[Length(max: 20, maxMessage: 'validation.field.max_length')]
        #[Type('string')]
        public readonly ?string $postalCode = null,

        #[Type('string')]
        public readonly ?string $coordinates = null,

        /**
         * @var list<int>|null
         */
        #[Type('array')]
        public readonly ?array $facilities = null,

    )
    {
    }

}
