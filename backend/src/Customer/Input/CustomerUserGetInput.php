<?php

declare(strict_types = 1);

namespace Pm\Customer\Input;

use OpenApi\InputMapper\Input;
use Pm\Core\Enum\Sort;
use Pm\Core\Input\FilterInput;
use Pm\Customer\Validator\CustomerUserSortFields;

class CustomerUserGetInput implements Input
{

    /**
     * @param list<FilterInput>|null $filter
     */
    public function __construct(
        public readonly ?string $search = null,
        public readonly ?int $limit = 10,
        public readonly ?int $offset = 0,
        public readonly ?CustomerUserSortFields $sortBy = null,
        public readonly ?Sort $sortMethod = null,
        public readonly ?array $filter = null,
    )
    {
    }

}
