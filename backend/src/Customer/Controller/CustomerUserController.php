<?php

declare(strict_types = 1);

namespace Pm\Customer\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Customer\Facade\CustomerUserFacade;
use Pm\Customer\HttpResponse\CustomerUserSimpleResponse;
use Pm\Customer\Input\CustomerUserCreateInput;
use Pm\Customer\Input\CustomerUserGetInput;
use Pm\Customer\Input\CustomerUserUpdateInput;
use Pm\User\Exception\NoUserFoundException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'CustomerUser')]
class CustomerUserController extends BaseController
{

    public function __construct(
        private readonly CustomerUserFacade $customerUserFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(CustomerUserSimpleResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/customer-user')]
    public function getListAction(
        #[MapQueryString]
        ?CustomerUserGetInput $customerUserGetInput,
    ): SuccessCountableOutput
    {
        $customerUserList = $this->customerUserFacade->getCustomerUserPaginatedListResponse(
            $customerUserGetInput ?? new CustomerUserGetInput(),
        );

        /**
         * @var list<CustomerUserSimpleResponse> $list
         */
        $list = $customerUserList->getArrayCopy();
        return new SuccessCountableOutput($list, $customerUserList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CustomerUserSimpleResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/customer-user/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $customerUser = $this->customerUserFacade->getCustomerUser($id);

        return new SuccessOutput($customerUser);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoUserFoundException
     */
    #[DetailResponse(CustomerUserSimpleResponse::class, 'customer-user-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Post(path: '/api/v1/customer-user')]
    public function createCustomerUserAction(
        #[MapRequestPayload]
        CustomerUserCreateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->customerUserFacade->createCustomerUser($request),
            Response::HTTP_CREATED,
            'customer-user-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoUserFoundException
     */
    #[DetailResponse(CustomerUserSimpleResponse::class, 'customer-user-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/customer-user/{id}')]
    public function updateCustomerUserAction(
        int $id,
        #[MapRequestPayload]
        CustomerUserUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->customerUserFacade->updateCustomerUser($id, $request),
            Response::HTTP_OK,
            'customer-user-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'customer-user-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/customer-user/{id}')]
    public function deleteCustomerUserAction(int $id): SuccessOutput
    {
        $this->customerUserFacade->deleteCustomerUser($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'customer-user-deleted',
        );
    }

}
