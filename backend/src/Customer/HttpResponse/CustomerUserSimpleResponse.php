<?php

declare(strict_types = 1);

namespace Pm\Customer\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Customer\Entity\CustomerUser;

class CustomerUserSimpleResponse implements ApiOutput
{

    public readonly int $id;

    public readonly int $customer_id;

    public readonly string $customer_name;

    public readonly int $user_id;

    public readonly string $user_identifier;

    public readonly ?string $user_first_name;

    public readonly ?string $user_last_name;

    public function __construct(CustomerUser $customerUser)
    {
        $this->id = $customerUser->getId();
        $this->customer_id = $customerUser->getCustomer()->getId();
        $this->customer_name = $customerUser->getCustomer()->getName();
        $this->user_id = $customerUser->getUser()->getId();
        $this->user_identifier = $customerUser->getUser()->getIdentifier();
        $this->user_first_name = $customerUser->getUser()->getFirstname();
        $this->user_last_name = $customerUser->getUser()->getLastName();
    }

}
