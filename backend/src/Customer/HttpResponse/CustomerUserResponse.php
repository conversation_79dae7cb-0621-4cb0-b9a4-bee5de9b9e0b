<?php

declare(strict_types = 1);

namespace Pm\Customer\HttpResponse;

use Pm\Core\Controller\ApiOutput;
use Pm\Customer\Entity\CustomerUser;
use Pm\User\HttpResponse\UserSimpleHttpResponse;

class CustomerUserResponse implements ApiOutput
{

    public readonly int $id;

    public readonly int $customer_id;

    public readonly CustomerSimpleHttpResponse $customer;

    public readonly int $user_id;

    public readonly UserSimpleHttpResponse $user;

    public function __construct(CustomerUser $customerUser)
    {
        $this->id = $customerUser->getId();
        $this->customer_id = $customerUser->getCustomer()->getId();
        $this->customer = new CustomerSimpleHttpResponse($customerUser->getCustomer());
        $this->user_id = $customerUser->getUser()->getId();
        $this->user = new UserSimpleHttpResponse($customerUser->getUser());
    }

}
