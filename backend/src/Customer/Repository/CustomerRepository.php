<?php

declare(strict_types = 1);

namespace Pm\Customer\Repository;

use Doctrine\ORM\Tools\Pagination\Paginator;
use Pm\Core\Enum\Sort;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Repository\BaseRepository;
use Pm\Customer\Entity\Customer;
use Pm\Customer\FilterInput\CustomerFilterInput;
use Pm\Customer\Input\CustomerGetInput;
use Pm\Tools\SortingHelper;

class CustomerRepository extends BaseRepository
{

    /**
     * @throws InvalidInputException
     */
    public function get(int $id): Customer
    {
        $customer = $this->entityManager->createQueryBuilder()
            ->select('c')
            ->from(Customer::class, 'c')
            ->where('c.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();

        if ($customer === null) {
            throw new InvalidInputException("Customer with ID {$id} not found.");
        }

        return $customer;
    }

    /**
     * @return Paginator<Customer>
     */
    public function getCustomerList(CustomerGetInput $customerGetInput): Paginator
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('c')
            ->from(Customer::class, 'c');

        if ($customerGetInput->search !== null) {
            $queryBuilder->where(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('c.name'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->orWhere(
                $queryBuilder->expr()->like(
                    (string) $queryBuilder->expr()->lower('c.cid'),
                    (string) $queryBuilder->expr()->lower(':search'),
                ),
            )->setParameter('search', '%' . $customerGetInput->search . '%');
        }

        $this->applyCriteria(
            null,
            $queryBuilder,
            new CustomerFilterInput($customerGetInput->filter),
            $customerGetInput->limit,
            $customerGetInput->offset,
            SortingHelper::sanitizeSortBy($customerGetInput->sortBy, 'c', 'name'),
            SortingHelper::sanitizeSortMethod($customerGetInput->sortMethod, Sort::Asc),
        );

        return new Paginator($queryBuilder, fetchJoinCollection: true);
    }

}
