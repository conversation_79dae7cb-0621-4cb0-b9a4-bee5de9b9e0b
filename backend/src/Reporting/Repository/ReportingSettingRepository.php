<?php

declare(strict_types = 1);

namespace Pm\Reporting\Repository;

use Doctrine\ORM\EntityManager;
use Pm\Facility\Entity\Facility;
use Pm\Reporting\Entity\ReportingSetting;

/**
 * ReportingSettingRepository
 *
 * This PM2 implementation
 */
readonly class ReportingSettingRepository
{

    public function __construct(
        private EntityManager $entityManager,
    )
    {
    }

    /**
     * Retrieve all recipients of notifications with their notification type selection
     *
     * @return array <ReportingSetting>
     */
    public function findReportingForUsers(Facility $facility, string $code): array
    {
        $qb = $this->entityManager->createQueryBuilder()
            ->select('rs', 'r', 'u')
            ->from(ReportingSetting::class, 'rs')
            ->innerJoin('rs.reporting', 'r')
            ->innerJoin('r.user', 'u')
            ->innerJoin('rs.eventCategory', 'e')
            ->andWhere('r.facility = :facility')
            ->andWhere('e.code = :code')
            ->setParameters([
                'facility' => $facility,
                'code' => $code,
            ]);

        return $qb->getQuery()->getResult();
    }

}
