<?php

declare(strict_types = 1);

namespace Pm\Reporting\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Pm\Core\Entity\BaseEntity;
use Pm\Core\Entity\Traits\BlameableInsertEntity;
use Pm\Core\Entity\Traits\BlameableUpdateEntity;
use Pm\Core\Entity\Traits\TimestampableEntity;
use Pm\EventCategory\Entity\EventCategory;

#[Entity]
#[UniqueConstraint(
    name: 'reporting_settings_unique_fk',
    columns: ['reporting_id', 'event_category_id'],
)]
class ReportingSetting extends BaseEntity
{

    use BlameableInsertEntity;
    use BlameableUpdateEntity;
    use TimestampableEntity;

    public const SENDING_TYPE_SMS = 1;
    public const SENDING_TYPE_EMAIL = 2;
    public const SENDING_TYPE_SMSEMAIL = 3;
    public const SENDING_TYPE_NOTIFICATION = 4;
    public const SENDING_TYPE_SMSNOTIFICATION = 5;
    public const SENDING_TYPE_EMAILNOTIFICATION = 6;
    public const SENDING_TYPE_SMSEMAILNOTIFICATION = 7;

    public function __construct(
        #[ManyToOne(targetEntity: Reporting::class)]
        #[JoinColumn(nullable: false)]
        private Reporting $reporting,
        #[ManyToOne(targetEntity: EventCategory::class)]
        #[JoinColumn(nullable: false)]
        private EventCategory $eventCategory,
        /**
         * Binary representation,
         * odkud se pak zjistuje binarni soucet & - SMS = 1,3,5,7
         *
         *     N E S
         * 1 = 0 0 1
         * 2 = 0 1 0
         * 3 = 0 1 1
         * 4 = 1 0 0
         * 5 = 1 0 1
         * 6 = 1 1 0
         * 7 = 1 1 1
         */
        #[Column(type: Types::INTEGER, nullable: false)]
        private int $sendingType,
    )
    {
    }

    public function getSendingType(): int
    {
        return $this->sendingType;
    }

    public function setSendingType(int $sendingType): void
    {
        $this->sendingType = $sendingType;
    }

    public function getReporting(): Reporting
    {
        return $this->reporting;
    }

    public function setReporting(Reporting $reporting): void
    {
        $this->reporting = $reporting;
    }

    public function getEventCategory(): EventCategory
    {
        return $this->eventCategory;
    }

    public function setEventCategory(EventCategory $eventCategory): void
    {
        $this->eventCategory = $eventCategory;
    }

}
