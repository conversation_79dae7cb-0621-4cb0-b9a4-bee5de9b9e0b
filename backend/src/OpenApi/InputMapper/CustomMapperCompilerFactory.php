<?php

declare(strict_types = 1);

namespace OpenApi\InputMapper;

use LogicException;
use OpenApi\InputMapper\Attribute\AllowWeakTypes;
use OpenApi\InputMapper\MapperCompiler\MapWeakBool;
use OpenApi\InputMapper\MapperCompiler\MapWeakFloat;
use OpenApi\InputMapper\MapperCompiler\MapWeakInt;
use OpenApi\InputMapper\MapperCompiler\MapWeakNullable;
use OpenApi\InputMapper\MapperCompiler\MapWeakString;
use PHPStan\PhpDocParser\Ast\Type\IdentifierTypeNode;
use PHPStan\PhpDocParser\Ast\Type\NullableTypeNode;
use PHPStan\PhpDocParser\Ast\Type\TypeNode;
use PHPStan\PhpDocParser\Lexer\Lexer;
use PHPStan\PhpDocParser\Parser\PhpDocParser;
use ReflectionParameter;
use ShipMonk\InputMapper\Compiler\Mapper\MapperCompiler;
use ShipMonk\InputMapper\Compiler\MapperFactory\DefaultMapperCompilerFactory;
use ShipMonk\InputMapper\Compiler\Validator\ValidatorCompiler;
use function count;
use function is_a;
use function strtolower;

class CustomMapperCompilerFactory extends DefaultMapperCompilerFactory
{

    public final const ALLOW_WEAK_TYPES = 'allowWeakTypes';

    /**
     * @param array<class-string, callable(class-string): MapperCompiler> $mapperCompilerFactories
     */
    public function __construct(
        Lexer $phpDocLexer,
        PhpDocParser $phpDocParser,
        array $mapperCompilerFactories = [],
    )
    {
        parent::__construct($phpDocLexer, $phpDocParser, $mapperCompilerFactories);
    }

    /**
     * @param array<string, mixed> $options
     */
    public function create(
        TypeNode $type,
        array $options = [],
    ): MapperCompiler
    {
        if (isset($options[self::ALLOW_WEAK_TYPES]) && $options[self::ALLOW_WEAK_TYPES] === true) {
            if ($type instanceof IdentifierTypeNode) {
                return match (strtolower($type->name)) {
                    'bool' => new MapWeakBool(),
                    'float' => new MapWeakFloat(),
                    'int' => new MapWeakInt(),
                    'string' => new MapWeakString(),
                    default => parent::create($type, [self::DELEGATE_OBJECT_MAPPING => false] + $options),
                };
            }

            if ($type instanceof NullableTypeNode) {
                return new MapWeakNullable($this->createInner($type->type, $options));
            }
        }

        return parent::create($type, $options);
    }

    /**
     * @param array<string, mixed> $options
     */
    protected function createParameterMapperCompiler(
        ReflectionParameter $parameterReflection,
        TypeNode $type,
        array $options,
    ): MapperCompiler
    {
        if ($this->areWeakTypesAllowed($parameterReflection)) {
            $options[self::ALLOW_WEAK_TYPES] = true;
            $options[self::DELEGATE_OBJECT_MAPPING] = false;
        }

        return parent::createParameterMapperCompiler($parameterReflection, $type, $options);
    }

    protected function addValidator(
        MapperCompiler $mapperCompiler,
        ValidatorCompiler $validatorCompiler,
    ): MapperCompiler
    {
        if ($mapperCompiler instanceof MapWeakNullable) {
            return new MapWeakNullable($this->addValidator($mapperCompiler->innerMapperCompiler, $validatorCompiler));
        }

        return parent::addValidator($mapperCompiler, $validatorCompiler);
    }

    private function areWeakTypesAllowed(ReflectionParameter $parameterReflection): bool
    {
        if (count($parameterReflection->getAttributes(AllowWeakTypes::class)) > 0) {
            return true;
        }

        $classReflection = $parameterReflection->getDeclaringClass();
        return $classReflection !== null && count($classReflection->getAttributes(AllowWeakTypes::class)) > 0;
    }

    /**
     * @param class-string $inputClassName
     * @param array<string, mixed> $options
     */
    protected function createObjectMappingByConstructorInvocation(
        string $inputClassName,
        array $options,
    ): MapperCompiler
    {
        if (!is_a($inputClassName, Input::class, true)) {
            throw new LogicException('Unable to generate mapping for non-input class: #[' . $inputClassName . ']#');
        }

        return parent::createObjectMappingByConstructorInvocation($inputClassName, $options);
    }

}
