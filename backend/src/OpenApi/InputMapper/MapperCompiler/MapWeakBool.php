<?php

declare(strict_types = 1);

namespace OpenApi\InputMapper\MapperCompiler;

use PHPStan\PhpDocParser\Ast\Type\IdentifierTypeNode;
use PHPStan\PhpDocParser\Ast\Type\TypeNode;
use ShipMonk\InputMapper\Compiler\Mapper\MapRuntime;
use ShipMonk\InputMapper\Runtime\Exception\MappingFailedException;
use function is_bool;

class MapWeakBool extends MapRuntime
{

    /**
     * @param list<string|int> $path
     *
     * @throws MappingFailedException
     */
    public static function mapValue(
        mixed $value,
        array $path,
    ): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        if ($value === '1' || $value === '0' || $value === 1 || $value === 0) {
            return (bool) $value;
        }

        if ($value === 'true') {
            return true;
        }

        if ($value === 'false') {
            return false;
        }

        throw MappingFailedException::incorrectValue($value, $path, 'bool-like');
    }

    public function getInputType(): TypeNode
    {
        return new IdentifierTypeNode('mixed');
    }

    public function getOutputType(): TypeNode
    {
        return new IdentifierTypeNode('bool');
    }

}
