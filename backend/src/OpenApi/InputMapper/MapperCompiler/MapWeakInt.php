<?php

declare(strict_types = 1);

namespace OpenApi\InputMapper\MapperCompiler;

use Nette\Utils\Strings;
use PHPStan\PhpDocParser\Ast\Type\IdentifierTypeNode;
use PHPStan\PhpDocParser\Ast\Type\TypeNode;
use ShipMonk\InputMapper\Compiler\Mapper\MapRuntime;
use ShipMonk\InputMapper\Runtime\Exception\MappingFailedException;
use function is_int;
use function is_numeric;

class MapWeakInt extends MapRuntime
{

    /**
     * @param list<string|int> $path
     *
     * @throws MappingFailedException
     */
    public static function mapValue(
        mixed $value,
        array $path,
    ): int
    {
        if (is_int($value)) {
            return $value;
        }

        if (is_numeric($value) && Strings::match((string) $value, '~^[+-]?[0-9]+$~') !== null) {
            return (int) $value;
        }

        throw MappingFailedException::incorrectValue($value, $path, 'int-like');
    }

    public function getInputType(): TypeNode
    {
        return new IdentifierTypeNode('mixed');
    }

    public function getOutputType(): TypeNode
    {
        return new IdentifierTypeNode('int');
    }

}
