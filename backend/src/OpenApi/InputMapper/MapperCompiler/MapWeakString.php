<?php

declare(strict_types = 1);

namespace OpenApi\InputMapper\MapperCompiler;

use PHPStan\PhpDocParser\Ast\Type\IdentifierTypeNode;
use PHPStan\PhpDocParser\Ast\Type\TypeNode;
use ShipMonk\InputMapper\Compiler\Mapper\MapRuntime;
use ShipMonk\InputMapper\Runtime\Exception\MappingFailedException;
use function is_int;
use function is_string;

class MapWeakString extends MapRuntime
{

    /**
     * @param list<string|int> $path
     *
     * @throws MappingFailedException
     */
    public static function mapValue(
        mixed $value,
        array $path,
    ): string
    {
        if (is_string($value)) {
            return $value;
        }

        if (is_int($value)) {
            return (string) $value;
        }

        throw MappingFailedException::incorrectValue($value, $path, 'string');
    }

    public function getInputType(): TypeNode
    {
        return new IdentifierTypeNode('mixed');
    }

    public function getOutputType(): TypeNode
    {
        return new IdentifierTypeNode('string');
    }

}
