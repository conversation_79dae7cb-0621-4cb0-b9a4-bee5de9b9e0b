<?php

declare(strict_types = 1);

namespace OpenApi\InputMapper;

use PHPStan\PhpDocParser\ParserConfig;
use ShipMonk\InputMapper\Compiler\MapperFactory\DefaultMapperCompilerFactoryProvider;
use ShipMonk\InputMapper\Compiler\MapperFactory\MapperCompilerFactory;

class CustomMapperCompilerFactoryProvider extends DefaultMapperCompilerFactoryProvider
{

    protected function create(): MapperCompilerFactory
    {
        $config = new ParserConfig([]);
        return new CustomMapperCompilerFactory($this->createPhpDocLexer($config), $this->createPhpDocParser($config));
    }

}
