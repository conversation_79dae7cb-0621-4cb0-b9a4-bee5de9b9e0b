includes:
    - phar://phpstan.phar/conf/config.levelmax.neon
    - phar://phpstan.phar/conf/bleedingEdge.neon
    - ./vendor/phpstan/phpstan-doctrine/extension.neon
    - ./vendor/phpstan/phpstan-phpunit/extension.neon
    - ./vendor/phpstan/phpstan-phpunit/rules.neon
    - ./vendor/phpstan/phpstan-symfony/extension.neon
    - ./vendor/phpstan/phpstan-webmozart-assert/extension.neon
    - ./vendor/phpstan/phpstan-strict-rules/rules.neon
    - ./vendor/symplify/phpstan-rules/config/services/services.neon
    - ./vendor/shipmonk/phpstan-rules/rules.neon
    - ./baseline.neon

parametersSchema:
    root: string()

parameters:
    editorUrl: 'jetbrains://php-storm/navigate/reference?project=pm&path=%%relFile%%:%%line%%' # requires usage of JetBrains Toolbox and matching project name "pm"
    editorUrlTitle: '%%relFile%%:%%line%%' # allows copy-pastable output in GitLab CI / console
    paths:
        - migrations
        - public
        - src
        - tests
    parallel:
        processTimeout: 1000.0
        maximumNumberOfProcesses: 14
    root: %rootDir%/../../..
    tmpDir: %rootDir%/../../../var/phpstan/
    symfony:
        containerXmlPath: %root%/var/cache/test/Pm_KernelTestDebugContainer.xml
    doctrine:
        repositoryClass: 'Doctrine\ORM\EntityRepository'
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    treatPhpDocTypesAsCertain: false
    exceptions:
        reportUncheckedExceptionDeadCatch: true
        implicitThrows: false
#        uncheckedExceptionClasses:
#            - 'LogicException'
        checkedExceptionClasses:
            - Pm\Core\Exception\RuntimeException
        check:
            missingCheckedExceptionInThrows: true
            tooWideThrowType: true
    excludePaths:
        - %root%/tests/bootstrap.php
        - %root%/src/Core/Doctrine/Dql/*
        - %root%/src/Kernel.php
        - %root%/public/index.php
    bootstrapFiles:
        - %root%/vendor/squizlabs/php_codesniffer/autoload.php
        - %root%/vendor/squizlabs/php_codesniffer/src/Util/Tokens.php
    stubFiles:
        - ./tools/PHPStan/stubs/BackedEnum.php.stub
    checkExplicitMixed: false
    shipmonkRules:
        forbidCheckedExceptionInCallable:
            enabled: true
            allowedCheckedExceptionCallables:
                'Doctrine\ORM\EntityManager::wrapInTransaction': 0
        forbidCustomFunctions:
            list:
                'var_dump': 'Please remove debug code' # deny function
                'Doctrine\Persistence\ObjectRepository::findBy': 'Do not use BaseRepository, always create custom repository method'
                'Doctrine\Persistence\ObjectRepository::findOneBy': 'Do not use BaseRepository, always create custom repository method'
                'Doctrine\Persistence\ObjectRepository::find': 'Do not use BaseRepository, always create custom repository method'

    ignoreErrors:
        # BackedEnum generics solution
        - '#^Enum .*? has @implements tag, but does not implement any interface.$#'
        - '#(.*) of method (.*)::convertToPHPValue\(\) should be contravariant with parameter \$value \(mixed\) of method (.*)::convertToPHPValue\(\)#'
        - '#(.*) of method (.*)::convertToDatabaseValue\(\) should be contravariant with parameter \$value \(mixed\) of method (.*)::convertToDatabaseValue\(\)#'
        - '#^Method (.*)Repository::(.*)\(\) should return (.*) but returns mixed.$#'
        - '#Exception \$e not passed as previous to \$e->toLogicException\(\)#'

        -
            message: "#^Cannot access offset (.*) on mixed.$#"
            path: tests/*

        -
            message: "#^Method Doctrine\\\\Persistence\\\\ObjectRepository::find(.*) is forbidden. Do not use BaseRepository, always create custom repository method$#"
            path: tests/*

        -
            message: "#^Method Doctrine\\\\Persistence\\\\ObjectRepository::find(.*) is forbidden. Do not use BaseRepository, always create custom repository method$#"
            path: src/User/Facade/DemoAccountFacade.php

        -
            message: "#^Method (.*)Test::(.*) throws checked exception (.*) but it's missing from the PHPDoc @throws tag.$#"
            path: tests/*

        -
            message: "#^Method (.*)::(.*) has (.*) in PHPDoc @throws tag but it's not thrown.$#"
            path: src/*

        -
            message: "#^Parameter \\#([0-9]) \\$haystack of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertCount\\(\\) expects Countable\\|iterable, mixed given\\.$#"
            path: tests/*

        -
            message: "#^Cannot cast mixed to string\\.$#"
            path: tests/*


