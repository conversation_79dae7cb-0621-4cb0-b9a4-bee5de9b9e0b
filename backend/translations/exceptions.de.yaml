Not implemented: 'Nicht implementiert'
Access Denied.: 'Zugriff verweigert.'
'Field #[VAR1]# is not allowed.': 'Feld #[VAR1]# ist nicht erlaubt.'
'User must be an instance of User class, but it is #[VAR1]#': '<PERSON>utzer muss eine Instanz der Benutzerklasse sein, aber es ist #[VAR1]#'
'Account with given name already exists.': 'Konto mit dem angegebenen Namen existiert bereits.'
'Validation failed.': 'Validierung fehlgeschlagen.'
"Cannot get ID when it's null.": "ID kann nicht abgerufen werden, wenn sie null ist."
'You must override the getDefaultName() method.': 'Sie müssen die Methode getDefaultName() überschreiben.'
'You must override the execute() method in the concrete command class.': 'Sie müssen die Methode execute() in der konkreten Befehlsklasse überschreiben.'
'Not found.': 'Nicht gefunden.'
'No override method getName implemented.': 'Keine Überschreibungsmethode getName implementiert.'
'Invalid data type.': 'Ungültiger Datentyp.'
"The parser's \"lookahead\" property is not populated with a type": "Die \"lookahead\"-Eigenschaft des Parsers ist nicht mit einem Typ gefüllt"
'Blame is reference, user must be an object': 'Blame ist eine Referenz, der Benutzer muss ein Objekt sein'
'Unsupported entity.': 'Nicht unterstützte Entität.'
'Unsupported entity for PopsObserverResponse.': 'Nicht unterstützte Entität für PopsObserverResponse.'
'Unsupported entity for PopsEventObserverResponse.': 'Nicht unterstützte Entität für PopsEventObserverResponse.'
'Unsupported entity for PopsEventAttachmentObserverResponse.': 'Nicht unterstützte Entität für PopsEventAttachmentObserverResponse.'
'Each filter must include a field, value, and type.': 'Jeder Filter muss ein Feld, einen Wert und einen Typ enthalten.'
'No API url configuration found for account #: #[VAR1]#': 'Keine API-URL-Konfiguration für Konto #: #[VAR1]# gefunden.'
'No API token configuration found for account #: #[VAR1]#': 'Keine API-Token-Konfiguration für Konto #: #[VAR1]# gefunden.'
'Invalid event: #[VAR1]#': 'Ungültiges Ereignis: #[VAR1]#'
'Invalid entity class: #[VAR1]#': 'Ungültige Entitätsklasse: #[VAR1]#'
'Missing entity ID for update endpoint.': 'Fehlende Entitäts-ID für Update-Endpunkt.'
'Missing entity ID for persist-attachment endpoint.': 'Fehlende Entitäts-ID für Persist-Anhang-Endpunkt.'
'Missing entity ID for reporting endpoint.': 'Fehlende Entitäts-ID für Reporting-Endpunkt.'
'Missing entity ID for lock endpoint.': 'Fehlende Entitäts-ID für Sperr-Endpunkt.'
'No API path configuration found for account #: #[VAR1]#': 'Keine API-Pfadkonfiguration für Konto #: #[VAR1]# gefunden.'
'Entity not found: #[VAR1]# #[VAR2]#': 'Entität nicht gefunden: #[VAR1]# #[VAR2]#'
'Entity already has originalDbId: #[VAR1]#': 'Entität hat bereits originalDbId: #[VAR1]#'
'#[VAR1]#: Invalid Input Value for Filter Type Between.': '#[VAR1]#: Ungültiger Eingabewert für Filtertyp Zwischen.'
'#[VAR1]#: Invalid Filter Type for Var Type: #[VAR2]#': '#[VAR1]#: Ungültiger Filtertyp für Var-Typ: #[VAR2]#'
'Invalid user instance.': 'Ungültige Benutzerinstanz.'
'File "#[VAR1]#" not found.': 'Datei "#[VAR1]#" nicht gefunden.'
'Missing parent category #[VAR1]#.': 'Fehlende übergeordnete Kategorie #[VAR1]#.'
'Event category not set.': 'Ereigniskategorie nicht gesetzt.'
'User not set.': 'Benutzer nicht gesetzt.'
'Source not set.': 'Quelle nicht gesetzt.'
'Type not set.': 'Typ nicht gesetzt.'
'Priority not set.': 'Priorität nicht gesetzt.'
'PopsEvent not set.': 'PopsEvent nicht gesetzt.'
'POP insertedBy must be set.': 'POP insertedBy muss gesetzt sein.'
'Facility not set.': 'Einrichtung nicht gesetzt.'
'Reporting not set.': 'Berichterstattung nicht gesetzt.'
'No enum #[VAR1]# found.': 'Kein Enum #[VAR1]# gefunden.'
'Invalid value #[VAR1]# for enum #[VAR2]#.': 'Ungültiger Wert #[VAR1]# für Enum #[VAR2]#.'
'Unknown phone number type "#[VAR1]#".': 'Unbekannter Telefonnummerntyp "#[VAR1]#".'
'The current validation does not concern an object.': 'Die aktuelle Validierung betrifft kein Objekt.'
'Invalid property path "#[VAR2]#" provided to "#[VAR2]#" constraint: ': 'Ungültiger Eigenschaftspfad "#[VAR2]#" für Einschränkung "#[VAR2]#": '
'Unable to use property path as the Symfony PropertyAccess component is not installed.': 'Eigenschaftspfad kann nicht verwendet werden, da die Symfony PropertyAccess-Komponente nicht installiert ist.'
'No foreign key for set.': 'Kein Fremdschlüssel für Set.'
'No loggable entity set.': 'Keine protokollierbare Entität gesetzt.'
'Foreign key already set.': 'Fremdschlüssel bereits gesetzt.'
'Log can be created within transaction only.': 'Protokoll kann nur innerhalb einer Transaktion erstellt werden.'
'Missing Notification.': 'Fehlende Benachrichtigung.'
'Notifier Message Send Failed: #[VAR1]#': 'Benachrichtigungsnachricht senden fehlgeschlagen: #[VAR1]#'
'Notification message has failed to send on the gateway (#[VAR1]#) more times than allowed.': 'Benachrichtigungsnachricht konnte auf dem Gateway (#[VAR1]#) mehrmals als erlaubt nicht gesendet werden.'
'Failed to send #[VAR1]# notification on Gateway: #[VAR2]# response code: #[VAR3]#': 'Senden der #[VAR1]#-Benachrichtigung auf Gateway fehlgeschlagen: #[VAR2]# Antwortcode: #[VAR3]#'
'Invalid Notification.': 'Ungültige Benachrichtigung.'
'Missing HTTP client.': 'Fehlender HTTP-Client.'
'SmsService gateway request failed.': 'SmsService-Gateway-Anfrage fehlgeschlagen.'
'SmsService API error: #[VAR1]#': 'SmsService-API-Fehler: #[VAR1]#'
'Unable to send the SMS: #[VAR1]#': 'SMS konnte nicht gesendet werden: #[VAR1]#'
'Invalid Email.': 'Ungültige E-Mail.'
'Email Notification Send Failed: #[VAR1]#': 'E-Mail-Benachrichtigung senden fehlgeschlagen: #[VAR1]#'
'Failed to send email notification: #[VAR1]#': 'Senden der E-Mail-Benachrichtigung fehlgeschlagen: #[VAR1]#'
'Email Notification send failed too many times (#[VAR1]#).': 'E-Mail-Benachrichtigung senden zu oft fehlgeschlagen (#[VAR1]#).'
'Invalid Phone Number.': 'Ungültige Telefonnummer.'
'Undefined Source Type: #[VAR1]#': 'Undefinierter Quellentyp: #[VAR1]#'
'User does not have MobileInfo registered.': 'Benutzer hat keine MobileInfo registriert.'
'Unable to generate mapping for non-input class: #[VAR1]#': 'Mapping für Nicht-Eingabeklasse #[VAR1]# kann nicht generiert werden.'
'Parent category cannot be the same as the category itself.': 'Übergeordnete Kategorie kann nicht dieselbe wie die Kategorie selbst sein.'
'DomPDF failed to generate PDF.': 'DomPDF konnte PDF nicht generieren.'
'Facility missing alert interval.': 'Einrichtung fehlt Alarmintervall.'
'Invalid value POPS InsertedBy.': 'Ungültiger Wert POPS InsertedBy.'
'Attachment can only be uploaded on last Pops Event.': 'Anhang kann nur beim letzten Pops-Ereignis hochgeladen werden.'
'Attachment can only be deleted on last Pops Event.': 'Anhang kann nur beim letzten Pops-Ereignis gelöscht werden.'
"Facility doesn't belong to the account.": 'Einrichtung gehört nicht zum Konto.'
'Pops already locked.': 'Pops bereits gesperrt.'
'OriginalDbId of EventCategory can not be null on observed entity.': 'OriginalDbId der Ereigniskategorie kann bei beobachteter Entität nicht null sein.'
'OriginalDbId of Pops can not be null on observed entity.': 'OriginalDbId von Pops kann bei beobachteter Entität nicht null sein.'
'Facility originalDbId can not be null on observed entity.': 'OriginalDbId der Einrichtung kann bei beobachteter Entität nicht null sein.'
'Root EventCategory Not Found for accountId: #[VAR1]# and type: #[VAR2]#': 'Root-Ereigniskategorie für Konto-ID: #[VAR1]# und Typ: #[VAR2]# nicht gefunden.'
'Invalid interval. End date has to be later than start date.': 'Ungültiges Intervall. Enddatum muss später als Startdatum sein.'
'Invalid interval. Current time has to be within POPS interval.': 'Ungültiges Intervall. Aktuelle Zeit muss innerhalb des POPS-Intervalls liegen.'
'Invalid interval. Pops already exists overlapping given interval.': 'Ungültiges Intervall. Pops existiert bereits und überschneidet sich mit dem angegebenen Intervall.'
'Invalid interval. Super PoPS already exists overlapping given interval.': 'Ungültiges Intervall. Super PoPS existiert bereits und überschneidet sich mit dem angegebenen Intervall.'
'#[VAR1]# is not an enum.': '#[VAR1]# ist kein Enum.'
'Invalid originalDbId format: #[VAR1]#': 'Ungültiges originalDbId-Format: #[VAR1]#'
'Cannot open resource.': 'Ressource kann nicht geöffnet werden.'
'Cannot get contents from resource.': 'Inhalte können nicht aus der Ressource abgerufen werden.'
'Unsupported file extension: #[VAR1]#': 'Nicht unterstützte Dateierweiterung: #[VAR1]#'
'The user identifier cannot be empty.': 'Der Benutzeridentifikator darf nicht leer sein.'
'Personal number is required for user.': 'Persönliche Nummer ist für Benutzer erforderlich.'
'User password is required.': 'Benutzerpasswort ist erforderlich.'
'Contact info is required for customer.': 'Kontaktinformationen sind für Kunden erforderlich.'
'Customer email is required.': 'Kunden-E-Mail ist erforderlich.'
'Customer password is required.': 'Kundenpasswort ist erforderlich.'
'Facility not found for account #[VAR1#]': 'Einrichtung für Konto #[VAR1#] nicht gefunden.'
'Event category not found.': 'Ereigniskategorie nicht gefunden.'
'User with given email already exists.': 'Benutzer mit angegebener E-Mail existiert bereits.'
'User must be instance of User, #[VAR1]# given.': 'Benutzer muss eine Instanz von Benutzer sein, #[VAR1]# gegeben.'
'Migration of admin is not supported yet.': 'Migration des Administrators wird noch nicht unterstützt.'
'This method is not supported.': 'Diese Methode wird nicht unterstützt.'
'Username could not be found.': 'Benutzername konnte nicht gefunden werden.'
'Value of #[VAR1]# is missing.': 'Wert von #[VAR1]# fehlt.'
'Value of #[VAR1]# must be #[VAR2]#.': 'Wert von #[VAR1]# muss #[VAR2]# sein.'
'Invalid filter type #[VAR1]#.': 'Ungültiger Filtertyp #[VAR1]#.'
'Expected argument of type "#[VAR1]#", "#[VAR2]#" given.': 'Erwartetes Argument vom Typ "#[VAR1]#", "#[VAR2]#" gegeben.'
'The "#[VAR1]#" transport only supports instances of "#[VAR2]#" (instance of "#[VAR3]#" given).': 'Der "#[VAR1]#"-Transport unterstützt nur Instanzen von "#[VAR2]#" (Instanz von "#[VAR3]#" gegeben).'
'The data must belong to a backed enumeration of type Pm\\Core\\Enum\\FilterTypeEnum': 'Die Daten müssen zu einer unterstützten Enumeration vom Typ Pm\\Core\\Enum\\FilterTypeEnum gehören.'
'This value should be of type string.': 'Dieser Wert sollte vom Typ Zeichenkette sein.'
'Invalid json message received': 'Ungültige JSON-Nachricht empfangen'
'No Facility ##[VAR1]# found.': 'Keine Einrichtung ##[VAR1]# gefunden.'
'No Facility group ##[VAR1]# found.': 'Keine Einrichtung Gruppe ##[VAR1]# gefunden.'
'No account ##[VAR1]# found.': 'Kein Konto ##[VAR1]# gefunden.'
'Invalid credentials.': 'Ungültige Anmeldeinformationen.'
'Notification ##[VAR1]# not found.': 'Benachrichtigung ##[VAR1]# nicht gefunden.'
'No permissionApplication ##[VAR1]# found.': 'Keine permissionApplication ##[VAR1]# gefunden.'
'No permission ##[VAR1]# found.': 'Keine Berechtigung ##[VAR1]# gefunden.'
'No permission group ##[VAR1]# found.': 'Keine Berechtigungsgruppe ##[VAR1]# gefunden.'
'No permission template ##[VAR1]# found.': 'Keine Berechtigungsvorlage ##[VAR1]# gefunden.'
'No City ##[VAR1]# found.': 'Keine Stadt ##[VAR1]# gefunden.'
'No EventCategory ##[VAR1]# found.': 'Keine Ereigniskategorie ##[VAR1]# gefunden.'
'No EventCategory by eventNumber: ##[VAR1]# found.': 'Keine Ereigniskategorie nach Ereignisnummer: ##[VAR1]# gefunden.'
'No PopsAlert by ID ##[VAR1]# found.': 'Kein PopsAlert nach ID ##[VAR1]# gefunden.'
'No PopsAlert by Pops ##[VAR1]# found.': 'Kein PopsAlert nach Pops ##[VAR1]# gefunden.'
'No Pops Attachment ##[VAR1]# found.': 'Kein Pops-Anhang ##[VAR1]# gefunden.'
'No Pops Attachment #[VAR1]# found.': 'Kein Pops-Anhang #[VAR1]# gefunden.'
'No Pops Event ##[VAR1]# found.': 'Kein Pops-Ereignis ##[VAR1]# gefunden.'
'No Pops ##[VAR1]# found.': 'Kein Pops ##[VAR1]# gefunden.'
'No SuperPops ##[VAR1]# found.': 'Kein SuperPops ##[VAR1]# gefunden.'
'Too many attachments on Pops Event.': 'Zu viele Anhänge bei Pops-Ereignis.'
'Cannot change Pops Event datetime outside of Pops start and end.': 'Pops-Ereignis-Datum und -Uhrzeit können nicht außerhalb des Pops-Starts und -Endes geändert werden.'
'Cannot change order of Pops Events in Pops.': 'Reihenfolge der Pops-Ereignisse in Pops kann nicht geändert werden.'
'Pops ##[VAR1]# is locked.': 'Pops ##[VAR1]# ist gesperrt.'
'Super Pops ##[VAR1]# is locked.': 'Super Pops ##[VAR1]# ist gesperrt.'
'Account with ID #[VAR1]# is not allowed to access requested data.': 'Konto mit ID #[VAR1]# darf nicht auf angeforderte Daten zugreifen.'
'No Tenant Group ##[VAR1]# found.': 'Keine Mietergruppe ##[VAR1]# gefunden.'
'No tenant ##[VAR1]# found.': 'Kein Mieter ##[VAR1]# gefunden.'
'Can not read file: #[VAR1]#': 'Datei kann nicht gelesen werden: #[VAR1]#'
'Invalid file #[VAR1]# with size #[VAR2]#MB. Max file size is #[VAR3]#MB.': 'Ungültige Datei #[VAR1]# mit Größe #[VAR2]#MB. Maximale Dateigröße ist #[VAR3]#MB.'
'Mime type not detected. Invalid image data. Try another format (jpg, png, webp, svg).': 'Mime-Typ nicht erkannt. Ungültige Bilddaten. Versuchen Sie ein anderes Format (jpg, png, webp, svg).'
'SVG sanitize failed. Try to use another image format. Try another format (jpg, png, webp, svg).': 'SVG-Säuberung fehlgeschlagen. Versuchen Sie ein anderes Bildformat. Versuchen Sie ein anderes Format (jpg, png, webp, svg).'
'No user ##[VAR1]# found.': 'Kein Benutzer ##[VAR1]# gefunden.'
'No user #[VAR1]# found.': 'Kein Benutzer #[VAR1]# gefunden.'
'Pops Event datetime cannot be in the future.': 'Pops-Ereignis-Datum und -Uhrzeit können nicht in der Zukunft liegen.'
'Some events are not included in the Pops interval.': 'Einige Ereignisse sind nicht im PoPS-Intervall enthalten.'
'No facility can be added to Super PoPS.': 'Keine Einrichtung kann zu Super PoPS hinzugefügt werden.'
'Cannot create pops for another account.': 'Pops für ein anderes Konto kann nicht erstellt werden.'
'Pops Event description cannot be empty.': 'Die Beschreibung des Pops-Ereignisses darf nicht leer sein.'
'Invalid interval. Active PoPS cannot last longer than #[VAR1]# hours.': 'Ungültiges Intervall. Aktive PoPS können nicht länger als #[VAR1]# Stunden dauern.'
'No attachments uploaded on Pops Event.': 'Keine Anhänge auf Pops-Ereignis hochgeladen.'
'This name is already used.': 'Dieser Name wird bereits verwendet.'
'Currency name cannot be empty.': 'Der Währungsname darf nicht leer sein.'
'Currency abbreviation cannot be empty.': 'Das Währungskürzel darf nicht leer sein.'
'Currency with the same name or abbreviation already exists.': 'Eine Währung mit gleichem Namen oder gleicher Abkürzung ist bereits vorhanden.'
'Contract name cannot be empty.': 'Der Vertragsname darf nicht leer sein.'
'Contract code cannot be empty.': 'Der Vertragscode darf nicht leer sein.'
'Contract with the same name or code already exists.': 'Es besteht bereits ein Vertrag mit gleichem Namen oder Code.'
'Project name cannot be empty.': 'Der Projektname darf nicht leer sein.'
'Project code cannot be empty.': 'Der Projektcode darf nicht leer sein.'
'Project with the same name or code already exists.': 'Es besteht bereits ein Projekt mit gleichem Namen oder Code.'
'Legislation with the same name or code already exists.': 'Es besteht bereits eine Gesetzgebung mit gleichem Namen oder Code.'
'Legislation name cannot be empty.': 'Der Name der Gesetzgebung darf nicht leer sein.'
'Legislation code cannot be empty.': 'Der Gesetzgebungscode darf nicht leer sein.'
'Cannot delete facility because it is used in other parts of the system.': 'Die Einrichtung kann nicht gelöscht werden, da sie in anderen Teilen des Systems verwendet wird.'
'Company with ID ##[{VAR1}]# not found.': 'Unternehmen mit ID ##[{VAR1}]# nicht gefunden.'
'Contract with ID ##[{VAR1}]# not found.': 'Vertrag mit ID ##[{VAR1}]# nicht gefunden.'
'Project with ID ##[{$VAR1}]# not found.': 'Projekt mit ID ##[{VAR1}]# nicht gefunden.'
'Unit code cannot be empty.': 'Der Einheitencode darf nicht leer sein.'
'Unit name cannot be empty.': 'Der Einheitenname darf nicht leer sein.'
'Unit with the same name or code already exists.': 'Eine Einheit mit gleichem Namen oder Code ist bereits vorhanden.'
'Company name already exists': 'Firmenname existiert bereits.'
'Company abbr already exists': 'Firmenabkürzung existiert bereits.'
'Company code already exists': 'Buchungskreis ist bereits vorhanden.'
'Expected instance of Tenant.': 'Erwartete Instanz des Mandanten.'
'Facility ID cannot be null.': 'Die Einrichtungs-ID darf nicht null sein.'
'Tenant group name cannot be empty.': 'Der Name der Mandantengruppe darf nicht leer sein.'
'At least one tenant must be provided to create a tenant group.': 'Zum Erstellen einer Mandantengruppe muss mindestens ein Mandant angegeben werden.'
'A group with the same name already exists.': 'Eine Gruppe mit demselben Namen existiert bereits.'
'Entity with ID {id} not found.': 'Entity mit ID {id} wurde nicht gefunden.'
