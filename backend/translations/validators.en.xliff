<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
    <file datatype="plaintext" original="validators.en.xliff" source-language="en">
        <body>
            <trans-unit id="validation.field.required">
                <source>validation.field.required</source>
                <target>Entered value for field {{ property }} is required.</target>
            </trans-unit>
            <trans-unit id="validation.field.max_length">
                <source>validation.field.max_length</source>
                <target>Entered value for field {{ property }} is too long. It should have {{ limit }} characters or less.</target>
            </trans-unit>
            <trans-unit id="validation.email.invalid">
                <source>validation.email.invalid</source>
                <target>Entered value for field {{ property }} is not a valid email address.</target>
            </trans-unit>
            <trans-unit id="validation.field.unique">
                <source>validation.field.unique</source>
                <target>Entered value for field {{ property }} already exists.</target>
            </trans-unit>
            <trans-unit id="validation.phone.invalid">
                <source>validation.phone.invalid</source>
                <target>Entered value for field {{ property }} is not a valid phone number.</target>
            </trans-unit>
            <trans-unit id="validation.field.min_length">
                <source>validation.field.min_length</source>
                <target>Entered value for field {{ property }} is too short. It should have {{ limit }} characters or more.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
