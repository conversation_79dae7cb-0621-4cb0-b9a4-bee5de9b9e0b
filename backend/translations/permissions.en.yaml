# Permission translations - English
permission:
  PERM_COMMON: "Common"
  PERM_OBJECTS: "Object operations"
  PERM_POSITION_SECRET_SHOW: "Show secret positions"
  PERM_IS_CONTACT: "Show as a contact person"
  PERM_OBJECT_GROUP_SELECT: "Choose a group of objects"
  PERM_PANIC_RECEIVE: "Receive emergency message"
  PERM_PANIC_BUTTON_TENANT: "Use both panic buttons"
  PERM_OBJECT_CREATE: "Create an object"
  PERM_OBJECT_DELETE: "Remove an object"
  PERM_OBJECT_EDIT: "Edit an object"
  PERM_OBJECT_EDIT_ALL: "Edit an object - complete"
  PERM_OBJECT_ACTIVATE: "Activate an object"
  PERM_OBJECT_SHOW: "Show object"
  PERM_OBJECT_EDIT_POPS_ACTIVITY: "Edit object - SPR activity settings"
  PERM_OBJECT_EDIT_DOCUMENTATION: "Edit object - documentation"
  PERM_OBJECT_EDIT_MODE: "Edit object - mode (nuclear)"
  PERM_OBJECT_GROUP_CREATE: "Create object group"
  PERM_OBJECT_GROUP_DELETE: "Delete object group"
  PERM_OBJECT_GROUP_EDIT: "Edit object group"
  PERM_OBJECT_GROUP_SHOW: "Show object groups"
  PERM_POPS_OBJECT_ALERT: "Check SPR activity"
  PERM_POPS_CREATE: "Create POPS"
  PERM_POPS_EDIT: "Edit POPS"
  PERM_POPS_LOCK: "Lock POPS"
  PERM_POPS_SHOW: "Show POPS"
  PERM_POPS_EVENT_CREATE: "Create new POPS event"
  PERM_POPS_EVENT_EDIT: "Edit POPS event"
  PERM_POPS_EVENT_SHOW: "Show POPS event"
  PERM_POPS_EVENT_TIME: "Set POPS event time to other than current"
  PERM_POPS_SET_SEND_ATTACHMENT: "Set attachment sending for POPS"
  PERM_ABSENCE_CREATE: "Create an absence"
  PERM_ABSENCE_EDIT: "Edit an absence"
  PERM_ABSENCE_DELETE: "Delete an absence"
  PERM_ABSENCE_SHOW: "Show an absence"
  PERM_ABSENCE_TYPE_CREATE: "Create an absence type"
  PERM_ABSENCE_TYPE_EDIT: "Edit an absence type"
  PERM_ABSENCE_TYPE_DELETE: "Delete an absence type"
  PERM_ABSENCE_TYPE_SHOW: "Show an absence type"
  PERM_AGENC_RATE_CREATE: "Create an agency rate"
  PERM_AGENC_RATE_EDIT: "Edit an agency rate"
  PERM_AGENC_RATE_DELETE: "Delete an agency rate"
  PERM_AGENC_RATE_SHOW: "Show an agency rate"
  PERM_AGENCY_CREATE: "Create an agency"
  PERM_AGENCY_EDIT: "Edit an agency"
  PERM_AGENCY_DELETE: "Delete an agency"
  PERM_AGENCY_SHOW: "Show an agency"
  PERM_AGENCY_SERVICE_SHOW: "Show materials for a supplier"
  PERM_AGENCY_SERVICE_DETAIL_SHOW: "Show a material detail for a supplier"
  PERM_AGENCY_SUBJECT_CREATE: "Create an agency person"
  PERM_AGENCY_SUBJECT_EDIT: "Edit an agency person"
  PERM_AGENCY_SUBJECT_DELETE: "Delete an agency person"
  PERM_AGENCY_SUBJECT_SHOW: "Show an agency person"
  PERM_ATTENDANCE_END_CREATE: "Create a service termination"
  PERM_ATTENDANCE_END_EDIT: "Edit a service termination"
  PERM_ATTENDANCE_END_SHOW: "Show a service termination"
  PERM_ATTENDANCE_NO_PLAN_SHOW: "Show a list of \"Non/existing plan\""
  PERM_ATTENDANCE_NOT_APPROVED_EXPORT: "Export non-approved attendance and plans"
  PERM_ATTENDANCE_NOT_APPROVED_SHOW: "Show non-approved attendancies and plans"
  PERM_ATTENDANCE_RUNNING_EDIT: "Edit current service"
  PERM_ATTENDANCE_RUNNING_SHOW: "Show current service"
  PERM_ATTENDANCE_START_CREATE: "Create a service beginning"
  PERM_ATTENDANCE_START_EDIT: "Edit a service beginning"
  PERM_ATTENDANCE_START_DELETE: "Delete a service beginning"
  PERM_ATTENDANCE_START_CREATE_SERVICE: "Set \"haven't started\" to a service"
  PERM_ATTENDANCE_START_SHOW: "Show a service beginning"
  PERM_ATTENDANCE_START_CHECK_SHOW: "Show a premature attendance"
  PERM_ATTENDANCE_SHOW: "Show an attendance summary"
  PERM_ATTENDANCE_EDIT: "Edit an attendance summary"
  PERM_BILLING_DOCUMENT_ATTENDANCE_SHOW: "Show billing materials - Attendance"
  PERM_BILLING_DOCUMENT_SERVICE_SHOW: "Show billing materials - Plan"
  PERM_BILLING_DOCUMENT_ANNEX_SHOW: "Show billing materials - Generate materials"
  PERM_BILLING_DOCUMENT_ATTENDANCE_SUBJECT_SHOW: "Show billing materials - Attendance according to person"
  PERM_BILLING_DOCUMENT_SUBJECT_SHOW: "Show billing materials - Person's attendance detail"
  PERM_BILLING_DOCUMENT_EXPORT: "Billing materials - export of costs"
  PERM_BILLING_DOCUMENT_GROUP_ATTENDANCE_SHOW: "Show group billing materials - Attendance"
  PERM_BILLING_DOCUMENT_GROUP_SERVICE_SHOW: "Show group billing materials - Plan"
  PERM_BILLING_DOCUMENT_ANNEX_GROUP_SHOW: "Show group billing materials - Generate materials"
  PERM_BILLING_DOCUMENT_GROUP_EXPORT: "Group billing materials - Export costs"
  PERM_CALENDAR_ADMIN: "Show holidays' calendar"
  PERM_CALENDAR_ADMIN_EDIT: "Edit holidays' calendar"
  PERM_CITY_CREATE: "Create a city"
  PERM_CITY_EDIT: "Edit a city"
  PERM_CITY_DELETE: "Delete a city"
  PERM_CITY_SHOW: "Show a city"
  PERM_COMPANY_CREATE: "Create a company"
  PERM_COMPANY_EDIT: "Edit a company"
  PERM_COMPANY_DELETE: "Delete a company"
  PERM_COMPANY_SHOW: "Show a company"
  PERM_CONTRACT_CREATE: "Create a contract"
  PERM_CONTRACT_EDIT: "Edit a contract"
  PERM_CONTRACT_DELETE: "Delete a contract"
  PERM_CONTRACT_SHOW: "Show a contract"
  PERM_COUNTRY_CREATE: "Create a country"
  PERM_COUNTRY_EDIT: "Edit a country"
  PERM_COUNTRY_DELETE: "Delete a country"
  PERM_COUNTRY_SHOW: "Show a country"
  PERM_CURRENCY_CREATE: "Create a currency"
  PERM_CURRENCY_EDIT: "Edit a currency"
  PERM_CURRENCY_DELETE: "Delete a currency"
  PERM_CURRENCY_SHOW: "Show a currency"
  PERM_CUSTOMERS_CREATE: "Create a customer"
  PERM_CUSTOMERS_EDIT: "Edit a customer"
  PERM_CUSTOMERS_DELETE: "Delete a customer"
  PERM_CUSTOMERS_SHOW: "Show a customer"
  PERM_CUSTOMERS_EDIT_PASSWORD: "Edit customer password"
  PERM_CUSTOMER_CONTACT_CREATE: "Add a contact person of a customer"
  PERM_CUSTOMER_CONTACT_EDIT: "Edit a contact person of a customer"
  PERM_CUSTOMER_CONTACT_DELETE: "Kill a contact person of a customer"
  PERM_CUSTOMER_CONTACT_SHOW: "Show a contact person of a customer"
  PERM_DIMENSION_CREATE: "Create a dimension"
  PERM_DIMENSION_EDIT: "Edit a dimension"
  PERM_DIMENSION_DELETE: "Delete a dimension"
  PERM_DIMENSION_SHOW: "Show a dimension"
  PERM_DIMENSION_LEVEL_CREATE: "Create a dimension level"
  PERM_DIMENSION_LEVEL_EDIT: "Edit a dimension level"
  PERM_DIMENSION_LEVEL_DELETE: "Delete a dimension level"
  PERM_DIMENSION_LEVEL_SHOW: "Show a dimension level"
  PERM_EVENT_CREATE: "Create an event of an event tree"
  PERM_EVENT_EDIT: "Edit an event of an event tree"
  PERM_EVENT_ACTIVATE: "Activate an event of an event tree"
  PERM_EVENT_HIDE: "Hide an event in the tree of events"
  PERM_EVENT_DELETE: "Delete an event of an event tree"
  PERM_EVENT_SHOW: "Show an event of an event tree"
  PERM_EVENT_CATEGORY_CREATE: "Create event category"
  PERM_EVENT_CATEGORY_DELETE: "Delete event category"
  PERM_EVENT_CATEGORY_EDIT: "Edit event category"
  PERM_INDIVIDUAL_RATE_CREATE: "Create an individual tariff"
  PERM_INDIVIDUAL_RATE_EDIT: "Edit an individual tariff"
  PERM_INDIVIDUAL_RATE_DELETE: "Delete an individual tariff"
  PERM_INDIVIDUAL_RATE_SHOW: "Show an individual tariff"
  PERM_LOG: "Log access"
  PERM_LOGIN_LOG_SHOW: "Show login/logout log"
  PERM_LOGIN_LOG_EXPORT: "Export login/logout log"
  PERM_LOGS_DATA_SHOW: "Show data log"
  PERM_LOGS_DATA_EXPORT: "Export data log"
  PERM_LOGS_SYSTEM_SHOW: "Show system log"
  PERM_LOGS_SYSTEM_EXPORT: "Export system log"
  PERM_MATERIAL_CREATE: "Create a material"
  PERM_MATERIAL_EDIT: "Edit a material"
  PERM_MATERIAL_DELETE: "Delete a material"
  PERM_MATERIAL_SHOW: "Show a material"
  PERM_MATERIAL_CATEGORY_CREATE: "Create a material category"
  PERM_MATERIAL_CATEGORY_EDIT: "Edit a material category"
  PERM_MATERIAL_CATEGORY_DELETE: "Delete a material category"
  PERM_MATERIAL_CATEGORY_SHOW: "Show a material category"
  PERM_MATERIAL_UNIT_CREATE: "Create a material unit"
  PERM_MATERIAL_UNIT_EDIT: "Edit a material unit"
  PERM_MATERIAL_UNIT_DELETE: "Delete a material unit"
  PERM_MATERIAL_UNIT_SHOW: "Show a material unit"
  PERM_NOTIFICATION_CREATE: "Create a notification"
  PERM_NOTIFICATION_EDIT: "Edit a notification"
  PERM_NOTIFICATION_DELETE: "Delete a notification"
  PERM_NOTIFICATION_SHOW: "Show a notification"
  PERM_OBJECT_READ: "Read objects"
  PERM_OBJECT_WRITE: "Write objects"
  PERM_TENANT_READ: "Read tenants"
  PERM_TENANT_WRITE: "Write tenants"
  PERM_TENANT_GROUP_READ: "Read tenant groups"
  PERM_TENANT_GROUP_WRITE: "Write tenant groups"
  PERM_COMPANY_READ: "Read companies"
  PERM_COMPANY_WRITE: "Write companies"
  PERM_LEGISLATION_READ: "Read legislation"
  PERM_LEGISLATION_WRITE: "Write legislation"
  PERM_SUPERPOPS_CREATE: "Create SuperPOPS"
  PERM_SUPERPOPS_SHOW: "Show SuperPOPS"
  PERM_SUPERPOPS_LOCK: "Lock SuperPOPS"
  PERM_SUPERPOPS_EDIT: "Edit SuperPOPS"
  PERM_PROJECT_READ: "Read projects"
  PERM_PROJECT_WRITE: "Write projects"
  PERM_PERMISSION_MANAGE: "Manage permissions"
  PERM_PERMISSION_GROUP_CREATE: "Create permission group"
  PERM_PERMISSION_GROUP_DELETE: "Delete permission group"
  PERM_PERMISSION_GROUP_EDIT: "Edit permission group"
  PERM_PERMISSION_GROUP_SHOW: "Show permission groups"
  PERM_PERMISSION_TEMPLATE_CREATE: "Create permission template"
  PERM_PERMISSION_TEMPLATE_DELETE: "Delete permission template"
  PERM_PERMISSION_TEMPLATE_EDIT: "Edit permission template"
  PERM_PERMISSION_TEMPLATE_SHOW: "Show permission templates"
  PERM_DISABLE_AUTOLOGOUT: "Disable auto logout"
  PERM_DISABLE_CONTROL_PRESENCE: "Disable presence control"
  PERM_SUBJECT_HIRE: "Subject hire"
  PERM_PERIOD_RESTRICTED: "Period restricted"
  PERM_M2CDESK_EXCLUDE_GROUP: "Exclude objects from this group (M2CDesk, M2C Support, ABI Bridge, Customer portal)"
  PERM_POPS_TOP_20: "SPR Top 20"
  PERM_OBJECT_POPS_EXPORT: "Export SPR list (on an object)"
  PERM_OBJECT_POPS_SHOW: "Show SPR (on an object)"
  PERM_EVENTS_OBJECT_EDIT: "Edit SPR event (on an object)"
  PERM_EVENTS_OBJECT_TRANSLATE: "Translate a SPR event (for an object)"
  PERM_EVENTS_OBJECT_FOR_CUSTOMER: "SPR events for customer"
  PERM_SUGGESTION_NOTIFICATION: "Suggestions - notice via PM"
  PERM_SUGGESTION_NOTIFICATION_EMAIL: "Suggestions - email notice"
