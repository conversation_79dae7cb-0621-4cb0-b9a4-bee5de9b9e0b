<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
    <file datatype="plaintext" original="validators.de.xliff" source-language="en" target-language="de">
        <body>
            <trans-unit id="validation.field.required">
                <source>validation.field.required</source>
                <target>Eingegebener Wert für das Feld {{ property }} ist erforderlich.</target>
            </trans-unit>
            <trans-unit id="validation.field.max_length">
                <source>validation.field.max_length</source>
                <target>Eingegebener Wert für das Feld {{ property }} ist zu lang. Er sollte höchstens {{ limit }} Zeichen haben.</target>
            </trans-unit>
            <trans-unit id="validation.email.invalid">
                <source>validation.email.invalid</source>
                <target>Eingegebener Wert für das Feld {{ property }} ist keine gültige E-Mail-Adresse.</target>
            </trans-unit>
            <trans-unit id="validation.field.unique">
                <source>validation.field.unique</source>
                <target>Eingegebener Wert für das Feld {{ property }} existiert bereits.</target>
            </trans-unit>
            <trans-unit id="validation.phone.invalid">
                <source>validation.phone.invalid</source>
                <target>Eingegebener Wert für das Feld {{ property }} ist keine gültige Telefonnummer.</target>
            </trans-unit>
            <trans-unit id="validation.field.min_length">
                <source>validation.field.min_length</source>
                <target>Eingegebener Wert für das Feld {{ property }} ist zu kurz. Er sollte mindestens {{ limit }} Zeichen haben.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
