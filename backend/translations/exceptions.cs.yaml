Not implemented: 'Neimplementováno'
Access Denied.: 'Přístup odepřen.'
'Field #[VAR1]# is not allowed.': 'Pole #[VAR1]# není povoleno.'
'User must be an instance of User class, but it is #[VAR1]#': 'Uživatel musí být instancí třídy User, ale je #[VAR1]#'
'Account with given name already exists.': '<PERSON><PERSON>et s daným jménem již existuje.'
'Validation failed.': 'Validace selhala.'
"Cannot get ID when it's null.": "Nelze získat ID, když je null."
'You must override the getDefaultName() method.': 'Musíte přetížit metodu getDefaultName().'
'You must override the execute() method in the concrete command class.': 'Musíte přetížit metodu execute() ve třídě konkrétního příkazu.'
'Not found.': 'Nenalezeno.'
'No override method getName implemented.': 'Není implementována žádná metoda getName.'
'Invalid data type.': 'Neplatný typ dat.'
"The parser's \"lookahead\" property is not populated with a type": "Vlastnost \"lookahead\" parseru není naplněna typem"
'Blame is reference, user must be an object': 'Blame je reference, uživatel musí být objekt'
'Unsupported entity.': 'Nepodporovaná entita.'
'Unsupported entity for PopsObserverResponse.': 'Nepodporovaná entita pro PopsObserverResponse.'
'Unsupported entity for PopsEventObserverResponse.': 'Nepodporovaná entita pro PopsEventObserverResponse.'
'Unsupported entity for PopsEventAttachmentObserverResponse.': 'Nepodporovaná entita pro PopsEventAttachmentObserverResponse.'
'Each filter must include a field, value, and type.': 'Každý filtr musí obsahovat pole, hodnotu a typ.'
'No API url configuration found for account #: #[VAR1]#': 'Nebyla nalezena konfigurace URL API pro účet #: #[VAR1]#'
'No API token configuration found for account #: #[VAR1]#': 'Nebyla nalezena konfigurace tokenu API pro účet #: #[VAR1]#'
'Invalid event: #[VAR1]#': 'Neplatná událost: #[VAR1]#'
'Invalid entity class: #[VAR1]#': 'Neplatná třída entity: #[VAR1]#'
'Missing entity ID for update endpoint.': 'Chybějící ID entity pro aktualizační koncový bod.'
'Missing entity ID for persist-attachment endpoint.': 'Chybějící ID entity pro koncový bod persist-attachment.'
'Missing entity ID for reporting endpoint.': 'Chybějící ID entity pro koncový bod reporting.'
'Missing entity ID for lock endpoint.': 'Chybějící ID entity pro koncový bod lock.'
'No API path configuration found for account #: #[VAR1]#': 'Nebyla nalezena konfigurace cesty API pro účet #: #[VAR1]#'
'Entity not found: #[VAR1]# #[VAR2]#': 'Entita nebyla nalezena: #[VAR1]# #[VAR2]#'
'Entity already has originalDbId: #[VAR1]#': 'Entita již má originalDbId: #[VAR1]#'
'#[VAR1]#: Invalid Input Value for Filter Type Between.': '#[VAR1]#: Nesprávná hodnota vstupu pro typ filtru Mezi.'
'#[VAR1]#: Invalid Filter Type for Var Type: #[VAR2]#': '#[VAR1]#: Neplatný typ filtru pro typ proměnné: #[VAR2]#'
'Invalid user instance.': 'Neplatná instance uživatele.'
'File "#[VAR1]#" not found.': 'Soubor "#[VAR1]#" nebyl nalezen.'
'Missing parent category #[VAR1]#.': 'Chybějící nadřazená kategorie #[VAR1]#.'
'Event category not set.': 'Kategorie události není nastavena.'
'User not set.': 'Uživatel není nastaven.'
'Source not set.': 'Zdroj není nastaven.'
'Type not set.': 'Typ není nastaven.'
'Priority not set.': 'Priorita není nastavena.'
'PopsEvent not set.': 'PopsEvent není nastaven.'
'POP insertedBy must be set.': 'POP insertedBy musí být nastaven.'
'Facility not set.': 'Objekt není nastaven.'
'Reporting not set.': 'Reporting není nastaven.'
'No enum #[VAR1]# found.': 'Nebyla nalezena žádná hodnota enum #[VAR1]#.'
'Invalid value #[VAR1]# for enum #[VAR2]#.': 'Neplatná hodnota #[VAR1]# pro enum #[VAR2]#.'
'Unknown phone number type "#[VAR1]#".': 'Neznámý typ telefonního čísla "#[VAR1]#".'
'The current validation does not concern an object.': 'Aktuální validace se netýká objektu.'
'Invalid property path "#[VAR2]#" provided to "#[VAR2]#" constraint: ': 'Neplatná proměnná "#[VAR2]#" pro omezení "#[VAR2]#": '
'Unable to use property path as the Symfony PropertyAccess component is not installed.': 'Nelze použít proměnnou jako cestu k vlastnosti, protože komponenta Symfony PropertyAccess není nainstalována.'
'No foreign key for set.': 'Žádný cizí klíč pro nastavení.'
'No loggable entity set.': 'Není nastavena žádná entita pro záznam.'
'Foreign key already set.': 'Cizí klíč již byl nastaven.'
'Log can be created within transaction only.': 'Záznam lze vytvořit pouze v rámci transakce.'
'Missing Notification.': 'Chybějící oznámení.'
'Notifier Message Send Failed: #[VAR1]#': 'Odesílání zprávy oznámení selhalo: #[VAR1]#'
'Notification message has failed to send on the gateway (#[VAR1]#) more times than allowed.': 'Zpráva oznámení selhala při odesílání na bránu (#[VAR1]#) vícekrát, než je povoleno.'
'Failed to send #[VAR1]# notification on Gateway: #[VAR2]# response code: #[VAR3]#': 'Odesílání #[VAR1]# oznámení na bránu se nezdařilo: #[VAR2]# kód odpovědi: #[VAR3]#'
'Invalid Notification.': 'Neplatné oznámení.'
'Missing HTTP client.': 'Chybějící HTTP klient.'
'SmsService gateway request failed.': 'Požadavek brány SmsService selhal.'
'SmsService API error: #[VAR1]#': 'Chyba API SmsService: #[VAR1]#'
'Unable to send the SMS: #[VAR1]#': 'Nelze odeslat SMS: #[VAR1]#'
'Invalid Email.': 'Neplatný e-mail.'
'Email Notification Send Failed: #[VAR1]#': 'Odesílání e-mailového oznámení selhalo: #[VAR1]#'
'Failed to send email notification: #[VAR1]#': 'Odesílání e-mailového oznámení se nezdařilo: #[VAR1]#'
'Email Notification send failed too many times (#[VAR1]#).': 'Odesílání e-mailového oznámení selhalo příliš mnohokrát (#[VAR1]#).'
'Invalid Phone Number.': 'Neplatné telefonní číslo.'
'Undefined Source Type: #[VAR1]#': 'Nedefinovaný typ zdroje: #[VAR1]#'
'User does not have MobileInfo registered.': 'Uživatel nemá registrovány informace o mobilním zařízení.'
'Unable to generate mapping for non-input class: #[VAR1]#': 'Nelze generovat mapování pro třídu, která není vstupní: #[VAR1]#'
'Parent category cannot be the same as the category itself.': 'Nadřazená kategorie nemůže být stejná jako kategorie samotná.'
'DomPDF failed to generate PDF.': 'DomPDF se nepodařilo vygenerovat PDF.'
'Facility missing alert interval.': 'Objekt chybí interval upozornění.'
'Invalid value POPS InsertedBy.': 'Neplatná hodnota POPS InsertedBy.'
'Attachment can only be uploaded on last Pops Event.': 'Příloha může být nahrána pouze na poslední událost Pops.'
'Attachment can only be deleted on last Pops Event.': 'Příloha může být smazána pouze na poslední událost Pops.'
"Facility doesn't belong to the account.": 'Objekt nepatří k účtu.'
'Pops already locked.': 'Pops je již uzamčen.'
'OriginalDbId of EventCategory can not be null on observed entity.': 'OriginalDbId události kategorie nemůže být null na pozorované entitě.'
'OriginalDbId of Pops can not be null on observed entity.': 'OriginalDbId Pops nemůže být null na pozorované entitě.'
'Facility originalDbId can not be null on observed entity.': 'OriginalDbId objektu nemůže být null na pozorované entitě.'
'Root EventCategory Not Found for accountId: #[VAR1]# and type: #[VAR2]#': 'Kořenová událost kategorie nebyla nalezena pro účet: #[VAR1]# a typ: #[VAR2]#'
'Invalid interval. End date has to be later than start date.': 'Neplatný interval. Datum konce musí být později než datum začátku.'
'Invalid interval. Current time has to be within POPS interval.': 'Neplatný interval. Aktuální čas musí být v rámci intervalu POPS.'
'Invalid interval. Pops already exists overlapping given interval.': 'Neplatný interval. Pops již existuje překrývající se s daným intervalem.'
'Invalid interval. Super PoPS already exists overlapping given interval.': 'Neplatný interval. Super PoPS již existuje překrývající se s daným intervalem.'
'#[VAR1]# is not an enum.': '#[VAR1]# není enum.'
'Invalid originalDbId format: #[VAR1]#': 'Neplatný formát originalDbId: #[VAR1]#'
'Cannot open resource.': 'Nelze otevřít zdroj.'
'Cannot get contents from resource.': 'Nelze získat obsah ze zdroje.'
'Unsupported file extension: #[VAR1]#': 'Nepodporovaná přípona souboru: #[VAR1]#'
'The user identifier cannot be empty.': 'Identifikátor uživatele nemůže být prázdný.'
'Personal number is required for user.': 'Osobní číslo je vyžadováno pro uživatele.'
'User password is required.': 'Heslo uživatele je vyžadováno.'
'Contact info is required for customer.': 'Kontaktní informace jsou vyžadovány pro zákazníka.'
'Customer email is required.': 'E-mail zákazníka je vyžadován.'
'Customer password is required.': 'Heslo zákazníka je vyžadováno.'
'Facility not found for account #[VAR1#]': 'Objekt nebyl nalezen pro účet #[VAR1#]'
'Event category not found.': 'Kategorie události nebyla nalezena.'
'User with given email already exists.': 'Uživatel s daným e-mailem již existuje.'
'User must be instance of User, #[VAR1]# given.': 'Uživatel musí být instancí třídy User, byl zadán #[VAR1]#.'
'Migration of admin is not supported yet.': 'Migrace administrátora zatím není podporována.'
'This method is not supported.': 'Tato metoda není podporována.'
'Username could not be found.': 'Uživatelské jméno nebylo nalezeno.'
'Value of #[VAR1]# is missing.': 'Hodnota #[VAR1]# chybí.'
'Value of #[VAR1]# must be #[VAR2]#.': 'Hodnota #[VAR1]# musí být #[VAR2]#.'
'Invalid filter type #[VAR1]#.': 'Neplatný typ filtru #[VAR1]#.'
'Expected argument of type "#[VAR1]#", "#[VAR2]#" given.': 'Očekáván argument typu "#[VAR1]#", byl zadán "#[VAR2]#".'
'The "#[VAR1]#" transport only supports instances of "#[VAR2]#" (instance of "#[VAR3]#" given).': 'Transport "#[VAR1]#" podporuje pouze instance "#[VAR2]#" (byla zadána instance "#[VAR3]#").'
'The data must belong to a backed enumeration of type Pm\\Core\\Enum\\FilterTypeEnum': 'Data musí patřit k podporované enumeraci typu Pm\\Core\\Enum\\FilterTypeEnum'
'This value should be of type string.': 'Tato hodnota by měla být typu řetězec.'
'Invalid json message received': 'Byla přijata neplatná JSON zpráva'
'No Facility ##[VAR1]# found.': 'Nebyl nalezen žádný objekt ##[VAR1]#.'
'No Facility group ##[VAR1]# found.': 'Nebyla nalezena žádná skupina objektů ##[VAR1]#.'
'No account ##[VAR1]# found.': 'Nebyl nalezen žádný účet ##[VAR1]#.'
'Invalid credentials.': 'Neplatné přihlašovací údaje.'
'Notification ##[VAR1]# not found.': 'Oznámení ##[VAR1]# nebylo nalezeno.'
'No permissionApplication ##[VAR1]# found.': 'Nebyla nalezena žádná permissionApplication ##[VAR1]#.'
'No permission ##[VAR1]# found.': 'Nebyla nalezena žádná permission ##[VAR1]#.'
'No permission group ##[VAR1]# found.': 'Nebyla nalezena žádná skupina oprávnění ##[VAR1]#.'
'No permission template ##[VAR1]# found.': 'Nebyla nalezena žádná šablona oprávnění ##[VAR1]#.'
'No City ##[VAR1]# found.': 'Nebylo nalezeno žádné město ##[VAR1]#.'
'No EventCategory ##[VAR1]# found.': 'Nebyla nalezena žádná kategorie události ##[VAR1]#.'
'No EventCategory by eventNumber: ##[VAR1]# found.': 'Nebyla nalezena žádná kategorie události podle čísla události: ##[VAR1]#.'
'No PopsAlert by ID ##[VAR1]# found.': 'Nebylo nalezeno žádné PopsAlert podle ID ##[VAR1]#.'
'No PopsAlert by Pops ##[VAR1]# found.': 'Nebylo nalezeno žádné PopsAlert podle Pops ##[VAR1]#.'
'No Pops Attachment ##[VAR1]# found.': 'Nebyla nalezena žádná příloha Pops ##[VAR1]#.'
'No Pops Attachment #[VAR1]# found.': 'Nebyla nalezena žádná příloha Pops #[VAR1]#.'
'No Pops Event ##[VAR1]# found.': 'Nebyla nalezena žádná událost Pops ##[VAR1]#.'
'No Pops ##[VAR1]# found.': 'Nebyl nalezen žádný Pops ##[VAR1]#.'
'No SuperPops ##[VAR1]# found.': 'Nebyl nalezen žádný SuperPops ##[VAR1]#.'
'Too many attachments on Pops Event.': 'Příliš mnoho příloh na události Pops.'
'Cannot change Pops Event datetime outside of Pops start and end.': 'Nelze změnit datum a čas události Pops mimo začátek a konec Pops.'
'Cannot change order of Pops Events in Pops.': 'Nelze změnit pořadí událostí Pops v Pops.'
'Pops ##[VAR1]# is locked.': 'Pops ##[VAR1]# je uzamčen.'
'Super Pops ##[VAR1]# is locked.': 'Super Pops ##[VAR1]# je uzamčen.'
'Account with ID #[VAR1]# is not allowed to access requested data.': 'Účet s ID #[VAR1]# nemá povoleno přístup k požadovaným datům.'
'No Tenant Group ##[VAR1]# found.': 'Nebyla nalezena žádná skupina nájemců ##[VAR1]#.'
'No tenant ##[VAR1]# found.': 'Nebyl nalezen žádný nájemce ##[VAR1]#.'
'Can not read file: #[VAR1]#': 'Nelze číst soubor: #[VAR1]#'
'Invalid file #[VAR1]# with size #[VAR2]#MB. Max file size is #[VAR3]#MB.': 'Neplatný soubor #[VAR1]# o velikosti #[VAR2]#MB. Maximální velikost souboru je #[VAR3]#MB.'
'Mime type not detected. Invalid image data. Try another format (jpg, png, webp, svg).': 'Typ MIME nebyl detekován. Neplatná data obrázku. Zkuste jiný formát (jpg, png, webp, svg).'
'SVG sanitize failed. Try to use another image format. Try another format (jpg, png, webp, svg).': 'Sanitace SVG selhala. Zkuste použít jiný formát obrázku. Zkuste jiný formát (jpg, png, webp, svg).'
'No user ##[VAR1]# found.': 'Nebyl nalezen žádný uživatel ##[VAR1]#.'
'No user #[VAR1]# found.': 'Nebyl nalezen žádný uživatel #[VAR1]#.'
'Pops Event datetime cannot be in the future.': 'Datum a čas události Pops nemohou být v budoucnosti.'
'Some events are not included in the Pops interval.': 'Některé události nejsou zahrnuty v intervalu PoPS.'
'No facility can be added to Super PoPS.': 'Žádný objekt nemůže být přidán do Super PoPS.'
'Cannot create pops for another account.': 'Nelze vytvořit pops pro jiný účet.'
'Pops Event description cannot be empty.': 'Pops Popis události nemůže být prázdný.'
'Invalid interval. Active PoPS cannot last longer than #[VAR1]# hours.': 'Neplatný interval. Aktivní PoPS nemůže trvat déle než #[VAR1]# hodin.'
'No attachments uploaded on Pops Event.': 'Na událost nebyly nahrány žádné přílohy.'
'This name is already used.': 'Toto jméno je již použito'
'Currency name cannot be empty.': 'Název měny nemůže být prázdný.'
'Currency abbreviation cannot be empty.': 'Zkratka měny nemůže být prázdná.'
'Currency with the same name or abbreviation already exists.': 'Měna se stejným názvem nebo zkratkou již existuje.'
'Contract name cannot be empty.': 'Název smlouvy nemůže být prázdný.'
'Contract code cannot be empty.': 'Kód smlouvy nemůže být prázdná.'
'Contract with the same name or code already exists.': 'Smlouva se stejným názvem nebo kódem již existuje.'
'Project name cannot be empty.': 'Název projektu nemůže být prázdný.'
'Project code cannot be empty.': 'Kód projektu nemůže být prázdná.'
'Project with the same name or code already exists.': 'Projekt se stejným názvem nebo kódem již existuje.'
'Legislation name cannot be empty.': 'Název legislativy nemůže být prázdný.'
'Legislation code cannot be empty.': 'Kód legislativy nemůže být prázdný.'
'Legislation with the same name or code already exists.': 'Legislativa se stejným názvem nebo kódem již existuje.'
'Cannot delete facility because it is used in other parts of the system.': 'Nelze smazat objekt, protože je zadán v jiných částech systému.'
'Company with ID ##[{VAR1}]# not found.': 'Firma s ID ##[{VAR1}]# nebyla nalezena.'
'Contract with ID ##[{VAR1}]# not found.': 'Zakázka s ID ##[{VAR1}]# nebyla nalezena.'
'Project with ID ##[{$VAR1}]# not found.': 'Projekt s ID ##[{VAR1}]# nebyl nalezen.'
'Unit with the same name or code already exists.': 'Jednotka se stejným názvem nebo kódem již existuje.'
'Unit name cannot be empty.': 'Název jednotky nemůže být prázdný.'
'Unit code cannot be empty.': 'Kód jednotky nemůže být prázdný.'
'Company name already exists': 'Název společnosti již existuje.'
'Company abbr already exists': 'Zkratka společnosti již existuje.'
'Company code already exists': 'Kód společnosti již existuje.'
'Expected instance of Tenant.': 'Očekávaná instance nájemce.'
'Facility ID cannot be null.': 'ID objektu nemůže být null.'
'Tenant group name cannot be empty.': 'Název skupiny klientů nemůže být prázdný.'
'At least one tenant must be provided to create a tenant group.': 'Pro vytvoření skupiny nájemců musí být zadán alespoň jeden nájemce.'
'A group with the same name already exists.': 'Skupina se stejným názvem již existuje.'
'Entity with ID {id} not found.': 'Entity s ID {id} nebyl nalezen.'
