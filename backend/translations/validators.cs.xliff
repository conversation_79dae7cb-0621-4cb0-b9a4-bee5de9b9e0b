<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
    <file datatype="plaintext" original="validators.cs.xliff" source-language="en" target-language="cs">
        <body>
            <trans-unit id="validation.field.required">
                <source>validation.field.required</source>
                <target>Zadaná hodnota pro pole {{ property }} je povinná.</target>
            </trans-unit>
            <trans-unit id="validation.field.max_length">
                <source>validation.field.max_length</source>
                <target>Zadaná hodnota pro pole {{ property }} nesmí být delší než {{ limit }} znaků.</target>
            </trans-unit>
            <trans-unit id="validation.email.invalid">
                <source>validation.email.invalid</source>
                <target>Zadaná hodnota pro pole {{ property }} není platná e-mailová adresa.</target>
            </trans-unit>
            <trans-unit id="validation.field.unique">
                <source>validation.field.unique</source>
                <target>Zadaná hodnota pro pole {{ property }} již existuje.</target>
            </trans-unit>
            <trans-unit id="validation.phone.invalid">
                <source>validation.phone.invalid</source>
                <target>Zadaná hodnota pro pole {{ property }} není platné telefonní číslo.</target>
            </trans-unit>
            <trans-unit id="validation.field.min_length">
                <source>validation.field.min_length</source>
                <target>Zadaná hodnota pro pole {{ property }} je příliš krátká. Musí mít alespoň {{ limit }} znaků.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
