#!/bin/sh

if [ "${ENTRYPOINT_CONFIG}" = "true" ]; then
    echo "running special entrypoint config steps"

    mkdir -p config/jwt
    echo $JWT_PRIVATE_KEY_B64 | base64 -d > config/jwt/private.pem
    echo $JWT_PUBLIC_KEY_B64 | base64 -d > config/jwt/public.pem

    gosu www-data:www-data php bin/console assets:install
    gosu www-data:www-data composer dump-autoload || true
else
    echo "skipping special entrypoint config steps"
fi

if [ "${ENTRYPOINT_MIGRATIONS}" = "true" ]; then
    echo "running migrations"
    php bin/console doctrine:migrations:migrate --no-interaction
    php bin/console rabbitmq:setup-fabric --debug
else
    echo "skipping migrations"
fi

chown www-data:www-data -R var

if [ -n "${ENTRYPOINT_COMMAND}" ]; then
    echo "ENTRYPOINT_COMMAND is set. Running: ${ENTRYPOINT_COMMAND}"
    exec sh "${ENTRYPOINT_COMMAND}"
else
    echo "ENTRYPOINT_COMMAND is empty. Running php-fpm..."
    php-fpm
fi