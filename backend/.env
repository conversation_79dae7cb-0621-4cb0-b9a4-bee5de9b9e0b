###> symfony/framework-bundle ###
APP_ENV=prod
APP_SECRET=2fbdfa010b97d49cc205c6db88f628f3
APP_POPS_ALERT_DURATION_MINUTES=5
ACTIVITY_ACKNOWLEDGED_EVENT_CATEGORY_NUMBER=1000
ACTIVITY_ACKNOWLEDGED_EVENT_CATEGORY_NAME='User Activity Acknowledged'
POPS_EVENT_NOT_CREATED_CATEGORY_NUMBER=252
APP_NOTIFICATION_MAX_SEND_ATTEMPTS=5
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
DATABASE_URL=********************************/pm?charset=utf8&sslmode=prefer
###< doctrine/doctrine-bundle ###

###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=semaphore
###< symfony/lock ###

###> sentry/sentry-symfony ###
SENTRY_DSN=
###< sentry/sentry-symfony ###

RABBITMQ_HOST=rabbit
RABBITMQ_DEFAULT_USER=rabbitmq
RABBITMQ_DEFAULT_PASS=rabbitmqy
RABBITMQ_DEFAULT_VHOST=pm
RABBITMQ_DEFAULT_PORT=5672

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=****************************************************************
JWT_TTL=3600
###< lexik/jwt-authentication-bundle ###

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

AWS_S3_ACCESS_ID=''
AWS_S3_ACCESS_SECRET=''

APP_UPLOADS_SOURCE=storage.local

###> sms-service ###
SMSSERVICE_DSN='smsservice://<EMAIL>/'
###< sms-service ###

###> symfony/firebase-notifier ###
FIREBASE_DSN=firebase://TOKEN@default
###< symfony/firebase-notifier ###

###> symfony/mailer ###
MAILER_DSN=null://null
###< symfony/mailer ###

###> symfony/amazon-mailer ###
# MAILER_DSN=ses://ACCESS_KEY:SECRET_KEY@default?region=eu-west-1
# MAILER_DSN=ses+smtp://ACCESS_KEY:SECRET_KEY@default?region=eu-west-1
MAILER_FROM='PM3 <<EMAIL>>'
###< symfony/amazon-mailer ###

###> symfony/mercure-bundle ###
# The URL of the Mercure hub, used by the app to publish updates (can be a local URL)
MERCURE_URL=http://mercure:4000/.well-known/mercure
# The public URL of the Mercure hub, used by the browser to connect
MERCURE_PUBLIC_URL=https://localhost:4001/.well-known/mercure
# The secret used to sign the JWTs
MERCURE_JWT_SECRET=developmentSecretHaveToBeChangedOnProduction!
MERCURE_PUBLISHER_JWT_KEY=developmentSecretHaveToBeChangedOnProduction!
MERCURE_SUBSCRIBER_JWT_KEY=developmentSecretHaveToBeChangedOnProduction!
MERCURE_JWT_TOKEN=eyJhbGciOiJIUzI1NiJ9.eyJtZXJjdXJlIjp7InB1Ymxpc2giOlsiKiJdLCJzdWJzY3JpYmUiOlsiKiJdLCJwYXlsb2FkIjp7InVzZXIiOiJodHRwczovL2V4YW1wbGUuY29tL3VzZXJzL2R1bmdsYXMiLCJyZW1vdGVBZGRyIjoiMTI3LjAuMC4xIn19fQ.iYuDR0kCpMKqYQg-2czhQVikpu0akO8iJzvlFFcUyZU
###< symfony/mercure-bundle ###

ENTITY_OBSERVER_QUEUE_NAME='observed_entities'
ENTITY_OBSERVER_EXCHANGE_NAME='entity_observer'

# Optionally dis/allow API endpoints based on environment. "false" means disallowed.
# createAccountAction is disallowed by default. Use it for testing purposes only.
API_OPTIONAL_ENDPOINT='{"AccountController_createAccountAction": "false"}'

PM2_API_ACCOUNT_CS_TOKEN=''
PM2_API_ACCOUNT_CS_URL='https://operation-tst.m2c.eu'
PM2_API_ACCOUNT_CS_PATH='/api-pm3'

REDIS_URL=redis://pm-redis:6379/0
