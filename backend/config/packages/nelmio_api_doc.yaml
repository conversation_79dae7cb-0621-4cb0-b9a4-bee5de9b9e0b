nelmio_api_doc:
  type_info: true
  documentation:
    components:
      securitySchemes:
        Bearer:
          type: http
          scheme: bearer
          bearerFormat: JWT
      security:
        - Bearer: [ ]
  areas: # to filter documented areas
    default:
      path_patterns:
        - ^/api(?!/doc) # Accepts routes under /api except /api/doc
      documentation:
        info:
          title: M2C PM3 API
          version: 1.0.0
