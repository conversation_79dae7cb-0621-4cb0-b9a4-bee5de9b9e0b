security:
    role_hierarchy:
        ROLE_ADMIN: ['ROLE_USER']
        ROLE_USER: []
        ROLE_CUSTOMER_USER: []


    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        legacy:
            algorithm: sha1
            encode_as_base64: false
            iterations: 1
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
            algorithm: bcrypt
            migrate_from:
                - legacy
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        app_user_provider:
            entity:
                class: Pm\User\Entity\User
                property: identifier
        user_migrate_provider:
            id: Pm\User\Security\UserMigrateProvider
    firewalls:
        login:
            pattern: ^/api/v1/auth/token$
            provider: user_migrate_provider
            stateless: true
            json_login:
                check_path: /api/v1/auth/token
                username_path: identifier
                failure_handler: lexik_jwt_authentication.handler.authentication_failure
                success_handler: lexik_jwt_authentication.handler.authentication_success
        api:
            pattern: ^/api/v1
            stateless: true
            provider: app_user_provider
            entry_point: jwt
            logout:
                path: /api/v1/auth/logout
            refresh_jwt:
                check_path: /api/v1/auth/token/refresh
            jwt: ~


            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/api/v1/auth/init$, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/auth/token/refresh, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/auth/token$, roles: PUBLIC_ACCESS }
        - { path: ^/api,  roles: IS_AUTHENTICATED_FULLY }

when@test:
    security:
        password_hashers:
            legacy:
                algorithm: sha1
                encode_as_base64: false
                iterations: 1
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: bcrypt
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10
                migrate_from:
                    - legacy
