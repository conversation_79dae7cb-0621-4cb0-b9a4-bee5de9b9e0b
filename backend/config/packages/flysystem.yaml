# Read the documentation at https://github.com/thephpleague/flysystem-bundle/blob/master/docs/1-getting-started.md
flysystem:
    storages:
        storage.local:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/storage/default'
        storage.aws:
          adapter: 'aws'
          visibility: private
          options:
            client: 'Aws\S3\S3Client'
            bucket: '%env(AWS_S3_BUCKET)%'
            prefix: ''
            streamReads: true
        storage:
          adapter: 'lazy'
          options:
            source: '%env(APP_UPLOADS_SOURCE)%'
