framework:
    notifier:
        texter_transports:
            smsservice: '%env(SMSSERVICE_DSN)%'
#        chatter_transports:
#            firebase: '%env(FIREBASE_DSN)%'
        channel_policy:
            # use chat/slack, chat/telegram, sms/twilio or sms/nexmo
            urgent: ['sms','chat']
            high: ['sms','chat']
            medium: ['sms','chat']
            low: ['email']
        admin_recipients:
            - { email: <EMAIL> }
