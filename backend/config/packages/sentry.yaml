sentry:
  dsn: '%env(SENTRY_DSN)%'
  register_error_listener: false # Disables the ErrorListener to avoid duplicated log in sentry
  options:
    traces_sample_rate: 1
    ignore_exceptions:
      - 'Symfony\Component\HttpKernel\Exception\BadRequestHttpException'
      - 'Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException'
      - 'Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException'
      - 'Symfony\Component\HttpKernel\Exception\ConflictHttpException'
