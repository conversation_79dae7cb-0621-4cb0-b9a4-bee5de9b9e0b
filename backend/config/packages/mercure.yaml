mercure:
    hubs:
        default:
            url: '%env(MERCURE_URL)%'
            public_url: '%env(MERCURE_PUBLIC_URL)%'
            jwt:
                secret: '%env(MERCURE_PUBLISHER_JWT_KEY)%'
                publish: '*'

when@test:
  mercure:
    hubs:
      default:
        url: '%env(MERCURE_URL)%'
        public_url: '%env(MERCURE_PUBLIC_URL)%'
        jwt:
          secret: '%env(MERCURE_PUBLISHER_JWT_KEY)%'
          publish: '*'
          subscribe: '*'
