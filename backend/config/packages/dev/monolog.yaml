monolog:
  channels: [deprecation]
  handlers:
    main:
      type: stream
      path: "php://stderr"
      level: debug
      channels: ["!event", "!doctrine", "!console", "!deprecation"]
    console:
      type: console
      process_psr_3_messages: false
      channels: ["!event", "!doctrine", "!console", "!deprecation"]
    deprecation:
      type: stream
      path: "php://stderr"
      level: debug
      channels: [deprecation]
    # Uncomment to log doctrine queries in development
    # doctrine:
    #   type: stream
    #   path: "php://stderr"
    #   level: debug
    #   channels: [doctrine]
