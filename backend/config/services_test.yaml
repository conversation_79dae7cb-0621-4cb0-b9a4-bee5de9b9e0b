services:
  _defaults:
    public: true
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: false # Automatically registers your services as commands, event subscribers, etc.
    bind:
      $container: '@service_container'

  _instanceof:
    Pm\Core\Command\BaseCommand:
      tags:
        - console.command
    Pm\Core\Controller\BaseController:
      tags:
        - controller.service_arguments

  Pm\Core\Controller\ApiDocController:
