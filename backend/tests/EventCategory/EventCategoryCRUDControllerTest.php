<?php

declare(strict_types = 1);

namespace Pm\Tests\EventCategory;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Core\Test\Builder\EventCategoryBuilder;
use Pm\EventCategory\Entity\EventCategory;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class EventCategoryCRUDControllerTest extends TestCase
{

    public function testCreateEventCategory(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_EVENT_CATEGORY_CREATE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $parameters = [
            'label' => 'Test Category',
            'is_hidden' => false,
            'code' => 'cat-360',
            'parent_category' => null,
            'severity' => 'CRITICAL',
            'translations' => [
                [
                    'locale' => 'cs',
                    'localizations' => [
                        [
                            'field' => 'label',
                            'value' => 'cesky',
                            ],
                    ],
                ],
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/event-category', content: Json::encode($parameters));

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(201, $responseData['code']);

        self::assertSame($parameters['label'], $responseData['data']['label']);
        self::assertSame($parameters['code'], $responseData['data']['code']);
        self::assertSame($parameters['severity'], $responseData['data']['severity']);
        self::assertSame($parameters['translations'][0]['localizations'][0]['value'], $responseData['data']['translations'][0]['localizations'][0]['value']);
        self::assertNotNull($responseData['data']['id']);
    }

    public function testUpdateEventCategory(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_EVENT_CATEGORY_EDIT,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        /**
         * @var EventCategoryBuilder $eventCategoryBuilder
         */
        $eventCategoryBuilder = $this->getObjectFactory()->getEventCategoryBuilder();
        $eventCategoryBuilder->withAccount($user->getAccount());

        $eventCategory = $eventCategoryBuilder->build();
        $this->getEntityManager()->persist($eventCategory);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear(EventCategory::class);

        $parameters = [
            'code' => '361',
            'label' => 'test post changed',
            'severity' => 'MEDIUM',
            'parent_category' => $eventCategory->getParentCategory() instanceof EventCategory ? $eventCategory->getParentCategory()->getId() : null,
            'is_hidden' => false,
            'translations' => [
                [
                    'locale' => 'cs',
                    'localizations' => [
                        [
                            'field' => 'label',
                            'value' => 'ceskyedit',
                            ],
                    ],
                ],
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, '/api/v1/event-category/' . $eventCategory->getId(), content: Json::encode($parameters));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);
        self::assertSame($parameters['label'], $responseData['data']['label']);
        self::assertSame($parameters['code'], $responseData['data']['code']);
        self::assertSame($parameters['severity'], $responseData['data']['severity']);
        self::assertSame($parameters['translations'][0]['localizations'][0]['value'], $responseData['data']['translations'][0]['localizations'][0]['value']);
    }

    public function testDeleteEventCategory(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_EVENT_CATEGORY_DELETE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $eventCategoryParent = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withAccount($user->getAccount())
            ->build();
        $this->getEntityManager()->persist($eventCategoryParent);
        $this->getEntityManager()->flush();

        $eventCategory = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withAccount($user->getAccount())
            ->build();
        $this->getEntityManager()->persist($eventCategory);
        $eventCategory->setParentCategory($eventCategoryParent);
        $this->getEntityManager()->flush();

        $this->getEntityManager()->clear(EventCategory::class);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/event-category/' . $eventCategoryParent->getId());
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);

        self::assertSame('event-category-deleted', $responseData['message']);

        $this->getEntityManager()->clear();
        $eventCategoryParent = $this->refreshEntity($eventCategoryParent);
        self::assertInstanceOf(EventCategory::class, $eventCategoryParent);
        self::assertFalse($eventCategoryParent->isActive());
        $eventCategory = $this->refreshEntity($eventCategory);
        self::assertInstanceOf(EventCategory::class, $eventCategory);
        self::assertFalse($eventCategory->isActive());
    }

    public function testDeleteEventCategoryNotAllowed(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_EVENT_CATEGORY_SHOW,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        /**
         * @var EventCategoryBuilder $eventCategoryBuilder
         */
        $eventCategoryBuilder = $this->getObjectFactory()->getEventCategoryBuilder();
        $eventCategoryBuilder->withAccount($user->getAccount());

        $eventCategory = $eventCategoryBuilder->build();
        $this->getEntityManager()->persist($eventCategory);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/event-category/' . $eventCategory->getId());
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testUpdatePermissionTemplateNotAllowed(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_EVENT_CATEGORY_SHOW,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        /**
         * @var EventCategoryBuilder $eventCategoryBuilder
         */
        $eventCategoryBuilder = $this->getObjectFactory()->getEventCategoryBuilder();
        $eventCategoryBuilder->withAccount($user->getAccount());

        $eventCategory = $eventCategoryBuilder->build();
        $this->getEntityManager()->persist($eventCategory);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $parameters = [
            'code' => '361',
            'label' => 'test post changed',
            'severity' => 'MEDIUM',
            'parent_category' => $eventCategory->getParentCategory() instanceof EventCategory ? $eventCategory->getParentCategory()->getId() : null,
            'is_hidden' => false,
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, '/api/v1/event-category/' . $permissionTemplate->getId(), content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

}
