<?php

declare(strict_types = 1);

namespace Pm\Tests\EventCategory;

use Pm\Core\Http\RequestMethod;
use Pm\Tests\TestCase;
use function array_column;
use function count;
use function http_build_query;
use function stripos;

class EventCategoryControllerTest extends TestCase
{

    public function testListWithOneFound(): void
    {
        $admin = $this->prepareAdmin();
        $client = $this->createClientWithUser($admin);

        $eventCategoryNameToSearch = 'test';
        $eventCategory = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withName($eventCategoryNameToSearch . ' ab') // suffix added
            ->withAccount($admin->getAccount())
            ->build();

        $this->getEntityManager()->flush();

        $params = [
            'search' => $eventCategoryNameToSearch,
            'language' => 'en',
        ];
        $url = http_build_query($params);
        $client->request(
            RequestMethod::GET,
            "/api/v1/event-category?{$url}",
        );

        $response = $this->assertResponseSuccess($client);

        self::assertCount(1, $response['list']);
        self::assertSame($eventCategory->getId(), $response['list'][0]['id']);
        self::assertSame($eventCategory->getCode(), $response['list'][0]['code']);
    }

    public function testListInvalidPermission(): void
    {
        $admin = $this->prepareAdmin();
        $client = $this->createClientWithUser($admin);

        $eventCategoryNameToSearch = 'test';
        $this->getObjectFactory()->getEventCategoryBuilder()
            ->withName($eventCategoryNameToSearch . ' ab') // suffix added
            ->withAccount($admin->getAccount())
            ->build();

        $anotherAccount = $this->getObjectFactory()->getAccountBuilder()->build();

        $this->getEntityManager()->flush();

        $params = [
            'accountId' => $anotherAccount->getId(),
            'search' => $eventCategoryNameToSearch,
            'language' => 'en',
        ];
        $url = http_build_query($params);
        $client->request(
            RequestMethod::GET,
            "/api/v1/event-category?{$url}",
        );

        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testEmptyList(): void
    {
        $admin = $this->prepareAdmin();
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $this->refreshEntity($admin);
        $client = $this->createClientWithUser($admin);

        $params = [
            'search' => 'whatever',
            'language' => 'en',
        ];
        $url = http_build_query($params);
        $client->request(
            RequestMethod::GET,
            "/api/v1/event-category?{$url}",
        );

        $response = $this->assertResponseSuccess($client);
        self::assertCount(0, $response['list']);
    }

    public function testCategoryTree(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();

        $this->getObjectFactory()->getEventCategoryBuilder()->buildTree($account);
        $this->getEntityManager()->flush();

        $client = $this->createClientWithUser($user);
        $client->request(
            RequestMethod::GET,
            '/api/v1/event-category-tree',
            [
                'search' => 'nál',
            ],
        );

        $response = $this->assertResponseSuccess($client);

        $foundCategories = [];
        $this->findCategoriesInTree($response['data'], 'nál', $foundCategories);

        self::assertCount(3, $foundCategories);

        $labels = array_column($foundCategories, 'label');
        self::assertContains('nálevka', $labels);
        self::assertContains('Rvačka - nález', $labels);
        self::assertContains('Odchod personálu', $labels);

        foreach ($foundCategories as $category) {
            self::assertTrue($category['search_match']);
        }
    }

    /**
     * @param array<mixed> $tree
     * @param array<mixed> $found
     */
    private function findCategoriesInTree(array $tree, string $search, array &$found): void
    {
        foreach ($tree as $category) {
            if (stripos((string) $category['label'], $search) !== false) {
                $found[] = $category;
            }
            if (count($category['children']) > 0) {
                $this->findCategoriesInTree($category['children'], $search, $found);
            }
        }
    }

    public function testCategoryTreeSearchCode(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();

        $this->getObjectFactory()->getEventCategoryBuilder()->buildTree($account);
        $this->getEntityManager()->flush();

        $client = $this->createClientWithUser($user);
        $client->request(
            RequestMethod::GET,
            '/api/v1/event-category-tree',
            [
                'search' => '1',
            ],
        );

        $response = $this->assertResponseSuccess($client);

        self::assertSame('Všechny události', $response['data'][0]['label']);
        self::assertTrue($response['data'][0]['search_match']);
    }

    /**
     * @return void
     * test loading tree with translations for editing
     */
    public function testCategoryTreeEdit(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();

        $this->getObjectFactory()->getEventCategoryBuilder()->buildTree($account);
        $this->getEntityManager()->flush();

        $client = $this->createClientWithUser($user);
        $client->request(
            RequestMethod::GET,
            '/api/v1/event-category-tree-edit',
            [
                'search' => 'nál',
            ],
        );

        $response = $this->assertResponseSuccess($client);

        $foundCategories = [];
        $this->findCategoriesInTree($response['data'], 'nál', $foundCategories);

        self::assertCount(3, $foundCategories);

        $labels = array_column($foundCategories, 'label');
        self::assertContains('nálevka', $labels);
        self::assertContains('Rvačka - nález', $labels);
        self::assertContains('Odchod personálu', $labels);

        foreach ($foundCategories as $category) {
            self::assertTrue($category['search_match']);
        }
    }

    public function testCategoryTreeResultWithRootCategory(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();
        $this->getObjectFactory()->getEventCategoryBuilder()->buildTree($account);
        $this->getEntityManager()->flush();

        $client = $this->createClientWithUser($user);
        $client->request(
            RequestMethod::GET,
            '/api/v1/event-category-tree',
            [
                'search' => 'O',
            ],
        );

        $response = $this->assertResponseSuccess($client);

        self::assertSame('Všechny události', $response['data'][0]['label']);

        $foundCategories = [];
        $this->findCategoriesInTree($response['data'], 'O', $foundCategories);

        self::assertGreaterThanOrEqual(1, count($foundCategories));

        $labels = array_column($foundCategories, 'label');
        self::assertContains('Monitoring', $labels);

        foreach ($foundCategories as $category) {
            if (stripos((string) $category['label'], 'O') !== false) {
                self::assertTrue($category['search_match']);
            }
        }
    }

    public function testListSearch(): void
    {
        $admin = $this->prepareAdmin();
        $client = $this->createClientWithUser($admin);

        $eventCategory = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withName('Požár')
            ->withAccount($admin->getAccount())
            ->build();

        $this->getEntityManager()->flush();

        $client->request(
            RequestMethod::GET,
            '/api/v1/event-category?search=pžr',
        );

        $response = $this->assertResponseSuccess($client);

        self::assertCount(1, $response['list']);
        self::assertSame($eventCategory->getLabel(), $response['list'][0]['label']);
    }

    public function testFetchEventCategoryById(): void
    {
        $admin = $this->prepareAdmin();
        $client = $this->createClientWithUser($admin);

        $this->getObjectFactory()->getEventCategoryBuilder()
            ->withName('Požár')
            ->withAccount($admin->getAccount())
            ->build();

        $this->getEntityManager()->flush();

        $requestData = [
            'offset' => 0,
            'limit' => 20,
            'filter' => [
                [
                    'field' => 'id',
                    'value' => 1,
                    'type' => 'match',
                ],
            ],
            'sortBy' => 'id',
            'sortMethod' => 'DESC',
            'nullOrIsLocked' => true,
        ];

        $parameters = http_build_query($requestData);
        $client->request(RequestMethod::GET, "/api/v1/event-category?{$parameters}");
        $response = $this->assertResponseSuccess($client);

        self::assertSame(1, $response['list'][0]['id']);
        self::assertSame('Požár', $response['list'][0]['label']);
    }

    public function testFetchEventCategoryByParentId(): void
    {
        $admin = $this->prepareAdmin();
        $client = $this->createClientWithUser($admin);

        $parentCategory = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withName('Parent Category')
            ->withAccount($admin->getAccount())
            ->build();

        $childCategory = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withName('Child Category')
            ->withAccount($admin->getAccount())
            ->build();

        $childCategory->setParentCategory($parentCategory);
        $this->getEntityManager()->flush();

        $requestData = [
            'offset' => 0,
            'limit' => 20,
            'filter' => [
                [
                    'field' => 'parentCategory.id',
                    'value' => $parentCategory->getId(),
                    'type' => 'match',
                ],
            ],
            'sortBy' => 'id',
            'sortMethod' => 'DESC',
            'nullOrIsLocked' => true,
        ];

        $parameters = http_build_query($requestData);
        $client->request('GET', "/api/v1/event-category?{$parameters}");
        $response = $this->assertResponseSuccess($client);

        self::assertSame($childCategory->getId(), $response['list'][0]['id']);
        self::assertSame('Child Category', $response['list'][0]['label']);
        self::assertSame($parentCategory->getId(), $response['list'][0]['parent_id']);
    }

}
