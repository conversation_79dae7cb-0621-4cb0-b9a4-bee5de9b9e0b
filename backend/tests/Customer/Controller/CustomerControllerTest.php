<?php

declare(strict_types = 1);

namespace Pm\Tests\Customer\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use function http_build_query;

class CustomerControllerTest extends TestCase
{

    public function testGetCustomerList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $customer1 = $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer A')
            ->setCid('CID001')
            ->build();
        $customer2 = $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer B')
            ->setCid('CID002')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/customer');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'], 'Response data is null');
        self::assertCount(2, $responseData['list']);
        self::assertSame($customer1->getName(), $responseData['list'][0]['name']);
        self::assertSame($customer2->getName(), $responseData['list'][1]['name']);
    }

    public function testGetListFilterName(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer A')
            ->setCid('CID001')
            ->build();
        $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer B')
            ->setCid('CID002')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $refreshedUser = $this->refreshEntity($user);

        // Ensure $user is not null before passing it to createClientWithUser
        self::assertNotNull($refreshedUser, 'User should not be null');

        /** @var User $refreshedUser */
        $refreshedUser = $refreshedUser;

        $client = $this->createClientWithUser($refreshedUser);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'ASC',
            'filter' => [
                [
                    'field' => 'name',
                    'value' => 'Customer A',
                    'type' => 'match',
                ],
            ],
        ];

        $client->request(RequestMethod::GET, '/api/v1/customer?' . http_build_query($requestData));
        $responseData = $this->assertResponseSuccess($client);

        self::assertCount(1, $responseData['list']);
        self::assertSame('Customer A', $responseData['list'][0]['name']);
    }

    public function testCreateCustomer(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Customer C',
            'cid' => 'CID003',
            'country' => 'Czech Republic',
            'city' => 'Prague',
        ];

        $client->request(RequestMethod::POST, '/api/v1/customer', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Customer C', $responseData['data']['name']);
        self::assertSame('CID003', $responseData['data']['cid']);
        self::assertSame('Czech Republic', $responseData['data']['country']);
        self::assertSame('Prague', $responseData['data']['city']);
    }

    public function testCreateCustomerWithFacilities(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setName('Facility 1')
            ->setCode('FAC001')
            ->withoutPermission()
            ->build();
        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setName('Facility 2')
            ->setCode('FAC002')
            ->withoutPermission()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Customer D',
            'cid' => 'CID004',
            'facilities' => [$facility1->getId(), $facility2->getId()],
        ];

        $client->request(RequestMethod::POST, '/api/v1/customer', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Customer D', $responseData['data']['name']);
        self::assertSame('CID004', $responseData['data']['cid']);

        // Verify that facilities are not included in customer response
        self::assertArrayHasKey('facilities', $responseData['data']);
    }

    public function testUpdateCustomer(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $customer = $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer E')
            ->setCid('CID005')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Customer E',
            'cid' => 'CID005_UPDATED',
            'country' => 'Slovakia',
        ];

        $client->request(RequestMethod::PUT, "/api/v1/customer/{$customer->getId()}", [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Updated Customer E', $responseData['data']['name']);
        self::assertSame('CID005_UPDATED', $responseData['data']['cid']);
        self::assertSame('Slovakia', $responseData['data']['country']);
    }

    public function testDeleteCustomer(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $customer = $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer F')
            ->setCid('CID006')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/customer/{$customer->getId()}");
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(204, $responseData['code']);
    }

    public function testGetCustomerDetail(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $customer = $this->getObjectFactory()->getCustomerBuilder()
            ->setName('Customer G')
            ->setCid('CID007')
            ->setCountry('Germany')
            ->setCity('Berlin')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, "/api/v1/customer/{$customer->getId()}");
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Customer G', $responseData['data']['name']);
        self::assertSame('CID007', $responseData['data']['cid']);
        self::assertSame('Germany', $responseData['data']['country']);
        self::assertSame('Berlin', $responseData['data']['city']);
    }

    public function testCreateCustomerWithPostalCode(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CUSTOMER_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Customer H',
            'cid' => 'CID008',
            'country' => 'Czech Republic',
            'city' => 'Prague',
            'street' => 'Main Street 123',
            'postalCode' => '12345',
            'coordinates' => '50.0755,14.4378',
        ];

        $client->request(RequestMethod::POST, '/api/v1/customer', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Customer H', $responseData['data']['name']);
        self::assertSame('CID008', $responseData['data']['cid']);
        self::assertSame('Czech Republic', $responseData['data']['country']);
        self::assertSame('Prague', $responseData['data']['city']);
        self::assertSame('Main Street 123', $responseData['data']['street']);
        self::assertSame('12345', $responseData['data']['postal_code']);
        self::assertSame('50.0755,14.4378', $responseData['data']['coordinates']);
    }

}
