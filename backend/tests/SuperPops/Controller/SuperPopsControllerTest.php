<?php

declare(strict_types = 1);

namespace Pm\Tests\SuperPops\Controller;

use DateTime;
use DateTimeImmutable;
use Nette\Utils\FileSystem;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use Pm\Account\Entity\Account;
use Pm\Core\Http\RequestMethod;
use Pm\Core\Http\ResponseStatusCode;
use Pm\EventCategory\Enum\EventCategorySeverityEnum;
use Pm\Permission\Enum\PermissionEnum;
use Pm\SuperPops\Entity\SuperPops;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use function count;
use function http_build_query;
use function tempnam;

/**
 * @return void
 */
class SuperPopsControllerTest extends TestCase
{

    public function testInvalidInterval(): void
    {
        $user = $this->prepareUser();

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall', 'OC23', $user)
            ->build();

        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $startsAt = new DateTime('-2 day');
        $endsAt = new DateTime('-1 day');

        $client->request(RequestMethod::POST, '/api/v1/pops', [
            'accountId' => $account->getId(),
            'facilityId' => $facility1->getId(),
            'startsAt' => $startsAt->format('Y-m-d H:i:s'),
            'endsAt' => $endsAt->format('Y-m-d H:i:s'),
        ]);

        $this->assertResponseError($client, 400);
    }

    public function testCreateSuperPops(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy', 'OC23', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('KFC fast food', 'FF01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov', 'MMBen', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $startsAt = new DateTime('-12 hours');
        $endsAt = new DateTime('+12 hours');

        $client->request(RequestMethod::POST, '/api/v1/super-pops', [
            'accountId' => $account->getId(),
            'facilitiesIds' => [$facility1->getId(), $facility2->getId(), $facility3->getId()],
            'startsAt' => $startsAt->format('Y-m-d H:i:s'),
            'endsAt' => $endsAt->format('Y-m-d H:i:s'),
        ]);

        $responseData = $this->assertResponseSuccess($client);
        self::assertSame(201, $responseData['code']);
        self::assertArrayHasKey('message', $responseData);
        self::assertSame('super-pops-created', $responseData['message']);
        self::assertSame(1, $responseData['data']['id']);
        self::assertFalse($responseData['data']['is_locked']);
        self::assertSame([$facility1->getId(), $facility2->getId(), $facility3->getId()], $responseData['data']['facilities_ids']);
        self::assertSame($startsAt->format('c'), $responseData['data']['starts_at']);
        self::assertSame($endsAt->format('c'), $responseData['data']['ends_at']);

        $superPops = $this->getEntityManager()->getRepository(SuperPops::class)->findAll();
        self::assertCount(1, $superPops);
    }

    public function testUpdateSuperPops(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy for update', 'OC23', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('KFC fast food for update', 'FF01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov for update', 'MMBen', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility4 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Nový obchod', 'OC99', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility5 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('MC Joke food', 'MC01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $superPops = $this->getObjectFactory()->getSuperPopsBuilder()->setAccount($account)->setFacilities([$facility1, $facility2, $facility3])->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $superPopsId = $superPops->getId();

        $startsAt = new DateTime('-12 hours');
        $endsAt = new DateTime('+12 hours');

        $client->request(RequestMethod::PUT, '/api/v1/super-pops/' . $superPopsId, [
            'accountId' => $account->getId(),
            'facilitiesIds' => [$facility4->getId(), $facility5->getId()],
            'startsAt' => $startsAt->format('Y-m-d H:i:s'),
            'endsAt' => $endsAt->format('Y-m-d H:i:s'),
        ]);

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);
        self::assertArrayHasKey('message', $responseData);
        self::assertSame('super-pops-updated', $responseData['message']);
        self::assertSame($superPopsId, $responseData['data']['id']);
        self::assertFalse($responseData['data']['is_locked']);
        self::assertSame([$facility4->getId(), $facility5->getId()], $responseData['data']['facilities_ids']);
        self::assertSame($startsAt->format('c'), $responseData['data']['starts_at']);
        self::assertSame($endsAt->format('c'), $responseData['data']['ends_at']);
    }

    public function testLockSuperPops(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy for update', 'OC23', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $superPops = $this->getObjectFactory()->getSuperPopsBuilder()
            ->setAccount($account)
            ->setFacilities([$facility1])
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $superPopsId = $superPops->getId();

        $client->request(
            RequestMethod::PUT,
            "/api/v1/super-pops/{$superPopsId}/lock",
            [
                'id' => $superPopsId,
            ],
        );

        $dateTimeAfterLock = (new DateTimeImmutable())->modify('+1 minute');

        $responseData = $this->assertResponseSuccess($client);

        $endsAt = new DateTimeImmutable($responseData['data']['ends_at']);

        self::assertSame(200, $responseData['code']);
        self::assertArrayHasKey('message', $responseData);
        self::assertSame('super-pops-locked', $responseData['message']);
        self::assertLessThan($dateTimeAfterLock->getTimestamp(), $endsAt->getTimestamp());
        self::assertSame($superPopsId, $responseData['data']['id']);
        self::assertTrue($responseData['data']['is_locked']);
        self::assertSame([$facility1->getId()], $responseData['data']['facilities_ids']);

        /**
         * @var SuperPops $reloadedSuperPops
         */
        $reloadedSuperPops = $this->getEntityManager()->getRepository(SuperPops::class)->findOneBy(['id' => $superPopsId]);

        self::assertTrue($reloadedSuperPops->isLocked());
        self::assertLessThan($dateTimeAfterLock->getTimestamp(), $reloadedSuperPops->getEndsAt()->getTimestamp());
    }

    public function testGetDetailAction(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
            PermissionEnum::PERM_SUPERPOPS_SHOW,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setUser($user)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $account = $user->getAccount();

        $superPops = $this->getObjectFactory()->getSuperPopsBuilder()
            ->setAccount($account)->setFacilities([$facility])
            ->build();

        $eventCategoryCritical = $this->getObjectFactory()->getEventCategoryBuilder()->withType(EventCategorySeverityEnum::CRITICAL)->build();
        $eventCategoryLow = $this->getObjectFactory()->getEventCategoryBuilder()->withType(EventCategorySeverityEnum::LOW)->build();
        $eventCategoryMedium = $this->getObjectFactory()->getEventCategoryBuilder()->withType(EventCategorySeverityEnum::MEDIUM)->build();
        $eventCategoryInternal = $this->getObjectFactory()->getEventCategoryBuilder()->withIsHidden(true)->build();
        $eventCategoryInternal->setSeverity(EventCategorySeverityEnum::LOW);

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryCritical)
            ->build();
        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryCritical)
            ->setEventDateTime((new DateTimeImmutable())->modify('-25 hours'))
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryInternal)
            ->build();
        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryInternal)
            ->build();
        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryLow)
            ->build();
        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryMedium)
            ->build();
        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryMedium)
            ->setEventDateTime((new DateTimeImmutable())->modify('+25 hours'))
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);
        $this->refreshExistingEntity($account);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/super-pops/' . $superPops->getId());
        $responseData = $this->assertResponseSuccess($client);

        self::assertEquals($superPops->getStartsAt(), new DateTimeImmutable($responseData['data']['starts_at']));
        self::assertEquals($superPops->getEndsAt(), new DateTimeImmutable($responseData['data']['ends_at']));
        self::assertEquals(1, $responseData['data']['count_of_critical_events']);
        self::assertEquals(1, $responseData['data']['count_of_medium_events']);
        self::assertEquals(2, $responseData['data']['count_of_internal_events']);
        self::assertEquals(3, $responseData['data']['count_of_low_events']);
        self::assertSame([
            'PERM_SUPERPOPS_CREATE',
            'PERM_SUPERPOPS_SHOW',
        ], $responseData['data']['permissions']);
    }

    public function testGetListAction(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
            PermissionEnum::PERM_SUPERPOPS_SHOW,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy for update', 'OC23', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Abc for update', 'MAbc', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov for update', 'MMBen', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility4 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Nový obchod', 'OC99', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility5 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('MC Joke food', 'MC01', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $eventCategoryCritical = $this->getObjectFactory()->getEventCategoryBuilder()->withType(EventCategorySeverityEnum::CRITICAL)->build();

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility2)
            ->setAccount($user->getAccount())
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryCritical)
            ->build();

        $superPops1 = $this->getObjectFactory()->getSuperPopsBuilder()->setAccount($account)->setFacilities([$facility1, $facility2, $facility3])->build();
        $superPops2 = $this->getObjectFactory()->getSuperPopsBuilder()->setAccount($account)->setFacilities([$facility4, $facility5])->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $parameters = [
            'account' => $account->getId(),
            'isLocked' => false,
        ];
        $url = http_build_query($parameters);
        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, "/api/v1/super-pops?{$url}");
        $responseData = $this->assertResponseSuccess($client);

        self::assertEquals(2, $responseData['total_count']);

        foreach ($responseData['list'] as $responseDataSuperPops) {
            if ($responseDataSuperPops['id'] === $superPops1->getId()) {
                self::assertEquals(1, $responseDataSuperPops['count_of_critical_events']);
            }

            if ($responseDataSuperPops['id'] === $superPops2->getId()) {
                self::assertEquals(2, count($responseDataSuperPops['facilities_ids']));
            }

            self::assertSame([
                'PERM_SUPERPOPS_CREATE',
                'PERM_SUPERPOPS_SHOW',
            ], $responseDataSuperPops['permissions']);
        }
    }

    public function testGetSuperPopsEventList(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();
        $superPops = $this->prepareSuperPopsEventList($user, $account);
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'eventCategory.severity',
                    'value' => 'CRITICAL',
                    'type' => 'match',
                ],
            ],
            'search' => 'EVent60',
        ];

        $client->request(
            RequestMethod::GET,
            '/api/v1/super-pops/' . $superPops->getId() . '/pops-event?limit=10&offset=0&' . http_build_query($requestData),
        );
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'][0]);
        self::assertSame('testing event60', $responseData['list'][0]['description']);
        self::assertGreaterThan(0, $responseData['list'][0]['pops_id']);
        self::assertGreaterThan(0, $responseData['list'][0]['facility_id']);
        self::assertSame('OC Nakupy', $responseData['list'][0]['facility_name']);
        self::assertSame('OC23', $responseData['list'][0]['facility_code']);
        self::assertSame(1, $responseData['total_count']);
    }

    public function testGetSuperPopsEventListFilterCategory(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();
        $superPops = $this->prepareSuperPopsEventList($user, $account);
        $user = $this->refreshExistingEntity($user);
        $superPops = $this->refreshExistingEntity($superPops);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'eventCategory.severity',
                    'value' => 'MEDIUM',
                    'type' => 'match',
                ],
            ],
            'search' => 'Medium',
        ];

        $client->request(
            RequestMethod::GET,
            '/api/v1/super-pops/' . $superPops->getId() . '/pops-event?limit=10&offset=0&' . http_build_query($requestData),
        );
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'][0]);
        self::assertSame('Testing Event61', $responseData['list'][0]['description']);
        self::assertGreaterThan(0, $responseData['list'][0]['pops_id']);
        self::assertGreaterThan(0, $responseData['list'][0]['facility_id']);
        self::assertSame('Muzeum Abc', $responseData['list'][0]['facility_name']);
        self::assertSame('MAbc', $responseData['list'][0]['facility_code']);
        self::assertSame(2, $responseData['total_count']);
    }

    public function testGetSuperPopsEventListCaseInsensitive(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();
        $superPops = $this->prepareSuperPopsEventList($user, $account);
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'eventCategory.severity',
                    'value' => 'MEDIUM',
                    'type' => 'match',
                ],
            ],
            'search' => 'event63',
        ];

        $client->request(
            RequestMethod::GET,
            '/api/v1/super-pops/' . $superPops->getId() . '/pops-event?limit=10&offset=0&' . http_build_query($requestData),
        );
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'][0]);
        self::assertSame('TESTING EVENT63', $responseData['list'][0]['description']);
        self::assertGreaterThan(0, $responseData['list'][0]['pops_id']);
        self::assertGreaterThan(0, $responseData['list'][0]['facility_id']);
        self::assertSame('Muzeum Benesov', $responseData['list'][0]['facility_name']);
        self::assertSame('MMBen', $responseData['list'][0]['facility_code']);
        self::assertSame(1, $responseData['total_count']);
    }

    public function testGetSuperPopsEventPdfList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_OBJECT_SHOW,
        ]);

        $user->addPermissionTemplate($permissionTemplate);

        $facility = $this->prepareFacility($user);

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->build();

        $superPops = $this->getObjectFactory()
            ->getSuperPopsBuilder()
            ->setAccount($user->getAccount())
            ->setFacilities([$facility])
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);
        $superPops = $this->refreshExistingEntity($superPops);

        $client = $this->createClientWithUser($user);

        $client->request(
            RequestMethod::GET,
            '/api/v1/super-pops/' . $superPops->getId() . '/pops-event-pdf?offset=0',
        );

        $response = $client->getResponse();

        $responseContent = $response->getContent();

        self::assertNotFalse($responseContent);
        self::assertSame(200, $response->getStatusCode());
    }

    public function testGetSuperPopsEventXlsList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_OBJECT_SHOW,
        ]);

        $user->addPermissionTemplate($permissionTemplate);

        $facility = $this->prepareFacility($user);

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->build();

        $popsEvent = $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->build();

        $hiddenEventCategory = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withAccount($user->getAccount())
            ->withType(EventCategorySeverityEnum::NONE)
            ->withIsHidden(true)
            ->withCode(500)
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->withCategory($hiddenEventCategory)
            ->build();

        $superPops = $this->getObjectFactory()
            ->getSuperPopsBuilder()
            ->setAccount($user->getAccount())
            ->setFacilities([$facility])
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $client->request(
            RequestMethod::GET,
            '/api/v1/super-pops/' . $superPops->getId() . '/pops-event-xls?offset=0',
        );

        $response = $client->getResponse();

        $responseContent = $response->getContent();

        self::assertNotFalse($responseContent);
        self::assertSame(200, $response->getStatusCode());

        $tmpfname = tempnam('/tmp', 'FOO');
        FileSystem::write($tmpfname, $responseContent);

        $readerXlsx = new Xlsx();
        $spreadsheet = $readerXlsx->load($tmpfname);

        // read cell value and compare to stored value. Will verify that XLS was generated correctly
        $description = $spreadsheet->getActiveSheet()->getCell([4, 2])->getValue();
        self::assertSame($popsEvent->getDescription(), $description);
        self::assertNull($spreadsheet->getActiveSheet()->getCell([1, 3])->getValue());
        self::assertNull($spreadsheet->getActiveSheet()->getCell([4, 3])->getValue());
    }

    public function testGetSuperPopsEventListFilterDateTime(): void
    {
        $user = $this->prepareUser();
        $account = $user->getAccount();
        $superPops = $this->prepareSuperPopsEventList($user, $account);
        $user = $this->refreshExistingEntity($user);
        $superPops = $this->refreshExistingEntity($superPops);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'eventDateTime',
                    'value' => (new DateTimeImmutable('-1 hour'))->format('Y-m-d H:i:s'),
                    'type' => 'greaterThan',
                ],
            ],
        ];

        $client->request(
            RequestMethod::GET,
            '/api/v1/super-pops/' . $superPops->getId() . '/pops-event?limit=10&offset=0&' . http_build_query($requestData),
        );
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(3, $responseData['total_count']);
        self::assertNotNull($responseData['list'][0]);
        self::assertSame('testing event60', $responseData['list'][0]['description']);
        self::assertGreaterThan(0, $responseData['list'][0]['pops_id']);
        self::assertGreaterThan(0, $responseData['list'][0]['facility_id']);
        self::assertSame('OC Nakupy', $responseData['list'][0]['facility_name']);
        self::assertSame('OC23', $responseData['list'][0]['facility_code']);
    }

    public function testCreateSuperPopsLinkedFacility(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy', 'OC23', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('KFC fast food', 'FF01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov', 'MMBen', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility4 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Nový obchod', 'OC99', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility5 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Starý obchod', 'OC98', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $this->getObjectFactory()->getFacilityGroupBuilder()
            ->setAccount($user->getAccount())
            ->setFacilities([$facility4, $facility5])
            ->setName('Test group')
            ->build();

        $this->getObjectFactory()->getSuperPopsBuilder()
            ->setFacilities([$facility3, $facility4])
            ->setAccount($user->getAccount())
            ->setStartsAt(new DateTimeImmutable('-1 day'))
            ->setEndsAt(new DateTimeImmutable('+1 day'))
            ->build();

        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $startsAt = new DateTime('-12 hours');
        $endsAt = new DateTime('+12 hours');

        $client->request(RequestMethod::POST, '/api/v1/super-pops', [
            'accountId' => $account->getId(),
            'facilitiesIds' => [$facility1->getId(), $facility2->getId(), $facility3->getId()],
            'startsAt' => $startsAt->format('Y-m-d H:i:s'),
            'endsAt' => $endsAt->format('Y-m-d H:i:s'),
        ]);

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(201, $responseData['code']);
        self::assertArrayHasKey('message', $responseData);
        self::assertSame('super-pops-created', $responseData['message']);
        self::assertSame(2, $responseData['data']['id']);
        self::assertFalse($responseData['data']['is_locked']);
        self::assertSame([$facility1->getId(), $facility2->getId()], $responseData['data']['facilities_ids']);
        self::assertSame($startsAt->format('c'), $responseData['data']['starts_at']);
        self::assertSame($endsAt->format('c'), $responseData['data']['ends_at']);
        self::assertCount(1, $responseData['data']['other_super_pops_linked_facilities']);
        self::assertSame($facility3->getId(), $responseData['data']['other_super_pops_linked_facilities'][0]['id']);

        $superPops = $this->getEntityManager()->getRepository(SuperPops::class)->findAll();
        self::assertCount(2, $superPops);
    }

    public function testUpdateSuperPopsLinkedFacility(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUPERPOPS_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy for update', 'OC23', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('KFC fast food for update', 'FF01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov for update', 'MMBen', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility4 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Nový obchod', 'OC99', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility5 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('MC Joke food', 'MC01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility6 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Nový obchod', 'OC99', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility7 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Starý obchod', 'OC98', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $this->getObjectFactory()->getFacilityGroupBuilder()
            ->setAccount($user->getAccount())
            ->setFacilities([$facility6, $facility7])
            ->setName('Test group')
            ->build();

        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $superPops = $this->getObjectFactory()
            ->getSuperPopsBuilder()
            ->setAccount($account)
            ->setFacilities([$facility1, $facility2, $facility3, $facility6])
            ->build();
        $this->getObjectFactory()->getSuperPopsBuilder()->setAccount($account)->setFacilities([$facility5])->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $superPopsId = $superPops->getId();

        $startsAt = new DateTime('-12 hours');
        $endsAt = new DateTime('+12 hours');

        $client->request(RequestMethod::PUT, '/api/v1/super-pops/' . $superPopsId, [
            'accountId' => $account->getId(),
            'facilitiesIds' => [$facility4->getId(), $facility5->getId()],
            'startsAt' => $startsAt->format('Y-m-d H:i:s'),
            'endsAt' => $endsAt->format('Y-m-d H:i:s'),
        ]);

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);
        self::assertArrayHasKey('message', $responseData);
        self::assertSame('super-pops-updated', $responseData['message']);
        self::assertSame($superPopsId, $responseData['data']['id']);
        self::assertFalse($responseData['data']['is_locked']);
        self::assertSame([$facility4->getId()], $responseData['data']['facilities_ids']);
        self::assertSame($startsAt->format('c'), $responseData['data']['starts_at']);
        self::assertSame($endsAt->format('c'), $responseData['data']['ends_at']);
        self::assertCount(1, $responseData['data']['other_super_pops_linked_facilities']);
        self::assertSame($facility5->getId(), $responseData['data']['other_super_pops_linked_facilities'][0]['id']);
    }

    public function testCreateSuperPopsWithoutFacility(): void
    {
        $user = $this->prepareUser();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $startsAt = new DateTime('-12 hours');
        $endsAt = new DateTime('+12 hours');

        $client->request(RequestMethod::POST, '/api/v1/super-pops', [
            'accountId' => $user->getAccount()->getId(),
            'facilitiesIds' => [],
            'startsAt' => $startsAt->format('Y-m-d H:i:s'),
            'endsAt' => $endsAt->format('Y-m-d H:i:s'),
        ]);
        $this->assertResponseBadRequest($client, 'No facility can be added to Super PoPS.', ResponseStatusCode::BAD_REQUEST_400);
    }

    private function prepareSuperPopsEventList(User $user, Account $account): SuperPops
    {
        $permissionTemplate = $this->preparePermissionTemplate($user);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy', 'OC23', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Abc', 'MAbc', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov', 'MMBen', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility4 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Nový obchod', 'OC99', $user)
            ->setAccount($account)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $eventCategoryCritical = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withType(EventCategorySeverityEnum::CRITICAL)
            ->withName('Critical type event')
            ->withAccount($account)
            ->build();

        $eventCategoryMedium = $this->getObjectFactory()->getEventCategoryBuilder()
            ->withType(EventCategorySeverityEnum::MEDIUM)
            ->withName('Medium type event')
            ->withAccount($account)
            ->build();

        $pops1 = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility1)
            ->setAccount($user->getAccount())
            ->build();

        $pops2 = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility2)
            ->setAccount($user->getAccount())
            ->build();

        $pops3 = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility3)
            ->setAccount($user->getAccount())
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops1)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryCritical)
            ->withName('testing event60')
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops2)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryMedium)
            ->withName('Testing Event61')
            ->build();

        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops3)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryMedium)
            ->withName('TESTING EVENT63')
            ->build();

        // event in a pastime have to be ignored
        $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops3)
            ->setInsertedBy($user)
            ->withCategory($eventCategoryMedium)
            ->setEventDateTime(new DateTimeImmutable('-10 day'))
            ->withName('TESTING EVENT65 past')
            ->build();

        $superPops = $this->getObjectFactory()->getSuperPopsBuilder()
            ->setAccount($account)
            ->setFacilities([$facility1, $facility2, $facility3, $facility4])
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        return $superPops;
    }

}
