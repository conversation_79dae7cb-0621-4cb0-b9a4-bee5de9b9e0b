<?php

declare(strict_types = 1);

namespace Pm\Tests\Notification\Service;

use Pm\Notification\Entity\Notification;
use Pm\Notification\Producer\EmailNotificationProducer;
use Pm\Notification\Producer\PushNotificationProducer;
use Pm\Notification\Producer\SmsNotificationProducer;
use Pm\Notification\Service\NotificationService;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class NotificationServiceTest extends TestCase
{

    public function testPopsEventCreated(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_POPS_SHOW,
        ]);

        $user->addPermissionTemplate($permissionTemplate);

        $facility = $this->prepareFacility($user);

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->build();

        $popsEvent = $this->getObjectFactory()->getPopsEventBuilder()
            ->setPops($pops)
            ->setInsertedBy($user)
            ->build();

        $this->getObjectFactory()->getMobileSettingBuilder()->withUser($user)->build();

        $eventCategory = $this->getObjectFactory()->getEventCategoryBuilder()->build();

        $this->getObjectFactory()->getMobileInfoBuilder()->withUser($user)->build();

        $reporting = $this->getObjectFactory()->getReportingBuilder()->withFacility($pops->getFacility())->withUser($user)->build();

        $this->getObjectFactory()->getReportingSettingBuilder()->withEventCategory($eventCategory)->withReporting($reporting)->withSendingType(7)->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $user = $this->refreshExistingEntity($user);
        $popsEvent = $this->refreshExistingEntity($popsEvent);

        // build mock producers
        $emailNotificationProducerMock = $this->getMockBuilder(EmailNotificationProducer::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['publish'])
            ->getMock();
        $emailNotificationProducerMock->expects(self::atLeastOnce())
            ->method('publish')
            ->with(
                self::anything(),
            );
        self::getContainer()->set(EmailNotificationProducer::class, $emailNotificationProducerMock);

        $smsNotificationProducerMock = $this->getMockBuilder(SmsNotificationProducer::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['publish'])
            ->getMock();
        $smsNotificationProducerMock->expects(self::atLeastOnce())
            ->method('publish')
            ->with(
                self::anything(),
            );
        self::getContainer()->set(SmsNotificationProducer::class, $smsNotificationProducerMock);

        $pushNotificationProducerMock = $this->getMockBuilder(PushNotificationProducer::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['publish'])
            ->getMock();
        $pushNotificationProducerMock->expects(self::atLeastOnce())
            ->method('publish')
            ->with(
                self::anything(),
            );
        self::getContainer()->set(PushNotificationProducer::class, $pushNotificationProducerMock);

        // ---

        /** @var NotificationService $notificationService */
        $notificationService = self::getContainer()->get(NotificationService::class);

        $notificationService->popsEventCreated($popsEvent);

        // ------
        $notifications = $this->getEntityManager()->getRepository(Notification::class)->findAll();

        self::assertCount(3, $notifications);
    }

}
