<?php

declare(strict_types = 1);

namespace Pm\Tests\AuditLog\Service;

use Pm\AuditLog\Entity\AuditLog;
use Pm\AuditLog\Enum\AuditLogAction;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use Pm\User\Enum\UserRole;
use function assert;

class AuditLogServiceTest extends TestCase
{

    public function testLog(): void
    {
        // create user
        $account = $this->getObjectFactory()->getAccountBuilder()->build();
        $user = $this->getObjectFactory()->getUserBuilder()
            ->setAccount($account)
            ->build();
        $this->getEntityManager()->flush();

        $auditLogRepository = $this->getEntityManager()->getRepository(AuditLog::class);

        $user = $this->getEntityManager()->find(User::class, $user->getId());
        assert($user instanceof User, 'Check user entity type');

        $auditLog = $auditLogRepository->findBy(
            [
                'action' => AuditLogAction::INSERT->value,
            ],
            [
                'id' => 'DESC',
            ],
            1,
        )[0];

        self::assertSame(AuditLogAction::INSERT->value, $auditLog->getAction()?->value);
        self::assertSame(User::class, $auditLog->getEntityClass());
        self::assertSame($user->getId(), $auditLog->getEntityId());

        // update user
        $user->setRole(UserRole::ADMIN);
        $this->getEntityManager()->flush();

        $auditLog = $auditLogRepository->findBy([], ['id' => 'DESC'], 1)[0];
        $updateChanges = ['old' => ['role' => UserRole::USER->value], 'new' => ['role' => UserRole::ADMIN->value]];
        $auditLogData = $auditLog->getData();
        $old = $auditLogData['old'] ?? [];
        $new = $auditLogData['new'] ?? [];

        self::assertSame(AuditLogAction::UPDATE->value, $auditLog->getAction()?->value);
        self::assertSame(User::class, $auditLog->getEntityClass());
        self::assertSame($user->getId(), $auditLog->getEntityId());
        self::assertSame($updateChanges['old']['role'], $old['role']);
        self::assertSame($updateChanges['new']['role'], $new['role']);

        $userId = $user->getId();
        // delete user
        $this->getEntityManager()->remove($user);
        $this->getEntityManager()->flush();

        $auditLog = $auditLogRepository->findBy([], ['id' => 'DESC'], 1)[0];
        $auditLogData = $auditLog->getData();
        $old = $auditLogData['old'] ?? [];
        $new = $auditLogData['new'] ?? [];

        self::assertSame(AuditLogAction::DELETE->value, $auditLog->getAction()?->value);
        self::assertSame(User::class, $auditLog->getEntityClass());
        self::assertSame($userId, $auditLog->getEntityId());
        self::assertSame($userId, $old['id']);
        self::assertNull($new['id']);
    }

}
