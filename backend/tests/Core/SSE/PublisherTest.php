<?php

declare(strict_types = 1);

namespace Pm\Tests\Core\SSE;

use DateTimeImmutable;
use Pm\Core\SSE\Publisher;
use Pm\Core\SSE\TopicFactory;
use Pm\Pops\HttpResponse\PopsHttpResponse;
use Pm\Tests\TestCase;

class PublisherTest extends TestCase
{

    public function testPublishPublic(): void
    {
        /** @var Publisher $publisher */
        $publisher = self::getContainer()->get(Publisher::class);
        /** @var TopicFactory $topicFactory */
        $topicFactory = self::getContainer()->get(TopicFactory::class);

        $user = $this->prepareUser();
        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setAccount($user->getAccount())
            ->build();
        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();
        $popsResponse = new PopsHttpResponse($pops, null, 0, 0, 0, 0, null);

        $result = $publisher->send(
            $topicFactory->create($pops),
            'insert',
            (new DateTimeImmutable())->format('c'),
            $popsResponse,
        );

        self::assertIsString($result);
        self::assertStringStartsWith('urn:uuid:', $result);
    }

    public function testPublishPrivate(): void
    {
        /** @var Publisher $publisher */
        $publisher = self::getContainer()->get(Publisher::class);
        /** @var TopicFactory $topicFactory */
        $topicFactory = self::getContainer()->get(TopicFactory::class);

        $user = $this->prepareUser();
        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setAccount($user->getAccount())
            ->build();
        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();
        $popsResponse = new PopsHttpResponse($pops, null, 0, 0, 0, 0, null);

        $result = $publisher->send(
            $topicFactory->create($pops),
            'insert',
            (new DateTimeImmutable())->format('c'),
            $popsResponse,
            true,
        );

        self::assertIsString($result);
        self::assertStringStartsWith('urn:uuid:', $result);
    }

}
