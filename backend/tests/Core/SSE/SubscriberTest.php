<?php

declare(strict_types = 1);

namespace Pm\Tests\Core\SSE;

use DateTimeImmutable;
use Pm\Core\SSE\Publisher;
use Pm\Core\SSE\TopicFactory;
use Pm\Pops\HttpResponse\PopsHttpResponse;
use Pm\Tests\TestCase;
use Symfony\Component\HttpClient\Chunk\ServerSentEvent;
use Symfony\Component\HttpClient\EventSourceHttpClient;
use Symfony\Component\HttpClient\HttpClient;
use function usleep;

class SubscriberTest extends TestCase
{

    public function testSubscribe(): void
    {
        /** @var Publisher $publisher */
        $publisher = self::getContainer()->get(Publisher::class);
        /** @var TopicFactory $topicFactory */
        $topicFactory = self::getContainer()->get(TopicFactory::class);

        $client = new EventSourceHttpClient(
            HttpClient::create(['auth_bearer' => $_ENV['MERCURE_JWT_TOKEN']]),
            3,
        );

        $user = $this->prepareUser();
        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setAccount($user->getAccount())
            ->build();
        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();
        $popsResponse = new PopsHttpResponse($pops, null, 0, 0, 0, 0, null);

        $topic = $topicFactory->create($pops);

        $result = null;
        $source = $client->connect($_ENV['MERCURE_URL'] . '?topic=' . $topic);
        $sent = false;
        $iteration = 0;
        $maxIterations = 10;
        while ($source !== null && $iteration < $maxIterations) {
            foreach ($client->stream($source, 2) as $chunk) {
                if (!$sent) {
                    $publisher->send(
                        $topic,
                        'insert',
                        (new DateTimeImmutable())->format('c'),
                        $popsResponse,
                    );
                    $sent = true;
                }
                if ($chunk->isTimeout()) {
                    continue;
                }
                if ($chunk->isLast()) {
                    $source = null;
                    return;
                }
                if ($chunk instanceof ServerSentEvent) {
                    $result = $chunk->getArrayData();
                    $source = null;
                }
            }
            usleep(50);
            $iteration++;
        }

        self::assertIsArray($result);
        self::assertArrayHasKey('event', $result);
        self::assertSame('insert', $result['event']);
        self::assertArrayHasKey('data', $result);
        self::assertSame($pops->getId(), $result['data']['id']);
    }

}
