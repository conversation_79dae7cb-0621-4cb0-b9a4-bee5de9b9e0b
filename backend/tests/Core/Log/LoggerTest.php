<?php

declare(strict_types = 1);

namespace Pm\Tests\Core\Log;

use Exception;
use Pm\Log\Logger;
use Pm\Tests\TestCase;
use Psr\Log\LoggerInterface;

class LoggerTest extends TestCase
{

    public function testLogInfoMessage(): void
    {
        $loggerInterfaceMock = $this->createMock(LoggerInterface::class);
        $loggerInterfaceMock->expects(self::once())
            ->method('info')
            ->with('Info message');

        $logger = new Logger($loggerInterfaceMock);
        $logger->logInfoMessage('Info message');
    }

    public function testLogErrorMessage(): void
    {
        $loggerInterfaceMock = $this->createMock(LoggerInterface::class);
        $loggerInterfaceMock->expects(self::once())
            ->method('error')
            ->with('Error message');

        $logger = new Logger($loggerInterfaceMock);
        $logger->logErrorMessage('Error message');
    }

    public function testLogInfoException(): void
    {
        $loggerInterfaceMock = $this->createMock(LoggerInterface::class);
        $loggerInterfaceMock->expects(self::once())
            ->method('info')
            ->with('Info exception message');

        $logger = new Logger($loggerInterfaceMock);
        try {
            throw new Exception('Info exception message');
        } catch (Exception $e) {
            $logger->logInfoException($e);
        }
    }

    public function testLogErrorException(): void
    {
        $loggerInterfaceMock = $this->createMock(LoggerInterface::class);
        $loggerInterfaceMock->expects(self::once())
            ->method('error')
            ->with('Error exception message');

        $logger = new Logger($loggerInterfaceMock);
        try {
            throw new Exception('Error exception message');
        } catch (Exception $e) {
            $logger->logErrorException($e);
        }
    }

}
