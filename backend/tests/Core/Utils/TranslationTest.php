<?php

declare(strict_types = 1);

namespace Pm\Tests\Core\Utils;

use Pm\Tests\TestCase;

class TranslationTest extends TestCase
{

    public function testTranslationConst(): void
    {
        $translator = self::getContainer()->get('translator');
        $locale = $translator->getLocale();
        self::assertSame('en', "$locale");

        self::assertSame(
            'Suggestion M2C ID 1 from object account',
            $translator->trans('suggestion.customer_subject', [
                '%suggestion_id%' => 1,
                '%object%' => 'account',
            ]),
        );

        self::assertSame(
            'Podnět M2C ID 1 z objektu account',
            $translator->trans('suggestion.customer_subject', [
                '%suggestion_id%' => 1,
                '%object%' => 'account',
            ], null, 'cs'),
        );
    }

}
