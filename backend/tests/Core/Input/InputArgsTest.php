<?php

declare(strict_types = 1);

namespace Pm\Tests\Core\Input;

use DateTimeImmutable;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\Input\InputArgs;
use Pm\Tests\TestCase;

class InputArgsTest extends TestCase
{

    private const ARGUMENTS = [
        'array' => [
            'string' => 'hello',
        ],
        'bool' => true,
        'date' => '2019-12-24',
        'float' => 1.25,
        'int' => 1,
        'string' => 'hello',
    ];

    private function getInputArgs(): InputArgs
    {
        return InputArgs::create(self::ARGUMENTS);
    }

    public function testGetString(): void
    {
        $inputArgs = $this->getInputArgs();

        self::assertSame(self::ARGUMENTS['string'], $inputArgs->getString('string'));
        self::assertSame(self::ARGUMENTS['string'], $inputArgs->getStringOrNull('string'));

        self::assertNull($inputArgs->getStringOrNull('missing'));

        $this->expectException(InvalidInputException::class);
        $inputArgs->getString('missing');
    }

    public function testGetStringInvalidValue(): void
    {
        $inputArgs = $this->getInputArgs();

        $this->expectException(InvalidInputException::class);
        $inputArgs->getString('int');
    }

    public function testGetList(): void
    {
        $inputArgs = $this->getInputArgs();
        self::assertSame(self::ARGUMENTS['array']['string'], $inputArgs->getList('array')->getString('string'));

        $this->expectException(InvalidInputException::class);
        $inputArgs->getList('missing');
    }

    public function testGetListInvalidValue(): void
    {
        $inputArgs = $this->getInputArgs();

        $this->expectException(InvalidInputException::class);
        $inputArgs->getList('int');
    }

    public function testGetInt(): void
    {
        $inputArgs = $this->getInputArgs();

        self::assertSame(self::ARGUMENTS['int'], $inputArgs->getInt('int'));
        self::assertSame(self::ARGUMENTS['int'], $inputArgs->getIntOrNull('int'));

        self::assertNull($inputArgs->getIntOrNull('missing'));

        $this->expectException(InvalidInputException::class);
        $inputArgs->getInt('missing');
    }

    public function testGetIntInvalidValue(): void
    {
        $inputArgs = $this->getInputArgs();

        $this->expectException(InvalidInputException::class);
        $inputArgs->getInt('string');
    }

    public function testGetFloat(): void
    {
        $inputArgs = $this->getInputArgs();

        self::assertSame(self::ARGUMENTS['float'], $inputArgs->getFloat('float'));
        self::assertSame((float) self::ARGUMENTS['int'], $inputArgs->getFloat('int'));

        $this->expectException(InvalidInputException::class);
        $inputArgs->getFloat('missing');
    }

    public function testGetFloatInvalidValue(): void
    {
        $inputArgs = $this->getInputArgs();

        $this->expectException(InvalidInputException::class);
        $inputArgs->getFloat('string');
    }

    public function testGetDate(): void
    {
        $inputArgs = $this->getInputArgs();

        self::assertEquals(new DateTimeImmutable(self::ARGUMENTS['date']), $inputArgs->getDate('date'));

        $this->expectException(InvalidInputException::class);
        $inputArgs->getDate('missing');
    }

    public function testGetBool(): void
    {
        $inputArgs = $this->getInputArgs();

        self::assertTrue($inputArgs->getBool('bool'));

        $this->expectException(InvalidInputException::class);
        $inputArgs->getBool('missing');
    }

    public function testGetBoolInvalidValue(): void
    {
        $inputArgs = $this->getInputArgs();

        $this->expectException(InvalidInputException::class);
        $inputArgs->getBool('string');
    }

}
