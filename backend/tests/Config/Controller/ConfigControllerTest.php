<?php

declare(strict_types = 1);

namespace Pm\Tests\Config\Controller;

use Pm\Core\Http\RequestMethod;
use Pm\Pops\Facade\PopsEventFacade;
use Pm\Tests\TestCase;

class ConfigControllerTest extends TestCase
{

    public function testGetConfig(): void
    {
        $user = $this->prepareUser();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/config');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['data'], 'Response data is null');
        self::assertArrayHasKey('max_attachments_total', $responseData['data']);
        self::assertSame(PopsEventFacade::MAX_ATTACHMENTS_TOTAL, $responseData['data']['max_attachments_total']);

        self::assertArray<PERSON>as<PERSON><PERSON>('allowed_origins', $responseData['data']);
        self::assertIsArray($responseData['data']['allowed_origins']);
    }

}
