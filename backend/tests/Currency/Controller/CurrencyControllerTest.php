<?php

declare(strict_types = 1);

namespace Pm\Tests\Currency\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Core\Http\ResponseStatusCode;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class CurrencyControllerTest extends TestCase
{

    public function testGetCurrencyList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency1 = $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Euro')
            ->setAbbr('EUR')
            ->build();
        $currency2 = $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Dollar')
            ->setAbbr('USD')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/currency');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'], 'Response data is null');
        self::assertCount(2, $responseData['list']);
        self::assertSame($currency1->getName(), $responseData['list'][0]['name']);
        self::assertSame($currency1->getAbbr(), $responseData['list'][0]['abbr']);
        self::assertSame($currency2->getName(), $responseData['list'][1]['name']);
        self::assertSame($currency2->getAbbr(), $responseData['list'][1]['abbr']);
    }

    public function testGetCurrency(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Euro')
            ->setAbbr('EUR')
            ->build();
        $currency2 = $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Dollar')
            ->setAbbr('USD')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/currency/2');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['data'], 'Response data is null');
        self::assertSame($currency2->getName(), $responseData['data']['name']);
        self::assertSame($currency2->getAbbr(), $responseData['data']['abbr']);
    }

    public function testCreateCurrency(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);
        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Pound',
            'abbr' => 'GBP',
        ];

        $client->request(RequestMethod::POST, '/api/v1/currency', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Pound', $responseData['data']['name']);
        self::assertSame('GBP', $responseData['data']['abbr']);
    }

    public function testCreateCurrencyEmptyNameOrAbbr(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => '',
            'abbr' => '',
        ];

        $client->request(RequestMethod::POST, '/api/v1/currency', [], [], [], content: Json::encode($payload));
        $this->assertResponseError($client, ResponseStatusCode::UNPROCESSABLE_ENTITY_422);
        $responseContent = (string) $client->getResponse()->getContent();
        self::assertStringContainsString('validation-failed', $responseContent);
        self::assertStringContainsString('Entered value for field Name is required', $responseContent);
        self::assertStringContainsString('Entered value for field Abbreviation is required', $responseContent);
    }

    public function testUpdateCurrency(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Yen')
            ->setAbbr('JPY')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Yen',
            'abbr' => 'UJPY',
        ];

        $client->request(RequestMethod::PUT, "/api/v1/currency/{$currency->getId()}", [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Updated Yen', $responseData['data']['name']);
        self::assertSame('UJPY', $responseData['data']['abbr']);
    }

    public function testUpdateCurrencyWrongId(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Yen')
            ->setAbbr('JPY')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Yen',
            'abbr' => 'UJPY',
        ];

        $client->request(RequestMethod::PUT, '/api/v1/currency/10000', [], [], [], content: Json::encode($payload));
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(400, $responseData['code']);
    }

    public function testDeleteCurrency(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Franc')
            ->setAbbr('CHF')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/currency/{$currency->getId()}");
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(204, $responseData['code']);
    }

    public function testDeleteCurrencyWrongId(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CURRENCY_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getCurrencyBuilder()
            ->setName('Franc')
            ->setAbbr('CHF')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/currency/1000000');
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(400, $responseData['code']);
    }

}
