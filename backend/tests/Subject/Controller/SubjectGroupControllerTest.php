<?php

declare(strict_types = 1);

namespace Pm\Tests\Subject\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class SubjectGroupControllerTest extends TestCase
{

    public function testGetSubjectGroups(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_GROUP_READ,
            PermissionEnum::PERM_OBJECT_SHOW,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getObjectFactory()->getSubjectGroupBuilder()
            ->addSubject($subject)
            ->build();

        $this->getObjectFactory()->getSubjectGroupBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/subject-group');
        $responseData = $this->assertResponseSuccess($client);

        self::assertCount(2, $responseData['list']);
        self::assertCount(1, $responseData['list'][0]['subjects']);
        self::assertSame($subject->getId(), $responseData['list'][0]['subjects'][0]['id']);
    }

    public function testGetSubjectGroupDetail(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_GROUP_READ,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $subjectGroup = $this->getObjectFactory()->getSubjectGroupBuilder()
            ->addSubject($subject)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, "/api/v1/subject-group/{$subjectGroup->getId()}");

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame($subjectGroup->getId(), $responseData['data']['id']);
        self::assertSame($subjectGroup->getName(), $responseData['data']['name']);
        self::assertSame($subjectGroup->isActive(), $responseData['data']['active']);
        self::assertCount(1, $responseData['data']['subjects']);
        self::assertSame($subject->getId(), $responseData['data']['subjects'][0]['id']);
    }

    public function testCreateSubjectGroup(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_GROUP_WRITE,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestBody = [
            'name' => 'Test Subject Group',
            'active' => true,
            'subjects' => [$subject->getId()],
        ];
        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/subject-group', [], [], [], content: Json::encode($requestBody));

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('subject-group-created', $responseData['message']);
        self::assertSame('Test Subject Group', $responseData['data']['name']);
        self::assertTrue($responseData['data']['active']);
        self::assertCount(1, $responseData['data']['subjects']);
        self::assertSame($subject->getId(), $responseData['data']['subjects'][0]['id']);
    }

    public function testUpdateSubjectGroup(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_GROUP_WRITE,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $subjectGroup = $this->getObjectFactory()->getSubjectGroupBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestBody = [
            'name' => 'Updated Subject Group',
            'active' => false,
            'subjects' => [$subject->getId()],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, "/api/v1/subject-group/{$subjectGroup->getId()}", [], [], [], content: Json::encode($requestBody));

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('subject-group-updated', $responseData['message']);
        self::assertSame('Updated Subject Group', $responseData['data']['name']);
        self::assertFalse($responseData['data']['active']);
        self::assertCount(1, $responseData['data']['subjects']);
        self::assertSame($subject->getId(), $responseData['data']['subjects'][0]['id']);
    }

    public function testDeleteSubjectGroup(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_GROUP_WRITE,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subjectGroup = $this->getObjectFactory()->getSubjectGroupBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/subject-group/{$subjectGroup->getId()}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertSame(204, $responseData['code']);
    }

}
