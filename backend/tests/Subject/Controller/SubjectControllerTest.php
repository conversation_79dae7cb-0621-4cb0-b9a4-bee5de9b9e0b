<?php

declare(strict_types = 1);

namespace Pm\Tests\Subject\Controller;

use Pm\Core\Http\RequestMethod;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class SubjectControllerTest extends TestCase
{

    public function testGetSubjects(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_READ,
            PermissionEnum::PERM_OBJECT_SHOW,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/subject');
        $responseData = $this->assertResponseSuccess($client);

        self::assertCount(2, $responseData['list']);
        self::assertSame($subject->getEmail(), $responseData['list'][0]['email']);
        self::assertSame($subject->getPhone(), $responseData['list'][0]['phone']);
        self::assertSame($subject->getFirstName(), $responseData['list'][0]['first_name']);
        self::assertSame($subject->getLastName(), $responseData['list'][0]['last_name']);
    }

    public function testGetSubjectDetail(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_READ,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, "/api/v1/subject/{$subject->getId()}");

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame($subject->getId(), $responseData['data']['id']);
        self::assertSame($subject->getFirstName(), $responseData['data']['first_name']);
        self::assertSame($subject->getLastName(), $responseData['data']['last_name']);
        self::assertSame($subject->getPhone(), $responseData['data']['phone']);
        self::assertSame($subject->getEmail(), $responseData['data']['email']);
    }

    public function testCreateSubject(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_WRITE,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/subject', [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'gid' => '1234567890',
            'titleBeforeName' => 'Ing.',
            'titleAfterName' => 'PhD.',
            'phone' => '+420777123456',
            'email' => '<EMAIL>',
            'active' => true,
        ]);

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('subject-created', $responseData['message']);
        self::assertSame('John', $responseData['data']['first_name']);
        self::assertSame('Doe', $responseData['data']['last_name']);
        self::assertSame('1234567890', $responseData['data']['gid']);
        self::assertSame('Ing.', $responseData['data']['title_before_name']);
        self::assertSame('PhD.', $responseData['data']['title_after_name']);
        self::assertSame('+420777123456', $responseData['data']['phone']);
        self::assertSame('<EMAIL>', $responseData['data']['email']);
        self::assertTrue($responseData['data']['active']);
    }

    public function testUpdateSubject(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_WRITE,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, "/api/v1/subject/{$subject->getId()}", [
            'firstName' => 'Jane',
            'lastName' => 'Smith',
            'gid' => '0987654321',
            'titleBeforeName' => 'Dr.',
            'titleAfterName' => 'MBA',
            'phone' => '+49301234567',
            'email' => '<EMAIL>',
            'active' => false,
        ]);

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('subject-updated', $responseData['message']);
        self::assertSame('Jane', $responseData['data']['first_name']);
        self::assertSame('Smith', $responseData['data']['last_name']);
        self::assertSame('0987654321', $responseData['data']['gid']);
        self::assertSame('Dr.', $responseData['data']['title_before_name']);
        self::assertSame('MBA', $responseData['data']['title_after_name']);
        self::assertSame('+49301234567', $responseData['data']['phone']);
        self::assertSame('<EMAIL>', $responseData['data']['email']);
        self::assertFalse($responseData['data']['active']);
    }

    public function testDeleteSubject(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_SUBJECT_WRITE,
        ]);
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $subject = $this->getObjectFactory()->getSubjectBuilder()
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/subject/{$subject->getId()}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertSame(204, $responseData['code']);
    }

}
