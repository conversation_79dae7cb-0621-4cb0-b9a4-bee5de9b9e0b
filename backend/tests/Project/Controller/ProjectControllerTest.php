<?php

declare(strict_types = 1);

namespace Pm\Tests\Project\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use function http_build_query;

class ProjectControllerTest extends TestCase
{

    public function testGetProjectList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PROJECT_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $project1 = $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project A')
            ->setCode('PA')
            ->setActive(true)
            ->build();
        $project2 = $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project B')
            ->setCode('PB')
            ->setActive(false)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/project');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'], 'Response data is null');
        self::assertCount(2, $responseData['list']);
        self::assertSame($project1->getName(), $responseData['list'][0]['name']);
        self::assertSame($project1->getCode(), $responseData['list'][0]['code']);
        self::assertSame($project2->getName(), $responseData['list'][1]['name']);
        self::assertSame($project2->getCode(), $responseData['list'][1]['code']);
    }

    public function testGetProject(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PROJECT_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project A')
            ->setCode('PA')
            ->setActive(true)
            ->build();
        $project2 = $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project B')
            ->setCode('PB')
            ->setActive(false)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/project/2');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['data'], 'Response data is null');
        self::assertSame($project2->getName(), $responseData['data']['name']);
        self::assertSame($project2->getCode(), $responseData['data']['code']);
        self::assertFalse($responseData['data']['active']);
    }

    public function testGetListFilterActive(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PROJECT_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project A')
            ->setActive(true)
            ->build();
        $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project B')
            ->setActive(false)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $refreshedUser = $this->refreshEntity($user);

        // Ensure $user is not null before passing it to createClientWithUser
        self::assertNotNull($refreshedUser, 'User should not be null');

        /** @var User $refreshedUser */
        $refreshedUser = $refreshedUser;

        $client = $this->createClientWithUser($refreshedUser);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'ASC',
            'filter' => [
                [
                    'field' => 'active',
                    'value' => 'false',
                    'type' => 'match',
                ],
            ],
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/project?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotEmpty($responseData['list']);
        self::assertSame('Project B', $responseData['list'][0]['name']);
        self::assertSame($responseData['total_count'], 1);
    }

    public function testCreateProject(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PROJECT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);
        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Project C',
            'code' => 'PC',
            'active' => true,
        ];

        $client->request(RequestMethod::POST, '/api/v1/project', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Project C', $responseData['data']['name']);
        self::assertSame('PC', $responseData['data']['code']);
        self::assertTrue($responseData['data']['active']);
    }

    public function testUpdateProject(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PROJECT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $project = $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project D')
            ->setCode('PD')
            ->setActive(true)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Project D',
            'code' => 'UPD',
            'active' => false,
        ];

        $client->request(RequestMethod::PUT, "/api/v1/project/{$project->getId()}", [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Updated Project D', $responseData['data']['name']);
        self::assertSame('UPD', $responseData['data']['code']);
        self::assertFalse($responseData['data']['active']);
    }

    public function testDeleteProject(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PROJECT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $project = $this->getObjectFactory()->getProjectBuilder()
            ->setName('Project F')
            ->setCode('PF')
            ->setActive(true)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/project/{$project->getId()}");
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(204, $responseData['code']);
    }

}
