<?php

declare(strict_types = 1);

namespace Pm\Tests;

use OldSound\RabbitMqBundle\RabbitMq\AMQPConnectionFactory;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;

class AMQPTestCase extends TestCase
{

    protected AMQPChannel $channel;

    public function setUp(): void
    {
        parent::setUp();
        $this->channel = $this->prepareAMQPChannel();
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        $this->channel->getConnection()?->close();
        $this->channel->close();
    }

    protected function prepareAMQPChannel(): AMQPChannel
    {
        /** @var AMQPConnectionFactory $connectionFactory */
        $connectionFactory = self::getContainer()->get('old_sound_rabbit_mq.connection_factory.default');
        $connection = $connectionFactory->createConnection();

        $queue = $_ENV['ENTITY_OBSERVER_QUEUE_NAME'];
        $channel = $connection->channel();
        $channel->queue_declare($queue, false, true, false, false);
        $channel->queue_purge($queue);
        $message = $channel->basic_get($queue, true);
        self::assertNull($message);

        return $channel;
    }

    protected function readMessage(): ?AMQPMessage
    {
        return $this->channel->basic_get($_ENV['ENTITY_OBSERVER_QUEUE_NAME'], true);
    }

    protected function purgeQueue(): void
    {
        $this->channel->queue_purge($_ENV['ENTITY_OBSERVER_QUEUE_NAME']);
    }

}
