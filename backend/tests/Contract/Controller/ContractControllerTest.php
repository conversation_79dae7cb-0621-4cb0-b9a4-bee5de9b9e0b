<?php

declare(strict_types = 1);

namespace Pm\Tests\Contract\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Core\Http\ResponseStatusCode;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use function http_build_query;

class ContractControllerTest extends TestCase
{

    public function testGetContractList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $contract1 = $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract A')
            ->setCode('CA')
            ->setActive(true)
            ->build();
        $contract2 = $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract B')
            ->setCode('CB')
            ->setActive(false)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/contract');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'], 'Response data is null');
        self::assertCount(2, $responseData['list']);
        self::assertSame($contract1->getName(), $responseData['list'][0]['name']);
        self::assertSame($contract1->getCode(), $responseData['list'][0]['code']);
        self::assertSame($contract2->getName(), $responseData['list'][1]['name']);
        self::assertSame($contract2->getCode(), $responseData['list'][1]['code']);
    }

    public function testGetContract(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract A')
            ->setCode('CA')
            ->setActive(true)
            ->build();
        $contract2 = $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract B')
            ->setCode('CB')
            ->setActive(false)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/contract/2');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['data'], 'Response data is null');
        self::assertSame($contract2->getName(), $responseData['data']['name']);
        self::assertSame($contract2->getCode(), $responseData['data']['code']);
        self::assertFalse($responseData['data']['active']);
    }

    public function testGetListFilterActive(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract A')
            ->setActive(true)
            ->build();
        $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract B')
            ->setActive(false)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $refreshedUser = $this->refreshEntity($user);

        // Ensure $user is not null before passing it to createClientWithUser
        self::assertNotNull($refreshedUser, 'User should not be null');

        /** @var User $refreshedUser */
        $refreshedUser = $refreshedUser;

        $client = $this->createClientWithUser($refreshedUser);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'ASC',
            'filter' => [
                [
                    'field' => 'active',
                    'value' => 'false',
                    'type' => 'match',
                ],
            ],
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/contract?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotEmpty($responseData['list']);
        self::assertSame('Contract B', $responseData['list'][0]['name']);
        self::assertSame($responseData['total_count'], 1);
    }

    public function testCreateContract(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);
        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Contract C',
            'code' => 'CC',
            'active' => true,
        ];

        $client->request(RequestMethod::POST, '/api/v1/contract', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Contract C', $responseData['data']['name']);
        self::assertSame('CC', $responseData['data']['code']);
        self::assertTrue($responseData['data']['active']);
    }

    public function testCreateContractEmptyNameOrCode(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => '',
            'code' => '',
            'active' => true,
        ];

        $client->request(RequestMethod::POST, '/api/v1/contract', [], [], [], content: Json::encode($payload));
        $this->assertResponseError($client, ResponseStatusCode::UNPROCESSABLE_ENTITY_422);
        $responseContent = (string) $client->getResponse()->getContent();
        self::assertStringContainsString('validation-failed', $responseContent);
        self::assertStringContainsString('Entered value for field Name is required', $responseContent);
    }

    public function testUpdateContract(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $contract = $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract D')
            ->setCode('CD')
            ->setActive(true)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Contract D',
            'code' => 'UCD',
            'active' => false,
        ];

        $client->request(RequestMethod::PUT, "/api/v1/contract/{$contract->getId()}", [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Updated Contract D', $responseData['data']['name']);
        self::assertSame('UCD', $responseData['data']['code']);
        self::assertFalse($responseData['data']['active']);
    }

    public function testUpdateContractWrongId(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract E')
            ->setCode('CE')
            ->setActive(true)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Contract E',
            'code' => 'UCE',
            'active' => false,
        ];

        $client->request(RequestMethod::PUT, '/api/v1/contract/10000', [], [], [], content: Json::encode($payload));
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(400, $responseData['code']);
    }

    public function testDeleteContract(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $contract = $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract F')
            ->setCode('CF')
            ->setActive(true)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/contract/{$contract->getId()}");
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(204, $responseData['code']);
    }

    public function testDeleteContractWrongId(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_CONTRACT_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getContractBuilder()
            ->setName('Contract G')
            ->setCode('CG')
            ->setActive(true)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/contract/1000000');
        $responseContent = $client->getResponse()->getContent();
        $responseData = $responseContent !== false ? Json::decode($responseContent, Json::FORCE_ARRAY) : [];

        self::assertSame(400, $responseData['code']);
    }

}
