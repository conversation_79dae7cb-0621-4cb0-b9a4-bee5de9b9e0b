<?php

declare(strict_types = 1);

namespace Pm\Tests\Pops\Controller;

use DateTimeImmutable;
use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Log\Logger;
use Pm\Pops\Command\PopsCheckAttentionCommand;
use Pm\Pops\Entity\PopsEvent;
use Pm\Pops\Facade\PopsAlertFacade;
use Pm\Tests\TestCase;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\NullOutput;

class PopsAlertControllerTest extends TestCase
{

    public function testAcknowledgePopsAlertAction(): void
    {
        $user = $this->prepareUser();

        $popsAlert = $this->getObjectFactory()->getPopsAlertBuilder()
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);

        $parameters = [
            'popsAlerts' => [
                $popsAlert->getId(),
            ],
        ];

        $client->request(RequestMethod::PUT, '/api/v1/pops-alert/acknowledge', content: Json::encode($parameters));

        $this->assertResponseSuccess($client);

        $popsEvents = $this->getEntityManager()->getRepository(PopsEvent::class)->findAll();

        // test that event has been created
        self::assertCount(1, $popsEvents);
    }

    public function testGetActiveAlertsAction(): void
    {
        $user = $this->prepareUser();

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setUser($user)
            ->setIsPopsAlert(true)
            ->setPopsAlertInterval(15)
            ->build();

        $account = $user->getAccount();

        $this->getObjectFactory()->getEventCategoryBuilder()->withAccount($account)->withCode(252)->build();

        // set date created of pop in past to pop alert be created
        $now = new DateTimeImmutable('- 30 minutes');

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setCreatedAt($now)
            ->setFacility($facility1)
            ->setAccount($account)
            ->setInsertedBy($user)
            ->build();

        $this->getObjectFactory()->getEventCategoryBuilder()
            ->withAccount($account)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $user = $this->refreshExistingEntity($user);
        $account = $this->refreshExistingEntity($account);

        $popsCheckAttentionCommand = new PopsCheckAttentionCommand(self::getContainer()->get(Logger::class), self::getContainer()->get(PopsAlertFacade::class));
        $popsCheckAttentionCommand->execute(new ArrayInput([]), new NullOutput());

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/pops-alert/active');
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame($pops->getId(), $responseData['list'][0]['pops_id']);
        self::assertSame($pops->getFacility()->getId(), $responseData['list'][0]['facility_id']);
    }

}
