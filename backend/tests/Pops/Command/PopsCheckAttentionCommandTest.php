<?php

declare(strict_types = 1);

namespace Pm\Tests\Pops\Command;

use DateTimeImmutable;
use Pm\Log\Logger;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Pops\Command\PopsCheckAttentionCommand;
use Pm\Pops\Entity\PopsAlert;
use Pm\Pops\Facade\PopsAlertFacade;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Tester\CommandTester;

class PopsCheckAttentionCommandTest extends TestCase
{

    public function testExecute(): void
    {
        $user = $this->prepareUser();

        $account = $user->getAccount();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_POPS_CREATE,
            PermissionEnum::PERM_POPS_SHOW,
        ]);

        $this->getObjectFactory()->getEventCategoryBuilder()->withAccount($account)->withCode(252)->build();

        $user->addPermissionTemplate($permissionTemplate);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->setIsPopsAlert(true)
            ->setPopsAlertInterval(15)
            ->setUser($user)
            ->setAccount($account)
            ->build();

        // set date created of pop in past to pop alert be created
        $now = new DateTimeImmutable('- 30 minutes');

        $pops = $this->getObjectFactory()->getPopsBuilder()
            ->setCreatedAt($now)
            ->setFacility($facility)
            ->setAccount($account)
            ->setInsertedBy($user)
            ->build();
        $this->getObjectFactory()->getEventCategoryBuilder()
            ->withAccount($account)
            ->build();

        if (!$pops->getInsertedBy() instanceof User) {
            throw new RuntimeException('Invalid value Pops InsertedBy', 400);
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $kernel = static::createKernel();
        $kernel->boot();

        $application = new Application($kernel);
        $application->add(new PopsCheckAttentionCommand(self::getContainer()->get(Logger::class), self::getContainer()->get(PopsAlertFacade::class)));

        $command = $application->find('pops:check-attention-alerts');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
        ]);

        $output = $commandTester->getStatusCode();

        self::assertSame(Command::SUCCESS, $output);

        $popsAlerts = $this->getEntityManager()->getRepository(PopsAlert::class)->findAll();

        self::assertCount(1, $popsAlerts);

        self::assertSame($pops->getId(), $popsAlerts[0]->getPops()->getId());
        self::assertSame($pops->getInsertedBy()->getId(), $popsAlerts[0]->getAlertedUser()->getId());
    }

}
