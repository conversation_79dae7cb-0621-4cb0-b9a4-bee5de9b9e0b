<?php

declare(strict_types = 1);

namespace Pm\Tests\Permission\Facade;

use Pm\Permission\Entity\Permission;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Permission\Facade\CreatePermissionsFacade;
use Pm\Tests\TestCase;
use function count;

class CreatePermissionFacadeTest extends TestCase
{

    public function testCreate(): void
    {
        /** @var CreatePermissionsFacade $createPermissionsFacade */
        $createPermissionsFacade = self::getContainer()->get(CreatePermissionsFacade::class);

        $createPermissionsFacade->createPermissions();

        $permissionEnum = PermissionEnum::cases();
        $permissionEnumCount = count($permissionEnum);

        $permissionRepository = $this->getEntityManager()->getRepository(Permission::class);
        $permissions = $permissionRepository->findAll();

        self::assertCount($permissionEnumCount, $permissions);
    }

}
