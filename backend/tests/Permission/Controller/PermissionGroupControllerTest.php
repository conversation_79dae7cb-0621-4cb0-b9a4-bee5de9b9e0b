<?php

declare(strict_types = 1);

namespace Pm\Tests\Permission\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Permission\Entity\PermissionGroup;
use Pm\Permission\Entity\PermissionTemplate;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;
use Pm\User\Enum\UserRole;
use function array_column;
use function http_build_query;
use function sort;
use function strtotime;
use function time;
use function uniqid;

class PermissionGroupControllerTest extends TestCase
{

    public function testGetLists(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group 1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group 2')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group');
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(2, $responseData['list']);
        self::assertSame($permissionGroup->getId(), $responseData['list'][0]['id']);
    }

    public function testGetListsLimit(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $total = 10;
        for ($i = 1; $i <= $total; $i++) {
            $this->getObjectFactory()->getPermissionGroupBuilder()
                ->setPermissionGroupName('Group ' . $i)
                ->setPermissionTemplate($permissionTemplate)
                ->setUser($user)
                ->build();
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group?limit=5&offset=5');
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(5, $responseData['list']);
        self::assertSame($total, $responseData['total_count']);
        self::assertSame('Group 5', $responseData['list'][0]['name']);
    }

    public function testGetListsFilterName(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group2')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'name',
                    'value' => $permissionGroup->getName(),
                    'type' => 'match',
                ],
            ],
        ];

        $client = $this->createClientWithUser($user);

        $client->request(RequestMethod::GET, '/api/v1/permission-group?' . http_build_query($requestData));
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(1, $responseData['list']);
        self::assertSame($permissionGroup->getName(), $responseData['list'][0]['name']);
    }

    public function testGetListsFilterUser(): void
    {
        // build account
        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $user = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $user2 = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group2')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user2)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'users.id',
                    'value' => $user2->getId(),
                    'type' => 'match',
                ],
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group?' . http_build_query($requestData));
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(1, $responseData['list']);
        self::assertSame($permissionGroup->getName(), $responseData['list'][0]['name']);
        self::assertSame($user2->getId(), $responseData['list'][0]['users'][0]['id']);
    }

    public function testGetListsFilterFacility(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall 1', 'OC23', $user)
            ->withoutPermission()
            ->build();
        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall 2', 'OC25', $user)
            ->withoutPermission()
            ->build();

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->addFacility($facility)
            ->build();

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group2')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->addFacility($facility2)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'facilities.id',
                    'value' => $facility2->getId(),
                    'type' => 'match',
                ],
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group?' . http_build_query($requestData));
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(1, $responseData['list']);
        self::assertSame($permissionGroup->getName(), $responseData['list'][0]['name']);
        self::assertSame($facility2->getId(), $responseData['list'][0]['facilities'][0]['id']);
    }

    public function testGetListsFilterPermissionTemplate(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionTemplate2 = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group2')
            ->setPermissionTemplate($permissionTemplate2)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestData = [
            'filter' => [
                0 => [
                    'field' => 'permissionTemplates.id',
                    'value' => $permissionTemplate2->getId(),
                    'type' => 'match',
                ],
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group?' . http_build_query($requestData));
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(1, $responseData['list']);
        self::assertSame($permissionGroup->getName(), $responseData['list'][0]['name']);
    }

    public function testCreatePermissionGroup(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $facility = $this->prepareFacility($user);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $name = 'Group 1';
        $usage = 'Test group';
        $parameters = [
            'name' => $name,
            'usage' => $usage,
            'permissionTemplates' => [
                $permissionTemplate->getId(),
            ],
            'facilities' => [
                $facility->getId(),
            ],
            'users' => [
                $user->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/permission-group', content: Json::encode($parameters));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(201, $responseData['code']);
        self::assertSame($name, $responseData['data']['name']);
        self::assertSame($usage, $responseData['data']['usage']);
        self::assertSame($permissionTemplate->getId(), $responseData['data']['permission_templates'][0]['id']);
        self::assertNotNull($responseData['data']['id']);
        $id = $responseData['data']['id'];

        /** @var PermissionGroup|null $permissionGroup */
        $permissionGroup = $this->getEntityManager()->find(PermissionGroup::class, $id);
        self::assertNotNull($permissionGroup);
        self::assertSame($name, $permissionGroup->getName());
        self::assertSame($usage, $permissionGroup->getUsage());

        $permissionTemplates = $permissionGroup->getPermissionTemplates();
        self::assertFalse($permissionTemplates->isEmpty());
        $firstPermissionTemplate = $permissionTemplates->first();
        self::assertNotEmpty($firstPermissionTemplate);
        self::assertSame($permissionTemplate->getId(), $firstPermissionTemplate->getId());

        self::assertCount(1, $permissionGroup->getFacilities());
        self::assertCount(1, $permissionGroup->getUsers());
    }

    public function testUpdatePermissionGroup(): void
    {
        // build account
        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $user = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $user2 = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);
        $permissionTemplate2 = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $facility = $this->prepareFacility($user);
        $facility2 = $this->prepareFacility($user);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionTemplates([$permissionTemplate, $permissionTemplate2])
            ->addFacility($facility)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        self::assertCount(1, $permissionGroup->getFacilities());
        self::assertCount(1, $permissionGroup->getUsers());

        $name = 'Test update';
        $usage = 'Test usage update';
        $parameters = [
            'name' => $name,
            'usage' => $usage,
            'permissionTemplates' => [
                $permissionTemplate->getId(),
            ],
            'users' => [
                $user->getId(),
                $user2->getId(),
            ],
            'facilities' => [
                $facility->getId(),
                $facility2->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, '/api/v1/permission-group/' . $permissionGroup->getId(), content: Json::encode($parameters));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);
        self::assertSame($name, $responseData['data']['name']);
        self::assertSame($usage, $responseData['data']['usage']);
        self::assertSame($permissionTemplate->getId(), $responseData['data']['permission_templates'][0]['id']);
        self::assertCount(1, $responseData['data']['permission_templates']);
        self::assertSame($permissionGroup->getId(), $responseData['data']['id']);

        $permissionGroup = $this->refreshExistingEntity($permissionGroup);
        self::assertSame($name, $permissionGroup->getName());
        self::assertSame($usage, $permissionGroup->getUsage());
        $permissionTemplates = $permissionGroup->getPermissionTemplates();
        self::assertCount(1, $permissionTemplates);
        $firstPermissionTemplate = $permissionTemplates->first();
        self::assertInstanceOf(PermissionTemplate::class, $firstPermissionTemplate);
        self::assertSame($permissionTemplate->getId(), $firstPermissionTemplate->getId());
        self::assertCount(2, $permissionGroup->getFacilities());
        self::assertCount(2, $permissionGroup->getUsers());
    }

    public function testGetPermissionGroup(): void
    {
        $now = time() - 10;
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group/' . $permissionGroup->getId());
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);
        self::assertSame($permissionGroup->getName(), $responseData['data']['name']);
        self::assertSame($permissionGroup->getUsage(), $responseData['data']['usage']);
        self::assertSame($permissionTemplate->getId(), $responseData['data']['permission_templates'][0]['id']);
        self::assertSame($permissionGroup->getId(), $responseData['data']['id']);
        self::assertCount(1, $responseData['data']['users']);
        self::assertGreaterThan($now, strtotime((string) $responseData['data']['created_at']));
        self::assertGreaterThan($now, strtotime((string) $responseData['data']['updated_at']));
        self::assertSame($user->getId(), $responseData['data']['inserted_by']['id']);
        self::assertSame($user->getId(), $responseData['data']['updated_by']['id']);
    }

    public function testGetListsSearch(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionTemplate2 = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $permissionGroup2 = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group2')
            ->setPermissionTemplate($permissionTemplate2)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestData = [
            'search' => 'Group',
            'sortBy' => 'name',
            'sortMethod' => 'DESC',
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group?' . http_build_query($requestData));
        $responseData = $this->assertResponseSuccess($client);
        self::assertCount(2, $responseData['list']);
        self::assertSame($permissionGroup2->getName(), $responseData['list'][0]['name']);
    }

    public function testDeletePermissionGroup(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/permission-group/' . $permissionGroup->getId());
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame(200, $responseData['code']);
        self::assertSame('permission-group-deleted', $responseData['message']);

        $permissionGroup = $this->refreshEntity($permissionGroup);
        self::assertNotInstanceOf(PermissionGroup::class, $permissionGroup);
    }

    public function testDeletePermissionGroupNotAllowed(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, []);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/permission-group/' . $permissionGroup->getId());
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testDeletePermissionGroupDifferentAccountNotAllowed(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $user2 = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('xxx')
            ->setRole(UserRole::USER)
            ->build();
        $account2 = $this->getObjectFactory()->getAccountBuilder()->build();
        $user2->setAccount($account2);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group to delete')
            ->setUser($user2)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/permission-group/' . $permissionGroup->getId());
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testUpdatePermissionGroupNotAllowed(): void
    {
        // build account
        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $user = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $user2 = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $permissionTemplate = $this->preparePermissionTemplate($user, []);

        $facility = $this->prepareFacility($user);
        $facility2 = $this->prepareFacility($user);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->addFacility($facility)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        self::assertCount(1, $permissionGroup->getFacilities());
        self::assertCount(1, $permissionGroup->getUsers());

        $name = 'Test update';
        $usage = 'Test usage update';
        $parameters = [
            'name' => $name,
            'usage' => $usage,
            'permissionTemplates' => [
                    $permissionTemplate->getId(),
            ],
            'users' => [
                $user->getId(),
                $user2->getId(),
            ],
            'facilities' => [
                $facility->getId(),
                $facility2->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, '/api/v1/permission-group/' . $permissionGroup->getId(), content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testUpdatePermissionGroupDifferentAccountNotAllowed(): void
    {
        // build account
        $account = $this->getObjectFactory()->getAccountBuilder()->build();

        $user = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount($account)
            ->build();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $facility = $this->prepareFacility($user);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->addFacility($facility)
            ->setUser($user)
            ->build();

        $user2 = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount(
                $this->getObjectFactory()->getAccountBuilder()->build(),
            )
            ->build();
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user2)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user2 = $this->refreshExistingEntity($user2);

        self::assertCount(1, $permissionGroup->getFacilities());
        self::assertCount(1, $permissionGroup->getUsers());

        $name = 'Test update';
        $usage = 'Test usage update';
        $parameters = [
            'name' => $name,
            'usage' => $usage,
            'permissionTemplates' => [
                $permissionTemplate->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user2);
        $client->request(RequestMethod::PUT, '/api/v1/permission-group/' . $permissionGroup->getId(), content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testGetPermissionGroupNotAllowed(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, []);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/permission-group/' . $permissionGroup->getId());
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testGetPermissionGroupDifferentAccountNotAllowed(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionGroup = $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $user2 = $this->getObjectFactory()->getUserBuilder()
            ->setIdentifier('<EMAIL>')
            ->setRole(UserRole::USER)
            ->setAccount(
                $this->getObjectFactory()->getAccountBuilder()->build(),
            )
            ->build();
        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group2')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user2)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user2 = $this->refreshExistingEntity($user2);

        $client = $this->createClientWithUser($user2);
        $client->request(RequestMethod::GET, '/api/v1/permission-group/' . $permissionGroup->getId());
        $this->assertResponseBadRequest($client, "You don't have permissions to do this action.", 403);
    }

    public function testCreateActionEmptyName(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $facility = $this->prepareFacility($user);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $usage = 'Test group';
        $parameters = [
            'name' => '',
            'usage' => $usage,
            'permissionTemplates' => [
                $permissionTemplate->getId(),
            ],
            'facilities' => [
                $facility->getId(),
            ],
            'users' => [
                $user->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/permission-group', content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, 'validation-failed', 422);
        $content = $client->getResponse()->getContent();
        self::assertNotFalse($content);
        $expectedJsonArray = [
            'code' => 422,
            'message' => 'validation-failed',
            'errors' => [
                [
                    'property' => 'name',
                    'message' => 'This value should not be blank.',
                ],
                [
                    'property' => 'name',
                    'message' => 'This value is too short. It should have 1 character or more.',
                ],
            ],
        ];
        self::assertSame($expectedJsonArray, Json::decode($content, Json::FORCE_ARRAY));
    }

    public function testCreateActionEmptyTemplate(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $facility = $this->prepareFacility($user);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $usage = 'Test group';
        $parameters = [
            'name' => 'Test group 1',
            'usage' => $usage,
            'permissionTemplates' => [],
            'facilities' => [
                $facility->getId(),
            ],
            'users' => [
                $user->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/permission-group', content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, 'Permission templates are required', 400);
        $content = $client->getResponse()->getContent();
        self::assertNotFalse($content);
        $expectedJsonArray = [
            'code' => 400,
            'message' => 'Permission templates are required',
        ];
        self::assertSame($expectedJsonArray, Json::decode($content, Json::FORCE_ARRAY));
    }

    public function testSuccessCreationMultiplePermissionTemplates(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionTemplate2 = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $permissionTemplate3 = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($this->permissionTemplate)
            ->setUser($user)
            ->build();

        $facility = $this->prepareFacility($user);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $usage = 'Test group';
        $parameters = [
            'name' => 'Test group 1' . uniqid(),
            'usage' => $usage,
            'permissionTemplates' => [
                $permissionTemplate->getId(),
                $permissionTemplate2->getId(),
                $permissionTemplate3->getId(),
            ],
            'facilities' => [
                $facility->getId(),
            ],
            'users' => [
                $user->getId(),
            ],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/permission-group', content: Json::encode($parameters));
        $this->assertResponseSuccess($client);
        $responseData = $client->getResponse()->getContent();
        self::assertNotFalse($responseData);
        $responseDataArray = Json::decode($responseData, Json::FORCE_ARRAY);

        self::assertArrayHasKey('data', $responseDataArray);
        self::assertIsArray($responseDataArray['data']);
        self::assertArrayHasKey('permission_templates', $responseDataArray['data']);
        self::assertCount(3, $responseDataArray['data']['permission_templates']);
        /** @var array<string> $responsePermissionTemplates */
        $responsePermissionTemplates = $responseDataArray['data']['permission_templates'];
        /** @var array<int> $expectedPermissionTemplatesIds */
        $expectedPermissionTemplatesIds = [
            $permissionTemplate->getId(),
            $permissionTemplate2->getId(),
            $permissionTemplate3->getId(),
        ];
        sort($expectedPermissionTemplatesIds);
        sort($responsePermissionTemplates);
        self::assertSame($expectedPermissionTemplatesIds, array_column($responsePermissionTemplates, 'id'));
    }

    public function testCannotCreatePermissionGroupWithDuplicateName(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_PERMISSION_MANAGE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->setPermissionGroupName('UniqueName')
            ->setUser($user)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $parameters = [
            'name' => 'UniqueName', // Duplicate name
            'usage' => 'Test usage', // Added required field
            'users' => [$user->getId()], // Added required field
            'facilities' => [], // Added required field
            'permissionTemplates' => [$permissionTemplate->getId()], // Added required field
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/permission-group', content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, "This 'UniqueName' is already used.", 422);
        $content = $client->getResponse()->getContent();
        self::assertNotFalse($content);
        $expectedJsonArray = [
            'code' => 422,
            'message' => "This 'UniqueName' is already used.",
            ];
        self::assertSame($expectedJsonArray, Json::decode($content, Json::FORCE_ARRAY));
    }

}
