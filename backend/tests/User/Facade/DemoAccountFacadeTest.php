<?php

declare(strict_types = 1);

namespace Pm\Tests\User\Facade;

use Pm\EventCategory\Entity\EventCategory;
use Pm\Facility\Entity\Facility;
use Pm\Permission\Entity\Permission;
use Pm\Permission\Entity\PermissionApplication;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Permission\Input\PermissionTemplateGetInput;
use Pm\Permission\Repository\PermissionTemplateRepository;
use Pm\Pops\Entity\PopsEvent;
use Pm\Tenant\Entity\Tenant;
use Pm\Tenant\Repository\TenantGroupRepository;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use Pm\User\Facade\DemoAccountFacade;
use function count;
use function str_contains;

class DemoAccountFacadeTest extends TestCase
{

    public function testCreateAndDelete(): void
    {
        /** @var DemoAccountFacade $demoAccountFacade */
        $demoAccountFacade = self::getContainer()->get(DemoAccountFacade::class);
        $demoAccountFacade->deleteDemoData();

        $userRepository = $this->getEntityManager()->getRepository(User::class);
        $tenantRepository = $this->getEntityManager()->getRepository(Tenant::class);

        $tenants = $tenantRepository->findAll();
        self::assertCount(0, $tenants);

        /** @var PermissionTemplateRepository $permissionTemplateRepository */
        $permissionTemplateRepository = self::getContainer()->get(PermissionTemplateRepository::class);

        $permissionTemplates = $permissionTemplateRepository->getPermissionTemplateList(new PermissionTemplateGetInput());
        $permissionTemplatesCount = count($permissionTemplates);
        self::assertEquals(0, $permissionTemplatesCount);

        $permissionTemplatesByPermission = $permissionTemplateRepository
            ->getTemplatesByPermission(PermissionEnum::PERM_PANIC_BUTTON_TENANT);
        self::assertCount(0, $permissionTemplatesByPermission);

        // create demo data
        $demoAccountFacade->createDemoData(true);
        /** @var User[] $users */
        $users = $userRepository->findAll();
        self::assertCount(17, $users);
        $tenants = $tenantRepository->findAll();
        self::assertCount(146, $tenants);

        $facilityRepository = $this->getEntityManager()->getRepository(Facility::class);
        self::assertCount(146, $facilityRepository->findAll());

        $eventCategoryRepository = $this->getEntityManager()->getRepository(EventCategory::class);
        self::assertCount(756, $eventCategoryRepository->findAll());

        $popsEventRepository = $this->getEntityManager()->getRepository(PopsEvent::class);
        self::assertCount(1182, $popsEventRepository->findAll());

        $permissionApplication = $this->getEntityManager()->getRepository(PermissionApplication::class);
        self::assertCount(6, $permissionApplication->findAll());

        // second call should not create new users
        $demoAccountFacade->createDemoData(true);

        self::assertCount(17, $userRepository->findAll());
        self::assertCount(146, $facilityRepository->findAll());
        self::assertCount(1512, $eventCategoryRepository->findAll()); // always imported new set from file
        self::assertCount(2364, $popsEventRepository->findAll()); // always crated new set of events
        self::assertCount(146, $tenants);

        $facility = $facilityRepository->findOneBy([], ['id' => 'ASC']);
        self::assertInstanceOf(Facility::class, $facility);
        $tenantGroupRepository = self::getContainer()->get(TenantGroupRepository::class);
        $tenantGroups = $tenantGroupRepository->getByFacilityAndAccount($facility, $facility->getAccount());
        self::assertCount(1, $tenantGroups);

        $permissionTemplatesByPermission = $permissionTemplateRepository
            ->getTemplatesByPermission(PermissionEnum::PERM_PANIC_BUTTON_TENANT);
        self::assertCount(17, $permissionTemplatesByPermission);

        $permissionTemplates = $permissionTemplateRepository->getPermissionTemplateList(new PermissionTemplateGetInput());
        self::assertCount(17, $permissionTemplates);

        $permissionApplication = $this->getEntityManager()->getRepository(PermissionApplication::class);
        self::assertCount(6, $permissionApplication->findAll());

        $firstApplication = $permissionApplication->findAll()[0];
        $secondApplication = $permissionApplication->findAll()[1];

        self::assertSame('pops', $firstApplication->getCode());
        self::assertSame('Pops', $firstApplication->getName());

        self::assertSame('super-pops', $secondApplication->getCode());
        self::assertSame('SuperPops', $secondApplication->getName());

        $permissionRepository = $this->getEntityManager()->getRepository(Permission::class);
        $permissions = $permissionRepository->findAll();

        foreach ($permissions as $permission) {
            if (str_contains($permission->getCode()->value, 'PERM_POPS_')) {
                self::assertSame('pops', $permission->getApplication()->getCode());
            }

            if (str_contains($permission->getCode()->value, 'PERM_PANIC_')) {
                self::assertSame('code-list', $permission->getApplication()->getCode());
            }
        }

        // delete demo data
        $demoAccountFacade->deleteDemoData();

        /** @var User[] $users */
        $users = $userRepository->findAll();
        self::assertCount(0, $users);

        self::assertCount(0, $facilityRepository->findAll());

        $eventCategoryRepository = $this->getEntityManager()->getRepository(EventCategory::class);
        self::assertCount(0, $eventCategoryRepository->findAll());

        $popsEventRepository = $this->getEntityManager()->getRepository(PopsEvent::class);
        self::assertCount(0, $popsEventRepository->findAll());

        $tenantRepository = $this->getEntityManager()->getRepository(Tenant::class);
        self::assertCount(0, $tenantRepository->findAll());
    }

}
