<?php

declare(strict_types = 1);

namespace Pm\Tests;

use Gesdinet\JWTRefreshTokenBundle\Security\Http\Authenticator\RefreshTokenAuthenticator;
use Pm\Tools\ServicesConfigProvider;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Throwable;
use function array_keys;
use function in_array;

class ServicesTest extends WebTestCase
{

    private const IGNORED_SERVICES = [
        'Symfony\\Component\\Security\\Guard\\AbstractGuardAuthenticator', // todo class not found
        'gesdinet.jwtrefreshtoken',
        'gesdinet.jwtrefreshtoken.refresh_token_generator',
        'gesdinet.jwtrefreshtoken.refresh_token_manager',
        RefreshTokenAuthenticator::class,
    ];

    public function testCreateAllServices(): void
    {
        $container = self::getContainer();

        $allServices = $container->getServiceIds();

        foreach ($allServices as $key) {
            if (in_array($key, self::IGNORED_SERVICES, true)) {
                continue;
            }

            try {
                $container->get($key);
            } catch (Throwable $e) {
                $message = $e->getMessage();
                self::fail("Service {$key} could not be created. '{$message}'");
            }
        }
        $this->expectNotToPerformAssertions();
    }

    public function testOrderOfServices(): void
    {
        $servicesConfigProvided = self::getContainer()->get(ServicesConfigProvider::class);
        $services = $servicesConfigProvided->getServices();

        $currentOrder = array_keys($services);
        $expectedOrder = array_keys($servicesConfigProvided->sort($services));

        foreach ($currentOrder as $order => $value) {
            self::assertSame(
                $expectedOrder[$order],
                $value,
                "Services are not sorted properly. Service: '{$value}' is out of order. Check {$servicesConfigProvided->getServicePath()}",
            );
        }
    }

}
