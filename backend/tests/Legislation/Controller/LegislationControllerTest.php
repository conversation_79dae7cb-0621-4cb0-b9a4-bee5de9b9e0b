<?php

declare(strict_types = 1);

namespace Pm\Tests\Legislation\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class LegislationControllerTest extends TestCase
{

    public function testGetLegislationList(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_LEGISLATION_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setAbbr('EUR')
            ->setName('Euro')
            ->build();

        $legislation1 = $this->getObjectFactory()->getLegislationBuilder()
            ->setName('Legislation X')
            ->setActive(true)
            ->setCurrency($currency)
            ->build();

        $legislation2 = $this->getObjectFactory()->getLegislationBuilder()
            ->setName('Legislation Y')
            ->setActive(false)
            ->setCurrency($currency)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/legislation');
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['list'], 'Response data is null');
        self::assertCount(2, $responseData['list']);
        self::assertSame($legislation1->getName(), $responseData['list'][0]['name']);
        self::assertSame($legislation2->getName(), $responseData['list'][1]['name']);
        self::assertTrue($responseData['list'][0]['active']);
        self::assertFalse($responseData['list'][1]['active']);

        // Test currency field in response
        self::assertArrayHasKey('currency', $responseData['list'][0]);
        self::assertArrayHasKey('currency', $responseData['list'][1]);
        self::assertSame($currency->getId(), $responseData['list'][0]['currency']['id']);
        self::assertSame($currency->getName(), $responseData['list'][0]['currency']['name']);
        self::assertSame($currency->getAbbr(), $responseData['list'][0]['currency']['abbr']);
    }

    public function testGetLegislationEntity(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_LEGISLATION_READ,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setAbbr('EUR')
            ->setName('Euro')
            ->build();

        $legislation = $this->getObjectFactory()->getLegislationBuilder()
            ->setName('Legislation X')
            ->setActive(true)
            ->setCurrency($currency)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, '/api/v1/legislation/' . $legislation->getId());
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['data'], 'Response data is null');
        self::assertSame($legislation->getName(), $responseData['data']['name']);
        self::assertTrue($responseData['data']['active']);

        // Test currency field in response
        self::assertArrayHasKey('currency', $responseData['data']);
        self::assertSame($currency->getId(), $responseData['data']['currency']['id']);
        self::assertSame($currency->getName(), $responseData['data']['currency']['name']);
        self::assertSame($currency->getAbbr(), $responseData['data']['currency']['abbr']);
    }

    public function testCreateLegislationEntity(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_LEGISLATION_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setAbbr('EUR')
            ->setName('Euro')
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Legislation X',
            'active' => true,
            'currencyId' => $currency->getId(),
        ];

        $client->request(RequestMethod::POST, '/api/v1/legislation', [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotNull($responseData['data'], 'Response data is null');
        self::assertSame('Legislation X', $responseData['data']['name']);
        self::assertTrue($responseData['data']['active']);

        // Test currency field in response
        self::assertArrayHasKey('currency', $responseData['data']);
        self::assertSame($currency->getId(), $responseData['data']['currency']['id']);
        self::assertSame($currency->getName(), $responseData['data']['currency']['name']);
        self::assertSame($currency->getAbbr(), $responseData['data']['currency']['abbr']);
    }

    public function testUpdateLegislationEntity(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_LEGISLATION_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setAbbr('EUR')
            ->setName('Euro')
            ->build();

        $legislation = $this->getObjectFactory()->getLegislationBuilder()
            ->setName('Legislation X')
            ->setActive(true)
            ->setCurrency($currency)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Legislation Y',
            'active' => false,
            'currencyId' => $currency->getId(),
        ];

        $client->request(RequestMethod::PUT, '/api/v1/legislation/' . $legislation->getId(), [], [], [], content: Json::encode($payload));
        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('Legislation Y', $responseData['data']['name']);
        self::assertFalse($responseData['data']['active']);

        // Test currency field in response
        self::assertArrayHasKey('currency', $responseData['data']);
        self::assertSame($currency->getId(), $responseData['data']['currency']['id']);
        self::assertSame($currency->getName(), $responseData['data']['currency']['name']);
        self::assertSame($currency->getAbbr(), $responseData['data']['currency']['abbr']);
    }

    public function testDeleteLegislationEntity(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_LEGISLATION_WRITE,
        ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();
        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setAbbr('EUR')
            ->setName('Euro')
            ->build();

        $legislation = $this->getObjectFactory()->getLegislationBuilder()
            ->setName('Legislation X')
            ->setActive(true)
            ->setCurrency($currency)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, '/api/v1/legislation/' . $legislation->getId());
        $responseData = $this->assertResponseSuccess($client);

        $data = $responseData['data'] ?? null;
        self::assertNull($data);
    }

    public function testUpdateLegislationWrongId(): void
    {
        $user = $this->prepareUser();

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_LEGISLATION_WRITE,
            ]);

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Group1')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->build();

        $currency = $this->getObjectFactory()->getCurrencyBuilder()
            ->setAbbr('EUR')
            ->setName('Euro')
            ->build();

        $this->getObjectFactory()->getLegislationBuilder()
            ->setName('Legislation E')
            ->setActive(true)
            ->setCurrency($currency)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $user = $this->refreshExistingEntity($user);

        $client = $this->createClientWithUser($user);
        $payload = [
            'name' => 'Updated Legislation E',
            'active' => false,
            'currencyId' => $currency->getId(),
        ];

        $client->request(RequestMethod::PUT, '/api/v1/legislation/10000', [], [], [], content: Json::encode($payload));
        $this->assertResponseBadRequest($client, 'Legislation with ID 10000 not found.', 400);
    }

}
