<?php

declare(strict_types = 1);

namespace Pm\Tests\Tenant\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;

class TenantGroupControllerTest extends TestCase
{

    public function testGetTenantGroups(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_TENANT_GROUP_READ,
            PermissionEnum::PERM_OBJECT_SHOW,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->setParameters('OC Mall', 'OC23', $user)
            ->build();
        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setPermissionTemplate($permissionTemplate)
            ->setParameters('OC Mall 2', 'OC24', $user)
            ->build();

        $tenant = $this->getObjectFactory()->getTenantBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();
        $group = $this->getObjectFactory()->getTenantGroupBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();
        $group->addTenant($tenant);

        $this->getObjectFactory()->getTenantGroupBuilder()
            ->setFacility($facility2)
            ->setAccount($user->getAccount())
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, "/api/v1/tenant-group/facility/{$facility->getId()}");
        $responseData = $this->assertResponseSuccess($client);

        self::assertCount(1, $responseData['list']);
        self::assertSame($tenant->getId(), $responseData['list'][0]['id']);
        self::assertSame($facility->getAccount()->getId(), $responseData['list'][0]['tenants'][0]['account']['id']);
        self::assertSame($facility->getId(), $responseData['list'][0]['tenants'][0]['facility']['id']);
    }

    public function testGetTenantGroupDetail(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_TENANT_GROUP_READ,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall', 'OC23', $user)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $tenant = $this->getObjectFactory()->getTenantBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();

        $tenantGroup = $this->getObjectFactory()->getTenantGroupBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->addTenant($tenant)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::GET, "/api/v1/tenant-group/{$tenantGroup->getId()}");

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame($tenantGroup->getId(), $responseData['data']['id']);
        self::assertSame($tenantGroup->getName(), $responseData['data']['name']);
        self::assertSame($tenantGroup->isActive(), $responseData['data']['active']);
        self::assertSame($facility->getId(), $responseData['data']['tenants'][0]['facility']['id']);
        self::assertCount(1, $responseData['data']['tenants']);
        self::assertSame($tenant->getId(), $responseData['data']['tenants'][0]['id']);
        self::assertFalse($responseData['data']['read_only']);
        self::assertFalse($responseData['data']['tenants'][0]['read_only']);
    }

    public function testCreateTenantGroup(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_TENANT_GROUP_WRITE,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall', 'OC23', $user)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $tenant = $this->getObjectFactory()->getTenantBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $requestBody = [
            'name' => 'Test Tenant Group',
            'active' => true,
            'tenants' => [$tenant->getId()],
        ];

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::POST, '/api/v1/tenant-group', [], [], [], content: Json::encode($requestBody));

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('tenant-group-created', $responseData['message']);
        self::assertSame('Test Tenant Group', $responseData['data']['name']);
        self::assertTrue($responseData['data']['active']);
        self::assertCount(1, $responseData['data']['tenants']);
        self::assertSame($tenant->getId(), $responseData['data']['tenants'][0]['id']);
    }

    public function testUpdateTenantGroup(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_TENANT_GROUP_WRITE,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall', 'OC23', $user)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $tenant = $this->getObjectFactory()->getTenantBuilder()
            ->setAccount($user->getAccount())
            ->setFacility($facility)
            ->build();

        $tenantGroup = $this->getObjectFactory()->getTenantGroupBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->addTenant($tenant)
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::PUT, "/api/v1/tenant-group/{$tenantGroup->getId()}", [
            'name' => 'Updated Tenant Group',
            'active' => false,
            'tenants' => [$tenant->getId()],
        ]);

        $responseData = $this->assertResponseSuccess($client);

        self::assertSame('tenant-group-updated', $responseData['message']);
        self::assertSame('Updated Tenant Group', $responseData['data']['name']);
        self::assertFalse($responseData['data']['active']);
        self::assertCount(1, $responseData['data']['tenants']);
        self::assertSame($tenant->getId(), $responseData['data']['tenants'][0]['id']);
    }

    public function testDeleteTenantGroup(): void
    {
        $user = $this->prepareUser();
        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_TENANT_GROUP_WRITE,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Mall', 'OC23', $user)
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $tenantGroup = $this->getObjectFactory()->getTenantGroupBuilder()
            ->setFacility($facility)
            ->setAccount($user->getAccount())
            ->build();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $client = $this->createClientWithUser($user);
        $client->request(RequestMethod::DELETE, "/api/v1/tenant-group/{$tenantGroup->getId()}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertSame(204, $responseData['code']);
    }

}
