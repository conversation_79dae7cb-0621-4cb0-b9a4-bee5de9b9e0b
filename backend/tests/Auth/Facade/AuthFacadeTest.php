<?php

declare(strict_types = 1);

namespace Pm\Tests\Auth\Facade;

use Pm\Core\Security\Security;
use Pm\Tests\TestCase;

class AuthFacadeTest extends TestCase
{

    public function testIsTokenValid(): void
    {
        $user = $this->getObjectFactory()->getUserBuilder()
            ->disable()
            ->build();

        $securityMock = $this->createMock(Security::class);
        $securityMock->expects(self::once())
            ->method('getUser')
            ->willReturn($user);

        self::assertFalse($securityMock->getUser()->isActive());

        $enabledUser = $this->getObjectFactory()->getUserBuilder()
            ->build();

        $securityMock = $this->createMock(Security::class);
        $securityMock->expects(self::once())
            ->method('getUser')
            ->willReturn($enabledUser);

        self::assertTrue($securityMock->getUser()->isActive());
    }

}
