<?php

declare(strict_types = 1);

namespace Pm\Tests\Facility\Permission;

use Pm\Core\Exception\AclException;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;
use Pm\User\Security\UserVoter;

class FacilityPermissionTest extends TestCase
{

    /**
     * test facility permission validation for signed in user
     */
    public function testHasPermissionForFacility(): void
    {
        $admin = $this->getObjectFactory()
            ->getUserBuilder()
            ->setAccount($this->getObjectFactory()->getAccountBuilder()->build())
            ->build();

        $permissionTemplate = $this->preparePermissionTemplate($admin, [
            PermissionEnum::PERM_POPS_CREATE,
        ]);

        $facility = $this->getObjectFactory()->getFacilityBuilder()
            ->setName('OC Mall')
            ->setCode('OC23')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($admin)
            ->build();

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Test Permission Group')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($admin)
            ->addFacility($facility)
            ->build();

        $this->getEntityManager()->flush();

        $this->createClientWithUser($admin);

        $container = self::getContainer();
        $userVoter = $container->get(UserVoter::class);

        $result = $userVoter->hasPermissionForFacility($facility, PermissionEnum::PERM_POPS_CREATE);
        self::assertSame($result, true);

        $this->expectException(AclException::class);
        $userVoter->hasPermissionForFacility($facility, PermissionEnum::PERM_EVENTS_OBJECT_EDIT);
    }

}
