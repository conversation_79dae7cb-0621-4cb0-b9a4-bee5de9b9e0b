<?php

declare(strict_types = 1);

namespace Pm\Tests\Facility\Controller;

use Nette\Utils\Json;
use Pm\Core\Http\RequestMethod;
use Pm\Facility\Entity\FacilityGroup;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Tests\TestCase;
use Pm\User\Entity\User;
use function array_map;
use function array_merge;
use function http_build_query;

class FacilityGroupControllerTest extends TestCase
{

    /**
     * @param list<PermissionEnum> $permissions
     */
    private function prepareData(User $user, int $numberOfGroups = 1, array $permissions = []): FacilityGroup
    {
        $permissionTemplate = $this->preparePermissionTemplate($user, array_merge([
            PermissionEnum::PERM_OBJECT_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_SHOW,
        ], $permissions));

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('OC Nakupy', 'OC23', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('KFC fast food', 'FF01', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility3 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Muzeum Benesov', 'MMBen', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Test Permission Group')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->addFacility($facility1)
            ->addFacility($facility2)
            ->addFacility($facility3)
            ->build();

        $this->getObjectFactory()->getPermissionGroupBuilder()
            ->setPermissionGroupName('Secondary Test Permission Group')
            ->setPermissionTemplate($permissionTemplate)
            ->setUser($user)
            ->addFacility($facility2)
            ->build();

        $facilityGroup = $this->getObjectFactory()->getFacilityGroupBuilder()
            ->setName('Test Facility Group 1')
            ->setAccount($user->getAccount())
            ->setUser($user)
            ->setFacilities([$facility1, $facility2, $facility3])
            ->build();
        if ($numberOfGroups > 1) {
            for ($i = 2; $i <= $numberOfGroups; $i++) {
                $this->getObjectFactory()->getFacilityGroupBuilder()
                    ->setName('Test Facility Group ' . $i)
                    ->setAccount($user->getAccount())
                    ->setUser($user)
                    ->setFacilities([$facility1, $facility2, $facility3])
                    ->build();
            }
        }

        $this->getEntityManager()->flush();

        return $facilityGroup;
    }

    public function testGetAction(): void
    {
        $user = $this->prepareUser();
        $facilityGroup = $this->prepareData($user);
        $this->getEntityManager()->clear();
        $facilityGroup = $this->refreshEntity($facilityGroup);
        self::assertInstanceOf(FacilityGroup::class, $facilityGroup);
        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);

        $client = $this->createClientWithUser($user);

        $client->request(RequestMethod::GET, "/api/v1/facility-group/{$facilityGroup->getId()}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertNotEmpty($responseData['data']);
        self::assertFalse($responseData['data']['read_only']);
        self::assertSame('Test Facility Group 1', $responseData['data']['name']);
        self::assertSame($user->getAccount()->getId(), $responseData['data']['account']['id']);
        self::assertCount(3, $responseData['data']['facilities']);

        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['data']['facilities']);
        self::assertContains('OC23', $facilityCodes);
        self::assertContains('FF01', $facilityCodes);
        self::assertContains('MMBen', $facilityCodes);
    }

    public function testGetListAction(): void
    {
        $user = $this->prepareUser();
        $this->prepareData($user, 4);
        $this->getEntityManager()->clear();
        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'DESC',
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/facility-group?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertNotEmpty($responseData['list']);

        self::assertSame('Test Facility Group 4', $responseData['list'][0]['name']);
        self::assertSame('usage.4', $responseData['list'][0]['usage']);
        self::assertSame($responseData['total_count'], 4);

        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['list'][0]['facilities']);
        self::assertContains('OC23', $facilityCodes);
    }

    public function testGetListSearchAction(): void
    {
        $user = $this->prepareUser();
        $this->prepareData($user, 4);
        $this->getEntityManager()->clear();
        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'ASC',
            'search' => 'Group 3',
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/facility-group?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertNotEmpty($responseData['list']);

        self::assertSame('Test Facility Group 3', $responseData['list'][0]['name']);
        self::assertSame('usage.3', $responseData['list'][0]['usage']);
        self::assertSame($responseData['total_count'], 1);

        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['list'][0]['facilities']);
        self::assertContains('OC23', $facilityCodes);
    }

    public function testGetListFilterNameAction(): void
    {
        $user = $this->prepareUser();
        $this->prepareData($user, 4);
        $this->getEntityManager()->clear();
        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'ASC',
            'filter' => [
                [
                    'field' => 'name',
                    'value' => 'Group',
                    'type' => 'contains',
                ],
            ],
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/facility-group?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertNotEmpty($responseData['list']);

        self::assertSame('Test Facility Group 1', $responseData['list'][0]['name']);
        self::assertSame('usage.1', $responseData['list'][0]['usage']);
        self::assertSame($responseData['total_count'], 4);

        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['list'][0]['facilities']);
        self::assertContains('OC23', $facilityCodes);
    }

    public function testGetListFilterIdAction(): void
    {
        $user = $this->prepareUser();
        $facilityGroup = $this->prepareData($user, 4);
        $this->getEntityManager()->clear();
        $facilityGroup = $this->refreshEntity($facilityGroup);
        self::assertInstanceOf(FacilityGroup::class, $facilityGroup);
        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'offset' => 0,
            'limit' => 10,
            'sortBy' => 'id',
            'sortMethod' => 'ASC',
            'filter' => [
                [
                    'field' => 'id',
                    'value' => $facilityGroup->getId(),
                    'type' => 'match',
                ],
            ],
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/facility-group?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertNotEmpty($responseData['list']);

        self::assertSame('Test Facility Group 1', $responseData['list'][0]['name']);
        self::assertSame('usage.1', $responseData['list'][0]['usage']);
        self::assertSame($responseData['total_count'], 1);

        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['list'][0]['facilities']);
        self::assertContains('OC23', $facilityCodes);
    }

    public function testGetListFilterLimitAction(): void
    {
        $user = $this->prepareUser();
        $this->prepareData($user, 4);
        $this->getEntityManager()->clear();
        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);

        $client = $this->createClientWithUser($user);

        $requestData = [
            'offset' => 0,
            'limit' => 2,
            'sortBy' => 'name',
            'sortMethod' => 'DESC',
        ];
        $parameters = http_build_query($requestData);

        $client->request(RequestMethod::GET, "/api/v1/facility-group?{$parameters}");
        $responseData = $this->assertResponseSuccess($client);
        self::assertNotEmpty($responseData['list']);

        self::assertSame('Test Facility Group 4', $responseData['list'][0]['name']);
        self::assertSame('usage.4', $responseData['list'][0]['usage']);
        self::assertSame($responseData['total_count'], 4);
        // Check that the facility is present in the response, without relying on specific order
        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['list'][0]['facilities']);
        self::assertContains('OC23', $facilityCodes);
    }

    public function testCreateAction(): void
    {
        $user = $this->prepareUser();
        $this->prepareData($user, 1, [PermissionEnum::PERM_OBJECT_GROUP_CREATE]);

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_OBJECT_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Facility 1', 'F1', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Facility 2', 'F2', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);
        $client = $this->createClientWithUser($user);

        $parameters = [
            'name' => 'Test Facility Group Create',
            'usage' => 'Test Create',
            'facilities' => [
                $facility1->getId(),
                $facility2->getId(),
            ],
        ];

        $client->request(RequestMethod::POST, '/api/v1/facility-group', content: Json::encode($parameters));
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotEmpty($responseData['data']);

        self::assertSame('Test Facility Group Create', $responseData['data']['name']);
        self::assertSame($user->getAccount()->getId(), $responseData['data']['account']['id']);
        self::assertCount(2, $responseData['data']['facilities']);
        // Check that all facilities are present in the response, without relying on specific order
        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['data']['facilities']);
        self::assertContains('F1', $facilityCodes);
        self::assertContains('F2', $facilityCodes);
    }

    public function testUpdateAction(): void
    {
        $user = $this->prepareUser();
        $facilityGroup = $this->prepareData($user, 1, [PermissionEnum::PERM_OBJECT_GROUP_EDIT]);

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_OBJECT_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_EDIT,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Facility 1', 'F1', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Facility 2', 'F2', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);
        $client = $this->createClientWithUser($user);

        $parameters = [
            'name' => 'Test Facility Group Update',
            'usage' => 'Test Update',
            'facilities' => [
                $facility1->getId(),
                $facility2->getId(),
            ],
        ];

        $client->request(RequestMethod::PUT, '/api/v1/facility-group/' . $facilityGroup->getId(), content: Json::encode($parameters));
        $responseData = $this->assertResponseSuccess($client);

        self::assertNotEmpty($responseData['data']);

        self::assertSame('Test Facility Group Update', $responseData['data']['name']);
        self::assertSame($user->getAccount()->getId(), $responseData['data']['account']['id']);
        self::assertCount(2, $responseData['data']['facilities']);
        // Check that all facilities are present in the response, without relying on specific order
        $facilityCodes = array_map(static function (array $facility): string {
            return (string) $facility['code'];
        }, $responseData['data']['facilities']);
        self::assertContains('F1', $facilityCodes);
        self::assertContains('F2', $facilityCodes);
    }

    public function testDeleteAction(): void
    {
        $user = $this->prepareUser();
        $facilityGroup = $this->prepareData($user, 1, [PermissionEnum::PERM_OBJECT_GROUP_DELETE]);
        $this->getEntityManager()->clear();
        $facilityGroup = $this->refreshEntity($facilityGroup);
        self::assertInstanceOf(FacilityGroup::class, $facilityGroup);
        $user = $this->refreshEntity($user);

        self::assertInstanceOf(User::class, $user);
        $client = $this->createClientWithUser($user);

        $client->request(RequestMethod::DELETE, '/api/v1/facility-group/' . $facilityGroup->getId());
        $responseData = $this->assertResponseSuccess($client);

        self::assertEmpty($responseData['data']);
        self::assertSame('facility-group-deleted', $responseData['message']);
    }

    public function testCreateActionEmptyName(): void
    {
        $user = $this->prepareUser();
        $this->prepareData($user, 1, [PermissionEnum::PERM_OBJECT_GROUP_CREATE]);

        $permissionTemplate = $this->preparePermissionTemplate($user, [
            PermissionEnum::PERM_OBJECT_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_SHOW,
            PermissionEnum::PERM_OBJECT_GROUP_CREATE,
        ]);

        $facility1 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Facility 1', 'F1', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();

        $facility2 = $this->getObjectFactory()->getFacilityBuilder()
            ->setParameters('Facility 2', 'F2', $user)
            ->setAccount($user->getAccount())
            ->setPermissionTemplate($permissionTemplate)
            ->build();
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $user = $this->refreshEntity($user);
        self::assertInstanceOf(User::class, $user);
        $client = $this->createClientWithUser($user);

        $parameters = [
            'name' => '',
            'usage' => 'Test Create',
            'facilities' => [
                $facility1->getId(),
                $facility2->getId(),
            ],
        ];

        $client->request(RequestMethod::POST, '/api/v1/facility-group', content: Json::encode($parameters));
        $this->assertResponseBadRequest($client, 'validation-failed', 422);
        $content = $client->getResponse()->getContent();
        self::assertNotFalse($content);
        $expectedJsonArray = [
            'code' => 422,
            'message' => 'validation-failed',
            'errors' => [
                [
                    'property' => 'name',
                    'message' => 'This value should not be blank.',
                ],
                [
                    'property' => 'name',
                    'message' => 'This value is too short. It should have 1 character or more.',
                ],
            ],
        ];
        self::assertSame($expectedJsonArray, Json::decode($content, Json::FORCE_ARRAY));
    }

}
