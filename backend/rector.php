<?php

declare(strict_types = 1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Php55\Rector\String_\StringClassNameToClassConstantRector;
use <PERSON>\Php74\Rector\Closure\ClosureToArrowFunctionRector;
use <PERSON>\Php80\Rector\Class_\ClassPropertyAssignToConstructorPromotionRector;
use <PERSON>\Set\ValueObject\LevelSetList;
use Rector\Set\ValueObject\SetList;
use <PERSON>\Symfony\Set\SymfonySetList;
use <PERSON>\Transform\Rector\StaticCall\StaticCallToFuncCallRector;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/bin',
        __DIR__ . '/config',
        __DIR__ . '/migrations',
        __DIR__ . '/public',
        __DIR__ . '/src',
        __DIR__ . '/tests',
    ]);

    $rectorConfig->sets([
        SetList::TYPE_DECLARATION,
        LevelSetList::UP_TO_PHP_82,
        SymfonySetList::SYMFONY_64,
    ]);

    $rectorConfig->skip([ // phpcs:ignore SlevomatCodingStandard.Arrays.DisallowPartiallyKeyed.DisallowedPartiallyKeyed
        0 => ClosureToArrowFunctionRector::class,
        1 => StaticCallToFuncCallRector::class,
        ClassPropertyAssignToConstructorPromotionRector::class => [
            __DIR__ . '/src/Account/Entity',
            __DIR__ . '/src/Auth/Entity',
            __DIR__ . '/src/AuditLog/Entity',
            __DIR__ . '/src/Location/Entity',
            __DIR__ . '/src/Core/Entity',
            __DIR__ . '/src/Facility/Entity',
            __DIR__ . '/src/Log/Entity',
            __DIR__ . '/src/Permission/Entity',
            __DIR__ . '/src/User/Entity',
            __DIR__ . '/src/Pops/Entity',
            __DIR__ . '/src/EventCategory/Entity',
            __DIR__ . '/src/Tenant/Entity',
            __DIR__ . '/src/SuperPops/Entity',
        ],
        StringClassNameToClassConstantRector::class => [
            __DIR__ . '/migrations/Version20250423091152.php',
        ],
    ]);
};
