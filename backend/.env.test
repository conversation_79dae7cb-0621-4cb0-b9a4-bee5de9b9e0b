# define your env variables for the test env here
APP_ENV=test
KERNEL_CLASS='Pm\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

DATABASE_URL=*************************************/pm-test?charset=utf8&sslmode=prefer

###> symfony/firebase-notifier ###
FIREBASE_DSN=firebase://sample_to_fail_TOKEN:password@default
###< symfony/firebase-notifier ###

MERCURE_JWT_TOKEN=eyJhbGciOiJIUzI1NiJ9.eyJtZXJjdXJlIjp7InB1Ymxpc2giOlsiKiJdLCJzdWJzY3JpYmUiOlsiKiJdLCJwYXlsb2FkIjp7InVzZXIiOiJodHRwczovL2V4YW1wbGUuY29tL3VzZXJzL2R1bmdsYXMiLCJyZW1vdGVBZGRyIjoiMTI3LjAuMC4xIn19fQ.iYuDR0kCpMKqYQg-2czhQVikpu0akO8iJzvlFFcUyZU
MERCURE_JWT_SECRET=developmentSecretHaveToBeChangedOnProduction!
MERCURE_PUBLISHER_JWT_KEY=developmentSecretHaveToBeChangedOnProduction!
MERCURE_SUBSCRIBER_JWT_KEY=developmentSecretHaveToBeChangedOnProduction!

# Prevent failing local filesystem on generating public url on local storage
FS_TEST_PUBLIC_URL=http://localhost/

ENTITY_OBSERVER_QUEUE_NAME='test_observed_entities'
ENTITY_OBSERVER_EXCHANGE_NAME='test_entity_observer'

# Optionally dis/allow API endpoints based on environment. "false" means disallowed.
# createAccountAction is disallowed by default. Use it for testing purposes only.
API_OPTIONAL_ENDPOINT='{}'

AWS_S3_BUCKET=assets.pm3.dev.m2c.eu
