parameters:
    ignoreErrors:
        -
            message: "#^Constant fetch \\:\\:class is prohibited on unknown type \\(\\$entity\\)$#"
            count: 1
            path: src/AuditLog/Service/AuditLogService.php

        -
            message: "#^Method Pm\\\\AuditLog\\\\Service\\\\AuditLogService\\:\\:collectionDiff\\(\\) has parameter \\$collection with generic class Doctrine\\\\ORM\\\\PersistentCollection but does not specify its types\\: TKey, T$#"
            count: 1
            path: src/AuditLog/Service/AuditLogService.php

        -
            message: "#^Method Pm\\\\AuditLog\\\\Service\\\\AuditLogService\\:\\:diff\\(\\) has parameter \\$ch with generic class Doctrine\\\\ORM\\\\PersistentCollection but does not specify its types\\: TKey, T$#"
            count: 1
            path: src/AuditLog/Service/AuditLogService.php

        -
            message: "#^Using \\(array\\) is discouraged, please avoid using that\\.$#"
            count: 1
            path: src/AuditLog/Service/AuditLogService.php

        -
            message: "#^Exception \\(Nette\\\\Utils\\\\JsonException\\) not passed as previous to \\\\Pm\\\\Core\\\\Exception\\\\InvalidInputException\\:\\:createInvalidTypeException\\(\\$key, 'json'\\)$#"
            count: 1
            path: src/Core/Input/InputArgs.php

        -
            message: "#^Overwriting variable \\$value while changing its type from string to int$#"
            count: 1
            path: src/Core/Input/InputArgs.php

        -
            message: "#^Property Pm\\\\Log\\\\Entity\\\\LogEntity\\:\\:loggableEntity has useless default value \\(overwritten in constructor\\)$#"
            count: 1
            path: src/Log/Entity/LogEntity.php
