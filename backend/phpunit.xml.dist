<?xml version="1.0" encoding="UTF-8"?>
<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd" colors="true" enforceTimeLimit="false" defaultTimeLimit="15" timeoutForMediumTests="30" timeoutForLargeTests="270" bootstrap="tests/bootstrap.php" beStrictAboutOutputDuringTests="true" beStrictAboutChangesToGlobalState="true" beStrictAboutTodoAnnotatedTests="true" beStrictAboutTestsThatDoNotTestAnything="true" stderr="true" failOnEmptyTestSuite="true" failOnIncomplete="true" failOnNotice="true" failOnRisky="true" failOnDeprecation="false" failOnWarning="true" displayDetailsOnIncompleteTests="true" displayDetailsOnSkippedTests="true" displayDetailsOnTestsThatTriggerDeprecations="true" displayDetailsOnTestsThatTriggerErrors="true" displayDetailsOnTestsThatTriggerNotices="true" displayDetailsOnTestsThatTriggerWarnings="true" cacheDirectory="var/phpunit.result" beStrictAboutCoverageMetadata="false">
    <!-- todo failOnDeprecation Set to true to enable deprecation notices -->
    <php>
        <ini name="error_reporting" value="-1"/>
        <server name="APP_ENV" value="test" force="true"/>
        <server name="SHELL_VERBOSITY" value="-1"/>
        <server name="SYMFONY_PHPUNIT_REMOVE" value=""/>
        <server name="SYMFONY_PHPUNIT_VERSION" value="9.5"/>
        <env name="APP_ENV" value="test" force="true" />
    </php>
    <testsuites>
        <testsuite name="Project Test Suite">
            <directory>tests</directory>
        </testsuite>
    </testsuites>
    <!-- Run `composer require symfony/panther` before enabling this extension -->
    <!-- <extensions>
        <extension class="Symfony\Component\Panther\ServerExtension" />
        </extensions>
    -->
    <source>
        <include>
            <directory suffix=".php">src</directory>
        </include>
    </source>
</phpunit>
