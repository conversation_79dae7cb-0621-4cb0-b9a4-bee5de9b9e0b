<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html charset=utf-8" />
        <title>{{ title }}</title>
        <style type="text/css">
            @font-face {
                font-family: "Montserrat";
                src: url("{{ fontDir }}Montserrat-Regular.ttf") format("truetype");
                font-weight: normal;
                font-style: normal;
            }

            @font-face {
                font-family: "Montserrat";
                src: url("{{ fontDir }}Montserrat-Medium.ttf") format("truetype");
                font-weight: medium;
                font-style: normal;
            }

            @font-face {
                font-family: "Montserrat";
                src: url("{{ fontDir }}Montserrat-Bold.ttf") format("truetype");
                font-weight: bold;
                font-style: normal;
            }

            * { font-family: DejaVu Sans, sans-serif; }

            table {
                border-spacing: 0;
            }

            td,
            th,
            p,
            span {
                font-family: "Montserrat", sans-serif;
                color: rgba(41, 35, 92, 1);
            }

            .body1 {
                font-size: 15px;
                font-weight: normal;
                line-height: 20px;
            }

            .body2 {
                font-size: 13px;
                font-weight: normal;
                line-height: 16px;
            }

            .h5 {
                font-size: 24px;
                font-weight: 700;
                line-height: 30px;
            }

            .fullwidth {
                width: 100%;
            }

            .border-divider {
                border-color: rgba(41, 35, 92, 0.05);
            }

            .border-left {
                border-left-width: 1px;
                border-left-style: solid;
            }

            .border-bottom {
                border-bottom-width: 1px;
                border-bottom-style: solid;
            }

            .category {
                border-radius: 16px;
                padding: 4px;
            }

            .category p,
            .category td,
            .category span {
                color: inherit;
            }

            .category.critical {
                background-color: #dd494926;
                color: #df5131;
            }

            .category.medium {
                background-color: #fac30326;
                color: #c66113;
            }

            .category.low {
                background-color: #45b94326;
                color: #32ac30;
            }

            table.header .header-col {
                padding: 32px 24px;
                white-space: nowrap;
            }

            table.events-count td {
                padding: 32px 0;
                padding-right: 24px;
            }

            table.events-count .category {
                margin: 0;
                padding: 16px;
            }

            table.events-count .category td {
                padding: 0;
            }

            table.events-count .category .name {
                font-size: 20px;
                font-weight: 700;
                line-height: 24px;
                padding-right: 64px;
                white-space: nowrap;
            }

            table.events-count .category .value {
                font-size: 24px;
                font-weight: 700;
                line-height: 30px;
                width: 100%;
                text-align: right;
                white-space: nowrap;
            }

            table.events th {
                text-align: left;
            }

            table.events th,
            table.events td {
                padding: 16px;
                border: 1px solid rgba(41, 35, 92, 0.05);
                color: rgba(41, 35, 92, 0.7);
                font-size: 14px;
                word-wrap: break-word;
                overflow-wrap: break-word;
                white-space: normal;
            }
        </style>
    </head>
    <body>
        <table class="fullwidth header border-bottom border-divider">
            <tr>
                <td
                    style="padding-left: 0; padding-right: 16px"
                    class="header-col"
                >
                    <img
                        width="84"
                        height="24"
                        alt="M2C logo"
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAVAAAABgCAMAAAB8O9ryAAAAulBMVEUAAAAoJ1woJ1woJ14oKF4oJ1wAq+wqJlwqJF4Aq+wAq+wAq+sAreoApuoAq+wVebAAq+wArO0ArO4pJ1wAq+wAq+wAqu0pJ1wAqO4Aqe8pJ1wAq+wAq+wAq+wpJlwAq+0Aq+wpJ1wAq+woKFspJ1wpJlspJ1wArOwArOwqJ1sAq+wpKV4Aq+wArOwoJ1wpJ10pJ1wnJ1wArOsAq+wAq+0pJ1wAq+0pJ1sqKFspJ10ArO0AquwpJ1wAq+zXYFYoAAAAPHRSTlMAt6QTJsqzmTF321giFnQI+jws5MyRb2geD/3tu4FZVPXyqh/v3b+kXVJMK+fW0sl3QDPvxZaKhm2dl2zpc+B0AAAC9klEQVR42u3d61LaUBiF4Q+hVKwaIEDwXAoI4gnUqm2/ff+31TJlZjOTI5slkrDeS3j+7JVMJlt0o/WGpbeZxFU1K/V8X+10JbInca98+fPaH6hjopvPn5ZdQcNVmxLRwXlT3Losneq8PIGqDgJn0HDtKFBTb5+4cA5VNYegqqUyDNS8HEWAGnPWkRXrX6vmFVSHFRioaXlh0HmthqzSzUBzDKp+HwZqRmHQ//3oSuaCU801qD54MFAziQE13y88yda7as5B9RcO9D4CdNHBlWTp5jT/oDqDgZqrEKgty4Sq9LQAoI840FECaJYJ9ahFANUZDPQ5CTR9QgVaDNA9GKg5jAdNn1CeXxDQHg60EQ+aPqECLQio7sNAr+JB0yfUQ2FAAxjot3jQ1Al1q4UBfdsEaOqEmhYH9OsmQFMnVKk4oHubAE2dUD5BXUBtraYs5ylB1wANn/Z9gq4DGt6jtx8O+kWSK2tEJUmuEg3q0LETaPwT0370cenWjoHODySCAkDtZCIoAtSOeoICQO1jJ0EBoHYqERQBaqcSQQGgdioRFABqpxJBAaB2KhEUAWqnEkEBoHYqERQDuphKBAWBLqYSQWGg86lEUBjoS0eEoEudNMbHtbiq6aCeEHSp8Xl9zTf2QlDb5P6fD0FhoK/GEBQIWjUERYK2DUGRoGNDUCSod0dQKGjNODchaATogXGuSdAwaNO4d0LQMGjHONcSgoZBX41zNYJGgI6Ma/UuQaGfM7aFoEjQuyOCIkHrDSEoELQ+FoICQc8aQlAg6KgrBIWB3v1+EiHoSqAXtbj+TOaaBF0RNHEREZSgBCUoQQlKUIISlKAEJShBFxGUoAQlKEEJStAigDp1KEntzG+GPgm0T1AsqEdQLKj4BMWClgiKBZ0SFAt6S1AsqDwQFAsaEBQL6vkEhYJKQFAsqDwSFAta6RE0M+jnX1C1i6DyTtBMoFtxyd9ugsrNgKDJoFtzUequgopcDgkaB7pNl03vMujiOnTnWfoX2V8H0PbAxpUAAAAASUVORK5CYII="
                    />
                </td>

                <td
                    class="border-left border-divider header-col"
                    style="padding-left: 16px"
                >
                    <img
                        width="96"
                        height="32"
                        alt="SuperPoPS logo"
                        src="data:image/svg+xml,%3Csvg%20width%3D%2296%22%20height%3D%2232%22%20viewBox%3D%220%200%2096%2032%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20clip-path%3D%22url(%23clip0_2092_13935)%22%3E%3Cpath%20d%3D%22M44.1764%2028.1943V14.6343H50.4723C51.7362%2014.6343%2052.8253%2014.8343%2053.7513%2015.2343C54.6716%2015.6343%2055.3821%2016.2114%2055.8714%2016.9657C56.3664%2017.72%2056.611%2018.6114%2056.611%2019.6286C56.611%2020.6457%2056.3664%2021.5714%2055.8714%2022.3086C55.3763%2023.0514%2054.6716%2023.6286%2053.7513%2024.0343C52.8311%2024.44%2051.7362%2024.6457%2050.4723%2024.6457H46.3255L48.0844%2022.96V28.1886H44.1764V28.1943ZM48.0844%2023.4114L46.3255%2021.6114H50.2335C51.0489%2021.6114%2051.6605%2021.44%2052.0682%2021.0971C52.4759%2020.7543%2052.6797%2020.2686%2052.6797%2019.6343C52.6797%2019%2052.4759%2018.5486%2052.0682%2018.1886C51.6605%2017.8343%2051.0489%2017.6571%2050.2335%2017.6571H46.3255L48.0844%2015.8971V23.4114Z%22%20fill%3D%22%2329235C%22%2F%3E%3Cpath%20d%3D%22M63.2856%2028.3716C62.1149%2028.3716%2061.0724%2028.1374%2060.1638%2027.6745C59.2552%2027.2116%2058.5388%2026.5659%2058.0205%2025.7488C57.5021%2024.9316%2057.2401%2023.9945%2057.2401%2022.9488C57.2401%2021.9031%2057.5021%2020.9488%2058.0205%2020.1316C58.5388%2019.3145%2059.2552%2018.6688%2060.1638%2018.2059C61.0724%2017.7431%2062.1091%2017.5088%2063.2856%2017.5088C64.462%2017.5088%2065.4987%2017.7431%2066.4131%2018.2059C67.3275%2018.6688%2068.0439%2019.3145%2068.5681%2020.1316C69.0923%2020.9488%2069.3485%2021.8916%2069.3485%2022.9488C69.3485%2024.0059%2069.0864%2024.9259%2068.5681%2025.7488C68.0497%2026.5716%2067.3334%2027.2116%2066.4131%2027.6745C65.4987%2028.1374%2064.4562%2028.3716%2063.2856%2028.3716ZM63.2856%2025.4459C63.7049%2025.4459%2064.0835%2025.3488%2064.4213%2025.1545C64.7591%2024.9602%2065.027%2024.6745%2065.2308%2024.2916C65.4347%2023.9088%2065.5395%2023.4574%2065.5395%2022.9259C65.5395%2022.3945%2065.4347%2021.9316%2065.2308%2021.5716C65.027%2021.2116%2064.7591%2020.9316%2064.4213%2020.7374C64.0835%2020.5431%2063.7049%2020.4459%2063.2856%2020.4459C62.8662%2020.4459%2062.4876%2020.5431%2062.1498%2020.7374C61.812%2020.9316%2061.5441%2021.2116%2061.3403%2021.5716C61.1364%2021.9316%2061.0316%2022.3831%2061.0316%2022.9259C61.0316%2023.4688%2061.1306%2023.9088%2061.3403%2024.2916C61.5441%2024.6745%2061.812%2024.9602%2062.1498%2025.1545C62.4876%2025.3488%2062.8662%2025.4459%2063.2856%2025.4459Z%22%20fill%3D%22%2329235C%22%2F%3E%3Cpath%20d%3D%22M70.8629%2028.1943V14.6343H77.1588C78.4226%2014.6343%2079.5118%2014.8343%2080.4378%2015.2343C81.358%2015.6343%2082.0686%2016.2114%2082.5578%2016.9657C83.0529%2017.72%2083.2975%2018.6114%2083.2975%2019.6286C83.2975%2020.6457%2083.0529%2021.5714%2082.5578%2022.3086C82.0628%2023.0514%2081.358%2023.6286%2080.4378%2024.0343C79.5176%2024.44%2078.4226%2024.6457%2077.1588%2024.6457H73.012L74.7709%2022.96V28.1886H70.8629V28.1943ZM74.7767%2023.4114L73.0178%2021.6114H76.9258C77.7412%2021.6114%2078.3528%2021.44%2078.7604%2021.0971C79.1681%2020.7543%2079.372%2020.2686%2079.372%2019.6343C79.372%2019%2079.1681%2018.5486%2078.7604%2018.1886C78.3528%2017.8343%2077.7412%2017.6571%2076.9258%2017.6571H73.0178L74.7767%2015.8971V23.4114Z%22%20fill%3D%22%2329235C%22%2F%3E%3Cpath%20d%3D%22M89.8555%2028.4687C88.7081%2028.4687%2087.6132%2028.3316%2086.5707%2028.0516C85.5223%2027.7716%2084.6778%2027.423%2084.0313%2026.9944L85.3126%2024.1487C85.93%2024.5373%2086.6464%2024.8573%2087.4559%2025.1087C88.2655%2025.3602%2089.0692%2025.4859%2089.873%2025.4859C90.4263%2025.4859%2090.8631%2025.4402%2091.1834%2025.3487C91.5037%2025.2573%2091.7425%2025.1316%2091.894%2024.9602C92.0454%2024.7944%2092.1211%2024.5944%2092.1211%2024.3602C92.1211%2024.063%2091.9755%2023.8287%2091.6843%2023.6516C91.3931%2023.4744%2091.0145%2023.3316%2090.5486%2023.2059C90.0826%2023.0859%2089.5759%2022.9659%2089.0285%2022.8573C88.481%2022.7487%2087.9277%2022.6002%2087.3627%2022.423C86.7978%2022.2402%2086.2853%2022.0059%2085.8252%2021.7087C85.365%2021.4116%2084.9923%2021.023%2084.7069%2020.5487C84.4215%2020.0687%2084.2818%2019.4687%2084.2818%2018.7487C84.2818%2017.9487%2084.5031%2017.2173%2084.9515%2016.5602C85.4%2015.903%2086.0756%2015.3716%2086.9842%2014.9716C87.8927%2014.5716%2089.0226%2014.3716%2090.3797%2014.3716C91.2882%2014.3716%2092.1793%2014.4744%2093.053%2014.6744C93.9266%2014.8744%2094.7012%2015.1773%2095.371%2015.5773L94.1887%2018.4059C93.5306%2018.0459%2092.8782%2017.7773%2092.2318%2017.6116C91.5853%2017.4459%2090.9621%2017.3602%2090.3564%2017.3602C89.8147%2017.3602%2089.3837%2017.4173%2089.0518%2017.5373C88.7256%2017.6516%2088.4868%2017.8002%2088.3412%2017.983C88.1956%2018.1659%2088.1257%2018.3602%2088.1257%2018.5659C88.1257%2018.8744%2088.2713%2019.1202%2088.5625%2019.2916C88.8537%2019.463%2089.2265%2019.6059%2089.6866%2019.7202C90.1467%2019.8287%2090.6592%2019.943%2091.2183%2020.0573C91.7775%2020.1716%2092.3308%2020.3202%2092.8841%2020.4916C93.4374%2020.6687%2093.9499%2020.8973%2094.4158%2021.1887C94.8818%2021.4802%2095.2603%2021.863%2095.5515%2022.3316C95.8428%2022.8059%2095.9884%2023.3944%2095.9884%2024.103C95.9884%2024.8916%2095.7612%2025.6173%2095.3069%2026.2802C94.8526%2026.943%2094.1712%2027.4802%2093.2626%2027.8802C92.3541%2028.2802%2091.2184%2028.4802%2089.8497%2028.4802L89.8555%2028.4687Z%22%20fill%3D%22%2329235C%22%2F%3E%3Cpath%20d%3D%22M47.2924%2011.8173C46.6401%2011.8173%2046.011%2011.7315%2045.4112%2011.5601C44.8113%2011.3887%2044.322%2011.1658%2043.9609%2010.8915L44.6016%209.49726C44.9452%209.74297%2045.3529%209.94297%2045.8363%2010.0973C46.3197%2010.2515%2046.8031%2010.3315%2047.2924%2010.3315C47.6651%2010.3315%2047.968%2010.2915%2048.1951%2010.2173C48.4223%2010.143%2048.5912%2010.0401%2048.7018%209.9144C48.8125%209.78868%2048.8649%209.64011%2048.8649%209.4744C48.8649%209.26297%2048.7775%209.09154%2048.6086%208.96583C48.4397%208.84011%2048.2126%208.73725%2047.9272%208.65725C47.6418%208.57725%2047.3331%208.50297%2047.0012%208.42868C46.6692%208.3544%2046.3314%208.26868%2045.9936%208.16583C45.6558%208.06297%2045.3471%207.92583%2045.0617%207.7544C44.7763%207.58297%2044.555%207.36011%2044.3861%207.08011C44.2172%206.80011%2044.1357%206.45154%2044.1357%206.0344C44.1357%205.57726%2044.258%205.16011%2044.5084%204.78297C44.7588%204.40583%2045.1374%204.10297%2045.6441%203.88011C46.1508%203.65725%2046.7915%203.54297%2047.5603%203.54297C48.0728%203.54297%2048.5795%203.60583%2049.0804%203.72583C49.5813%203.84583%2050.0181%204.02297%2050.3908%204.25154L49.7968%205.64583C49.4298%205.44011%2049.0571%205.28583%2048.6669%205.18297C48.2767%205.08011%2047.9039%205.02868%2047.5486%205.02868C47.1934%205.02868%2046.873%205.06868%2046.6517%205.14868C46.4246%205.22868%2046.2615%205.33725%2046.1567%205.4744C46.0518%205.61154%2045.9994%205.76583%2045.9994%205.93154C45.9994%206.14297%2046.0868%206.3144%2046.2557%206.44583C46.4246%206.57725%2046.6517%206.6744%2046.9313%206.74868C47.2108%206.82297%2047.5195%206.89154%2047.8631%206.96583C48.2068%207.04011%2048.5446%207.12583%2048.8765%207.2344C49.2085%207.34297%2049.5172%207.4744%2049.7968%207.64011C50.0763%207.80583%2050.3035%208.02297%2050.4782%208.30297C50.6529%208.58297%2050.7403%208.92583%2050.7403%209.33726C50.7403%209.78868%2050.6122%2010.2001%2050.3617%2010.5715C50.1113%2010.943%2049.7269%2011.2458%2049.2143%2011.4744C48.7018%2011.703%2048.0612%2011.8173%2047.2924%2011.8173Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20d%3D%22M57.7642%2011.817C56.6052%2011.817%2055.7025%2011.5027%2055.0443%2010.8685C54.392%2010.2342%2054.0659%209.3256%2054.0659%208.14274V3.66846H55.9645V8.09703C55.9645%208.85131%2056.1218%209.39989%2056.4421%209.73703C56.7625%2010.0742%2057.2051%2010.2456%2057.77%2010.2456C58.335%2010.2456%2058.7893%2010.0742%2059.1096%209.73703C59.4299%209.39989%2059.5872%208.85131%2059.5872%208.09703V3.66846H61.4393V8.14274C61.4393%209.3256%2061.1131%2010.2342%2060.4608%2010.8685C59.8085%2011.5027%2058.9058%2011.817%2057.7526%2011.817H57.7642Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20d%3D%22M65.4288%2011.6799V3.66846H68.9641C69.6921%203.66846%2070.327%203.78274%2070.8512%204.01703C71.3753%204.25131%2071.7888%204.58274%2072.0742%205.01703C72.3596%205.45131%2072.5052%205.9656%2072.5052%206.55989C72.5052%207.15417%2072.3596%207.67989%2072.0742%208.10846C71.7888%208.53703%2071.3812%208.87417%2070.8512%209.10846C70.3211%209.34274%2069.6921%209.46274%2068.9641%209.46274H66.4655L67.3275%208.62846V11.6742H65.4288V11.6799ZM67.3275%208.8456L66.4655%207.9656H68.8593C69.4359%207.9656%2069.8727%207.8456%2070.1639%207.59989C70.4609%207.35417%2070.6065%207.01131%2070.6065%206.57131C70.6065%206.13131%2070.4609%205.78846%2070.1639%205.54846C69.8669%205.30846%2069.4359%205.18846%2068.8593%205.18846H66.4655L67.3275%204.30846V8.85131V8.8456Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20d%3D%22M77.8227%2010.1942H82.2666V11.6799H75.9473V3.66846H82.1151V5.15417H77.8227V10.1885V10.1942ZM77.6829%206.8856H81.6143V8.33703H77.6829V6.8856Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20d%3D%22M85.8718%2011.6799V3.66846H89.407C90.1351%203.66846%2090.7699%203.78274%2091.2941%204.01703C91.8183%204.25131%2092.2318%204.58274%2092.5172%205.01703C92.8025%205.45131%2092.9481%205.9656%2092.9481%206.55989C92.9481%207.15417%2092.8025%207.67989%2092.5172%208.10846C92.2318%208.53703%2091.8241%208.86846%2091.2941%209.09131C90.7641%209.31417%2090.1351%209.42846%2089.407%209.42846H86.9085L87.7704%208.62846V11.6742H85.8718V11.6799ZM87.7704%208.8456L86.9085%207.9656H89.3022C89.8788%207.9656%2090.3156%207.8456%2090.6068%207.59989C90.9039%207.35417%2091.0495%207.01131%2091.0495%206.57131C91.0495%206.13131%2090.9039%205.78846%2090.6068%205.54846C90.3098%205.30846%2089.8788%205.18846%2089.3022%205.18846H86.9085L87.7704%204.30846V8.85131V8.8456ZM91.0728%2011.6799L89.0227%208.77131H91.0495L93.0996%2011.6799H91.0728Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20d%3D%22M27.5367%2027.7486H3.91385C1.75308%2027.7486%200%2026.0743%200%2024.0114V3.74286C0%201.67429%201.75308%200%203.91385%200H10.6117C11.5552%200%2012.4696%200.325714%2013.1859%200.92L15.8301%203.12571C16.5407%203.72%2017.4551%204.04571%2018.4044%204.04571H27.5426C29.7033%204.04571%2031.4564%205.72%2031.4564%207.78286V24.0057C31.4564%2026.0686%2029.7033%2027.7429%2027.5426%2027.7429L27.5367%2027.7486Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20d%3D%22M14.5488%208.94837C13.2384%2010.4227%2010.734%2012.4055%206.57549%2012.7827C5.77758%2012.8569%205.16022%2013.4512%205.16022%2014.1941V17.8912C5.16022%2022.6855%207.69956%2027.1769%2011.957%2029.9027L14.8458%2031.7541C15.3758%2032.0912%2016.0747%2032.0912%2016.5989%2031.7541L19.4877%2029.9027C23.7452%2027.1769%2026.2845%2022.6912%2026.2845%2017.8912V14.1941C26.2845%2013.4512%2025.6672%2012.8569%2024.8692%2012.7827C20.705%2012.4055%2018.2064%2010.4227%2016.8959%208.94837C16.2902%208.26265%2015.1487%208.26265%2014.543%208.94837H14.5488Z%22%20fill%3D%22%2329235C%22%2F%3E%3Cpath%20d%3D%22M15.7253%2013.6001L18.0783%2017.3144L22.4057%2018.3601L19.5285%2021.703L19.8547%2026.063L15.7253%2024.4115L11.596%2026.063L11.9221%2021.703L9.04498%2018.3601L13.3724%2017.3144L15.7253%2013.6001Z%22%20fill%3D%22%2300ABEC%22%2F%3E%3Cpath%20opacity%3D%220.2%22%20d%3D%22M26.1681%204.04571C24.613%2015.0971%2016.8028%2024.1829%206.36001%2027.7486H3.91385C1.75308%2027.7486%200%2026.0743%200%2024.0057V3.74286C0%201.67429%201.75308%200%203.91385%200H10.6117C11.561%200%2012.4696%200.331429%2013.1859%200.925714L15.8301%203.12571C16.5407%203.72%2017.4551%204.04571%2018.3986%204.04571H26.1681Z%22%20fill%3D%22white%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3CclipPath%20id%3D%22clip0_2092_13935%22%3E%3Crect%20width%3D%2296%22%20height%3D%2232%22%20fill%3D%22white%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3C%2Fsvg%3E"
                    />
                </td>

                <td class="border-left border-divider header-col body1">
                    {% if author %}
                    <span>{{ author_title }}:</span><br><b>{{ author }}</b>
                    {% endif %}
                </td>

                <td
                    class="border-left border-divider header-col fullwidth body1"
                >
                    {% if date_from %}
                    <span>{{ shift_range_title }}:</span><br><b>{{ date_from }} - {{ date_to }}</b>
                    {% endif %}
                </td>
            </tr>
        </table>

        <table class="fullwidth events-count border-bottom border-divider">
            <tr>
                <td>
                    <table class="category critical fullwidth">
                        <tr>
                            <td class="name">{{ critical_label }}</td>
                            <td class="value">{{ critical_total }}</td>
                        </tr>
                    </table>
                </td>

                <td>
                    <table class="category medium fullwidth">
                        <tr>
                            <td class="name">{{ medium_label }}</td>
                            <td class="value">{{ medium_total }}</td>
                        </tr>
                    </table>
                </td>

                <td>
                    <table class="category low fullwidth">
                        <tr>
                            <td class="name">{{ low_label }}</td>
                            <td class="value">{{ low_total }}</td>
                        </tr>
                    </table>
                </td>

                <td class="fullwidth"></td>
            </tr>
        </table>

        <table class="fullwidth events" style="table-layout: fixed; width: 100%;">
            <thead>
                <tr>
                    <th style="width: 12%;">{{ date_title }}</th>
                    <th style="width: 12%;">{{ category_title }}</th>
                    <th style="width: 12%;">{{ title_title }}</th>
                    <th style="width: 40%;">{{ description_title }}</th>
                    <th style="width: 10%;">{{ author_title }}</th>
                    <th style="width: 14%;">{{ facility_title }}</th>
                </tr>
            </thead>
            <tbody>
                {% for p in pops_events %}
                <tr>
                    <td style="width: 12%; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;">{{ p.date }}</td>
                    <td style="width: 12%; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;">
                        <span class="category {{ p.category }}"
                            >{{ p.category_title }}</span
                        >
                    </td>
                    <td style="width: 12%; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;">{{ p.title }}</td>
                    <td style="width: 40%; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;">{{ p.description }}</td>
                    <td style="width: 10%; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;">{{ p.author }}</td>
                    <td style="width: 14%; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;">{{ p.facility }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </body>
</html>
