# Data pump

## Development

### Requirements
- [Docker](https://www.docker.com/products/docker-desktop)
- [Docker compose](https://docs.docker.com/compose/install/)

### Data transfer from PM2 to PM3
Command `app:import-data` reads from PM2 database and inserts/updates data in corresponding table in PM3 database which contains PM2 tables.\
Usage: `php bin/console app:import-data <db_name> <account_name>`

### Run Locally
1. Install composer dependencies\
    `docker exec m2c-importer-php-fpm bash -c 'composer install'`

2. Setup PM3 database connection\
    Start PM3 database of your local `backend` docker compose setup.
    Edit `.env` file and set the correct connection values if you use other than the default
    ```
    DB_HOST=host.docker.internal
    DB_PORT=55432
    DB_NAME=pm
    DB_USER=user
    DB_PASS=password
    ```

3. Create PM2 database structure\
    `docker exec m2c-importer-php-fpm bash -c './bin/init-local-db-pm2'`\

4. Import data to PM2 database\
    * First create sql dump of PM2 database.
    * Disable foreign key checks - execute sql script: `database\migrations\02_01_disable_fk_checks.sql`
    * Import backup
    * Enable foreign key checks - execute sql script: `database\migrations\02_02_enable_fk_checks.sql`

5. Run import data command\
    `docker exec m2c-importer-php-fpm bash -c 'php bin/console app:import-data ablpm2_cz cz'`

## Useful Commands
- Run checks: `docker exec m2c-importer-php-fpm bash -c 'composer check'`
- Access php container: `docker exec -it m2c-importer-php-fpm bash;`
- Actualise the phpstan baseline: `docker exec m2c-importer-php-fpm bash -c 'php -d memory_limit=2G ./vendor/bin/phpstan analyse -c phpstan.neon --generate-baseline baseline.neon'`
