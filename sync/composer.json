{"name": "developartment/m2c-db-importer", "description": "Database import", "license": "MIT", "type": "project", "version": "1.0.0", "require": {"php": "~8.2.0", "ext-pdo": "*", "nette/utils": "^4.0", "php-amqplib/rabbitmq-bundle": "^2.17", "symfony/console": "^6.3", "symfony/dotenv": "^6.3", "symfony/framework-bundle": "^6.3", "symfony/http-kernel": "^6.3", "symfony/lock": "^6.3", "symfony/monolog-bundle": "^3.10", "symfony/yaml": "^6.3", "vlucas/phpdotenv": "^5.5"}, "require-dev": {"editorconfig-checker/editorconfig-checker": "^10.6", "ergebnis/composer-normalize": "2.42.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpstan/phpstan": "^1.10", "phpstan/phpstan-strict-rules": "^1.5", "phpstan/phpstan-symfony": "^1.3", "phpstan/phpstan-webmozart-assert": "^1.2", "shipmonk/phpstan-rules": "^2.12", "slevomat/coding-standard": "^8.15", "squizlabs/php_codesniffer": "^3.9", "symfony/var-dumper": "^6.3", "symplify/phpstan-rules": "^12.4"}, "minimum-stability": "stable", "autoload": {"psr-4": {"Sync\\": "src/"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true}}, "scripts": {"check": ["@check:ws", "@check:composer", "@check:cs", "@check:json", "@check:lint", "@check:types"], "check:composer": "bin/echo-title 'composer normalize' && composer normalize --dry-run --no-update-lock", "check:composer:fix": "bin/echo-title 'composer normalize fix' && composer normalize --no-update-lock", "check:cs": "bin/echo-title 'phpcs' && php vendor/bin/phpcs", "check:cs:files": "bin/echo-title 'phpcs files' && php vendor/bin/phpcs", "check:cs:fix": "bin/echo-title 'phpcs fix' && php vendor/bin/phpcbf", "check:json": "bin/echo-title 'Check json files' && php bin/check-json-files.php", "check:lint": " bin/echo-title 'Lint' && parallel-lint --gitlab --show-deprecated -e php -j $(nproc) bin config database docker src tools", "check:lint:files": "bin/echo-title 'Lint files' && parallel-lint --show-deprecated -e php -j $(nproc)", "check:tests": "bin/echo-title 'phpunit tests' && php vendor/bin/phpunit tests --colors=always --stderr --stop-on-failure", "check:tests:services-definition": ["php bin/echo-title 'Services definition test'", "phpunit --colors=always ./tests/ServicesTest.php"], "check:types": "bin/echo-title 'phpstan' && php -d memory_limit=2G ./vendor/bin/phpstan analyse -c phpstan.neon", "check:ws": "bin/echo-title 'editor config' && vendor/bin/ec .", "check:ws:files": "php bin/echo-title 'Check Editor config files formatting' && vendor/bin/ec", "fix": ["@check:cs:fix", "@check:composer:fix"], "generate:baseline:phpstan": " php -d memory_limit=2G ./vendor/bin/phpstan analyse -c phpstan.neon --generate-baseline=baseline.neon -vvv && sed -i -e 's/\\t/    /g' baseline.neon"}}