FROM phpdockerio/php:8.2-fpm
WORKDIR "/application"

# Fix debconf warnings upon build
ARG DEBIAN_FRONTEND=noninteractive

# Install selected extensions and other stuff
RUN apt-get update && \
    apt-get install -y libxml2-dev


RUN apt-get update \
    && apt-get -y --no-install-recommends install php8.2-pgsql php8.2-intl postgresql-client cron \
    && apt-get clean; rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /usr/share/doc/* \
    # Install Composer
    && curl \
        --location \
        --silent \
        --show-error \
        https://github.com/composer/composer/releases/download/2.7.2/composer.phar \
        > \
        /usr/local/bin/composer \
    && chmod +x /usr/local/bin/composer

# configure cron
RUN systemctl enable cron
# Add crontab file in the cron directory
COPY docker/php-fpm/crontab /etc/cron.d/crontab.local
# Give execution rights on the cron job
RUN chmod 0644 /etc/cron.d/crontab.local
# Apply cron job
RUN crontab /etc/cron.d/crontab.local
# Create the log file to be able to run tail
RUN touch /var/log/cron.log

# add code, install deps
COPY . .
RUN composer install --no-interaction --no-scripts
RUN chown -R www-data:www-data /application

# Run the command on container startup
CMD cron -f
