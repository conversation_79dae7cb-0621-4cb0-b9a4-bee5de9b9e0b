parameters:
    ignoreErrors:
        -
            message: "#^Exception \\$e not passed as previous to new \\\\RuntimeException\\(\"Can't connect to database\"\\)$#"
            count: 1
            path: src/Database/DatabaseConnection.php

        -
            message: "#^Method Sync\\\\Database\\\\DatabaseConnection\\:\\:getConnection\\(\\) should return PDO but returns PDO\\|null\\.$#"
            count: 1
            path: src/Database/DatabaseConnection.php

        -
            message: "#^Variable \\$dbAccess might not be defined\\.$#"
            count: 4
            path: src/Database/DatabaseConnection.php

        -
            message: "#^Exception \\$e not passed as previous to new \\\\RuntimeException\\(\"Database update error\\: \\{\\$e\\-\\>getMessage\\(\\)\\}\"\\)$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertCity\\:\\:importCities\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertCity\\:\\:updateCities\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'created_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 7
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'inserted_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'name' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'original_db_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'updated_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Offset 'updated_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCity.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertCountry\\:\\:importCountries\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertCountry\\:\\:updateCountries\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'created_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 6
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'inserted_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'name' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'original_db_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'updated_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Offset 'updated_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertCountry.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertFacility\\:\\:doInsert\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertFacility.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertFacility\\:\\:doInsert\\(\\) has parameter \\$row with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertFacility.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertFacility\\:\\:insertFacility\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertFacility.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertFacilityGroup\\:\\:insertFacilityGroup\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Offset 'created_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 3
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Offset 'inserted_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Offset 'name' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Offset 'updated_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Offset 'updated_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroup.php

        -
            message: "#^Comparison int\\|null \\> int contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 2
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertFacilityGroupFacilityM2N\\:\\:insertFacilityGroupFacilityM2N\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertFacilityGroupFacilityM2N\\:\\:insertFacilityGroupFacilityM2NDiff\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Null value involved in binary operation\\: int\\|null \\> int$#"
            count: 2
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$facilityGroupOriginalId$#"
            count: 1
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$facilityOriginalId$#"
            count: 1
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Offset 'object_group_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Offset 'object_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertFacilityGroupFacilityM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertMobileInfo\\:\\:insertMobileInfo\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Offset 'created_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 4
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Offset 'locale' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Offset 'registration_token' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Offset 'subject_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Offset 'updated_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertMobileInfo.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertMobileSetting\\:\\:insertMobileSetting\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertMobileSetting.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 3
            path: src/Database/InsertMobileSetting.php

        -
            message: "#^Offset 'news' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertMobileSetting.php

        -
            message: "#^Offset 'objects' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertMobileSetting.php

        -
            message: "#^Offset 'subject_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertMobileSetting.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionGroup\\:\\:insertPermissionGroupPm3\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroup.php

        -
            message: "#^Offset 'created_at' on \\*NEVER\\* on left side of \\?\\? always exists and is not nullable\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroup.php

        -
            message: "#^Offset 'inserted_by' on \\*NEVER\\* on left side of \\?\\? always exists and is not nullable\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroup.php

        -
            message: "#^Offset 'permission_template…' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroup.php

        -
            message: "#^Offset 'updated_at' on \\*NEVER\\* on left side of \\?\\? always exists and is not nullable\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroup.php

        -
            message: "#^Offset 'updated_by' on \\*NEVER\\* on left side of \\?\\? always exists and is not nullable\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroup.php

        -
            message: "#^Comparison int\\|null \\> int contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 2
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionGroupFacilityM2N\\:\\:insertPermissionGroupFacilityM2NPm3\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionGroupFacilityM2N\\:\\:insertPermissionGroupFacilityM2NPm3Diff\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Null value involved in binary operation\\: int\\|null \\> int$#"
            count: 2
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$facilityOriginalId$#"
            count: 1
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$permissionGroupOriginalId$#"
            count: 1
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Offset 'object_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Offset 'permission_group_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupFacilityM2N.php

        -
            message: "#^Casting to int something that's already int\\<1, max\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Comparison int\\|null \\> int contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionGroupUserM2N\\:\\:insertPermissionGroupUserM2NPm3\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionGroupUserM2N\\:\\:insertPermissionGroupUserM2NPm3Diff\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Null value involved in binary operation\\: int\\|null \\> int$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$permissionGroupOriginalId$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$userId$#"
            count: 3
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Offset 'permission_group_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Offset 'subject_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionGroupUserM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionTemplate\\:\\:insertPermissionTemplatePm3\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'created_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'description' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 3
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'inserted_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'name' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'updated_at' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Offset 'updated_by' on array\\<int, mixed\\> on left side of \\?\\? does not exist\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplate.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionTemplatePermission\\:\\:insertPermissionTemplatePermissionPm3\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplatePermission.php

        -
            message: "#^Offset 'permission_template…' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplatePermission.php

        -
            message: "#^Casting to int something that's already int\\<1, max\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Comparison int\\|null \\> int contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionTemplateUserM2N\\:\\:insertPermissionTemplateUserM2NPm3\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertPermissionTemplateUserM2N\\:\\:insertPermissionTemplateUserM2NPm3Diff\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Null value involved in binary operation\\: int\\|null \\> int$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$permissionTemplateOriginalId$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Null value involved in string interpolation with \\$userId$#"
            count: 6
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Offset 'permission_template…' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertPermissionTemplateUserM2N.php

        -
            message: "#^Comparison mixed \\> int contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 1
            path: src/Database/InsertTenant.php

        -
            message: "#^Offset 'object_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertTenant.php

        -
            message: "#^Comparison mixed \\> int contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 1
            path: src/Database/InsertTenantGroup.php

        -
            message: "#^Offset 'object_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertTenantGroup.php

        -
            message: "#^Exception \\$e not passed as previous to new \\\\RuntimeException\\(\"Database update error\\: \\{\\$e\\-\\>getMessage\\(\\)\\}\"\\)$#"
            count: 2
            path: src/Database/InsertUser.php

        -
            message: "#^Exception \\$e not passed as previous to new \\\\RuntimeException\\(\"SELECT query error\\: \\{\\$e\\-\\>getMessage\\(\\)\\}\"\\)$#"
            count: 1
            path: src/Database/InsertUser.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertUser\\:\\:doInsert\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertUser.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertUser\\:\\:doInsert\\(\\) has parameter \\$row with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertUser.php

        -
            message: "#^Method Sync\\\\Database\\\\InsertUser\\:\\:updateUsers\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Database/InsertUser.php

        -
            message: "#^Offset 'id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 4
            path: src/Database/InsertUser.php

        -
            message: "#^Offset 'original_db_id' does not exist on array\\<int, mixed\\>\\.$#"
            count: 1
            path: src/Database/InsertUser.php

        -
            message: "#^Comparison int \\>\\= mixed contains non\\-comparable type, only int\\|float\\|string\\|DateTimeInterface or comparable tuple is allowed\\.$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Exception \\$e not passed as previous to new \\\\RuntimeException\\(\"Can't read data from table\\: \\{\\$sourceTableName\\}\"\\)$#"
            count: 2
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Exception \\$e not passed as previous to new \\\\RuntimeException\\(\"Can't read data from table\\: \\{\\$tableName\\}\"\\)$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Method Sync\\\\Facade\\\\ImportDataFacade\\:\\:phaseFour\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Method Sync\\\\Facade\\\\ImportDataFacade\\:\\:phaseOne\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Method Sync\\\\Facade\\\\ImportDataFacade\\:\\:phaseThree\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Method Sync\\\\Facade\\\\ImportDataFacade\\:\\:phaseTwo\\(\\) has parameter \\$account with no value type specified in iterable type array\\.$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Parameter \\$account of method Sync\\\\Database\\\\InsertUser\\:\\:insertUsers\\(\\) expects array\\{id\\: int, name\\: string, active\\: bool, created_at\\: string, updated_at\\: string\\}, array given\\.$#"
            count: 1
            path: src/Facade/ImportDataFacade.php

        -
            message: "#^Overwriting variable \\$value while changing its type from string to int$#"
            count: 2
            path: src/Input/InputArgs.php
