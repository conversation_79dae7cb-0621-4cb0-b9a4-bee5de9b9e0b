# config/services.yaml
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    _instanceof:
        Sync\Command\BaseCommand:
            tags:
                - console.command
    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    Sync\:
        resource: '../src/'
        exclude:
            - '../src/Kernel.php'

imports:
  - { resource: accounts_map.yaml }
