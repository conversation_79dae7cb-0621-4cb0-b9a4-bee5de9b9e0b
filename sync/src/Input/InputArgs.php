<?php

declare(strict_types = 1);

namespace Sync\Input;

use DateTimeImmutable;
use Nette\Utils\Strings;
use Sync\Exception\InvalidInputException;
use function array_key_exists;
use function is_array;
use function is_float;
use function is_int;
use function is_string;

class InputArgs
{

    /**
     * @var mixed[]
     */
    private array $args;

    private bool $weakTypes = false;

    /**
     * @param mixed[] $args
     */
    public function __construct(array $args)
    {
        $this->args = $args;
    }

    /**
     * @param mixed[] $args
     */
    public static function create(array $args): self
    {
        return new self($args);
    }

    public function allowWeakTypes(): void
    {
        $this->weakTypes = true;
    }

    private function hasValue(string $key): bool
    {
        return array_key_exists($key, $this->args);
    }

    /**
     * @throws InvalidInputException
     */
    private function getValue(string $key): mixed
    {
        if (!$this->hasValue($key)) {
            throw InvalidInputException::createMissingException($key);
        }

        return $this->args[$key];
    }

    /**
     * @throws InvalidInputException
     */
    private function getValueOrNull(string $key): mixed
    {
        if (!$this->hasValue($key)) {
            return null;
        }

        return $this->getValue($key);
    }

    /**
     * @throws InvalidInputException
     */
    public function getString(string $key): string
    {
        $value = $this->getValue($key);

        if (!is_string($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'string');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getStringOrNull(string $key): ?string
    {
        $value = $this->getValueOrNull($key);

        if ($value !== null && !is_string($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'string');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getList(string $key): self
    {
        $value = $this->getValue($key);

        if (!is_array($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'list');
        }

        return self::create($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getListOrNull(string $key): ?self
    {
        $value = $this->getValueOrNull($key);

        if ($value === null) {
            return null;
        }

        if (!is_array($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'list');
        }

        return self::create($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getInt(string $key): int
    {
        $value = $this->getValue($key);

        if ($this->weakTypes && is_string($value)) {
            $value = (int) $value;
        }

        if (!is_int($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'int');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getIntOrNull(string $key): ?int
    {
        $value = $this->getValueOrNull($key);

        if ($this->weakTypes && is_string($value)) {
            $value = (int) $value;
        }

        if ($value !== null && !is_int($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'int');
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getFloat(string $key): float
    {
        $value = $this->getValue($key);

        if (!is_float($value) && !is_int($value)) {
            throw InvalidInputException::createInvalidTypeException($key, 'float');
        }

        if (is_int($value)) {
            return (float) $value;
        }

        return $value;
    }

    /**
     * @throws InvalidInputException
     */
    public function getDate(string $key): DateTimeImmutable
    {
        $value = $this->getValue($key);

        if (!is_string($value) || Strings::match($value, '/^[1-9]{1}\d{3}\-\d{2}\-\d{2}$/') === null) {
            throw InvalidInputException::createInvalidTypeException($key, 'date');
        }

        return new DateTimeImmutable($value);
    }

    /**
     * @throws InvalidInputException
     */
    public function getBool(string $key): bool
    {
        $value = $this->getValue($key);

        if ($value !== true && $value !== false) {
            throw InvalidInputException::createInvalidTypeException($key, 'bool');
        }

        return $value;
    }

    /**
     * @return mixed[]
     */
    public function toArray(): array
    {
        return $this->args;
    }

}
