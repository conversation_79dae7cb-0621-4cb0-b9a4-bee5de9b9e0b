<?php

declare(strict_types = 1);

namespace Sync\Database;

use Exception;
use PDO;
use RuntimeException;
use Sync\Mapper\PermissionMapper;
use function in_array;
use function str_contains;

class InsertPermission extends GetData
{

    private const APPS = [
        ['code' => 'pops', 'name' => 'Pops'],
        ['code' => 'super-pops', 'name' => 'SuperPops'],
        ['code' => 'profile', 'name' => 'Profile'],
        ['code' => 'wap', 'name' => 'Wages and personnel'],
        ['code' => 'code-list', 'name' => 'Code Lists'],
        ['code' => 'permissions', 'name' => 'Permissions'],
    ];

    public function insertPermissionApplicationPm3(PDO $pdoTargetDatabase): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        try {
            $pdoTargetDatabase->beginTransaction();

            foreach (self::APPS as $app) {
                $query = 'INSERT INTO permission_application (
                            code,name
                        ) VALUES (
                            :code,:name
                        )  ON CONFLICT (code) DO NOTHING';
                $stmt = $pdoTargetDatabase->prepare($query);
                $stmt->bindValue(':code', $app['code']);
                $stmt->bindValue(':name', $app['name']);
                $result = $stmt->execute();

                if ($result === true) {
                    $this->logger->logInfoMessage('Successfully inserted permission_application');
                }
                if ($result === false) {
                    $pdoTargetDatabase->rollBack();

                    throw new RuntimeException("Can't insert into permission_application, query: {$query}");
                }
            }
            $pdoTargetDatabase->commit();
        } catch (Exception $e) {
            if ($pdoTargetDatabase->inTransaction()) {
                $pdoTargetDatabase->rollBack();
            }
            $this->logger->logErrorException($e);
        }
    }

    public function insertPermissionPm3(PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName, string $sourceTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $rows = $this->getColumnDistinct(pdo: $pdoSourceDatabase, tableName: $sourceTableName, column: 'permission');
        $existingRows = $this->getColumnDistinct(pdo: $pdoTargetDatabase, tableName: $targetTableName, column: 'code');
        $existingPermissions = [];
        foreach ($existingRows as $existingRow) {
            $existingPermissions[] = $existingRow['code'];
        }

        foreach ($rows as $row) {
            try {
                $permission = PermissionMapper::map($row['permission']);
                if ($permission === '' || in_array($permission, $existingPermissions, true)) {
                    continue;
                }
                $pdoTargetDatabase->beginTransaction();

                $query = 'INSERT INTO ' . $targetTableName . ' (
                            code,application
                            ) VALUES (
                            :code,:application
                            )  ON CONFLICT (code) DO NOTHING';
                $stmt = $pdoTargetDatabase->prepare($query);

                $stmt->bindValue(':code', $permission);
                $stmt->bindValue(':application', $this->recognizeApplication($permission));
                $result = $stmt->execute();
                $pdoTargetDatabase->commit();
                if ($result === true) {
                    $this->logger->logInfoMessage("Successfully inserted {$targetTableName} {$permission}");
                }
                if ($result === false) {
                    $pdoTargetDatabase->rollBack();

                    throw new RuntimeException("Can't insert into {$targetTableName} {$permission}, query: {$query}");
                }
            } catch (Exception $e) {
                if ($pdoTargetDatabase->inTransaction()) {
                    $pdoTargetDatabase->rollBack();
                }
                $this->logger->logErrorException($e);
            }
        }
    }

    private function recognizeApplication(string $permission): string
    {
        $app = 'code-list';

        if (str_contains($permission, '_POPS')) {
            $app = 'pops';
        } elseif (str_contains($permission, '_SERVICE')) {
            $app = 'wap';
        } elseif (str_contains($permission, '_PERMISSION')) {
            $app = 'permissions';
        }

        return $app;
    }

}
