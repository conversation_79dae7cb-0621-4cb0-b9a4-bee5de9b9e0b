<?php

declare(strict_types = 1);

namespace Sync\Database;

use DateTimeImmutable;
use Exception;
use PDO;
use RuntimeException;
use function strtolower;

class InsertMobileInfo extends GetData
{

    public function insertMobileInfo(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $tableName, int $limit): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $now = (new DateTimeImmutable())->format('c');
        $lastImportedRowId = $this->getLastImportedRowId(pdoTargetDatabase: $pdoTargetDatabase, tableName: $tableName, accountName: $account['name']);
        $rows = $this->getRows(pdo: $pdoSourceDatabase, tableName: $tableName, limit: $limit, offset: $lastImportedRowId);
        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();

                $userId = $this->getUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $row['subject_id'], throwNotFound: true);
                if ($userId === null) {
                    $pdoTargetDatabase->rollBack();
                    throw new RuntimeException("Can't find subject_id for {$tableName} {$row['id']}");
                }
                $query = 'INSERT INTO mobile_info (id,user_id,created_at,registration_token,updated_at,locale,account_id,original_db_id,migrated_completely)
                        VALUES (:id,:user_id,:created_at,:registration_token,:updated_at,:locale,:account_id,:originalDbId,:migratedCompletely)
                        ON CONFLICT (original_db_id) DO NOTHING';

                $stmt = $pdoTargetDatabase->prepare($query);
                $stmt->bindValue(':id', $this->getNextId(pdoTargetDatabase: $pdoTargetDatabase, tableName: $tableName));
                $stmt->bindValue(':user_id', $userId);
                $stmt->bindValue(':created_at', $row['created_at'] ?? $now);
                $stmt->bindValue(':updated_at', $row['updated_at'] ?? $now);
                $stmt->bindValue(':registration_token', $row['registration_token']);
                $stmt->bindValue(':locale', $row['locale']);
                $stmt->bindValue(':account_id', $account['id']);
                $stmt->bindValue(':originalDbId', strtolower($account['name']) . '_' . $row['id']);
                $stmt->bindValue(':migratedCompletely', true, PDO::PARAM_BOOL);
                $result = $stmt->execute();
                $pdoTargetDatabase->commit();
                if ($result === true) {
                    $this->logger->logInfoMessage("Successfully inserted {$tableName} {$row['id']}");
                }
                if ($result === false) {
                    $pdoTargetDatabase->rollBack();

                    throw new RuntimeException("Can't insert into {$tableName} {$row['id']}, query: {$query}");
                }
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

}
