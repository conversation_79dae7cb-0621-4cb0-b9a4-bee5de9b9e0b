<?php

declare(strict_types = 1);

namespace Sync\Database;

use Exception;
use PDO;
use RuntimeException;
use function array_diff;
use function count;
use function explode;
use function strtolower;

class InsertTenantGroupTenantM2N extends GetData
{

    /**
     * @param array<string, mixed> $account
     */
    public function insertTenantGroupTenantM2N(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        // import diff
        if ($this->getRowsCount($pdoTargetDatabase, $targetTableName) > 0) {
            $this->insertTenantGroupTenantM2NDiff($account, $pdoSourceDatabase, $pdoTargetDatabase, $targetTableName);
            return;
        }

        // import all items
        $query = 'SELECT tenant_group_id, id FROM tenant WHERE tenant_group_id IS NOT NULL';
        $stmt = $pdoSourceDatabase->prepare($query);
        $stmt->execute();
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();
                $this->doInsert($pdoTargetDatabase, $account['name'], $targetTableName, $row['tenant_group_id'], $row['id']);
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    /**
     * @param array<string, mixed> $account
     */
    private function insertTenantGroupTenantM2NDiff(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);

        // Get tenant-tenant_group relationships from source database
        $query = 'SELECT concat(tenant_group_id, \'-\', id) as row_id FROM tenant WHERE tenant_group_id IS NOT NULL';
        $stmt = $pdoSourceDatabase->prepare($query);
        $stmt->execute();
        $sourceRows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $rowsOriginal = [];
        foreach ($sourceRows as $row) {
            $rowsOriginal[] = $row['row_id'];
        }

        // Get existing relationships from target database
        $rows = $this->getAllRowsByOriginalDbIdM2N(
            pdo: $pdoTargetDatabase,
            tableName: $targetTableName,
            columnM: 'tenant_group_id',
            columnN: 'tenant_id',
            tableM: 'tenant_group',
            tableN: 'tenant',
            account: $account,
        );

        $diff = array_diff($rowsOriginal, $rows);
        $this->logger->logInfoMessage(message: __FUNCTION__ . ' count: ' . count($diff));
        $accountName = strtolower($account['name']);

        foreach ($diff as $row) {
            try {
                [$tenantGroupOriginalId, $tenantOriginalId] = explode('-', $row);
                $pdoTargetDatabase->beginTransaction();
                $this->doInsert($pdoTargetDatabase, $accountName, $targetTableName, (int) $tenantGroupOriginalId, (int) $tenantOriginalId);
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    private function doInsert(PDO $pdoTargetDatabase, string $accountName, string $tableName, ?int $tenantGroupOriginalId, ?int $tenantOriginalId): void
    {
        // Validate tenant group ID
        if ($tenantGroupOriginalId === null) {
            throw new RuntimeException('Tenant group original ID cannot be null');
        }

        if ($tenantGroupOriginalId <= 0) {
            throw new RuntimeException("Tenant group original ID must be positive, got: {$tenantGroupOriginalId}");
        }

        $tenantGroupId = null;
        $tenantGroup = $this->getRowByOriginalDbId(
            pdoTargetDatabase: $pdoTargetDatabase,
            tableName: 'tenant_group',
            accountName: $accountName,
            id: $tenantGroupOriginalId,
        );
        $tenantGroupId = $tenantGroup['id'];

        if ($tenantGroupId === null) {
            throw new RuntimeException("Can't find tenant_group with original_id: {$tenantGroupOriginalId}");
        }

        // Validate tenant ID
        if ($tenantOriginalId === null) {
            throw new RuntimeException('Tenant original ID cannot be null');
        }

        if ($tenantOriginalId <= 0) {
            throw new RuntimeException("Tenant original ID must be positive, got: {$tenantOriginalId}");
        }

        $tenantId = null;
        $tenant = $this->getRowByOriginalDbId(
            pdoTargetDatabase: $pdoTargetDatabase,
            tableName: 'tenant',
            accountName: $accountName,
            id: $tenantOriginalId,
        );
        $tenantId = $tenant['id'];

        if ($tenantId === null) {
            throw new RuntimeException("Can't find tenant with original_id: {$tenantOriginalId}");
        }

        $query = 'INSERT INTO ' . $tableName . ' (tenant_group_id, tenant_id)
                    VALUES (:tenant_group_id, :tenant_id) ON CONFLICT (tenant_group_id, tenant_id) DO NOTHING';
        $stmt = $pdoTargetDatabase->prepare($query);

        $stmt->bindValue(':tenant_group_id', $tenantGroupId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $pdoTargetDatabase->commit();

        if ($result === true) {
            $this->logger->logInfoMessage(message: "Successfully inserted {$tableName} {$tenantGroupId},{$tenantId}");
        }
        if ($result === false) {
            $pdoTargetDatabase->rollBack();
            $this->logger->logErrorMessage(message: "Can't insert into {$tableName} {$tenantGroupId},{$tenantId}, query: {$query}");
            throw new RuntimeException("Can't insert into {$tableName} {$tenantGroupId},{$tenantId}, query: {$query}");
        }
    }

}
