<?php

declare(strict_types = 1);

namespace Sync\Database;

use DateTimeImmutable;
use Exception;
use PDO;
use RuntimeException;
use function strtolower;

class InsertPermissionTemplate extends GetData
{

    public function insertPermissionTemplatePm3(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName, string $sourceTableName, int $limit): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $now = (new DateTimeImmutable())->format('c');
        $lastImportedRowId = $this->getLastImportedRowId(pdoTargetDatabase: $pdoTargetDatabase, tableName: $targetTableName, accountName: $account['name']);
        $rows = $this->getRows(pdo: $pdoSourceDatabase, tableName: $sourceTableName, limit: $limit, offset: $lastImportedRowId);
        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();

                $query = 'INSERT INTO ' . $targetTableName . ' (
                            id,inserted_by,updated_by,name,created_at,updated_at,usage,original_db_id,migrated_completely,account_id
                            ) VALUES (
                            :id,:inserted_by,:updated_by,:name,:created_at,:updated_at,:usage,:originalDbId,:migratedCompletely,:account_id
                            )  ON CONFLICT (original_db_id) DO NOTHING';
                $stmt = $pdoTargetDatabase->prepare($query);
                $stmt->bindValue(':id', $this->getNextId(pdoTargetDatabase: $pdoTargetDatabase, tableName: $targetTableName));
                $stmt->bindValue(':inserted_by', $this->getExistingUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $row['inserted_by'] ?? null));
                $stmt->bindValue(':updated_by', $this->getExistingUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $row['updated_by'] ?? null));
                $stmt->bindValue(':name', $row['name']);
                $stmt->bindValue(':account_id', $account['id']);
                $stmt->bindValue(':usage', $row['description']);
                $stmt->bindValue(':created_at', $row['created_at'] ?? $now);
                $stmt->bindValue(':updated_at', $row['updated_at'] ?? $now);
                $stmt->bindValue(':originalDbId', strtolower($account['name']) . '_' . $row['id']);
                $stmt->bindValue(':migratedCompletely', true, PDO::PARAM_BOOL);
                $result = $stmt->execute();
                $pdoTargetDatabase->commit();
                if ($result === true) {
                    $this->logger->logInfoMessage("Successfully inserted {$targetTableName} {$row['id']}");
                }
                if ($result === false) {
                    $pdoTargetDatabase->rollBack();

                    throw new RuntimeException("Can't insert into {$targetTableName} {$row['id']}, query: {$query}");
                }
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

}
