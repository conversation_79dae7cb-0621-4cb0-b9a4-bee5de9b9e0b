<?php

declare(strict_types = 1);

namespace Sync\Database;

use Exception;
use PDO;
use RuntimeException;
use Sync\Log\Logger;
use function count;
use function explode;
use function implode;
use function str_replace;
use function strtolower;

class GetData extends DataHandler
{

    // Map PM2 column to PM3 column
    public const COLUMN_MAPPING = [
        'inserted_at' => 'created_at',
        'reset_inserted_at' => 'reset_created_at',
    ];

    public function __construct(
        protected Logger $logger,
    )
    {
        parent::__construct($this->logger);
    }

    public function parseOriginalDbId(string $accountName, string $originalDbId): int
    {
        $parsedOriginalDbId = explode(strtolower($accountName) . '_', $originalDbId);

        return (int) $parsedOriginalDbId[1];
    }

    /**
     * @return array<int,array<int,mixed>>
     *
     * @throws RuntimeException
     */
    public function getRows(PDO $pdo, string $tableName, int $limit = 50, int $offset = 0): array
    {
        try {
            // Prepare and execute a SELECT query to fetch rows from the specified table
            // can't use offset as before, because it happens that for example subject had offset 3466,
            // and it started to return id from 3573, because of missing rows
            $query = 'SELECT ' . $this->getTableColumnsAsString($pdo, $tableName)
                . ' FROM ' . $tableName . ' WHERE id > :offset ORDER BY id LIMIT :limit';
            $stmt = $pdo->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return array<int,array<int,mixed>>
     *
     * @throws RuntimeException
     */
    public function getAllRows(PDO $pdo, string $tableName): array
    {
        try {
            // Prepare and execute a SELECT query to fetch rows from the specified table
            $query = 'SELECT ' . $this->getTableColumnsAsString($pdo, $tableName) . ' FROM ' . $tableName;
            $stmt = $pdo->prepare($query);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return list<string>
     *
     * @throws RuntimeException
     */
    public function getAllRowsM2N(PDO $pdo, string $tableName, string $columnM, string $columnN): array
    {
        try {
            // Prepare and execute a SELECT query to fetch rows from the specified table
            $query = 'SELECT concat(' . $columnM . ', \'-\',' . $columnN . ') as row_id FROM ' . $tableName;
            $stmt = $pdo->prepare($query);
            $stmt->execute();

            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $result = [];
            foreach ($rows as $row) {
                $result[] = $row['row_id'];
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @throws RuntimeException
     */
    public function getRowsCount(PDO $pdo, string $tableName, ?string $condition = null): int
    {
        try {
            $query = 'SELECT COUNT(*) FROM ' . $tableName;
            if ($condition !== null) {
                $query .= ' WHERE ' . $condition;
            }
            $stmt = $pdo->prepare($query);
            $stmt->execute();

            return (int) $stmt->fetchColumn();
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @param array<string,mixed> $account
     * @return list<string>
     *
     * @throws RuntimeException
     */
    public function getAllRowsByOriginalDbIdM2N(
        PDO $pdo,
        string $tableName,
        string $columnM,
        string $columnN,
        string $tableM,
        string $tableN,
        array $account,
    ): array
    {
        try {
            $accountName = strtolower($account['name']);
            $query = 'SELECT concat(replace(t1.original_db_id, \'' . $accountName . '_\', \'\'), \'-\', replace(t2.original_db_id, \'' . $accountName . '_\', \'\')) as row_id
                        FROM ' . $tableName . '
                        JOIN ' . $tableM . ' t1 ON t1.id = ' . $tableName . '.' . $columnM . '
                        JOIN ' . $tableN . ' t2 ON t2.id = ' . $tableName . '.' . $columnN . '
                        WHERE t1.account_id = :account_id
                            AND t2.account_id = :account_id
                            AND t1.original_db_id IS NOT NULL
                            AND t2.original_db_id IS NOT NULL';
            $stmt = $pdo->prepare($query);
            $stmt->execute(['account_id' => $account['id']]);

            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $result = [];
            foreach ($rows as $row) {
                $result[] = $row['row_id'];
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return array<int,array<int,mixed>>
     *
     * @throws RuntimeException
     */
    public function getIncompletelyMigratedRows(PDO $pdoTargetDatabase, string $tableName, string $accountName, int $limit = 50): array
    {
        try {
            // Prepare and execute a SELECT query to fetch rows from the specified table
            $query = "SELECT * FROM $tableName  WHERE migrated_completely = :migratedCompletely AND original_db_id LIKE :originalDbId ORDER BY id LIMIT :limit";
            $stmt = $pdoTargetDatabase->prepare($query);
            $stmt->bindValue(':originalDbId', strtolower($accountName) . '_%');
            $stmt->bindValue(':migratedCompletely', false, PDO::PARAM_BOOL);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}. Query: {$query}", (int) $e->getCode(), $e);
        }
    }

    public function getLastImportedRowId(PDO $pdoTargetDatabase, string $tableName, string $accountName): int
    {
        try {
            $accountName = strtolower($accountName);
            // Prepare and execute a SELECT query to fetch rows from the specified table
            $query = "SELECT cast(replace(original_db_id, '" . $accountName . "_', '') as int) as original_id
                        FROM $tableName
                        WHERE original_db_id LIKE '" . $accountName . "_%'
                        ORDER BY original_id DESC
                        LIMIT 1";
            $stmt = $pdoTargetDatabase->prepare($query);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($row === false) {
                return 0;
            }
            if (count($row) > 0) {
                return (int) $row['original_id'];
            }

            return 0;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}. Query: {$query}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return array<string,mixed>
     *
     * @throws RuntimeException
     */
    public function getRowById(PDO $pdo, string $tableName, int $id): array
    {
        try {
            $query = 'SELECT ' . $this->getTableColumnsAsString($pdo, $tableName) . ' FROM ' . $tableName . ' WHERE id = :id LIMIT 1';
            $stmt = $pdo->prepare($query);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result === false) {
                $this->logger->logErrorMessage(message: "Can't read data from table #{$tableName}. Query: {$query}");
                throw new RuntimeException("Can't read data from table #{$tableName}. Query: {$query}");
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table #{$tableName}. Query: {$query}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return array<string,mixed>
     *
     * @throws RuntimeException
     */
    public function getRowByOriginalDbId(PDO $pdoTargetDatabase, string $tableName, string $accountName, int $id): array
    {
        try {
            $originalDbId = strtolower($accountName) . '_' . $id;
            $query = "SELECT * FROM $tableName WHERE original_db_id = :originalDbId LIMIT 1";
            $stmt = $pdoTargetDatabase->prepare($query);
            $stmt->bindValue(':originalDbId', $originalDbId);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result === false) {
                throw new RuntimeException("Can't read data from table #{$tableName}. Query: {$query}. OriginalDbId: {$originalDbId}");
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table #{$tableName}. Query: {$query}. OriginalDbId: {$originalDbId}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return array<string,mixed>
     *
     * @throws RuntimeException
     */
    public function getRowByIdInOriginalDb(PDO $pdoSourceDatabase, string $tableName, int $id): array
    {
        try {
            $query = "SELECT * FROM $tableName WHERE id = :id LIMIT 1";
            $stmt = $pdoSourceDatabase->prepare($query);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result === false) {
                throw new RuntimeException("Can't read data from table #{$tableName}. Query: {$query}");
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table #{$tableName}. Query: {$query}", (int) $e->getCode(), $e);
        }
    }

    public function getUserReferenceId(PDO $pdoTargetDatabase, string $accountName, ?int $originalId, bool $throwNotFound = false): ?int
    {
        $subjectId = null;
        if ($originalId !== null) {
            try {
                $result = $this->getRowByOriginalDbId(pdoTargetDatabase: $pdoTargetDatabase, tableName: '"user"', accountName: $accountName, id: $originalId);
                $subjectId = (int) $result['id'];
            } catch (RuntimeException $exception) {
                // subject not found
                $this->logger->logErrorException($exception);
                if ($throwNotFound) {
                    throw new RuntimeException("Can't read user reference by accountName: $accountName and originalDbId: {$originalId}");
                }
            }
        }

        return $subjectId;
    }

    /**
     * @return array<int, mixed>
     *
     * @throws RuntimeException
     */
    public function getColumnDistinct(PDO $pdo, string $tableName, string $column, string $where = ''): array
    {
        try {
            $query = 'SELECT DISTINCT ' . $column . ' FROM ' . $tableName . ' ' . ($where !== '' ? ' WHERE ' . $where : '');
            $stmt = $pdo->prepare($query);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    public function getCategoryEventIdByNumber(PDO $pdo, string $eventNo): ?int
    {
        $eventCategoryId = null;
        if ($eventNo !== '') {
            try {
                $query = 'SELECT id FROM event_category WHERE code = :code LIMIT 1';
                $stmt = $pdo->prepare($query);
                $stmt->bindValue(':code', $eventNo);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($result === false) {
                    throw new RuntimeException("Can't read data from table event_category. Query: {$query}. code: " . $eventNo);
                }
                $eventCategoryId = (int) $result['id'];
            } catch (Exception $e) {
                $this->logger->logErrorException($e);
                throw new RuntimeException("Can't read data from table: event_category", (int) $e->getCode(), $e);
            }
        }

        return $eventCategoryId;
    }

    protected function getTableColumnsAsString(PDO $pdo, string $tableName): string
    {
        $columnNames = $this->getTableColumns($pdo, $tableName);

        return implode(', ', $columnNames);
    }

    protected function getNextId(PDO $pdoTargetDatabase, string $tableName): int
    {
        $tableName = str_replace('"', '', $tableName);
        $stmt = $pdoTargetDatabase->prepare('SELECT nextval(\'' . $tableName . '_id_seq\') as id');
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int) $result['id'];
    }

    protected function getExistingUserReferenceId(PDO $pdoTargetDatabase, string $accountName, ?int $originalId): ?int
    {
        $subjectId = $this->getUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $accountName, originalId: $originalId);

        if ($subjectId === null) {
            // get first one
            $stmt = $pdoTargetDatabase->prepare('SELECT MIN(id) as id FROM "user"');
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            $subjectId = (int) $result['id'];

            if ($subjectId === 0) {
                $subjectId = null;
            }

        }

        return $subjectId;
    }

    /**
     * @return list<string>
     */
    private function getTableColumns(PDO $pdo, string $tableName): array
    {
        $query = 'SELECT column_name FROM information_schema.columns WHERE lower(table_name)=lower(\'' . $tableName . '\')';
        $stmt = $pdo->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = [];
        foreach ($columns as $column) {
            $columnName = '"' . $column['column_name'] . '"';
            foreach (self::COLUMN_MAPPING as $replaceFrom => $replaceTo) {
                if ($column['column_name'] === $replaceFrom) {
                    $columnName .= ' as ' . $replaceTo;
                    break;
                }
            }
            $columnNames[] = $columnName;
        }

        return $columnNames;
    }

}
