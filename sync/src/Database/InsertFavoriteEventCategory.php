<?php

declare(strict_types = 1);

namespace Sync\Database;

use DateTimeImmutable;
use Exception;
use PDO;
use RuntimeException;
use function array_column;
use function array_diff;
use function count;
use function explode;
use function strtolower;

class InsertFavoriteEventCategory extends GetData
{

    /**
     * @param array<string, mixed> $account
     */
    public function insertFavoriteEventCategory(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        try {
            $accountName = strtolower($account['name']);
            $sourceList = $this->getSourceList(accountName: $accountName, pdoSourceDatabase: $pdoSourceDatabase);
            $targetList = $this->getTargetList(accountName: $accountName, pdoTargetDatabase: $pdoTargetDatabase);

            $diff = array_diff($sourceList, $targetList);
            $this->logger->logInfoMessage(message: __FUNCTION__ . ' count: ' . count($diff));

            if (count($diff) > 0) {
                foreach ($diff as $originalDbId) {
                    [, $ident] = explode(strtolower($accountName) . '_', $originalDbId);
                    [$originalFacilityId, $eventNo] = explode('-', $ident);

                    $facilityId = null;
                    if ((int) $originalFacilityId > 0) {
                        $facility = $this->getRowByOriginalDbId(pdoTargetDatabase: $pdoTargetDatabase, tableName: 'facility', accountName: $accountName, id: (int) $originalFacilityId);
                        $facilityId = $facility['id'];
                    }
                    if ($facilityId === null) {
                        throw new RuntimeException("Can't find facility with original id {$originalFacilityId}");
                    }
                    $eventCategoryId = null;
                    if ($eventNo !== '') {
                        try {
                            $query = 'SELECT * FROM event_category WHERE code = :code AND account_id = :account_id LIMIT 1';
                            $stmt = $pdoTargetDatabase->prepare($query);
                            $stmt->bindValue(':code', $eventNo);
                            $stmt->bindValue(':account_id', $account['id']);
                            $stmt->execute();
                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                            if ($result === false) {
                                throw new RuntimeException("Can't read data from table event_category. Query: {$query}. code: {$eventNo}");
                            }

                            $eventCategoryId = $result['id'];
                        } catch (Exception $e) {
                            $this->logger->logErrorException($e);
                            throw new RuntimeException("Can't read data from table event_category. Query: {$query}. code: {$eventNo}", (int) $e->getCode(), $e);
                        }
                    }
                    if ($eventCategoryId === null) {
                        throw new RuntimeException("Can't find event category with code {$eventNo}");
                    }

                    $this->doInsert($pdoTargetDatabase, $account, $facilityId, $eventCategoryId);
                }
            }
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("SELECT query error: {$e->getMessage()}", $e->getCode(), $e);
        }
    }

    /**
     * @param array<string, mixed> $account
     */
    private function doInsert(PDO $pdoTargetDatabase, array $account, int $facilityId, int $eventCategoryId): void
    {
        try {
            $pdoTargetDatabase->beginTransaction();
            $now = (new DateTimeImmutable())->format('c');

            $query = 'INSERT INTO favorite_event_category (
                        id, facility_id, event_category_id, account_id, created_at, updated_at
                    ) VALUES (
                        :id, :facility_id, :event_category_id, :account_id, :created_at, :updated_at
                    ) ON CONFLICT (facility_id, event_category_id, account_id) DO NOTHING';
            $stmt = $pdoTargetDatabase->prepare($query);
            $id = $this->getNextId(pdoTargetDatabase: $pdoTargetDatabase, tableName: 'favorite_event_category');
            $stmt->bindValue(':id', $id);
            $stmt->bindValue(':facility_id', $facilityId);
            $stmt->bindValue(':event_category_id', $eventCategoryId);
            $stmt->bindValue(':account_id', $account['id']);
            $stmt->bindValue(':created_at', $now);
            $stmt->bindValue(':updated_at', $now);
            $result = $stmt->execute();
            $pdoTargetDatabase->commit();
            if ($result === true) {
                $this->logger->logInfoMessage('Successfully inserted favorite_event_category ' . $id);
            }
            if ($result === false) {
                $pdoTargetDatabase->rollBack();
            }

        } catch (Exception $e) {
            $pdoTargetDatabase->rollBack();
            $this->logger->logErrorException($e);
        }
    }

    /**
     * @return list<string>
     */
    private function getSourceList(string $accountName, PDO $pdoSourceDatabase): array
    {
        try {
            $query = 'SELECT CONCAT(\'' . $accountName . '_\', pt.object_id, \'-\', e.eventno) as id
                FROM pops_top_20 pt
                JOIN popstop20_event pe ON pe.popstop20_id=pt.id
                JOIN event e ON pe.event_id = e.id';
            $stmt = $pdoSourceDatabase->prepare($query);
            $stmt->execute();

            $sourceIds = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return array_column($sourceIds, 'id');
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: pops_top_20", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return list<string>
     */
    private function getTargetList(string $accountName, PDO $pdoTargetDatabase): array
    {
        try {
            $query = 'SELECT concat(f.original_db_id, \'-\', ec.code) as id
                        FROM favorite_event_category fv
                        JOIN facility f ON f.id=fv.facility_id
                        JOIN event_category ec ON ec.id=fv.event_category_id
                        WHERE f.original_db_id LIKE \'' . $accountName . '_%\'';
            $stmt = $pdoTargetDatabase->prepare($query);
            $stmt->execute();

            $targetIds = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return array_column($targetIds, 'id');
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: favorite_event_category", (int) $e->getCode(), $e);
        }
    }

}
