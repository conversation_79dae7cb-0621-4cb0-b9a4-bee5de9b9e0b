<?php

declare(strict_types = 1);

namespace Sync\Database;

use DateTimeImmutable;
use Exception;
use PDO;
use RuntimeException;
use function strtolower;

class InsertCountry extends GetData
{

    /**
     * @throws RuntimeException
     */
    public function importCountries(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $tableName, int $limit): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $now = (new DateTimeImmutable())->format('c');
        $lastImportedRowId = $this->getLastImportedRowId(pdoTargetDatabase: $pdoTargetDatabase, tableName: $tableName, accountName: $account['name']);
        $rows = $this->getRows(pdo: $pdoSourceDatabase, tableName: $tableName, limit: $limit, offset: $lastImportedRowId);
        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();

                $query = 'INSERT INTO country (id, inserted_by, updated_by, name, created_at, updated_at, original_db_id, migrated_completely, account_id)
                        VALUES (:id, :inserted_by, :updated_by, :name, :created_at, :updated_at, :originalDbId, :migratedCompletely,:account_id)
                        ON CONFLICT (original_db_id) DO NOTHING';
                $stmt = $pdoTargetDatabase->prepare($query);
                $stmt->bindValue(':id', $this->getNextId(pdoTargetDatabase: $pdoTargetDatabase, tableName: $tableName));
                $stmt->bindValue(':inserted_by', $this->getExistingUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $row['inserted_by'] ?? null));
                $stmt->bindValue(':updated_by', $this->getExistingUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $row['updated_by'] ?? null));
                $stmt->bindValue(':name', $row['name']);
                $stmt->bindValue(':created_at', $row['created_at'] ?? $now);
                $stmt->bindValue(':updated_at', $row['updated_at'] ?? $now);
                $stmt->bindValue(':originalDbId', strtolower($account['name']) . '_' . $row['id']);
                $stmt->bindValue(':migratedCompletely', false, PDO::PARAM_BOOL);
                $stmt->bindValue(':account_id', $account['id']);
                $result = $stmt->execute();
                $pdoTargetDatabase->commit();
                if ($result === true) {
                    $this->logger->logInfoMessage("Successfully inserted {$tableName} {$row['id']}");
                }
                if ($result === false) {
                    $pdoTargetDatabase->rollBack();

                    throw new RuntimeException("Can't insert into {$tableName} {$row['id']}, query: {$query}");
                }
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    /**
     * @throws RuntimeException
     */
    public function updateCountries(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $tableName, int $limit): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $rows = $this->getIncompletelyMigratedRows(pdoTargetDatabase: $pdoTargetDatabase, tableName: $tableName, accountName: $account['name'], limit: $limit);
        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();
                $parsedOriginalDbId = $this->parseOriginalDbId(accountName: $account['name'], originalDbId: $row['original_db_id']);
                $originalRow = $this->getRowById(pdo: $pdoSourceDatabase, tableName: $tableName, id: $parsedOriginalDbId);

                $insertedBy = $this->getUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $originalRow['inserted_by']);
                $updatedBy = $this->getUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $account['name'], originalId: $originalRow['updated_by']);

                $query = 'UPDATE country SET inserted_by = :inserted_by, updated_by = :updated_by, migrated_completely = :migratedCompletely WHERE id = :id';
                $stmt = $pdoTargetDatabase->prepare($query);
                $stmt->bindValue(':inserted_by', $insertedBy);
                $stmt->bindValue(':updated_by', $updatedBy);
                $stmt->bindValue(':migratedCompletely', true, PDO::PARAM_BOOL);
                $stmt->bindValue(':id', $row['id']);
                $result = $stmt->execute();
                $pdoTargetDatabase->commit();
                if ($result === true) {
                    $this->logger->logInfoMessage("Successfully updated {$tableName} {$row['id']}");
                }
                if ($result === false) {
                    $pdoTargetDatabase->rollBack();

                    throw new RuntimeException("Can't insert into {$tableName} {$row['id']}, query: {$query}");
                }
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

}
