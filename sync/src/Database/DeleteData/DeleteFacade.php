<?php

declare(strict_types = 1);

namespace Sync\Database\DeleteData;

use Exception;
use PDO;
use RuntimeException;
use Sync\Database\GetData;
use function array_diff;
use function count;
use function explode;
use function strtolower;

class DeleteFacade extends GetData
{

    /**
     * Order of deletion is important, because of foreign key constraints
     */
    public function deleteData(string $accountName, string $databaseName): void
    {
        $this->loadEnv($accountName, $databaseName);
        $pdoSourceDatabase = $this->getSourceDbConnection($databaseName);
        $pdoTargetDatabase = $this->getTargetDbConnection($_ENV['DB_NAME']);
        $account = $this->getAccountByName(pdo: $pdoTargetDatabase, accountName: $accountName);

        // delete PM3 permissions
        $this->deleteM2N(account: $account, pdoSourceDatabase: $pdoSourceDatabase, pdoTargetDatabase: $pdoTargetDatabase, sourceTable: 'permission_template_subject_m2n', targetTable: 'permission_template_user_m2n', sourceColumnM: 'permission_template_id', sourceColumnN: 'subject_id', columnM: 'permission_template_id', columnN: 'user_id', tableM: 'permission_template', tableN: '"user"');
        $this->deleteM2N(account: $account, pdoSourceDatabase: $pdoSourceDatabase, pdoTargetDatabase: $pdoTargetDatabase, sourceTable: 'permission_group_subject_m2n', targetTable: 'permission_group_user_m2n', sourceColumnM: 'permission_group_id', sourceColumnN: 'subject_id', columnM: 'permission_group_id', columnN: 'user_id', tableM: 'permission_group', tableN: '"user"');
        $this->deleteM2N(account: $account, pdoSourceDatabase: $pdoSourceDatabase, pdoTargetDatabase: $pdoTargetDatabase, sourceTable: 'permission_group_object_m2n', targetTable: 'permission_group_facility_m2n', sourceColumnM: 'permission_group_id', sourceColumnN: 'object_id', columnM: 'permission_group_id', columnN: 'facility_id', tableM: 'permission_group', tableN: 'facility');
        $this->delete(accountName: $account['name'], pdoSourceDatabase: $pdoSourceDatabase, pdoTargetDatabase: $pdoTargetDatabase, sourceTable: 'permission_group', targetTable: 'permission_group');
        $this->delete(accountName: $account['name'], pdoSourceDatabase: $pdoSourceDatabase, pdoTargetDatabase: $pdoTargetDatabase, sourceTable: 'permission_template', targetTable: 'permission_template');
    }

    private function delete(
        string $accountName,
        PDO $pdoSourceDatabase,
        PDO $pdoTargetDatabase,
        string $sourceTable,
        string $targetTable,
    ): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__ . ' for table: ' . $targetTable);
        $sourceIds = $this->getAllSourceIds($pdoSourceDatabase, $sourceTable, $accountName);
        $targetIds = $this->getAllTargetIds($pdoTargetDatabase, $targetTable, $accountName);

        $diff = array_diff($targetIds, $sourceIds);
        $this->logger->logInfoMessage($targetTable . 'Rows to delete: ' . count($diff));
        if (count($diff) > 0) {
            $this->deleteTargetRows($pdoTargetDatabase, $targetTable, $diff);
        }
    }

    /**
     * @param array<string, mixed> $account
     */
    private function deleteM2N(
        array $account,
        PDO $pdoSourceDatabase,
        PDO $pdoTargetDatabase,
        string $sourceTable,
        string $targetTable,
        string $sourceColumnM,
        string $sourceColumnN,
        string $columnM,
        string $columnN,
        string $tableM,
        string $tableN,
    ): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__ . ' for table: ' . $targetTable);
        $sourceIds = $this->getAllRowsM2N($pdoSourceDatabase, $sourceTable, $sourceColumnM, $sourceColumnN);
        $targetIds = $this->getAllRowsByOriginalDbIdM2N($pdoTargetDatabase, $targetTable, $columnM, $columnN, $tableM, $tableN, $account);
        $diff = array_diff($targetIds, $sourceIds);

        $this->logger->logInfoMessage($targetTable . 'Rows to delete: ' . count($diff));
        if (count($diff) > 0) {
            $this->deleteTargetRowsM2N($pdoTargetDatabase, $targetTable, $diff, $columnM, $columnN, $tableM, $tableN, $account['name']);
        }
    }

    /**
     * @return list<string>
     */
    private function getAllSourceIds(PDO $pdo, string $tableName, string $accountName): array
    {
        try {
            $accountName = strtolower($accountName);
            $query = 'SELECT id FROM ' . $tableName;
            $stmt = $pdo->prepare($query);
            $stmt->execute();

            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $data = [];
            foreach ($result as $row) {
                $data[] = $accountName . '_' . $row['id'];
            }
            return $data;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @return list<string>
     */
    private function getAllTargetIds(PDO $pdo, string $tableName, string $accountName): array
    {
        try {
            $query = 'SELECT original_db_id FROM ' . $tableName . ' WHERE original_db_id LIKE \'' . $accountName . '_%\'';
            $stmt = $pdo->prepare($query);
            $stmt->execute();

            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $data = [];
            foreach ($result as $row) {
                $data[] = $row['original_db_id'];
            }
            return $data;
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
            throw new RuntimeException("Can't read data from table: {$tableName}", (int) $e->getCode(), $e);
        }
    }

    /**
     * @param non-empty-array<int<0, max>, string> $ids
     */
    private function deleteTargetRows(PDO $pdo, string $tableName, array $ids): void
    {
        foreach ($ids as $id) {
            try {
                $this->logger->logInfoMessage('Delete row from ' . $tableName . ' with original_db_id: ' . $id);
                $pdo->beginTransaction();
                $query = 'DELETE FROM ' . $tableName . ' WHERE original_db_id = :originalDbId';
                $stmt = $pdo->prepare($query);
                $stmt->bindValue(':originalDbId', $id);
                $stmt->execute();
                $pdo->commit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    /**
     * @param non-empty-array<int<0, max>, string> $ids
     */
    private function deleteTargetRowsM2N(
        PDO $pdo,
        string $tableName,
        array $ids,
        string $columnM,
        string $columnN,
        string $tableM,
        string $tableN,
        string $accountName,
    ): void
    {
        $accountName = strtolower($accountName);
        foreach ($ids as $id) {
            try {
                [$valueM, $valueN] = explode('-', $id);
                $valueM = $accountName . '_' . $valueM;
                $valueN = $accountName . '_' . $valueN;
                $this->logger->logInfoMessage('Delete row from ' . $tableName . ' with ' . $columnM . ': ' . $valueM . ' and ' . $columnN . ': ' . $valueN);
                $pdo->beginTransaction();
                $query = 'DELETE FROM ' . $tableName . ' t
                            USING ' . $tableM . ' m, ' . $tableN . ' n
                            WHERE m.id=t.' . $columnM . ' AND m.original_db_id=:valueM
                                AND n.id=t.' . $columnN . ' AND n.original_db_id=:valueN';
                $stmt = $pdo->prepare($query);
                $stmt->bindValue(':valueM', $valueM);
                $stmt->bindValue(':valueN', $valueN);
                $stmt->execute();
                $pdo->commit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

}
