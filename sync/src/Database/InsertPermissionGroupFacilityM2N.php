<?php

declare(strict_types = 1);

namespace Sync\Database;

use Exception;
use PDO;
use RuntimeException;
use function array_diff;
use function count;
use function explode;
use function strtolower;

class InsertPermissionGroupFacilityM2N extends GetData
{

    private function insertPermissionGroupFacilityM2NPm3Diff(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName, string $sourceTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $rowsOriginal = $this->getAllRowsM2N(pdo: $pdoSourceDatabase, tableName: $sourceTableName, columnM: 'permission_group_id', columnN: 'object_id');
        $rows = $this->getAllRowsByOriginalDbIdM2N(pdo: $pdoTargetDatabase, tableName: $targetTableName, columnM: 'permission_group_id', columnN: 'facility_id', tableM: 'permission_group', tableN: 'facility', account: $account);
        $diff = array_diff($rowsOriginal, $rows);
        $this->logger->logInfoMessage(message: __FUNCTION__ . ' count: ' . count($diff));
        $accountName = strtolower($account['name']);
        foreach ($diff as $row) {
            try {
                [$permissionGroupOriginalId, $objectOriginalId] = explode('-', $row);
                $pdoTargetDatabase->beginTransaction();
                $this->doInsertPm3($pdoTargetDatabase, $accountName, $targetTableName, (int) $permissionGroupOriginalId, (int) $objectOriginalId);
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    public function insertPermissionGroupFacilityM2NPm3(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName, string $sourceTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        // import diff
        if ($this->getRowsCount($pdoTargetDatabase, $targetTableName) > 0) {
            $this->insertPermissionGroupFacilityM2NPm3Diff($account, $pdoSourceDatabase, $pdoTargetDatabase, $targetTableName, $sourceTableName);
            return;
        }

        // import all items
        $rows = $this->getAllRows(pdo: $pdoSourceDatabase, tableName: $sourceTableName);
        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();

                $this->doInsertPm3($pdoTargetDatabase, $account['name'], $targetTableName, $row['permission_group_id'], $row['object_id']);
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    private function doInsertPm3(PDO $pdoTargetDatabase, string $accountName, string $tableName, ?int $permissionGroupOriginalId, ?int $facilityOriginalId): void
    {
        $permissionGroupId = null;
        if ($permissionGroupOriginalId > 0) {
            $permissionGroup = $this->getRowByOriginalDbId(
                pdoTargetDatabase: $pdoTargetDatabase,
                tableName: 'permission_group',
                accountName: $accountName,
                id: $permissionGroupOriginalId,
            );
            $permissionGroupId = $permissionGroup['id'];
        }
        if ($permissionGroupId === null) {
            throw new RuntimeException("Can't find permission group with original id {$permissionGroupOriginalId}");
        }
        $facilityId = null;
        if ($facilityOriginalId > 0) {
            $object = $this->getRowByOriginalDbId(pdoTargetDatabase: $pdoTargetDatabase, tableName: 'facility', accountName: $accountName, id: $facilityOriginalId);
            $facilityId = $object['id'];
        }
        if ($facilityId === null) {
            throw new RuntimeException("Can't find facility with original id {$facilityOriginalId}");
        }

        $query = 'INSERT INTO ' . $tableName . ' (permission_group_id,facility_id)
                    VALUES (:permission_group_id, :facility_id) ON CONFLICT (permission_group_id,facility_id) DO NOTHING';
        $stmt = $pdoTargetDatabase->prepare($query);

        $stmt->bindValue(':permission_group_id', $permissionGroupId);
        $stmt->bindValue(':facility_id', $facilityId);
        $result = $stmt->execute();
        $pdoTargetDatabase->commit();
        if ($result === true) {
            $this->logger->logInfoMessage(message: "Successfully inserted {$tableName} {$permissionGroupId},{$facilityId}");
        }
        if ($result === false) {
            $pdoTargetDatabase->rollBack();
            $this->logger->logErrorMessage(message: "Can't insert into {$tableName} {$permissionGroupId},{$facilityId}, query: {$query}");
            throw new RuntimeException("Can't insert into {$tableName} {$permissionGroupId},{$facilityId}, query: {$query}");
        }
    }

}
