<?php

declare(strict_types = 1);

namespace Sync\Database;

use Exception;
use PDO;
use RuntimeException;
use function array_diff;
use function count;
use function explode;
use function strtolower;

class InsertPermissionGroupUserM2N extends GetData
{

    private function doInsertPm3(PDO $pdoTargetDatabase, string $accountName, string $tableName, ?int $permissionGroupOriginalId, ?int $subjectOriginalId): void
    {
        $permissionGroupId = null;
        if ($permissionGroupOriginalId > 0) {
            $permissionGroup = $this->getRowByOriginalDbId(
                pdoTargetDatabase: $pdoTargetDatabase,
                tableName: 'permission_group',
                accountName: $accountName,
                id: (int) $permissionGroupOriginalId,
            );
            $permissionGroupId = $permissionGroup['id'];
        }
        if ($permissionGroupId === null) {
            throw new RuntimeException("Can't find permission group with original id {$permissionGroupOriginalId}");
        }

        $userId = $this->getUserReferenceId(pdoTargetDatabase: $pdoTargetDatabase, accountName: $accountName, originalId: $subjectOriginalId, throwNotFound: true);

        $query = 'INSERT INTO ' . $tableName . ' (permission_group_id,user_id)
                    VALUES (:permission_group_id, :user_id) ON CONFLICT (permission_group_id,user_id) DO NOTHING';
        $stmt = $pdoTargetDatabase->prepare($query);

        $stmt->bindValue(':permission_group_id', $permissionGroupId);
        $stmt->bindValue(':user_id', $userId);
        $result = $stmt->execute();
        $pdoTargetDatabase->commit();
        if ($result === true) {
            $this->logger->logInfoMessage(message: "Successfully inserted {$tableName} {$permissionGroupId},{$userId}");
        }
        if ($result === false) {
            $pdoTargetDatabase->rollBack();
            $this->logger->logErrorMessage(message: "Can't insert into {$tableName} {$permissionGroupId},{$userId}, query: {$query}");
            throw new RuntimeException("Can't insert into {$tableName} {$permissionGroupId},{$userId}, query: {$query}");
        }
    }

    private function insertPermissionGroupUserM2NPm3Diff(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName, string $sourceTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        $rowsOriginal = $this->getAllRowsM2N(pdo: $pdoSourceDatabase, tableName: $sourceTableName, columnM: 'permission_group_id', columnN: 'subject_id');
        $rows = $this->getAllRowsByOriginalDbIdM2N(pdo: $pdoTargetDatabase, tableName: $targetTableName, columnM: 'permission_group_id', columnN: 'user_id', tableM: 'permission_group', tableN: '"user"', account: $account);
        $diff = array_diff($rowsOriginal, $rows);
        $this->logger->logInfoMessage(message: __FUNCTION__ . ' count: ' . count($diff));
        $accountName = strtolower($account['name']);
        foreach ($diff as $row) {
            try {
                [$permissionGroupOriginalId, $subjectOriginalId] = explode('-', $row);
                $pdoTargetDatabase->beginTransaction();
                $this->doInsertPm3($pdoTargetDatabase, $accountName, $targetTableName, (int) $permissionGroupOriginalId, (int) $subjectOriginalId);
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

    public function insertPermissionGroupUserM2NPm3(array $account, PDO $pdoSourceDatabase, PDO $pdoTargetDatabase, string $targetTableName, string $sourceTableName): void
    {
        $this->logger->logInfoMessage('Start ' . __FUNCTION__);
        // import diff
        if ($this->getRowsCount($pdoTargetDatabase, $targetTableName) > 0) {
            $this->insertPermissionGroupUserM2NPm3Diff($account, $pdoSourceDatabase, $pdoTargetDatabase, $targetTableName, $sourceTableName);
            return;
        }

        // import all items
        $rows = $this->getAllRows(pdo: $pdoSourceDatabase, tableName: $sourceTableName);
        foreach ($rows as $row) {
            try {
                $pdoTargetDatabase->beginTransaction();
                $this->doInsertPm3($pdoTargetDatabase, $account['name'], $targetTableName, $row['permission_group_id'], $row['subject_id']);
            } catch (Exception $e) {
                $pdoTargetDatabase->rollBack();
                $this->logger->logErrorException($e);
            }
        }
    }

}
