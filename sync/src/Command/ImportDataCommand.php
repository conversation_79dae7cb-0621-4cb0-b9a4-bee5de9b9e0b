<?php

declare(strict_types = 1);

namespace Sync\Command;

use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Sync\Database\DeleteData\DeleteFacade;
use Sync\Facade\ImportDataFacade;
use Sync\Log\Logger;
use function microtime;
use function round;

#[AsCommand(
    name: 'app:import-data',
    hidden: false,
)]
class ImportDataCommand extends BaseCommand
{

    use LockableTrait;

    private const DB_NAME = 'dbName';
    private const ACCOUNT_NAME = 'accountName';
    private const LIMIT = 'limit';

    public function __construct(
        private Logger $logger,
        private ImportDataFacade $importDataFacade,
        private DeleteFacade $deleteFacade,
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument(self::DB_NAME, InputArgument::REQUIRED, 'Database name');
        $this->addArgument(self::ACCOUNT_NAME, InputArgument::REQUIRED, 'Account name');
        $this->addOption(self::LIMIT, 'l', InputArgument::OPTIONAL, 'Limit', 100000);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $startTime = microtime(true);
        $this->logger->logInfoMessage('Import data command started');

        $inputArgs = $this->getInputArgs($input);
        $inputArgs->allowWeakTypes();
        $limit = (int) $input->getOption(self::LIMIT);
        if (!$this->lock()) {
            $output->writeln('The command is already running in another process.');
            return Command::FAILURE;
        }
        try {
            $this->importDataFacade->importData(accountName: $inputArgs->getString(self::ACCOUNT_NAME), databaseName: $inputArgs->getString(self::DB_NAME), limit: $limit);
            $this->deleteFacade->deleteData(accountName: $inputArgs->getString(self::ACCOUNT_NAME), databaseName: $inputArgs->getString(self::DB_NAME));
        } catch (Exception $e) {
            $this->logger->logErrorException($e);
        }

        $this->release();
        $this->logger->logInfoMessage('Finished: ' . round(microtime(true) - $startTime, 2) . ' sec');

        return Command::SUCCESS;
    }

}
