<?php

declare(strict_types = 1);

namespace Sync\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Sync\Exception\InvalidInputException;
use Sync\Input\InputArgs;

abstract class BaseCommand extends Command
{

    /**
     * @throws InvalidInputException
     */
    protected function getInputArgs(InputInterface $input): InputArgs
    {
        return InputArgs::create($input->getArguments());
    }

}
