<?php

declare(strict_types = 1);

namespace Sync\Exception;

use Throwable;

class InvalidInputException extends RuntimeException
{

    public function __construct(string $message, ?Throwable $previous = null)
    {
        parent::__construct($message, $previous);
    }

    public static function createMissingException(string $path): self
    {
        return new self("Value of {$path} is missing.");
    }

    public static function createInvalidTypeException(string $path, string $type): self
    {
        return new self("Value of {$path} must be {$type}.");
    }

}
