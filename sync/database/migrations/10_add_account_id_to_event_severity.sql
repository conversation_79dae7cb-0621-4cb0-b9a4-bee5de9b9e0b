ALTER TABLE event_severity
    ADD COLUMN account_id INTEGER DEFAULT NULL,
    ADD CONSTRAINT event_severity_account_id_fk FOREIGN KEY (account_id) REFERENCES account(id);

UPDATE event_severity SET account_id = 1 WHERE original_db_id LIKE 'cs_%';
UPDATE event_severity SET account_id = 2 WHERE original_db_id LIKE 'sk_%';

ALTER TABLE event_severity ALTER COLUMN account_id DROP NOT NULL;

-- drop unique key for registration_token
ALTER TABLE event_severity
    DROP CONSTRAINT IF EXISTS uniq_b08c38bb77153098;
DROP INDEX IF EXISTS uniq_b08c38bb77153098;

-- -- Add a new unique constraint on two columns
ALTER TABLE event_severity
    ADD CONSTRAINT uniq_b08c38bb77153098 UNIQUE (code, account_id);

-- TODO: this table doesn't need to include account_id, the code will remain the same for all the language mutations, it should be  handled in the script
