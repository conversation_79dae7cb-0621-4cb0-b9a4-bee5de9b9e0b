ALTER TABLE mobile_info
    ADD COLUMN account_id INTEGER DEFAULT NULL,
    ADD CONSTRAINT mobile_info_account_id_fk FOREIGN KEY (account_id) REFERENCES account(id);

UPDATE mobile_info SET account_id = 1 WHERE original_db_id LIKE 'cs_%';
UPDATE mobile_info SET account_id = 2 WHERE original_db_id LIKE 'sk_%';

ALTER TABLE mobile_info ALTER COLUMN account_id DROP NOT NULL;

-- drop unique key for registration_token
ALTER TABLE mobile_info
    DROP CONSTRAINT IF EXISTS uniq_29bb29b5d09d01d3;
DROP INDEX IF EXISTS uniq_29bb29b5d09d01d3;

-- -- Add a new unique constraint on two columns
ALTER TABLE mobile_info
    ADD CONSTRAINT uniq_29bb29b5d09d01d3 UNIQUE (registration_token, account_id);
