-- make it not null
DO $$
    DECLARE
        tableName text;
BEGIN
    FOREACH tableName IN ARRAY ARRAY[
            'absence',
            'agency_rate',
            'attendance',
            'attendance_break',
            'attendance_break_type',
            'billing_type',
            'calendar',
            'city',
            'company',
            'contact_info',
            'contract',
            'contract_rate',
            'currency',
            'dimension',
            'dimension_level',
            'documentation',
            'download',
            'event',
            'file',
            'fluctuation',
            'log',
            'mobile_notification',
            'mobile_notification_queue',
            'news',
            'news_rss',
            'object',
            'object_group',
            'object_info',
            'overtime_control',
            'period',
            'periodic_payment',
            'permission_group',
            'permission_template',
            'pool',
            'pops',
            'pops_alert',
            'pops_event',
            'pops_event_invalid',
            'pops_event_translation',
            'pops_statistics',
            'product',
            'purchase',
            'reporting',
            'reporting_setting',
            'reporting_template',
            'reporting_template_setting',
            'revisions',
            'role',
            'role_group',
            'service',
            'service_template',
            'shortcut',
            'subject_group',
            'subject_rate',
            'subject_type',
            'suggestion',
            'tenant',
            'tenant_group'
        ]
            LOOP
-- EXECUTE format('ALTER TABLE %I ALTER COLUMN account_id SET NOT NULL;', tableName);
EXECUTE format('ALTER TABLE %I ALTER COLUMN account_id DROP NOT NULL;', tableName);

        END LOOP;
END $$;


-- --- ROLLBACK ---
-- DO $$
--     DECLARE
--         tableName text;
-- BEGIN
--     FOREACH tableName IN ARRAY ARRAY[
--             'tenant_group',
--             'tenant',
--             'suggestion',
--             'subject_type',
--             'subject_rate',
--             'subject_group',
--             'shortcut',
--             'service_template',
--             'service',
--             'role_group',
--             'role',
--             'revisions',
--             'reporting_template_setting',
--             'reporting_template',
--             'reporting_setting',
--             'reporting',
--             'purchase',
--             'product',
--             'pops_statistics',
--             'pops_event_translation',
--             'pops_event_invalid',
--             'pops_event',
--             'pops_alert',
--             'pops',
--             'permission_template',
--             'permission_group',
--             'periodic_payment',
--             'period',
--             'overtime_control',
--             'object_info',
--             'object_group',
--             'object',
--             'news_rss',
--             'news',
--             'mobile_notification_queue',
--             'mobile_notification',
--             'log',
--             'fluctuation',
--             'file',
--             'event',
--             'download',
--             'documentation',
--             'dimension_level',
--             'dimension',
--             'currency',
--             'contract_rate',
--             'contract',
--             'contact_info',
--             'company',
--             'city',
--             'calendar',
--             'billing_type',
--             'attendance_break_type',
--             'attendance_break',
--             'attendance',
--             'agency_rate',
--             'absence'
--         ]
--             LOOP
-- EXECUTE format('ALTER TABLE %I ALTER COLUMN account_id DROP NOT NULL;', tableName);
-- END LOOP;
-- END $$;
