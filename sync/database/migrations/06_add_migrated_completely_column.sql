DO
$$
    DECLARE
        tableName text;
    BEGIN
        FOREACH tableName IN ARRAY ARRAY [
            'absence',
            'absence_type',
            'agency_rate',
            'attendance',
            'attendance_break',
            'attendance_break_type',
            'billing_type',
            'calendar',
            'city',
            'company',
            'contact_info',
            'contract',
            'contract_rate',
            'country',
            'currency',
            'dimension',
            'dimension_level',
            'disciplinary_measure',
            'documentation',
            'download',
            'event',
            'event_severity',
            'event_translation',
            'ext_translations',
            'file',
            'fluctuation',
            'individual_rate',
            'log',
            'message',
            'mobile_info',
            'mobile_notification',
            'mobile_notification_queue',
            'mobile_setting',
            'multi_pops',
            'news',
            'news_rss',
            'notification__message',
            'object',
            'object_group',
            'object_info',
            'overtime_control',
            'pool',
            'pops',
            'pops_alert',
            'pops_event',
            'pops_event_invalid',
            'pops_event_translation',
            'pops_statistics',
            'pops_top_20',
            'period',
            'periodic_payment',
            'permission_group',
            'permission_template',
            'permission_template_permission',
            'product',
            'purchase',
            'reporting',
            'reporting_setting',
            'reporting_template',
            'reporting_template_setting',
            'revisions',
            'role',
            'role_group',
            'service',
            'service_template',
            'shortcut',
            'subject',
            'subject_group',
            'subject_rate',
            'subject_type',
            'suggestion',
            'tenant',
            'tenant_group'
            ]
            LOOP
                EXECUTE format(
                        'ALTER TABLE %I ADD COLUMN migrated_completely boolean DEFAULT false NOT NULL;',
                        tableName);
            END LOOP;
    END
$$;
