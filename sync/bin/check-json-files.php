<?php

declare(strict_types = 1);

namespace App;

use Nette\Utils\FileSystem;
use Nette\Utils\Finder;
use Nette\Utils\Strings;
use SplFileInfo;
use function count;
use function explode;

require __DIR__ . '/../vendor/autoload.php';

$command = new class {

    public function run(): int
    {
        $jsonFiles = Finder::findFiles('*.json')
            ->from(__DIR__ . '/../')
            ->exclude('vendor/*')
            ->getIterator();
        $exitCode = 0;
        /** @var SplFileInfo $json */
        foreach ($jsonFiles as $json) {
            $pathToJsonFile = $json->getRealPath();
            $originalContent = FileSystem::read($pathToJsonFile);
            $doubleEmptyLines = explode("\n\n", $originalContent);
            if (count($doubleEmptyLines) >= 2) {
                $emptyLineAfterString = Strings::substring($doubleEmptyLines[0], -20, 20);
                echo "{$pathToJsonFile} is not properly formatted. Double newline is allowed only one the end of file. Check after `{$emptyLineAfterString}` \n";
                $exitCode = 1;
                continue;
            }
            $originalContentPerLines = explode("\n", $originalContent);
            foreach ($originalContentPerLines as $lineNumber => $originalContentPerLine) {
                if (
                    // missing new line after [
                    Strings::contains($originalContentPerLine, '": ["')
                    // missing space after :
                    || Strings::contains($originalContentPerLine, '":"')
                    // missing new line after {
                    || Strings::contains($originalContentPerLine, '{"') // phpcs:ignore CodingStyle.Strings.ForbidJsonInString.JsonInStringForbidden
                ) {
                    $lineNumberStartingFromOne = $lineNumber + 1;
                    echo "{$pathToJsonFile} is not properly formatted. Check line {$lineNumberStartingFromOne}. \n";
                    $exitCode = 1;
                    break; // only one occurrence per file
                }
            }
        }

        return $exitCode;
    }

};

$exitCode = $command->run();

exit($exitCode); // phpcs:disable Generic.PHP.ForbiddenFunctions.Found
