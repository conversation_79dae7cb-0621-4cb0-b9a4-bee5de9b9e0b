#!/usr/bin/env bash

# old git (<2.18) compatibility hack
unset GIT_DIR

FILES="$(git diff --cached --name-only --diff-filter=ACM)"

# check for windows line endings
CRLFSETTINGS=$(git config core.autocrlf)
if [[ "$CRLFSETTINGS" = "false" ]]
then
	# shellcheck disable=SC2086
	CRLFFILES=$(grep -IUl $'\r' $FILES)
	if [[ "$CRLFFILES" != "" ]]
	then
		bin/echo-red "You have core.autocrlf = false, please change CRLF to LF in:"
		bin/echo-red "$CRLFFILES"
		exit 1
	fi
fi

# check for too long filenames (some encrypted filesystems cannot handle it)
git diff --cached --name-only --diff-filter=ACM | bin/check-long-filenames || exit 1

# check whitespace errors
# shellcheck disable=SC2086
if ! git diff-index --check --cached HEAD -- $FILES; then
	bin/echo-red "Fix whitespace errors before commit!"
	exit 1
fi


backend/bin/pre-commit || exit 1
sync/bin/pre-commit || exit 1
