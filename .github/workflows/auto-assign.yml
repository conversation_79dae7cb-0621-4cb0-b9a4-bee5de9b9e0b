name: Auto Assign

on:
  pull_request:
    types: [opened]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  run:
    if: github.event.pull_request.user.login != 'dependabot[bot]'
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - name: 'Auto-assign'
        uses: pozil/auto-assign-issue@v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          assignees: ${{ github.event.pull_request.user.login }}
          allowSelfAssign: true
          numOfAssignee: 1
