name: Check frontend

on:
  pull_request:
    paths:
      - frontend/**
      - .github/workflows/check-frontend.yml
  push:
    branches: [master]
    paths:
      - frontend/**
      - .github/workflows/check-frontend.yml

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  check:
    runs-on: ubuntu-latest

    name: Check
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "pnpm"
          cache-dependency-path: "./frontend/pnpm-lock.yaml"

      - name: Authenticate NPM
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc
        working-directory: ./frontend

      - name: Install dependencies
        run: pnpm install
        working-directory: ./frontend

      - name: PM3 lints
        run: pnpm pm3 lint
        working-directory: ./frontend

      - name: PM3 build
        run: pnpm pm3 build
        working-directory: ./frontend
