name: Backend Check

on:
  pull_request:
    paths:
      - backend/**
      - .github/**
  push:
    branches: [main]
    paths:
      - backend/**

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

defaults:
  run:
    working-directory: ./backend

jobs:
  check:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: pm-test
          POSTGRES_USER: user
          POSTGRES_PASSWORD: password
        ports:
          - 5432/tcp
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      rabbitmq:
        image: rabbitmq:3
        env:
          RABBITMQ_DEFAULT_USER: rabbitmq
          RABBITMQ_DEFAULT_PASS: rabbitmqy
          RABBITMQ_DEFAULT_VHOST: pm
        ports:
          - 5672

      redis:
        image: redis:latest
        ports:
          - 6379

      mercure:
        image: dunglas/mercure
        env:
          SERVER_NAME: ':4000'
          DEMO: 0
          MERCURE_PUBLISHER_JWT_KEY: ${{ secrets.MERCURE_PUBLISHER_JWT_KEY }}
          MERCURE_SUBSCRIBER_JWT_KEY: ${{ secrets.MERCURE_SUBSCRIBER_JWT_KEY }}
          PUBLISH_ALLOWED_ORIGINS: '*'
          CORS_ALLOWED_ORIGINS: '*'
          DEBUG: 0
          MERCURE_EXTRA_DIRECTIVES: |
            anonymous
            cors_origins *
        ports:
          - 4000

    name: Check
    steps:
      - name: "Checkout"
        uses: actions/checkout@v4

      - name: "Install system shared libraries"
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            librabbitmq-dev

      - name: "Install PHP"
        uses: shivammathur/setup-php@v2
        with:
          coverage: "none"
          php-version: "8.2"
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, amqp-2.1.1, simplexml

      - name: "Validate composer"
        run: composer validate

      - name: "Install dependencies"
        run: composer install --no-interaction --no-progress --no-suggest --prefer-dist
        env:
          DATABASE_URL: "postgres://user:password@127.0.0.1:${{ job.services.postgres.ports['5432'] }}/pm-test?charset=utf8&sslmode=prefer"

      - name: "Cache warm up"
        run: php bin/console cache:warmup --env=test

      - name: "Checks"
        run: composer run-script check

      - name: "JWT tokens"
        run: php bin/console lexik:jwt:generate-keypair --skip-if-exists

      - name: "Prepare test db"
        run: |
          composer prepare-test-database
          php bin/console doctrine:mapping:info
          php bin/console doctrine:schema:validate
        env:
          DATABASE_URL: "postgres://user:password@127.0.0.1:${{ job.services.postgres.ports['5432'] }}/pm-test?charset=utf8&sslmode=prefer"
          REDIS_URL: "redis://127.0.0.1:${{ job.services.redis.ports['6379'] }}/0"

      - name: "Check tests"
        run: composer run-script check:tests
        env:
          DATABASE_URL: "postgres://user:password@127.0.0.1:${{ job.services.postgres.ports['5432'] }}/pm-test?charset=utf8&sslmode=prefer"
          RABBITMQ_HOST: "127.0.0.1"
          RABBITMQ_PORT: ${{ job.services.rabbitmq.ports['5672'] }}
          RABBITMQ_DEFAULT_PORT: ${{ job.services.rabbitmq.ports['5672'] }}
          REDIS_URL: "redis://127.0.0.1:${{ job.services.redis.ports['6379'] }}/0"
          MERCURE_URL: "http://127.0.0.1:${{ job.services.mercure.ports['4000'] }}/.well-known/mercure"
