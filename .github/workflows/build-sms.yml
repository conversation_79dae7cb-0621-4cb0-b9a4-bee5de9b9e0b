name: SMS Build

on:
  push:
    branches:
      - main
    paths:
      - sms-service/**
      - .github/workflows/build-sms.yml
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: select an environment
        options:
          - development
          - production
        default: development

permissions:
  id-token: write
  contents: read

jobs:
  # development
  build-fpm-development:
    if: ${{ github.event_name != 'workflow_dispatch' || inputs.environment == 'development' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::321123738166:role/development_github_pm
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          context: ./sms-service
          file: ./sms-service/docker/php-fpm/Dockerfile
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/fpm:${{ github.sha }}
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/fpm:dev
          build-args: |
            SENTRY_RELEASE=${{ github.sha }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/pm3/sms/fpm:dev
          cache-to: type=inline

  build-nginx-development:
    if: ${{ github.event_name != 'workflow_dispatch' || inputs.environment == 'development' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::321123738166:role/development_github_pm
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          context: ./sms-service
          file: ./sms-service/docker/nginx/Dockerfile
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/nginx:${{ github.sha }}
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/nginx:dev
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/pm3/sms/nginx:dev
          cache-to: type=inline

  deploy-development:
    runs-on: ubuntu-latest
    needs: [build-fpm-development, build-nginx-development]
    container:
      image: bitnami/argo-cd:2.8.2-debian-11-r5
      options: --user root --entrypoint /bin/sh
    env:
      ARGOCD_TOKEN: ${{ secrets.ARGOCD_TOKEN }}
      ARGOCD_SERVER: ${{ secrets.ARGOCD_SERVER }}
    steps:
      - name: set version
        run: |
          argocd app set pm3-sms-development \
            --jsonnet-tla-str tagOverride=${{ github.sha }} \
            --auth-token ${ARGOCD_TOKEN} \
            --grpc-web
      - name: sync app
        run: |
          argocd app sync pm3-sms-development \
            --auth-token ${ARGOCD_TOKEN} \
            --grpc-web \
            --prune

  # production
  build-fpm-production:
    if: ${{ inputs.environment == 'production' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::211125549640:role/production_github_pm
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          context: ./sms-service
          file: ./sms-service/docker/php-fpm/Dockerfile
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/fpm:${{ github.sha }}
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/fpm:prod
          build-args: |
            SENTRY_RELEASE=${{ github.sha }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/pm3/sms/fpm:prod
          cache-to: type=inline

  build-nginx-production:
    if: ${{ inputs.environment == 'production' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::211125549640:role/production_github_pm
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          context: ./sms-service
          file: ./sms-service/docker/nginx/Dockerfile
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/nginx:${{ github.sha }}
            ${{ steps.login-ecr.outputs.registry }}/pm3/sms/nginx:prod
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/pm3/sms/nginx:prod
          cache-to: type=inline

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-fpm-production, build-nginx-production]
    steps:
      - name: clone infrastructure repo
        uses: actions/checkout@v4
        with:
          repository: GoodSailors/infrastructure
          ref: main
          path: infrastructure
          ssh-key: ${{ secrets.INFRASTRUCTURE_SSH_KEY }}
      - name: update versions file
        run: |
          cd infrastructure
          jq -r '.pm3.sms.production.fpm.tag="${{ github.sha }}"' versions.json > /tmp/versions.json
          jq -r '.pm3.sms.production.nginx.tag="${{ github.sha }}"' /tmp/versions.json > versions.json
      - name: show changes
        run: |
          cd infrastructure
          git diff
      - name: commit and push
        run: |
          cd infrastructure
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"
          git add versions.json
          git commit -m "chore: update versions.json (pm3/sms/fpm, pm3/sms/nginx: ${{ github.sha }})"
          git push
